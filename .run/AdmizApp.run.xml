<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="AdmizApp" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
    <option name="ACTIVE_PROFILES" value="dev" />
    <option name="ALTERNATIVE_JRE_PATH" value="$USER_HOME$/.jdks/graalvm-ce-21.0.2" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <module name="private.admiz.admiz.main" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="admiz.AdmizApp" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="admiz.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>