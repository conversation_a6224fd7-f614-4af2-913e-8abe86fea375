package admiz.common.errorhandling


import spock.lang.Specification

class ErrorDetailUtilTest extends Specification {
    def "test_DatabaseNamingStrategy"() {
        given:
        def a = 1;

        when:
        def b = 1

        then:
        a == b
    }

    def "test_of"() {
        when:
        def ofName = ErrorDetailUtil.of(CommonErrors.SERVER_ERROR.name()) ;
        def ofCode = ErrorDetailUtil.of(CommonErrors.SERVER_ERROR.code) ;

        then:
        ofName == CommonErrors.SERVER_ERROR
        ofCode == CommonErrors.SERVER_ERROR
    }

//    def "FindAllErrors"() {
//    }
//
//    def "TestFindAllErrors"() {
//        when:
//        def errors = ErrorDetailUtil.findAllErrors()
//
//        then:
//        errors.size() > 0
//    }
}
