/**
 * HugeRTE version 1.0.9 (2025-03-15)
 * Copyright (c) 2022 Ephox Corporation DBA Tiny Technologies, Inc.
 * Copyright (c) 2024 HugeRTE contributors
 * Licensed under the MIT license (https://github.com/hugerte/hugerte/blob/main/LICENSE.TXT)
 */
/**
 * This file bundles the code of DOMPurify, which is dual-licensed under the Mozilla Public License v2.0 and the Apache License, Version 2.0, meaning you can use it under either one of those licenses.
 * Copyright 2024 Dr.-Ing. <PERSON>, Cure53 and other contributors
 * https://github.com/cure53/DOMPurify/blob/main/LICENSE
 * The code of DOMPurify included in this file has been modified. The latest original code of DOMPurify can be found at https://github.com/cure53/DOMPurify.
 */
!function(){"use strict";var e=function(e){if(null===e)return"null";if(void 0===e)return"undefined";var t=typeof e;return"object"===t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"===t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t},t=function(e){return{eq:e}},n=t((function(e,t){return e===t})),o=function(e){return t((function(t,n){if(t.length!==n.length)return!1;for(var o=t.length,r=0;r<o;r++)if(!e.eq(t[r],n[r]))return!1;return!0}))},r=function(e){return t((function(r,s){var a=Object.keys(r),i=Object.keys(s);if(!function(e,n){return function(e,n){return t((function(t,o){return e.eq(n(t),n(o))}))}(o(e),(function(e){return function(e,t){return Array.prototype.slice.call(e).sort(t)}(e,n)}))}(n).eq(a,i))return!1;for(var l=a.length,d=0;d<l;d++){var c=a[d];if(!e.eq(r[c],s[c]))return!1}return!0}))},s=t((function(t,n){if(t===n)return!0;var a=e(t);return a===e(n)&&(function(e){return-1!==["undefined","boolean","number","string","function","xml","null"].indexOf(e)}(a)?t===n:"array"===a?o(s).eq(t,n):"object"===a&&r(s).eq(t,n))}));const a=Object.getPrototypeOf,i=(e,t,n)=>{var o;return!!n(e,t.prototype)||(null===(o=e.constructor)||void 0===o?void 0:o.name)===t.name},l=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&i(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":t})(t)===e,d=e=>t=>typeof t===e,c=e=>t=>e===t,u=(e,t)=>f(e)&&i(e,t,((e,t)=>a(e)===t)),m=l("string"),f=l("object"),g=e=>u(e,Object),p=l("array"),h=c(null),b=d("boolean"),v=c(void 0),y=e=>null==e,C=e=>!y(e),w=d("function"),E=d("number"),x=(e,t)=>{if(p(e)){for(let n=0,o=e.length;n<o;++n)if(!t(e[n]))return!1;return!0}return!1},_=()=>{},S=(e,t)=>(...n)=>e(t.apply(null,n)),k=(e,t)=>n=>e(t(n)),N=e=>()=>e,R=e=>e,A=(e,t)=>e===t;function T(e,...t){return(...n)=>{const o=t.concat(n);return e.apply(null,o)}}const O=e=>t=>!e(t),B=e=>()=>{throw new Error(e)},P=e=>e(),D=e=>{e()},L=N(!1),M=N(!0);class I{constructor(e,t){this.tag=e,this.value=t}static some(e){return new I(!0,e)}static none(){return I.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?I.some(e(this.value)):I.none()}bind(e){return this.tag?e(this.value):I.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:I.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return C(e)?I.some(e):I.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}I.singletonNone=new I(!1);const F=Array.prototype.slice,U=Array.prototype.indexOf,z=Array.prototype.push,j=(e,t)=>U.call(e,t),H=(e,t)=>j(e,t)>-1,$=(e,t)=>{for(let n=0,o=e.length;n<o;n++)if(t(e[n],n))return!0;return!1},q=(e,t)=>{const n=e.length,o=new Array(n);for(let r=0;r<n;r++){const n=e[r];o[r]=t(n,r)}return o},V=(e,t)=>{for(let n=0,o=e.length;n<o;n++)t(e[n],n)},W=(e,t)=>{for(let n=e.length-1;n>=0;n--)t(e[n],n)},K=(e,t)=>{const n=[],o=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?n:o).push(s)}return{pass:n,fail:o}},Y=(e,t)=>{const n=[];for(let o=0,r=e.length;o<r;o++){const r=e[o];t(r,o)&&n.push(r)}return n},G=(e,t,n)=>(W(e,((e,o)=>{n=t(n,e,o)})),n),X=(e,t,n)=>(V(e,((e,o)=>{n=t(n,e,o)})),n),Z=(e,t,n)=>{for(let o=0,r=e.length;o<r;o++){const r=e[o];if(t(r,o))return I.some(r);if(n(r,o))break}return I.none()},Q=(e,t)=>Z(e,t,L),J=(e,t)=>{for(let n=0,o=e.length;n<o;n++)if(t(e[n],n))return I.some(n);return I.none()},ee=e=>{const t=[];for(let n=0,o=e.length;n<o;++n){if(!p(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);z.apply(t,e[n])}return t},te=(e,t)=>ee(q(e,t)),ne=(e,t)=>{for(let n=0,o=e.length;n<o;++n)if(!0!==t(e[n],n))return!1;return!0},oe=e=>{const t=F.call(e,0);return t.reverse(),t},re=(e,t)=>Y(e,(e=>!H(t,e))),se=(e,t)=>{const n={};for(let o=0,r=e.length;o<r;o++){const r=e[o];n[String(r)]=t(r,o)}return n},ae=(e,t)=>{const n=F.call(e,0);return n.sort(t),n},ie=(e,t)=>t>=0&&t<e.length?I.some(e[t]):I.none(),le=e=>ie(e,0),de=e=>ie(e,e.length-1),ce=w(Array.from)?Array.from:e=>F.call(e),ue=(e,t)=>{for(let n=0;n<e.length;n++){const o=t(e[n],n);if(o.isSome())return o}return I.none()},me=(e,t)=>{const n=[],o=w(t)?e=>$(n,(n=>t(n,e))):e=>H(n,e);for(let t=0,r=e.length;t<r;t++){const r=e[t];o(r)||n.push(r)}return n},fe=Object.keys,ge=Object.hasOwnProperty,pe=(e,t)=>{const n=fe(e);for(let o=0,r=n.length;o<r;o++){const r=n[o];t(e[r],r)}},he=(e,t)=>be(e,((e,n)=>({k:n,v:t(e,n)}))),be=(e,t)=>{const n={};return pe(e,((e,o)=>{const r=t(e,o);n[r.k]=r.v})),n},ve=e=>(t,n)=>{e[n]=t},ye=(e,t,n,o)=>{pe(e,((e,r)=>{(t(e,r)?n:o)(e,r)}))},Ce=(e,t)=>{const n={};return ye(e,t,ve(n),_),n},we=(e,t)=>{const n=[];return pe(e,((e,o)=>{n.push(t(e,o))})),n},Ee=e=>we(e,R),xe=(e,t)=>_e(e,t)?I.from(e[t]):I.none(),_e=(e,t)=>ge.call(e,t),Se=(e,t)=>_e(e,t)&&void 0!==e[t]&&null!==e[t],ke=e=>{const t={};return V(e,(e=>{t[e]={}})),fe(t)},Ne=e=>void 0!==e.length,Re=Array.isArray,Ae=(e,t,n)=>{if(!e)return!1;if(n=n||e,Ne(e)){for(let o=0,r=e.length;o<r;o++)if(!1===t.call(n,e[o],o,e))return!1}else for(const o in e)if(_e(e,o)&&!1===t.call(n,e[o],o,e))return!1;return!0},Te=(e,t)=>{const n=[];return Ae(e,((o,r)=>{n.push(t(o,r,e))})),n},Oe=(e,t)=>{const n=[];return Ae(e,((o,r)=>{t&&!t(o,r,e)||n.push(o)})),n},Be=(e,t,n,o)=>{let r=v(n)?e[0]:n;for(let n=0;n<e.length;n++)r=t.call(o,r,e[n],n);return r},Pe=(e,t,n)=>{for(let o=0,r=e.length;o<r;o++)if(t.call(n,e[o],o,e))return o;return-1},De=e=>e[e.length-1],Le=e=>{let t,n=!1;return(...o)=>(n||(n=!0,t=e.apply(null,o)),t)},Me=()=>Ie(0,0),Ie=(e,t)=>({major:e,minor:t}),Fe={nu:Ie,detect:(e,t)=>{const n=String(t).toLowerCase();return 0===e.length?Me():((e,t)=>{const n=((e,t)=>{for(let n=0;n<e.length;n++){const o=e[n];if(o.test(t))return o}})(e,t);if(!n)return{major:0,minor:0};const o=e=>Number(t.replace(n,"$"+e));return Ie(o(1),o(2))})(e,n)},unknown:Me},Ue=(e,t)=>{const n=String(t).toLowerCase();return Q(e,(e=>e.search(n)))},ze=(e,t,n)=>""===t||e.length>=t.length&&e.substr(n,n+t.length)===t,je=(e,t)=>$e(e,t)?((e,t)=>e.substring(t))(e,t.length):e,He=(e,t,n=0,o)=>{const r=e.indexOf(t,n);return-1!==r&&(!!v(o)||r+t.length<=o)},$e=(e,t)=>ze(e,t,0),qe=(e,t)=>ze(e,t,e.length-t.length),Ve=e=>t=>t.replace(e,""),We=Ve(/^\s+|\s+$/g),Ke=Ve(/^\s+/g),Ye=Ve(/\s+$/g),Ge=e=>e.length>0,Xe=e=>!Ge(e),Ze=(e,t=10)=>{const n=parseInt(e,t);return isNaN(n)?I.none():I.some(n)},Qe=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Je=e=>t=>He(t,e),et=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>He(e,"edge/")&&He(e,"chrome")&&He(e,"safari")&&He(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Qe],search:e=>He(e,"chrome")&&!He(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>He(e,"msie")||He(e,"trident")},{name:"Opera",versionRegexes:[Qe,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Je("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Je("firefox")},{name:"Safari",versionRegexes:[Qe,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(He(e,"safari")||He(e,"mobile/"))&&He(e,"applewebkit")}],tt=[{name:"Windows",search:Je("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>He(e,"iphone")||He(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Je("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Je("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Je("linux"),versionRegexes:[]},{name:"Solaris",search:Je("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Je("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Je("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],nt={browsers:N(et),oses:N(tt)},ot="Edge",rt="Chromium",st="Opera",at="Firefox",it="Safari",lt=e=>{const t=e.current,n=e.version,o=e=>()=>t===e;return{current:t,version:n,isEdge:o(ot),isChromium:o(rt),isIE:o("IE"),isOpera:o(st),isFirefox:o(at),isSafari:o(it)}},dt=()=>lt({current:void 0,version:Fe.unknown()}),ct=lt,ut=(N(ot),N(rt),N("IE"),N(st),N(at),N(it),"Windows"),mt="Android",ft="Linux",gt="macOS",pt="Solaris",ht="FreeBSD",bt="ChromeOS",vt=e=>{const t=e.current,n=e.version,o=e=>()=>t===e;return{current:t,version:n,isWindows:o(ut),isiOS:o("iOS"),isAndroid:o(mt),isMacOS:o(gt),isLinux:o(ft),isSolaris:o(pt),isFreeBSD:o(ht),isChromeOS:o(bt)}},yt=()=>vt({current:void 0,version:Fe.unknown()}),Ct=vt,wt=(N(ut),N("iOS"),N(mt),N(ft),N(gt),N(pt),N(ht),N(bt),e=>window.matchMedia(e).matches);let Et=Le((()=>((e,t,n)=>{const o=nt.browsers(),r=nt.oses(),s=t.bind((e=>((e,t)=>ue(t.brands,(t=>{const n=t.brand.toLowerCase();return Q(e,(e=>{var t;return n===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Fe.nu(parseInt(t.version,10),0)})))})))(o,e))).orThunk((()=>((e,t)=>Ue(e,t).map((e=>{const n=Fe.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(o,e))).fold(dt,ct),a=((e,t)=>Ue(e,t).map((e=>{const n=Fe.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(r,e).fold(yt,Ct),i=((e,t,n,o)=>{const r=e.isiOS()&&!0===/ipad/i.test(n),s=e.isiOS()&&!r,a=e.isiOS()||e.isAndroid(),i=a||o("(pointer:coarse)"),l=r||!s&&a&&o("(min-device-width:768px)"),d=s||a&&!l,c=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(n),u=!d&&!l&&!c;return{isiPad:N(r),isiPhone:N(s),isTablet:N(l),isPhone:N(d),isTouch:N(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:N(c),isDesktop:N(u)}})(a,s,e,n);return{browser:s,os:a,deviceType:i}})(navigator.userAgent,I.from(navigator.userAgentData),wt)));const xt=()=>Et(),_t=navigator.userAgent,St=xt(),kt=St.browser,Nt=St.os,Rt=St.deviceType,At=-1!==_t.indexOf("Windows Phone"),Tt={transparentSrc:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",documentMode:kt.isIE()?document.documentMode||7:10,cacheSuffix:null,container:null,canHaveCSP:!kt.isIE(),windowsPhone:At,browser:{current:kt.current,version:kt.version,isChromium:kt.isChromium,isEdge:kt.isEdge,isFirefox:kt.isFirefox,isIE:kt.isIE,isOpera:kt.isOpera,isSafari:kt.isSafari},os:{current:Nt.current,version:Nt.version,isAndroid:Nt.isAndroid,isChromeOS:Nt.isChromeOS,isFreeBSD:Nt.isFreeBSD,isiOS:Nt.isiOS,isLinux:Nt.isLinux,isMacOS:Nt.isMacOS,isSolaris:Nt.isSolaris,isWindows:Nt.isWindows},deviceType:{isDesktop:Rt.isDesktop,isiPad:Rt.isiPad,isiPhone:Rt.isiPhone,isPhone:Rt.isPhone,isTablet:Rt.isTablet,isTouch:Rt.isTouch,isWebView:Rt.isWebView}},Ot=e=>y(e)?"":(""+e).trim(),Bt=function(e,t,n,o){o=o||this,e&&(n&&(e=e[n]),Ae(e,((e,r)=>!1!==t.call(o,e,r,n)&&(Bt(e,t,n,o),!0))))},Pt={trim:Ot,isArray:Re,is:(e,t)=>t?!("array"!==t||!Re(e))||typeof e===t:void 0!==e,toArray:e=>{if(Re(e))return e;{const t=[];for(let n=0,o=e.length;n<o;n++)t[n]=e[n];return t}},makeMap:(e,t,n={})=>{const o=m(e)?e.split(t||","):e||[];let r=o.length;for(;r--;)n[o[r]]={};return n},each:Ae,map:Te,grep:Oe,inArray:(e,t)=>{if(e)for(let n=0,o=e.length;n<o;n++)if(e[n]===t)return n;return-1},hasOwn:_e,extend:(e,...t)=>{for(let n=0;n<t.length;n++){const o=t[n];for(const t in o)if(_e(o,t)){const n=o[t];void 0!==n&&(e[t]=n)}}return e},walk:Bt,resolve:(e,t=window)=>{const n=e.split(".");for(let e=0,o=n.length;e<o&&(t=t[n[e]]);e++);return t},explode:(e,t)=>p(e)?e:""===e?[]:Te(e.split(t||","),Ot),_addCacheSuffix:e=>{const t=Tt.cacheSuffix;return t&&(e+=(-1===e.indexOf("?")?"?":"&")+t),e}},Dt=(e,t,n=A)=>e.exists((e=>n(e,t))),Lt=(e,t,n=A)=>Mt(e,t,n).getOr(e.isNone()&&t.isNone()),Mt=(e,t,n)=>e.isSome()&&t.isSome()?I.some(n(e.getOrDie(),t.getOrDie())):I.none(),It=(e,t)=>e?I.some(t):I.none(),Ft="undefined"!=typeof window?window:Function("return this;")(),Ut=(e,t)=>((e,t)=>{let n=null!=t?t:Ft;for(let t=0;t<e.length&&null!=n;++t)n=n[e[t]];return n})(e.split("."),t),zt=Object.getPrototypeOf,jt=e=>{const t=Ut("ownerDocument.defaultView",e);return f(e)&&((e=>((e,t)=>{const n=((e,t)=>Ut(e,t))(e,t);if(null==n)throw new Error(e+" not available on this browser");return n})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(zt(e).constructor.name))},Ht=e=>e.dom.nodeName.toLowerCase(),$t=e=>e.dom.nodeType,qt=e=>t=>$t(t)===e,Vt=e=>Wt(e)&&jt(e.dom),Wt=qt(1),Kt=qt(3),Yt=qt(9),Gt=qt(11),Xt=e=>t=>Wt(t)&&Ht(t)===e,Zt=(e,t,n)=>{if(!(m(n)||b(n)||E(n)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},Qt=(e,t,n)=>{Zt(e.dom,t,n)},Jt=(e,t)=>{const n=e.dom;pe(t,((e,t)=>{Zt(n,t,e)}))},en=(e,t)=>{const n=e.dom.getAttribute(t);return null===n?void 0:n},tn=(e,t)=>I.from(en(e,t)),nn=(e,t)=>{const n=e.dom;return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},on=(e,t)=>{e.dom.removeAttribute(t)},rn=e=>X(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),sn=(e,t)=>{const n=en(e,t);return void 0===n||""===n?[]:n.split(" ")},an=e=>void 0!==e.dom.classList,ln=e=>sn(e,"class"),dn=(e,t)=>((e,t,n)=>{const o=sn(e,t).concat([n]);return Qt(e,t,o.join(" ")),!0})(e,"class",t),cn=(e,t)=>((e,t,n)=>{const o=Y(sn(e,t),(e=>e!==n));return o.length>0?Qt(e,t,o.join(" ")):on(e,t),!1})(e,"class",t),un=(e,t)=>{an(e)?e.dom.classList.add(t):dn(e,t)},mn=e=>{0===(an(e)?e.dom.classList:ln(e)).length&&on(e,"class")},fn=(e,t)=>{an(e)?e.dom.classList.remove(t):cn(e,t),mn(e)},gn=(e,t)=>an(e)&&e.dom.classList.contains(t),pn=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},hn=(e,t)=>{const n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||n.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return pn(n.childNodes[0])},bn=(e,t)=>{const n=(t||document).createElement(e);return pn(n)},vn=(e,t)=>{const n=(t||document).createTextNode(e);return pn(n)},yn=pn,Cn=(e,t,n)=>I.from(e.dom.elementFromPoint(t,n)).map(pn),wn=(e,t)=>{const n=[],o=e=>(n.push(e),t(e));let r=t(e);do{r=r.bind(o)}while(r.isSome());return n},En=(e,t)=>{const n=e.dom;if(1!==n.nodeType)return!1;{const e=n;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},xn=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,_n=(e,t)=>e.dom===t.dom,Sn=(e,t)=>{const n=e.dom,o=t.dom;return n!==o&&n.contains(o)},kn=e=>yn(e.dom.ownerDocument),Nn=e=>Yt(e)?e:kn(e),Rn=e=>yn(Nn(e).dom.defaultView),An=e=>I.from(e.dom.parentNode).map(yn),Tn=e=>I.from(e.dom.parentElement).map(yn),On=(e,t)=>{const n=w(t)?t:L;let o=e.dom;const r=[];for(;null!==o.parentNode&&void 0!==o.parentNode;){const e=o.parentNode,t=yn(e);if(r.push(t),!0===n(t))break;o=e}return r},Bn=e=>I.from(e.dom.previousSibling).map(yn),Pn=e=>I.from(e.dom.nextSibling).map(yn),Dn=e=>oe(wn(e,Bn)),Ln=e=>wn(e,Pn),Mn=e=>q(e.dom.childNodes,yn),In=(e,t)=>{const n=e.dom.childNodes;return I.from(n[t]).map(yn)},Fn=e=>In(e,0),Un=e=>In(e,e.dom.childNodes.length-1),zn=e=>e.dom.childNodes.length,jn=e=>Gt(e)&&C(e.dom.host),Hn=w(Element.prototype.attachShadow)&&w(Node.prototype.getRootNode),$n=N(Hn),qn=Hn?e=>yn(e.dom.getRootNode()):Nn,Vn=e=>jn(e)?e:(e=>{const t=e.dom.head;if(null==t)throw new Error("Head is not available yet");return yn(t)})(Nn(e)),Wn=e=>yn(e.dom.host),Kn=e=>{if($n()&&C(e.target)){const t=yn(e.target);if(Wt(t)&&Yn(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return le(t)}}return I.from(e.target)},Yn=e=>C(e.dom.shadowRoot),Gn=e=>{const t=Kt(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const n=t.ownerDocument;return(e=>{const t=qn(e);return jn(t)?I.some(t):I.none()})(yn(t)).fold((()=>n.body.contains(t)),k(Gn,Wn))};var Xn=(e,t,n,o,r)=>e(n,o)?I.some(n):w(r)&&r(n)?I.none():t(n,o,r);const Zn=(e,t,n)=>{let o=e.dom;const r=w(n)?n:L;for(;o.parentNode;){o=o.parentNode;const e=yn(o);if(t(e))return I.some(e);if(r(e))break}return I.none()},Qn=(e,t,n)=>Xn(((e,t)=>t(e)),Zn,e,t,n),Jn=(e,t)=>{const n=e=>{for(let o=0;o<e.childNodes.length;o++){const r=yn(e.childNodes[o]);if(t(r))return I.some(r);const s=n(e.childNodes[o]);if(s.isSome())return s}return I.none()};return n(e.dom)},eo=(e,t,n)=>Zn(e,(e=>En(e,t)),n),to=(e,t)=>((e,t)=>{const n=void 0===t?document:t.dom;return xn(n)?I.none():I.from(n.querySelector(e)).map(yn)})(t,e),no=(e,t,n)=>Xn(((e,t)=>En(e,t)),eo,e,t,n),oo=(e,t=!1)=>{return Gn(e)?e.dom.isContentEditable:(n=e,no(n,"[contenteditable]")).fold(N(t),(e=>"true"===ro(e)));var n},ro=e=>e.dom.contentEditable,so=e=>void 0!==e.style&&w(e.style.getPropertyValue),ao=(e,t,n)=>{if(!m(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);so(e)&&e.style.setProperty(t,n)},io=(e,t,n)=>{const o=e.dom;ao(o,t,n)},lo=(e,t)=>{const n=e.dom;pe(t,((e,t)=>{ao(n,t,e)}))},co=(e,t)=>{const n=e.dom,o=window.getComputedStyle(n).getPropertyValue(t);return""!==o||Gn(e)?o:uo(n,t)},uo=(e,t)=>so(e)?e.style.getPropertyValue(t):"",mo=(e,t)=>{const n=e.dom,o=uo(n,t);return I.from(o).filter((e=>e.length>0))},fo=e=>{const t={},n=e.dom;if(so(n))for(let e=0;e<n.style.length;e++){const o=n.style.item(e);t[o]=n.style[o]}return t},go=(e,t)=>{((e,t)=>{so(e)&&e.style.removeProperty(t)})(e.dom,t),Dt(tn(e,"style").map(We),"")&&on(e,"style")},po=(e,t)=>{An(e).each((n=>{n.dom.insertBefore(t.dom,e.dom)}))},ho=(e,t)=>{Pn(e).fold((()=>{An(e).each((e=>{vo(e,t)}))}),(e=>{po(e,t)}))},bo=(e,t)=>{Fn(e).fold((()=>{vo(e,t)}),(n=>{e.dom.insertBefore(t.dom,n.dom)}))},vo=(e,t)=>{e.dom.appendChild(t.dom)},yo=(e,t)=>{po(e,t),vo(t,e)},Co=(e,t)=>{V(t,(t=>{vo(e,t)}))},wo=e=>{e.dom.textContent="",V(Mn(e),(e=>{Eo(e)}))},Eo=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},xo=e=>{const t=Mn(e);var n,o;t.length>0&&(n=e,V(o=t,((e,t)=>{const r=0===t?n:o[t-1];ho(r,e)}))),Eo(e)},_o=e=>q(e,yn),So=e=>e.dom.innerHTML,ko=(e,t)=>{const n=kn(e).dom,o=yn(n.createDocumentFragment()),r=((e,t)=>{const n=(t||document).createElement("div");return n.innerHTML=e,Mn(yn(n))})(t,n);Co(o,r),wo(e),vo(e,o)},No=(e,t,n,o)=>((e,t,n,o,r)=>{const s=((e,t)=>n=>{e(n)&&t((e=>{const t=yn(Kn(e).getOr(e.target)),n=()=>e.stopPropagation(),o=()=>e.preventDefault(),r=S(o,n);return((e,t,n,o,r,s,a)=>({target:e,x:t,y:n,stop:o,prevent:r,kill:s,raw:a}))(t,e.clientX,e.clientY,n,o,r,e)})(n))})(n,o);return e.dom.addEventListener(t,s,false),{unbind:T(Ro,e,t,s,false)}})(e,t,n,o),Ro=(e,t,n,o)=>{e.dom.removeEventListener(t,n,o)},Ao=(e,t)=>({left:e,top:t,translate:(n,o)=>Ao(e+n,t+o)}),To=Ao,Oo=(e,t)=>void 0!==e?e:void 0!==t?t:0,Bo=e=>{const t=e.dom,n=t.ownerDocument.body;return n===t?To(n.offsetLeft,n.offsetTop):Gn(e)?(e=>{const t=e.getBoundingClientRect();return To(t.left,t.top)})(t):To(0,0)},Po=e=>{const t=void 0!==e?e.dom:document,n=t.body.scrollLeft||t.documentElement.scrollLeft,o=t.body.scrollTop||t.documentElement.scrollTop;return To(n,o)},Do=(e,t,n)=>{const o=(void 0!==n?n.dom:document).defaultView;o&&o.scrollTo(e,t)},Lo=(e,t)=>{xt().browser.isSafari()&&w(e.dom.scrollIntoViewIfNeeded)?e.dom.scrollIntoViewIfNeeded(!1):e.dom.scrollIntoView(t)},Mo=(e,t,n,o)=>({x:e,y:t,width:n,height:o,right:e+n,bottom:t+o}),Io=e=>{const t=void 0===e?window:e,n=t.document,o=Po(yn(n));return(e=>{const t=void 0===e?window:e;return xt().browser.isFirefox()?I.none():I.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,n=e.clientWidth,r=e.clientHeight;return Mo(o.left,o.top,n,r)}),(e=>Mo(Math.max(e.pageLeft,o.left),Math.max(e.pageTop,o.top),e.width,e.height)))},Fo=(e,t)=>{let n=[];return V(Mn(e),(e=>{t(e)&&(n=n.concat([e])),n=n.concat(Fo(e,t))})),n},Uo=(e,t)=>((e,t)=>{const n=void 0===t?document:t.dom;return xn(n)?[]:q(n.querySelectorAll(e),yn)})(t,e),zo=(e,t,n)=>Zn(e,t,n).isSome(),jo=(e,t)=>((e,t)=>{const n=e.dom;return n.parentNode?((e,t)=>Q(e.dom.childNodes,(e=>t(yn(e)))).map(yn))(yn(n.parentNode),(n=>!_n(e,n)&&t(n))):I.none()})(e,t).isSome(),Ho=(e,t)=>Jn(e,t).isSome();class $o{constructor(e,t){this.node=e,this.rootNode=t,this.current=this.current.bind(this),this.next=this.next.bind(this),this.prev=this.prev.bind(this),this.prev2=this.prev2.bind(this)}current(){return this.node}next(e){return this.node=this.findSibling(this.node,"firstChild","nextSibling",e),this.node}prev(e){return this.node=this.findSibling(this.node,"lastChild","previousSibling",e),this.node}prev2(e){return this.node=this.findPreviousNode(this.node,e),this.node}findSibling(e,t,n,o){if(e){if(!o&&e[t])return e[t];if(e!==this.rootNode){let t=e[n];if(t)return t;for(let o=e.parentNode;o&&o!==this.rootNode;o=o.parentNode)if(t=o[n],t)return t}}}findPreviousNode(e,t){if(e){const n=e.previousSibling;if(this.rootNode&&n===this.rootNode)return;if(n){if(!t)for(let e=n.lastChild;e;e=e.lastChild)if(!e.lastChild)return e;return n}const o=e.parentNode;if(o&&o!==this.rootNode)return o}}}const qo="\ufeff",Vo="\xa0",Wo=e=>e===qo,Ko=/^[ \t\r\n]*$/,Yo=e=>Ko.test(e),Go=e=>"\n"===e||"\r"===e,Xo=(e,t=4,n=!0,o=!0)=>{const r=((e,t)=>t<=0?"":new Array(t+1).join(" "))(0,t),s=e.replace(/\t/g,r),a=X(s,((e,t)=>(e=>-1!==" \f\t\v".indexOf(e))(t)||t===Vo?e.pcIsSpace||""===e.str&&n||e.str.length===s.length-1&&o||((e,t)=>t<e.length&&t>=0&&Go(e[t]))(s,e.str.length+1)?{pcIsSpace:!1,str:e.str+Vo}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:Go(t),str:e.str+t}),{pcIsSpace:!1,str:""});return a.str},Zo=e=>t=>!!t&&t.nodeType===e,Qo=e=>!!e&&!Object.getPrototypeOf(e),Jo=Zo(1),er=e=>Jo(e)&&Vt(yn(e)),tr=e=>{const t=e.toLowerCase();return e=>C(e)&&e.nodeName.toLowerCase()===t},nr=e=>{const t=e.map((e=>e.toLowerCase()));return e=>{if(e&&e.nodeName){const n=e.nodeName.toLowerCase();return H(t,n)}return!1}},or=(e,t)=>{const n=t.toLowerCase().split(" ");return t=>{if(Jo(t)){const o=t.ownerDocument.defaultView;if(o)for(let r=0;r<n.length;r++){const s=o.getComputedStyle(t,null);if((s?s.getPropertyValue(e):null)===n[r])return!0}}return!1}},rr=e=>Jo(e)&&e.hasAttribute("data-mce-bogus"),sr=e=>Jo(e)&&"TABLE"===e.tagName,ar=e=>t=>{if(er(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},ir=nr(["textarea","input"]),lr=Zo(3),dr=Zo(4),cr=Zo(7),ur=Zo(8),mr=Zo(9),fr=Zo(11),gr=tr("br"),pr=tr("img"),hr=ar("true"),br=ar("false"),vr=nr(["td","th"]),yr=nr(["td","th","caption"]),Cr=nr(["video","audio","object","embed"]),wr=tr("li"),Er=tr("details"),xr=tr("summary"),_r={skipBogus:!0,includeZwsp:!1,checkRootAsContent:!1},Sr=("data-mce-bookmark",e=>Jo(e)&&e.hasAttribute("data-mce-bookmark"));const kr=(e,t,n,o)=>lr(e)&&!((e,t,n)=>Yo(e.data)&&!((e,t,n)=>{const o=yn(t),r=yn(e),s=n.getWhitespaceElements();return zo(r,(e=>_e(s,Ht(e))),T(_n,o))})(e,t,n))(e,t,n)&&(!o.includeZwsp||!(e=>{for(const t of e)if(!Wo(t))return!1;return!0})(e.data)),Nr=(e,t,n,o)=>w(o.isContent)&&o.isContent(t)||((e,t)=>Jo(e)&&_e(t.getNonEmptyElements(),e.nodeName))(t,e)||Sr(t)||(e=>Jo(e)&&"A"===e.nodeName&&!e.hasAttribute("href")&&(e.hasAttribute("name")||e.hasAttribute("id")))(t)||kr(t,n,e,o)||br(t)||hr(t)&&(e=>Tn(yn(e)).exists((e=>!oo(e))))(t),Rr=(e,t,n)=>{const o={..._r,...n};if(o.checkRootAsContent&&Nr(e,t,t,o))return!1;let r=t.firstChild,s=0;if(!r)return!0;const a=new $o(r,t);do{if(o.skipBogus&&Jo(r)){const e=r.getAttribute("data-mce-bogus");if(e){r=a.next("all"===e);continue}}if(ur(r))r=a.next(!0);else if(gr(r))s++,r=a.next();else{if(Nr(e,r,t,o))return!1;r=a.next()}}while(r);return s<=1},Ar=(e,t,n)=>Rr(e,t.dom,{checkRootAsContent:!0,...n}),Tr=(e,t,n)=>Nr(e,t,t,{includeZwsp:_r.includeZwsp,...n}),Or=e=>"svg"===e.toLowerCase(),Br=e=>Or(e.nodeName),Pr=e=>"svg"===(null==e?void 0:e.nodeName)?"svg":"html",Dr=["svg"],Lr="data-mce-block",Mr=e=>q((e=>Y(fe(e),(e=>!/[A-Z]/.test(e))))(e),(e=>`${e}:`+q(Dr,(t=>`not(${t} ${e})`)).join(":"))).join(","),Ir=(e,t)=>C(t.querySelector(e))?(t.setAttribute(Lr,"true"),"inline-boundary"===t.getAttribute("data-mce-selected")&&t.removeAttribute("data-mce-selected"),!0):(t.removeAttribute(Lr),!1),Fr=(e,t)=>{const n=Mr(e.getTransparentElements()),o=Mr(e.getBlockElements());return Y(t.querySelectorAll(n),(e=>Ir(o,e)))},Ur=(e,t,n)=>{var o;const r=n?"lastChild":"firstChild";for(let n=t[r];n;n=n[r])if(Rr(e,n,{checkRootAsContent:!0}))return void(null===(o=n.parentNode)||void 0===o||o.removeChild(n))},zr=(e,t,n)=>{const o=e.getBlockElements(),r=yn(t),s=e=>Ht(e)in o,a=e=>_n(e,r);V(_o(n),(t=>{Zn(t,s,a).each((n=>{const o=((t,o)=>Y(Mn(t),(t=>s(t)&&!e.isValidChild(Ht(n),Ht(t)))))(t);if(o.length>0){const t=Tn(n);V(o,(t=>{Zn(t,s,a).each((n=>{((e,t,n)=>{const o=document.createRange(),r=t.parentNode;if(r){o.setStartBefore(t),o.setEndBefore(n);const s=o.extractContents();Ur(e,s,!0),o.setStartAfter(n),o.setEndAfter(t);const a=o.extractContents();Ur(e,a,!1),Rr(e,s,{checkRootAsContent:!0})||r.insertBefore(s,t),Rr(e,n,{checkRootAsContent:!0})||r.insertBefore(n,t),Rr(e,a,{checkRootAsContent:!0})||r.insertBefore(a,t),r.removeChild(t)}})(e,n.dom,t.dom)}))})),t.each((t=>Fr(e,t.dom)))}}))}))},jr=(e,t)=>{const n=Fr(e,t);zr(e,t,n),((e,t,n)=>{V([...n,...Wr(e,t)?[t]:[]],(t=>V(Uo(yn(t),t.nodeName.toLowerCase()),(t=>{Kr(e,t.dom)&&xo(t)}))))})(e,t,n)},Hr=(e,t)=>{if(Vr(e,t)){const n=Mr(e.getBlockElements());Ir(n,t)}},$r=e=>e.hasAttribute(Lr),qr=(e,t)=>_e(e.getTransparentElements(),t),Vr=(e,t)=>Jo(t)&&qr(e,t.nodeName),Wr=(e,t)=>Vr(e,t)&&$r(t),Kr=(e,t)=>Vr(e,t)&&!$r(t),Yr=(e,t)=>1===t.type&&qr(e,t.name)&&m(t.attr(Lr)),Gr=xt().browser,Xr=e=>Q(e,Wt),Zr=(e,t)=>e.children&&H(e.children,t),Qr=(e,t={})=>{let n=0;const o={},r=yn(e),s=Nn(r),a=e=>{vo(Vn(r),e)},i=e=>{const t=Vn(r);to(t,"#"+e).each(Eo)},l=e=>xe(o,e).getOrThunk((()=>({id:"mce-u"+n++,passed:[],failed:[],count:0}))),d=e=>new Promise(((n,r)=>{let i;const d=Pt._addCacheSuffix(e),c=l(d);o[d]=c,c.count++;const u=(e,t)=>{V(e,D),c.status=t,c.passed=[],c.failed=[],i&&(i.onload=null,i.onerror=null,i=null)},m=()=>u(c.passed,2),f=()=>u(c.failed,3);if(n&&c.passed.push(n),r&&c.failed.push(r),1===c.status)return;if(2===c.status)return void m();if(3===c.status)return void f();c.status=1;const g=bn("link",s.dom);Jt(g,{rel:"stylesheet",type:"text/css",id:c.id}),t.contentCssCors&&Qt(g,"crossOrigin","anonymous"),t.referrerPolicy&&Qt(g,"referrerpolicy",t.referrerPolicy),i=g.dom,i.onload=m,i.onerror=f,a(g),Qt(g,"href",d)})),c=e=>{const t=Pt._addCacheSuffix(e);xe(o,t).each((e=>{0==--e.count&&(delete o[t],i(e.id))}))};return{load:d,loadRawCss:(e,t)=>{const n=l(e);o[e]=n,n.count++;const r=bn("style",s.dom);Jt(r,{rel:"stylesheet",type:"text/css",id:n.id}),r.dom.innerHTML=t,a(r)},loadAll:e=>Promise.allSettled(q(e,(e=>d(e).then(N(e))))).then((e=>{const t=K(e,(e=>"fulfilled"===e.status));return t.fail.length>0?Promise.reject(q(t.fail,(e=>e.reason))):q(t.pass,(e=>e.value))})),unload:c,unloadRawCss:e=>{xe(o,e).each((t=>{0==--t.count&&(delete o[e],i(t.id))}))},unloadAll:e=>{V(e,(e=>{c(e)}))},_setReferrerPolicy:e=>{t.referrerPolicy=e},_setContentCssCors:e=>{t.contentCssCors=e}}},Jr=(()=>{const e=new WeakMap;return{forElement:(t,n)=>{const o=qn(t).dom;return I.from(e.get(o)).getOrThunk((()=>{const t=Qr(o,n);return e.set(o,t),t}))}}})(),es=(e,t)=>C(e)&&(Tr(t,e)||t.isInline(e.nodeName.toLowerCase())),ts=e=>(e=>"span"===e.nodeName.toLowerCase())(e)&&"bookmark"===e.getAttribute("data-mce-type"),ns=(e,t,n,o)=>{var r;const s=o||t;if(Jo(t)&&ts(t))return t;const a=t.childNodes;for(let t=a.length-1;t>=0;t--)ns(e,a[t],n,s);if(Jo(t)){const e=t.childNodes;1===e.length&&ts(e[0])&&(null===(r=t.parentNode)||void 0===r||r.insertBefore(e[0],t))}return(e=>fr(e)||mr(e))(t)||Tr(n,t)||(e=>!!Jo(e)&&e.childNodes.length>0)(t)||((e,t,n)=>lr(e)&&e.data.length>0&&((e,t,n)=>{const o=new $o(e,t).prev(!1),r=new $o(e,t).next(!1),s=v(o)||es(o,n),a=v(r)||es(r,n);return s&&a})(e,t,n))(t,s,n)||e.remove(t),t},os=Pt.makeMap,rs=/[&<>\"\u0060\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,ss=/[<>&\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,as=/[<>&\"\']/g,is=/&#([a-z0-9]+);?|&([a-z0-9]+);/gi,ls={128:"\u20ac",130:"\u201a",131:"\u0192",132:"\u201e",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02c6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017d",145:"\u2018",146:"\u2019",147:"\u201c",148:"\u201d",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02dc",153:"\u2122",154:"\u0161",155:"\u203a",156:"\u0153",158:"\u017e",159:"\u0178"},ds={'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;","&":"&amp;","`":"&#96;"},cs={"&lt;":"<","&gt;":">","&amp;":"&","&quot;":'"',"&apos;":"'"},us=(e,t)=>{const n={};if(e){const o=e.split(",");t=t||10;for(let e=0;e<o.length;e+=2){const r=String.fromCharCode(parseInt(o[e],t));if(!ds[r]){const t="&"+o[e+1]+";";n[r]=t,n[t]=r}}return n}},ms=us("50,nbsp,51,iexcl,52,cent,53,pound,54,curren,55,yen,56,brvbar,57,sect,58,uml,59,copy,5a,ordf,5b,laquo,5c,not,5d,shy,5e,reg,5f,macr,5g,deg,5h,plusmn,5i,sup2,5j,sup3,5k,acute,5l,micro,5m,para,5n,middot,5o,cedil,5p,sup1,5q,ordm,5r,raquo,5s,frac14,5t,frac12,5u,frac34,5v,iquest,60,Agrave,61,Aacute,62,Acirc,63,Atilde,64,Auml,65,Aring,66,AElig,67,Ccedil,68,Egrave,69,Eacute,6a,Ecirc,6b,Euml,6c,Igrave,6d,Iacute,6e,Icirc,6f,Iuml,6g,ETH,6h,Ntilde,6i,Ograve,6j,Oacute,6k,Ocirc,6l,Otilde,6m,Ouml,6n,times,6o,Oslash,6p,Ugrave,6q,Uacute,6r,Ucirc,6s,Uuml,6t,Yacute,6u,THORN,6v,szlig,70,agrave,71,aacute,72,acirc,73,atilde,74,auml,75,aring,76,aelig,77,ccedil,78,egrave,79,eacute,7a,ecirc,7b,euml,7c,igrave,7d,iacute,7e,icirc,7f,iuml,7g,eth,7h,ntilde,7i,ograve,7j,oacute,7k,ocirc,7l,otilde,7m,ouml,7n,divide,7o,oslash,7p,ugrave,7q,uacute,7r,ucirc,7s,uuml,7t,yacute,7u,thorn,7v,yuml,ci,fnof,sh,Alpha,si,Beta,sj,Gamma,sk,Delta,sl,Epsilon,sm,Zeta,sn,Eta,so,Theta,sp,Iota,sq,Kappa,sr,Lambda,ss,Mu,st,Nu,su,Xi,sv,Omicron,t0,Pi,t1,Rho,t3,Sigma,t4,Tau,t5,Upsilon,t6,Phi,t7,Chi,t8,Psi,t9,Omega,th,alpha,ti,beta,tj,gamma,tk,delta,tl,epsilon,tm,zeta,tn,eta,to,theta,tp,iota,tq,kappa,tr,lambda,ts,mu,tt,nu,tu,xi,tv,omicron,u0,pi,u1,rho,u2,sigmaf,u3,sigma,u4,tau,u5,upsilon,u6,phi,u7,chi,u8,psi,u9,omega,uh,thetasym,ui,upsih,um,piv,812,bull,816,hellip,81i,prime,81j,Prime,81u,oline,824,frasl,88o,weierp,88h,image,88s,real,892,trade,89l,alefsym,8cg,larr,8ch,uarr,8ci,rarr,8cj,darr,8ck,harr,8dl,crarr,8eg,lArr,8eh,uArr,8ei,rArr,8ej,dArr,8ek,hArr,8g0,forall,8g2,part,8g3,exist,8g5,empty,8g7,nabla,8g8,isin,8g9,notin,8gb,ni,8gf,prod,8gh,sum,8gi,minus,8gn,lowast,8gq,radic,8gt,prop,8gu,infin,8h0,ang,8h7,and,8h8,or,8h9,cap,8ha,cup,8hb,int,8hk,there4,8hs,sim,8i5,cong,8i8,asymp,8j0,ne,8j1,equiv,8j4,le,8j5,ge,8k2,sub,8k3,sup,8k4,nsub,8k6,sube,8k7,supe,8kl,oplus,8kn,otimes,8l5,perp,8m5,sdot,8o8,lceil,8o9,rceil,8oa,lfloor,8ob,rfloor,8p9,lang,8pa,rang,9ea,loz,9j0,spades,9j3,clubs,9j5,hearts,9j6,diams,ai,OElig,aj,oelig,b0,Scaron,b1,scaron,bo,Yuml,m6,circ,ms,tilde,802,ensp,803,emsp,809,thinsp,80c,zwnj,80d,zwj,80e,lrm,80f,rlm,80j,ndash,80k,mdash,80o,lsquo,80p,rsquo,80q,sbquo,80s,ldquo,80t,rdquo,80u,bdquo,810,dagger,811,Dagger,81g,permil,81p,lsaquo,81q,rsaquo,85c,euro",32),fs=(e,t)=>e.replace(t?rs:ss,(e=>ds[e]||e)),gs=(e,t)=>e.replace(t?rs:ss,(e=>e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":ds[e]||"&#"+e.charCodeAt(0)+";")),ps=(e,t,n)=>{const o=n||ms;return e.replace(t?rs:ss,(e=>ds[e]||o[e]||e))},hs={encodeRaw:fs,encodeAllRaw:e=>(""+e).replace(as,(e=>ds[e]||e)),encodeNumeric:gs,encodeNamed:ps,getEncodeFunc:(e,t)=>{const n=us(t)||ms,o=os(e.replace(/\+/g,","));return o.named&&o.numeric?(e,t)=>e.replace(t?rs:ss,(e=>void 0!==ds[e]?ds[e]:void 0!==n[e]?n[e]:e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":"&#"+e.charCodeAt(0)+";")):o.named?t?(e,t)=>ps(e,t,n):ps:o.numeric?gs:fs},decode:e=>e.replace(is,((e,t)=>t?(t="x"===t.charAt(0).toLowerCase()?parseInt(t.substr(1),16):parseInt(t,10))>65535?(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t))):ls[t]||String.fromCharCode(t):cs[e]||ms[e]||(e=>{const t=bn("div").dom;return t.innerHTML=e,t.textContent||t.innerText||e})(e)))},bs=(e,t)=>(e=Pt.trim(e))?e.split(t||" "):[],vs=e=>new RegExp("^"+e.replace(/([?+*])/g,".$1")+"$"),ys=e=>Object.freeze(["id","accesskey","class","dir","lang","style","tabindex","title","role",..."html4"!==e?["contenteditable","contextmenu","draggable","dropzone","hidden","spellcheck","translate"]:[],..."html5-strict"!==e?["xml:lang"]:[]]),Cs=e=>{let t,n;t="address blockquote div dl fieldset form h1 h2 h3 h4 h5 h6 hr menu ol p pre table ul",n="a abbr b bdo br button cite code del dfn em embed i iframe img input ins kbd label map noscript object q s samp script select small span strong sub sup textarea u var #text #comment","html4"!==e&&(t+=" article aside details dialog figure main header footer hgroup section nav a ins del canvas map",n+=" audio canvas command data datalist mark meter output picture progress time wbr video ruby bdi keygen svg"),"html5-strict"!==e&&(n=[n,"acronym applet basefont big font strike tt"].join(" "),t=[t,"center dir isindex noframes"].join(" "));const o=[t,n].join(" ");return{blockContent:t,phrasingContent:n,flowContent:o}},ws=e=>{const{blockContent:t,phrasingContent:n,flowContent:o}=Cs(e),r=e=>Object.freeze(e.split(" "));return Object.freeze({blockContent:r(t),phrasingContent:r(n),flowContent:r(o)})},Es={html4:Le((()=>ws("html4"))),html5:Le((()=>ws("html5"))),"html5-strict":Le((()=>ws("html5-strict")))},xs=(e,t)=>{const{blockContent:n,phrasingContent:o,flowContent:r}=Es[e]();return"blocks"===t?I.some(n):"phrasing"===t?I.some(o):"flow"===t?I.some(r):I.none()},_s=e=>I.from(/^(@?)([A-Za-z0-9_\-.\u00b7\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u037d\u037f-\u1fff\u200c-\u200d\u203f-\u2040\u2070-\u218f\u2c00-\u2fef\u3001-\ud7ff\uf900-\ufdcf\ufdf0-\ufffd]+)$/.exec(e)).map((e=>({preset:"@"===e[1],name:e[2]}))),Ss={},ks=Pt.makeMap,Ns=Pt.each,Rs=Pt.extend,As=Pt.explode,Ts=(e,t={})=>{const n=ks(e," ",ks(e.toUpperCase()," "));return Rs(n,t)},Os=e=>Ts("td th li dt dd figcaption caption details summary",e.getTextBlockElements()),Bs=(e,t)=>{if(e){const n={};return m(e)&&(e={"*":e}),Ns(e,((e,o)=>{n[o]=n[o.toUpperCase()]="map"===t?ks(e,/[, ]/):As(e,/[, ]/)})),n}},Ps=(e={})=>{var t;const n={},o={};let r=[];const s={},a={},i=(t,n,o)=>{const r=e[t];if(r)return ks(r,/[, ]/,ks(r.toUpperCase(),/[, ]/));{let e=Ss[t];return e||(e=Ts(n,o),Ss[t]=e),e}},l=null!==(t=e.schema)&&void 0!==t?t:"html5",d=(e=>{const t=ys(e),{phrasingContent:n,flowContent:o}=Cs(e),r={},s=(e,t,n)=>{r[e]={attributes:se(t,N({})),attributesOrder:t,children:se(n,N({}))}},a=(e,n="",o="")=>{const r=bs(o),a=bs(e);let i=a.length;const l=[...t,...bs(n)];for(;i--;)s(a[i],l.slice(),r)},i=(e,t)=>{const n=bs(e),o=bs(t);let s=n.length;for(;s--;){const e=r[n[s]];for(let t=0,n=o.length;t<n;t++)e.attributes[o[t]]={},e.attributesOrder.push(o[t])}};return"html5-strict"!==e&&(V(bs("acronym applet basefont big font strike tt"),(e=>{a(e,"",n)})),V(bs("center dir isindex noframes"),(e=>{a(e,"",o)}))),a("html","manifest","head body"),a("head","","base command link meta noscript script style title"),a("title hr noscript br"),a("base","href target"),a("link","href rel media hreflang type sizes hreflang"),a("meta","name http-equiv content charset"),a("style","media type scoped"),a("script","src async defer type charset"),a("body","onafterprint onbeforeprint onbeforeunload onblur onerror onfocus onhashchange onload onmessage onoffline ononline onpagehide onpageshow onpopstate onresize onscroll onstorage onunload",o),a("dd div","",o),a("address dt caption","","html4"===e?n:o),a("h1 h2 h3 h4 h5 h6 pre p abbr code var samp kbd sub sup i b u bdo span legend em strong small s cite dfn","",n),a("blockquote","cite",o),a("ol","reversed start type","li"),a("ul","","li"),a("li","value",o),a("dl","","dt dd"),a("a","href target rel media hreflang type","html4"===e?n:o),a("q","cite",n),a("ins del","cite datetime",o),a("img","src sizes srcset alt usemap ismap width height"),a("iframe","src name width height",o),a("embed","src type width height"),a("object","data type typemustmatch name usemap form width height",[o,"param"].join(" ")),a("param","name value"),a("map","name",[o,"area"].join(" ")),a("area","alt coords shape href target rel media hreflang type"),a("table","border","caption colgroup thead tfoot tbody tr"+("html4"===e?" col":"")),a("colgroup","span","col"),a("col","span"),a("tbody thead tfoot","","tr"),a("tr","","td th"),a("td","colspan rowspan headers",o),a("th","colspan rowspan headers scope abbr",o),a("form","accept-charset action autocomplete enctype method name novalidate target",o),a("fieldset","disabled form name",[o,"legend"].join(" ")),a("label","form for",n),a("input","accept alt autocomplete checked dirname disabled form formaction formenctype formmethod formnovalidate formtarget height list max maxlength min multiple name pattern readonly required size src step type value width"),a("button","disabled form formaction formenctype formmethod formnovalidate formtarget name type value","html4"===e?o:n),a("select","disabled form multiple name required size","option optgroup"),a("optgroup","disabled label","option"),a("option","disabled label selected value"),a("textarea","cols dirname disabled form maxlength name readonly required rows wrap"),a("menu","type label",[o,"li"].join(" ")),a("noscript","",o),"html4"!==e&&(a("wbr"),a("ruby","",[n,"rt rp"].join(" ")),a("figcaption","",o),a("mark rt rp bdi","",n),a("summary","",[n,"h1 h2 h3 h4 h5 h6"].join(" ")),a("canvas","width height",o),a("data","value",n),a("video","src crossorigin poster preload autoplay mediagroup loop muted controls width height buffered",[o,"track source"].join(" ")),a("audio","src crossorigin preload autoplay mediagroup loop muted controls buffered volume",[o,"track source"].join(" ")),a("picture","","img source"),a("source","src srcset type media sizes"),a("track","kind src srclang label default"),a("datalist","",[n,"option"].join(" ")),a("article section nav aside main header footer","",o),a("hgroup","","h1 h2 h3 h4 h5 h6"),a("figure","",[o,"figcaption"].join(" ")),a("time","datetime",n),a("dialog","open",o),a("command","type label icon disabled checked radiogroup command"),a("output","for form name",n),a("progress","value max",n),a("meter","value min max low high optimum",n),a("details","open",[o,"summary"].join(" ")),a("keygen","autofocus challenge disabled form keytype name"),s("svg","id tabindex lang xml:space class style x y width height viewBox preserveAspectRatio zoomAndPan transform".split(" "),[])),"html5-strict"!==e&&(i("script","language xml:space"),i("style","xml:space"),i("object","declare classid code codebase codetype archive standby align border hspace vspace"),i("embed","align name hspace vspace"),i("param","valuetype type"),i("a","charset name rev shape coords"),i("br","clear"),i("applet","codebase archive code object alt name width height align hspace vspace"),i("img","name longdesc align border hspace vspace"),i("iframe","longdesc frameborder marginwidth marginheight scrolling align"),i("font basefont","size color face"),i("input","usemap align"),i("select"),i("textarea"),i("h1 h2 h3 h4 h5 h6 div p legend caption","align"),i("ul","type compact"),i("li","type"),i("ol dl menu dir","compact"),i("pre","width xml:space"),i("hr","align noshade size width"),i("isindex","prompt"),i("table","summary width frame rules cellspacing cellpadding align bgcolor"),i("col","width align char charoff valign"),i("colgroup","width align char charoff valign"),i("thead","align char charoff valign"),i("tr","align char charoff valign bgcolor"),i("th","axis align char charoff valign nowrap bgcolor width height"),i("form","accept"),i("td","abbr axis scope align char charoff valign nowrap bgcolor width height"),i("tfoot","align char charoff valign"),i("tbody","align char charoff valign"),i("area","nohref"),i("body","background bgcolor text link vlink alink")),"html4"!==e&&(i("input button select textarea","autofocus"),i("input textarea","placeholder"),i("a","download"),i("link script img","crossorigin"),i("img","loading"),i("iframe","sandbox seamless allow allowfullscreen loading")),"html4"!==e&&V([r.video,r.audio],(e=>{delete e.children.audio,delete e.children.video})),V(bs("a form meter progress dfn"),(e=>{r[e]&&delete r[e].children[e]})),delete r.caption.children.table,delete r.script,r})(l);!1===e.verify_html&&(e.valid_elements="*[*]");const c=Bs(e.valid_styles),u=Bs(e.invalid_styles,"map"),g=Bs(e.valid_classes,"map"),h=i("whitespace_elements","pre script noscript style textarea video audio iframe object code"),v=i("self_closing_elements","colgroup dd dt li option p td tfoot th thead tr"),y=i("void_elements","area base basefont br col frame hr img input isindex link meta param embed source wbr track"),C=i("boolean_attributes","checked compact declare defer disabled ismap multiple nohref noresize noshade nowrap readonly selected autoplay loop controls allowfullscreen"),w="td th iframe video audio object script code",E=i("non_empty_elements",w+" pre svg textarea summary",y),x=i("move_caret_before_on_enter_elements",w+" table",y),_="h1 h2 h3 h4 h5 h6",S=i("text_block_elements",_+" p div address pre form blockquote center dir fieldset header footer article section hgroup aside main nav figure"),k=i("block_elements","hr table tbody thead tfoot th tr td li ol ul caption dl dt dd noscript menu isindex option datalist select optgroup figcaption details summary html body multicol listing",S),R=i("text_inline_elements","span strong b em i font s strike u var cite dfn code mark q sup sub samp"),A=i("transparent_elements","a ins del canvas map"),T=i("wrap_block_elements","pre "+_);Ns("script noscript iframe noframes noembed title style textarea xmp plaintext".split(" "),(e=>{a[e]=new RegExp("</"+e+"[^>]*>","gi")}));const O=e=>{const t=I.from(n["@"]),o=/[*?+]/;V(((e,t)=>{const n=/^([#+\-])?([^\[!\/]+)(?:\/([^\[!]+))?(?:(!?)\[([^\]]+)])?$/;return te(bs(t,","),(t=>{const o=n.exec(t);if(o){const t=o[1],n=o[2],r=o[3],s=o[4],a=o[5],i={attributes:{},attributesOrder:[]};if(e.each((e=>((e,t)=>{pe(e.attributes,((e,n)=>{t.attributes[n]=e})),t.attributesOrder.push(...e.attributesOrder)})(e,i))),"#"===t?i.paddEmpty=!0:"-"===t&&(i.removeEmpty=!0),"!"===s&&(i.removeEmptyAttrs=!0),a&&((e,t)=>{const n=/^([!\-])?(\w+[\\:]:\w+|[^=~<]+)?(?:([=~<])(.*))?$/,o=/[*?+]/,{attributes:r,attributesOrder:s}=t;V(bs(e,"|"),(e=>{const a=n.exec(e);if(a){const e={},n=a[1],i=a[2].replace(/[\\:]:/g,":"),l=a[3],d=a[4];if("!"===n&&(t.attributesRequired=t.attributesRequired||[],t.attributesRequired.push(i),e.required=!0),"-"===n)return delete r[i],void s.splice(Pt.inArray(s,i),1);if(l&&("="===l?(t.attributesDefault=t.attributesDefault||[],t.attributesDefault.push({name:i,value:d}),e.defaultValue=d):"~"===l?(t.attributesForced=t.attributesForced||[],t.attributesForced.push({name:i,value:d}),e.forcedValue=d):"<"===l&&(e.validValues=Pt.makeMap(d,"?"))),o.test(i)){const n=e;t.attributePatterns=t.attributePatterns||[],n.pattern=vs(i),t.attributePatterns.push(n)}else r[i]||s.push(i),r[i]=e}}))})(a,i),r&&(i.outputName=n),"@"===n){if(!e.isNone())return[];e=I.some(i)}return[r?{name:n,element:i,aliasName:r}:{name:n,element:i}]}return[]}))})(t,null!=e?e:""),(({name:e,element:t,aliasName:s})=>{if(s&&(n[s]=t),o.test(e)){const n=t;n.pattern=vs(e),r.push(n)}else n[e]=t}))},B=e=>{r=[],V(fe(n),(e=>{delete n[e]})),O(e)},P=(e,t)=>{var r,a;delete Ss.text_block_elements,delete Ss.block_elements;const i=!!t.extends&&!oe(t.extends),d=t.extends;if(o[e]=d?o[d]:{},s[e]=null!=d?d:e,E[e.toUpperCase()]={},E[e]={},i||(k[e.toUpperCase()]={},k[e]={}),d&&!n[e]&&n[d]){const t=(e=>{const t=e=>p(e)?q(e,t):(e=>f(e)&&e.source&&"[object RegExp]"===Object.prototype.toString.call(e))(e)?new RegExp(e.source,e.flags):f(e)?he(e,t):e;return t(e)})(n[d]);delete t.removeEmptyAttrs,delete t.removeEmpty,n[e]=t}else n[e]={attributesOrder:[],attributes:{}};if(p(t.attributes)){const o=e=>{s.attributesOrder.push(e),s.attributes[e]={}},s=null!==(r=n[e])&&void 0!==r?r:{};delete s.attributesDefault,delete s.attributesForced,delete s.attributePatterns,delete s.attributesRequired,s.attributesOrder=[],s.attributes={},V(t.attributes,(e=>{const t=ys(l);_s(e).each((({preset:e,name:n})=>{e?"global"===n&&V(t,o):o(n)}))})),n[e]=s}if(b(t.padEmpty)){const o=null!==(a=n[e])&&void 0!==a?a:{};o.paddEmpty=t.padEmpty,n[e]=o}if(p(t.children)){const n={},r=e=>{n[e]={}},s=e=>{xs(l,e).each((e=>{V(e,r)}))};V(t.children,(e=>{_s(e).each((({preset:e,name:t})=>{e?s(t):r(t)}))})),o[e]=n}d&&pe(o,((t,n)=>{t[d]&&(o[n]=t=Rs({},o[n]),t[e]=t[d])}))},D=e=>{f(e)?pe(e,((e,t)=>P(t,e))):m(e)&&(e=>{V((e=>{const t=/^(~)?(.+)$/;return te(bs(e,","),(e=>{const n=t.exec(e);return n?[{cloneName:"~"===n[1]?"span":"div",name:n[2]}]:[]}))})(null!=e?e:""),(({name:e,cloneName:t})=>{P(e,{extends:t})}))})(e)},L=e=>{V((e=>{const t=/^([+\-]?)([A-Za-z0-9_\-.\u00b7\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u037d\u037f-\u1fff\u200c-\u200d\u203f-\u2040\u2070-\u218f\u2c00-\u2fef\u3001-\ud7ff\uf900-\ufdcf\ufdf0-\ufffd]+)\[([^\]]+)]$/;return te(bs(e,","),(e=>{const n=t.exec(e);if(n){const e=n[1],t=e?(e=>"-"===e?"remove":"add")(e):"replace";return[{operation:t,name:n[2],validChildren:te(bs(n[3],"|"),(e=>_s(e).toArray()))}]}return[]}))})(null!=e?e:""),(({operation:e,name:t,validChildren:n})=>{const r="replace"===e?{"#comment":{}}:o[t],s=t=>{"remove"===e?delete r[t]:r[t]={}};V(n,(({preset:e,name:t})=>{e?(e=>{xs(l,e).each((e=>{V(e,s)}))})(t):s(t)})),o[t]=r}))},M=e=>{const t=n[e];if(t)return t;let o=r.length;for(;o--;){const t=r[o];if(t.pattern.test(e))return t}},F=N(c),U=N(u),z=N(g),j=N(C),H=N(k),$=N(S),W=N(R),K=N(Object.seal(y)),Y=N(v),G=N(E),X=N(x),Z=N(h),Q=N(A),J=N(T),ee=N(Object.seal(a)),ne=(e,t)=>{const n=M(e);if(n){if(!t)return!0;{if(n.attributes[t])return!0;const e=n.attributePatterns;if(e){let n=e.length;for(;n--;)if(e[n].pattern.test(t))return!0}}}return!1},oe=e=>_e(H(),e),re=e=>!$e(e,"#")&&ne(e)&&!oe(e),ae=N(s);return e.valid_elements?(B(e.valid_elements),Ns(d,((e,t)=>{o[t]=e.children}))):(Ns(d,((e,t)=>{n[t]={attributes:e.attributes,attributesOrder:e.attributesOrder},o[t]=e.children})),Ns(bs("strong/b em/i"),(e=>{const t=bs(e,"/");n[t[1]].outputName=t[0]})),Ns(R,((t,o)=>{n[o]&&(e.padd_empty_block_inline_children&&(n[o].paddInEmptyBlock=!0),n[o].removeEmpty=!0)})),Ns(bs("ol ul blockquote a table tbody"),(e=>{n[e]&&(n[e].removeEmpty=!0)})),Ns(bs("p h1 h2 h3 h4 h5 h6 th td pre div address caption li summary"),(e=>{n[e]&&(n[e].paddEmpty=!0)})),Ns(bs("span"),(e=>{n[e].removeEmptyAttrs=!0}))),delete n.svg,D(e.custom_elements),L(e.valid_children),O(e.extended_valid_elements),L("+ol[ul|ol],+ul[ul|ol]"),Ns({dd:"dl",dt:"dl",li:"ul ol",td:"tr",th:"tr",tr:"tbody thead tfoot",tbody:"table",thead:"table",tfoot:"table",legend:"fieldset",area:"map",param:"video audio object"},((e,t)=>{n[t]&&(n[t].parentsRequired=bs(e))})),e.invalid_elements&&Ns(As(e.invalid_elements),(e=>{n[e]&&delete n[e]})),M("span")||O("span[!data-mce-type|*]"),{type:l,children:o,elements:n,getValidStyles:F,getValidClasses:z,getBlockElements:H,getInvalidStyles:U,getVoidElements:K,getTextBlockElements:$,getTextInlineElements:W,getBoolAttrs:j,getElementRule:M,getSelfClosingElements:Y,getNonEmptyElements:G,getMoveCaretBeforeOnEnterElements:X,getWhitespaceElements:Z,getTransparentElements:Q,getSpecialElements:ee,isValidChild:(e,t)=>{const n=o[e.toLowerCase()];return!(!n||!n[t.toLowerCase()])},isValid:ne,isBlock:oe,isInline:re,isWrapper:e=>_e(J(),e)||re(e),getCustomElements:ae,addValidElements:O,setValidElements:B,addCustomElements:D,addValidChildren:L}},Ds=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},Ls=e=>(e=>{return{value:(t=e,je(t,"#").toUpperCase())};var t})(Ds(e.red)+Ds(e.green)+Ds(e.blue)),Ms=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,Is=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,Fs=(e,t,n,o)=>({red:e,green:t,blue:n,alpha:o}),Us=(e,t,n,o)=>{const r=parseInt(e,10),s=parseInt(t,10),a=parseInt(n,10),i=parseFloat(o);return Fs(r,s,a,i)},zs=e=>{if("transparent"===e)return I.some(Fs(0,0,0,0));const t=Ms.exec(e);if(null!==t)return I.some(Us(t[1],t[2],t[3],"1"));const n=Is.exec(e);return null!==n?I.some(Us(n[1],n[2],n[3],n[4])):I.none()},js=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,Hs=e=>zs(e).map(Ls).map((e=>"#"+e.value)).getOr(e),$s=(e={},t)=>{const n=/(?:url(?:(?:\(\s*\"([^\"]+)\"\s*\))|(?:\(\s*\'([^\']+)\'\s*\))|(?:\(\s*([^)\s]+)\s*\))))|(?:\'([^\']+)\')|(?:\"([^\"]+)\")/gi,o=/\s*([^:]+):\s*([^;]+);?/g,r=/\s+$/,s={};let a,i;const l=qo;t&&(a=t.getValidStyles(),i=t.getInvalidStyles());const d="\\\" \\' \\; \\: ; : \ufeff".split(" ");for(let e=0;e<d.length;e++)s[d[e]]=l+e,s[l+e]=d[e];const c={parse:t=>{const a={};let i=!1;const d=e.url_converter,u=e.url_converter_scope||c,f=(e,t,n)=>{const o=a[e+"-top"+t];if(!o)return;const r=a[e+"-right"+t];if(!r)return;const s=a[e+"-bottom"+t];if(!s)return;const i=a[e+"-left"+t];if(!i)return;const l=[o,r,s,i];let d=l.length-1;for(;d--&&l[d]===l[d+1];);d>-1&&n||(a[e+t]=-1===d?l[0]:l.join(" "),delete a[e+"-top"+t],delete a[e+"-right"+t],delete a[e+"-bottom"+t],delete a[e+"-left"+t])},g=e=>{const t=a[e];if(!t)return;const n=t.indexOf(",")>-1?[t]:t.split(" ");let o=n.length;for(;o--;)if(n[o]!==n[0])return!1;return a[e]=n[0],!0},p=e=>(i=!0,s[e]),h=(e,t)=>(i&&(e=e.replace(/\uFEFF[0-9]/g,(e=>s[e]))),t||(e=e.replace(/\\([\'\";:])/g,"$1")),e),b=e=>String.fromCharCode(parseInt(e.slice(1),16)),v=e=>e.replace(/\\[0-9a-f]+/gi,b),y=(t,n,o,r,s,a)=>{if(s=s||a)return"'"+(s=h(s)).replace(/\'/g,"\\'")+"'";if(n=h(n||o||r||""),!e.allow_script_urls){const t=n.replace(/[\s\r\n]+/g,"");if(/(java|vb)script:/i.test(t))return"";if(!e.allow_svg_data_urls&&/^data:image\/svg/i.test(t))return""}return d&&(n=d.call(u,n,"style")),"url('"+n.replace(/\'/g,"\\'")+"')"};if(t){let s;for(t=(t=t.replace(/[\u0000-\u001F]/g,"")).replace(/\\[\"\';:\uFEFF]/g,p).replace(/\"[^\"]+\"|\'[^\']+\'/g,(e=>e.replace(/[;:]/g,p)));s=o.exec(t);){o.lastIndex=s.index+s[0].length;let t=s[1].replace(r,"").toLowerCase(),d=s[2].replace(r,"");if(t&&d){if(t=v(t),d=v(d),-1!==t.indexOf(l)||-1!==t.indexOf('"'))continue;if(!e.allow_script_urls&&("behavior"===t||/expression\s*\(|\/\*|\*\//.test(d)))continue;"font-weight"===t&&"700"===d?d="bold":"color"!==t&&"background-color"!==t||(d=d.toLowerCase()),m(e.force_hex_color)&&"off"!==e.force_hex_color&&zs(d).each((t=>{"always"!==e.force_hex_color&&1!==t.alpha||(d=Hs(js(t)))})),d=d.replace(n,y),a[t]=i?h(d,!0):d}}f("border","",!0),f("border","-width"),f("border","-color"),f("border","-style"),f("padding",""),f("margin",""),"border",w="border-style",E="border-color",g(C="border-width")&&g(w)&&g(E)&&(a.border=a[C]+" "+a[w]+" "+a[E],delete a[C],delete a[w],delete a[E]),"medium none"===a.border&&delete a.border,"none"===a["border-image"]&&delete a["border-image"]}var C,w,E;return a},serialize:(e,t)=>{let n="";const o=(t,o)=>{const r=o[t];if(r)for(let t=0,o=r.length;t<o;t++){const o=r[t],s=e[o];s&&(n+=(n.length>0?" ":"")+o+": "+s+";")}};return t&&a?(o("*",a),o(t,a)):pe(e,((e,o)=>{e&&((e,t)=>{if(!i||!t)return!0;let n=i["*"];return!(n&&n[e]||(n=i[t],n&&n[e]))})(o,t)&&(n+=(n.length>0?" ":"")+o+": "+e+";")})),n}};return c},qs={keyLocation:!0,layerX:!0,layerY:!0,returnValue:!0,webkitMovementX:!0,webkitMovementY:!0,keyIdentifier:!0,mozPressure:!0},Vs=(e,t)=>{const n=null!=t?t:{};for(const t in e)_e(qs,t)||(n[t]=e[t]);return C(e.composedPath)&&(n.composedPath=()=>e.composedPath()),C(e.getModifierState)&&(n.getModifierState=t=>e.getModifierState(t)),C(e.getTargetRanges)&&(n.getTargetRanges=()=>e.getTargetRanges()),n},Ws=(e,t,n,o)=>{var r;const s=Vs(t,o);return s.type=e,y(s.target)&&(s.target=null!==(r=s.srcElement)&&void 0!==r?r:n),(e=>y(e.preventDefault)||(e=>e instanceof Event||w(e.initEvent))(e))(t)&&(s.preventDefault=()=>{s.defaultPrevented=!0,s.isDefaultPrevented=M,w(t.preventDefault)&&t.preventDefault()},s.stopPropagation=()=>{s.cancelBubble=!0,s.isPropagationStopped=M,w(t.stopPropagation)&&t.stopPropagation()},s.stopImmediatePropagation=()=>{s.isImmediatePropagationStopped=M,s.stopPropagation()},(e=>e.isDefaultPrevented===M||e.isDefaultPrevented===L)(s)||(s.isDefaultPrevented=!0===s.defaultPrevented?M:L,s.isPropagationStopped=!0===s.cancelBubble?M:L,s.isImmediatePropagationStopped=L)),s},Ks=/^(?:mouse|contextmenu)|click/,Ys=(e,t,n,o)=>{e.addEventListener(t,n,o||!1)},Gs=(e,t,n,o)=>{e.removeEventListener(t,n,o||!1)},Xs=(e,t)=>{const n=Ws(e.type,e,document,t);if((e=>C(e)&&Ks.test(e.type))(e)&&v(e.pageX)&&!v(e.clientX)){const t=n.target.ownerDocument||document,o=t.documentElement,r=t.body,s=n;s.pageX=e.clientX+(o&&o.scrollLeft||r&&r.scrollLeft||0)-(o&&o.clientLeft||r&&r.clientLeft||0),s.pageY=e.clientY+(o&&o.scrollTop||r&&r.scrollTop||0)-(o&&o.clientTop||r&&r.clientTop||0)}return n},Zs=(e,t,n)=>{const o=e.document,r={type:"ready"};if(n.domLoaded)return void t(r);const s=()=>{Gs(e,"DOMContentLoaded",s),Gs(e,"load",s),n.domLoaded||(n.domLoaded=!0,t(r)),e=null};"complete"===o.readyState||"interactive"===o.readyState&&o.body?s():Ys(e,"DOMContentLoaded",s),n.domLoaded||Ys(e,"load",s)};class Qs{constructor(){this.domLoaded=!1,this.events={},this.count=1,this.expando="mce-data-"+(+new Date).toString(32),this.hasFocusIn="onfocusin"in document.documentElement,this.count=1}bind(e,t,n,o){const r=this;let s;const a=window,i=e=>{r.executeHandlers(Xs(e||a.event),l)};if(!e||lr(e)||ur(e))return n;let l;e[r.expando]?l=e[r.expando]:(l=r.count++,e[r.expando]=l,r.events[l]={}),o=o||e;const d=t.split(" ");let c=d.length;for(;c--;){let t=d[c],u=i,m=!1,f=!1;"DOMContentLoaded"===t&&(t="ready"),r.domLoaded&&"ready"===t&&"complete"===e.readyState?n.call(o,Xs({type:t})):(r.hasFocusIn||"focusin"!==t&&"focusout"!==t||(m=!0,f="focusin"===t?"focus":"blur",u=e=>{const t=Xs(e||a.event);t.type="focus"===t.type?"focusin":"focusout",r.executeHandlers(t,l)}),s=r.events[l][t],s?"ready"===t&&r.domLoaded?n(Xs({type:t})):s.push({func:n,scope:o}):(r.events[l][t]=s=[{func:n,scope:o}],s.fakeName=f,s.capture=m,s.nativeHandler=u,"ready"===t?Zs(e,u,r):Ys(e,f||t,u,m)))}return e=s=null,n}unbind(e,t,n){if(!e||lr(e)||ur(e))return this;const o=e[this.expando];if(o){let r=this.events[o];if(t){const o=t.split(" ");let s=o.length;for(;s--;){const t=o[s],a=r[t];if(a){if(n){let e=a.length;for(;e--;)if(a[e].func===n){const n=a.nativeHandler,o=a.fakeName,s=a.capture,i=a.slice(0,e).concat(a.slice(e+1));i.nativeHandler=n,i.fakeName=o,i.capture=s,r[t]=i}}n&&0!==a.length||(delete r[t],Gs(e,a.fakeName||t,a.nativeHandler,a.capture))}}}else pe(r,((t,n)=>{Gs(e,t.fakeName||n,t.nativeHandler,t.capture)})),r={};for(const e in r)if(_e(r,e))return this;delete this.events[o];try{delete e[this.expando]}catch(t){e[this.expando]=null}}return this}fire(e,t,n){return this.dispatch(e,t,n)}dispatch(e,t,n){if(!e||lr(e)||ur(e))return this;const o=Xs({type:t,target:e},n);do{const t=e[this.expando];t&&this.executeHandlers(o,t),e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow}while(e&&!o.isPropagationStopped());return this}clean(e){if(!e||lr(e)||ur(e))return this;if(e[this.expando]&&this.unbind(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName){this.unbind(e);const t=e.getElementsByTagName("*");let n=t.length;for(;n--;)(e=t[n])[this.expando]&&this.unbind(e)}return this}destroy(){this.events={}}cancel(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1}executeHandlers(e,t){const n=this.events[t],o=n&&n[e.type];if(o)for(let t=0,n=o.length;t<n;t++){const n=o[t];if(n&&!1===n.func.call(n.scope,e)&&e.preventDefault(),e.isImmediatePropagationStopped())return}}}Qs.Event=new Qs;const Js=Pt.each,ea=Pt.grep,ta="data-mce-style",na=Pt.makeMap("fill-opacity font-weight line-height opacity orphans widows z-index zoom"," "),oa=(e,t,n)=>{y(n)||""===n?on(e,t):Qt(e,t,n)},ra=e=>e.replace(/[A-Z]/g,(e=>"-"+e.toLowerCase())),sa=(e,t)=>{let n=0;if(e)for(let o=e.nodeType,r=e.previousSibling;r;r=r.previousSibling){const e=r.nodeType;(!t||!lr(r)||e!==o&&r.data.length)&&(n++,o=e)}return n},aa=(e,t)=>{const n=en(t,"style"),o=e.serialize(e.parse(n),Ht(t));oa(t,ta,o)},ia=(e,t,n)=>{const o=ra(t);y(n)||""===n?go(e,o):io(e,o,((e,t)=>E(e)?_e(na,t)?e+"":e+"px":e)(n,o))},la=(e,t={})=>{const n={},o=window,r={};let s=0;const a=Jr.forElement(yn(e),{contentCssCors:t.contentCssCors,referrerPolicy:t.referrerPolicy}),i=[],l=t.schema?t.schema:Ps({}),d=$s({url_converter:t.url_converter,url_converter_scope:t.url_converter_scope,force_hex_color:t.force_hex_color},t.schema),c=t.ownEvents?new Qs:Qs.Event,u=l.getBlockElements(),f=t=>t&&e&&m(t)?e.getElementById(t):t,h=e=>{const t=f(e);return C(t)?yn(t):null},b=(e,t,n="")=>{let o;const r=h(e);if(C(r)&&Wt(r)){const e=G[t];o=e&&e.get?e.get(r.dom,t):en(r,t)}return C(o)?o:n},v=e=>{const t=f(e);return y(t)?[]:t.attributes},E=(e,n,o)=>{B(e,(e=>{if(Jo(e)){const r=yn(e),s=""===o?null:o,a=en(r,n),i=G[n];i&&i.set?i.set(r.dom,s,n):oa(r,n,s),a!==s&&t.onSetAttrib&&t.onSetAttrib({attrElm:r.dom,attrName:n,attrValue:s})}}))},x=()=>t.root_element||e.body,S=(t,n)=>((e,t,n)=>{let o=0,r=0;const s=e.ownerDocument;if(n=n||e,t){if(n===e&&t.getBoundingClientRect&&"static"===co(yn(e),"position")){const n=t.getBoundingClientRect();return o=n.left+(s.documentElement.scrollLeft||e.scrollLeft)-s.documentElement.clientLeft,r=n.top+(s.documentElement.scrollTop||e.scrollTop)-s.documentElement.clientTop,{x:o,y:r}}let a=t;for(;a&&a!==n&&a.nodeType&&!Zr(a,n);){const e=a;o+=e.offsetLeft||0,r+=e.offsetTop||0,a=e.offsetParent}for(a=t.parentNode;a&&a!==n&&a.nodeType&&!Zr(a,n);)o-=a.scrollLeft||0,r-=a.scrollTop||0,a=a.parentNode;r+=(e=>Gr.isFirefox()&&"table"===Ht(e)?Xr(Mn(e)).filter((e=>"caption"===Ht(e))).bind((e=>Xr(Ln(e)).map((t=>{const n=t.dom.offsetTop,o=e.dom.offsetTop,r=e.dom.offsetHeight;return n<=o?-r:0})))).getOr(0):0)(yn(t))}return{x:o,y:r}})(e.body,f(t),n),k=(e,t,n)=>{const o=f(e);var r;if(!y(o)&&(er(o)||Jo(r=o)&&"http://www.w3.org/2000/svg"===r.namespaceURI))return n?co(yn(o),ra(t)):("float"===(t=t.replace(/-(\D)/g,((e,t)=>t.toUpperCase())))&&(t="cssFloat"),o.style?o.style[t]:void 0)},R=e=>{const t=f(e);if(!t)return{w:0,h:0};let n=k(t,"width"),o=k(t,"height");return n&&-1!==n.indexOf("px")||(n="0"),o&&-1!==o.indexOf("px")||(o="0"),{w:parseInt(n,10)||t.offsetWidth||t.clientWidth,h:parseInt(o,10)||t.offsetHeight||t.clientHeight}},A=(e,t)=>{if(!e)return!1;const n=p(e)?e:[e];return $(n,(e=>En(yn(e),t)))},T=(e,t,n,o)=>{const r=[];let s=f(e);o=void 0===o;const a=n||("BODY"!==x().nodeName?x().parentNode:null);if(m(t))if("*"===t)t=Jo;else{const e=t;t=t=>A(t,e)}for(;s&&!(s===a||y(s.nodeType)||mr(s)||fr(s));){if(!t||t(s)){if(!o)return[s];r.push(s)}s=s.parentNode}return o?r:null},O=(e,t,n)=>{let o=t;if(e){m(t)&&(o=e=>A(e,t));for(let t=e[n];t;t=t[n])if(w(o)&&o(t))return t}return null},B=function(e,t,n){const o=null!=n?n:this;if(p(e)){const n=[];return Js(e,((e,r)=>{const s=f(e);s&&n.push(t.call(o,s,r))})),n}{const n=f(e);return!!n&&t.call(o,n)}},P=(e,t)=>{B(e,(e=>{pe(t,((t,n)=>{E(e,n,t)}))}))},D=(e,t)=>{B(e,(e=>{const n=yn(e);ko(n,t)}))},L=(t,n,o,r,s)=>B(t,(t=>{const a=m(n)?e.createElement(n):n;return C(o)&&P(a,o),r&&(!m(r)&&r.nodeType?a.appendChild(r):m(r)&&D(a,r)),s?a:t.appendChild(a)})),M=(t,n,o)=>L(e.createElement(t),t,n,o,!0),I=hs.encodeAllRaw,F=(e,t)=>B(e,(e=>{const n=yn(e);return t&&V(Mn(n),(e=>{Kt(e)&&0===e.dom.length?Eo(e):po(n,e)})),Eo(n),n.dom})),U=(e,t,n)=>{B(e,(e=>{if(Jo(e)){const o=yn(e),r=t.split(" ");V(r,(e=>{C(n)?(n?un:fn)(o,e):((e,t)=>{const n=an(e)?e.dom.classList.toggle(t):((e,t)=>H(ln(e),t)?cn(e,t):dn(e,t))(e,t);mn(e)})(o,e)}))}}))},z=(e,t,n)=>B(t,(o=>{var r;const s=p(t)?e.cloneNode(!0):e;return n&&Js(ea(o.childNodes),(e=>{s.appendChild(e)})),null===(r=o.parentNode)||void 0===r||r.replaceChild(s,o),o})),j=()=>e.createRange(),q=(n,r,s,a)=>{if(p(n)){let e=n.length;const t=[];for(;e--;)t[e]=q(n[e],r,s,a);return t}return!t.collect||n!==e&&n!==o||i.push([n,r,s,a]),c.bind(n,r,s,a||Y)},W=(t,n,r)=>{if(p(t)){let e=t.length;const o=[];for(;e--;)o[e]=W(t[e],n,r);return o}if(i.length>0&&(t===e||t===o)){let e=i.length;for(;e--;){const[o,s,a]=i[e];t!==o||n&&n!==s||r&&r!==a||c.unbind(o,s,a)}}return c.unbind(t,n,r)},K=e=>{if(e&&er(e)){const t=e.getAttribute("data-mce-contenteditable");return t&&"inherit"!==t?t:"inherit"!==e.contentEditable?e.contentEditable:null}return null},Y={doc:e,settings:t,win:o,files:r,stdMode:!0,boxModel:!0,styleSheetLoader:a,boundEvents:i,styles:d,schema:l,events:c,isBlock:e=>m(e)?_e(u,e):Jo(e)&&(_e(u,e.nodeName)||Wr(l,e)),root:null,clone:(e,t)=>e.cloneNode(t),getRoot:x,getViewPort:e=>{const t=Io(e);return{x:t.x,y:t.y,w:t.width,h:t.height}},getRect:e=>{const t=f(e),n=S(t),o=R(t);return{x:n.x,y:n.y,w:o.w,h:o.h}},getSize:R,getParent:(e,t,n)=>{const o=T(e,t,n,!1);return o&&o.length>0?o[0]:null},getParents:T,get:f,getNext:(e,t)=>O(e,t,"nextSibling"),getPrev:(e,t)=>O(e,t,"previousSibling"),select:(n,o)=>{var r,s;const a=null!==(s=null!==(r=f(o))&&void 0!==r?r:t.root_element)&&void 0!==s?s:e;return w(a.querySelectorAll)?ce(a.querySelectorAll(n)):[]},is:A,add:L,create:M,createHTML:(e,t,n="")=>{let o="<"+e;for(const e in t)Se(t,e)&&(o+=" "+e+'="'+I(t[e])+'"');return Xe(n)&&_e(l.getVoidElements(),e)?o+" />":o+">"+n+"</"+e+">"},createFragment:t=>{const n=e.createElement("div"),o=e.createDocumentFragment();let r;for(o.appendChild(n),t&&(n.innerHTML=t);r=n.firstChild;)o.appendChild(r);return o.removeChild(n),o},remove:F,setStyle:(e,n,o)=>{B(e,(e=>{const r=yn(e);ia(r,n,o),t.update_styles&&aa(d,r)}))},getStyle:k,setStyles:(e,n)=>{B(e,(e=>{const o=yn(e);pe(n,((e,t)=>{ia(o,t,e)})),t.update_styles&&aa(d,o)}))},removeAllAttribs:e=>B(e,(e=>{const t=e.attributes;for(let n=t.length-1;n>=0;n--)e.removeAttributeNode(t.item(n))})),setAttrib:E,setAttribs:P,getAttrib:b,getPos:S,parseStyle:e=>d.parse(e),serializeStyle:(e,t)=>d.serialize(e,t),addStyle:t=>{if(Y!==la.DOM&&e===document){if(n[t])return;n[t]=!0}let o=e.getElementById("mceDefaultStyles");if(!o){o=e.createElement("style"),o.id="mceDefaultStyles",o.type="text/css";const t=e.head;t.firstChild?t.insertBefore(o,t.firstChild):t.appendChild(o)}o.styleSheet?o.styleSheet.cssText+=t:o.appendChild(e.createTextNode(t))},loadCSS:e=>{e||(e=""),V(e.split(","),(e=>{r[e]=!0,a.load(e).catch(_)}))},addClass:(e,t)=>{U(e,t,!0)},removeClass:(e,t)=>{U(e,t,!1)},hasClass:(e,t)=>{const n=h(e),o=t.split(" ");return C(n)&&ne(o,(e=>gn(n,e)))},toggleClass:U,show:e=>{B(e,(e=>go(yn(e),"display")))},hide:e=>{B(e,(e=>io(yn(e),"display","none")))},isHidden:e=>{const t=h(e);return C(t)&&Dt(mo(t,"display"),"none")},uniqueId:e=>(e||"mce_")+s++,setHTML:D,getOuterHTML:e=>{const t=h(e);return C(t)?Jo(t.dom)?t.dom.outerHTML:(e=>{const t=bn("div"),n=yn(e.dom.cloneNode(!0));return vo(t,n),So(t)})(t):""},setOuterHTML:(e,t)=>{B(e,(e=>{Jo(e)&&(e.outerHTML=t)}))},decode:hs.decode,encode:I,insertAfter:(e,t)=>{const n=f(t);return B(e,(e=>{const t=null==n?void 0:n.parentNode,o=null==n?void 0:n.nextSibling;return t&&(o?t.insertBefore(e,o):t.appendChild(e)),e}))},replace:z,rename:(e,t)=>{if(e.nodeName!==t.toUpperCase()){const n=M(t);return Js(v(e),(t=>{E(n,t.nodeName,b(e,t.nodeName))})),z(n,e,!0),n}return e},findCommonAncestor:(e,t)=>{let n=e;for(;n;){let e=t;for(;e&&n!==e;)e=e.parentNode;if(n===e)break;n=n.parentNode}return!n&&e.ownerDocument?e.ownerDocument.documentElement:n},run:B,getAttribs:v,isEmpty:(e,t,n)=>{if(g(t)){const o=e=>{const n=e.nodeName.toLowerCase();return Boolean(t[n])};return Rr(l,e,{...n,isContent:o})}return Rr(l,e,n)},createRng:j,nodeIndex:sa,split:(e,t,n)=>{let o,r,s=j();if(e&&t&&e.parentNode&&t.parentNode){const a=e.parentNode;return s.setStart(a,sa(e)),s.setEnd(t.parentNode,sa(t)),o=s.extractContents(),s=j(),s.setStart(t.parentNode,sa(t)+1),s.setEnd(a,sa(e)+1),r=s.extractContents(),a.insertBefore(ns(Y,o,l),e),n?a.insertBefore(n,e):a.insertBefore(t,e),a.insertBefore(ns(Y,r,l),e),F(e),n||t}},bind:q,unbind:W,fire:(e,t,n)=>c.dispatch(e,t,n),dispatch:(e,t,n)=>c.dispatch(e,t,n),getContentEditable:K,getContentEditableParent:e=>{const t=x();let n=null;for(let o=e;o&&o!==t&&(n=K(o),null===n);o=o.parentNode);return n},isEditable:e=>{if(C(e)){const t=Jo(e)?e:e.parentElement;return C(t)&&er(t)&&oo(yn(t))}return!1},destroy:()=>{if(i.length>0){let e=i.length;for(;e--;){const[t,n,o]=i[e];c.unbind(t,n,o)}}pe(r,((e,t)=>{a.unload(t),delete r[t]}))},isChildOf:(e,t)=>e===t||t.contains(e),dumpRng:e=>"startContainer: "+e.startContainer.nodeName+", startOffset: "+e.startOffset+", endContainer: "+e.endContainer.nodeName+", endOffset: "+e.endOffset},G=((e,t,n)=>{const o=t.keep_values,r={set:(e,o,r)=>{const s=yn(e);w(t.url_converter)&&C(o)&&(o=t.url_converter.call(t.url_converter_scope||n(),String(o),r,e)),oa(s,"data-mce-"+r,o),oa(s,r,o)},get:(e,t)=>{const n=yn(e);return en(n,"data-mce-"+t)||en(n,t)}},s={style:{set:(t,n)=>{const r=yn(t);o&&oa(r,ta,n),on(r,"style"),m(n)&&lo(r,e.parse(n))},get:t=>{const n=yn(t),o=en(n,ta)||en(n,"style");return e.serialize(e.parse(o),Ht(n))}}};return o&&(s.href=s.src=r),s})(d,t,N(Y));return Y};la.DOM=la(document),la.nodeIndex=sa;const da=la.DOM;class ca{constructor(e={}){this.states={},this.queue=[],this.scriptLoadedCallbacks={},this.queueLoadedCallbacks=[],this.loading=!1,this.settings=e}_setReferrerPolicy(e){this.settings.referrerPolicy=e}loadScript(e){return new Promise(((t,n)=>{const o=da;let r;const s=()=>{o.remove(a),r&&(r.onerror=r.onload=r=null)},a=o.uniqueId();r=document.createElement("script"),r.id=a,r.type="text/javascript",r.src=Pt._addCacheSuffix(e),this.settings.referrerPolicy&&o.setAttrib(r,"referrerpolicy",this.settings.referrerPolicy),r.onload=()=>{s(),t()},r.onerror=()=>{s(),n("Failed to load script: "+e)},(document.getElementsByTagName("head")[0]||document.body).appendChild(r)}))}isDone(e){return 2===this.states[e]}markDone(e){this.states[e]=2}add(e){const t=this;return t.queue.push(e),void 0===t.states[e]&&(t.states[e]=0),new Promise(((n,o)=>{t.scriptLoadedCallbacks[e]||(t.scriptLoadedCallbacks[e]=[]),t.scriptLoadedCallbacks[e].push({resolve:n,reject:o})}))}load(e){return this.add(e)}remove(e){delete this.states[e],delete this.scriptLoadedCallbacks[e]}loadQueue(){const e=this.queue;return this.queue=[],this.loadScripts(e)}loadScripts(e){const t=this,n=(e,n)=>{xe(t.scriptLoadedCallbacks,n).each((t=>{V(t,(t=>t[e](n)))})),delete t.scriptLoadedCallbacks[n]},o=e=>{const t=Y(e,(e=>"rejected"===e.status));return t.length>0?Promise.reject(te(t,(({reason:e})=>p(e)?e:[e]))):Promise.resolve()},r=e=>Promise.allSettled(q(e,(e=>2===t.states[e]?(n("resolve",e),Promise.resolve()):3===t.states[e]?(n("reject",e),Promise.reject(e)):(t.states[e]=1,t.loadScript(e).then((()=>{t.states[e]=2,n("resolve",e);const s=t.queue;return s.length>0?(t.queue=[],r(s).then(o)):Promise.resolve()}),(()=>(t.states[e]=3,n("reject",e),Promise.reject(e)))))))),s=e=>(t.loading=!0,r(e).then((e=>{t.loading=!1;const n=t.queueLoadedCallbacks.shift();return I.from(n).each(D),o(e)}))),a=ke(e);return t.loading?new Promise(((e,n)=>{t.queueLoadedCallbacks.push((()=>{s(a).then(e,n)}))})):s(a)}}ca.ScriptLoader=new ca;const ua=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},ma={},fa=ua("en"),ga=()=>xe(ma,fa.get()),pa={getData:()=>he(ma,(e=>({...e}))),setCode:e=>{e&&fa.set(e)},getCode:()=>fa.get(),add:(e,t)=>{let n=ma[e];n||(ma[e]=n={});const o=q(fe(t),(e=>e.toLowerCase()));pe(t,((e,r)=>{const s=r.toLowerCase();s!==r&&((e,t)=>{const n=e.indexOf(t);return-1!==n&&e.indexOf(t,n+1)>n})(o,s)?(_e(t,s)||(n[s]=e),n[r]=e):n[s]=e}))},translate:e=>{const t=ga().getOr({}),n=e=>w(e)?Object.prototype.toString.call(e):o(e)?"":""+e,o=e=>""===e||null==e,r=e=>{const o=n(e);return _e(t,o)?n(t[o]):xe(t,o.toLowerCase()).map(n).getOr(o)},s=e=>e.replace(/{context:\w+}$/,"");if(o(e))return"";if(f(a=e)&&_e(a,"raw"))return n(e.raw);var a;if((e=>p(e)&&e.length>1)(e)){const t=e.slice(1);return s(r(e[0]).replace(/\{([0-9]+)\}/g,((e,o)=>_e(t,o)?n(t[o]):e)))}return s(r(e))},isRtl:()=>ga().bind((e=>xe(e,"_dir"))).exists((e=>"rtl"===e)),hasCode:e=>_e(ma,e)},ha=()=>{const e=[],t={},n={},o=[],r=(e,t)=>{const n=Y(o,(n=>n.name===e&&n.state===t));V(n,(e=>e.resolve()))},s=e=>_e(t,e),a=(e,n)=>{const o=pa.getCode();!o||n&&-1===(","+(n||"")+",").indexOf(","+o+",")||ca.ScriptLoader.add(t[e]+"/langs/"+o+".js")},i=(e,t="added")=>"added"===t&&(e=>_e(n,e))(e)||"loaded"===t&&s(e)?Promise.resolve():new Promise((n=>{o.push({name:e,state:t,resolve:n})}));return{items:e,urls:t,lookup:n,get:e=>{if(n[e])return n[e].instance},requireLangPack:(e,t)=>{!1!==ha.languageLoad&&(s(e)?a(e,t):i(e,"loaded").then((()=>a(e,t))))},add:(t,o)=>(e.push(o),n[t]={instance:o},r(t,"added"),o),remove:e=>{delete t[e],delete n[e]},createUrl:(e,t)=>m(t)?m(e)?{prefix:"",resource:t,suffix:""}:{prefix:e.prefix,resource:t,suffix:e.suffix}:t,load:(e,o)=>{if(t[e])return Promise.resolve();let s=m(o)?o:o.prefix+o.resource+o.suffix;0!==s.indexOf("/")&&-1===s.indexOf("://")&&(s=ha.baseURL+"/"+s),t[e]=s.substring(0,s.lastIndexOf("/"));const a=()=>(r(e,"loaded"),Promise.resolve());return n[e]?a():ca.ScriptLoader.add(s).then(a)},waitFor:i}};ha.languageLoad=!0,ha.baseURL="",ha.PluginManager=ha(),ha.ThemeManager=ha(),ha.ModelManager=ha();const ba=e=>{const t=ua(I.none()),n=()=>t.get().each((e=>clearInterval(e)));return{clear:()=>{n(),t.set(I.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:o=>{n(),t.set(I.some(setInterval(o,e)))}}},va=()=>{const e=(e=>{const t=ua(I.none()),n=()=>t.get().each(e);return{clear:()=>{n(),t.set(I.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{n(),t.set(I.some(e))}}})(_);return{...e,on:t=>e.get().each(t)}},ya=(e,t)=>{let n=null;return{cancel:()=>{h(n)||(clearTimeout(n),n=null)},throttle:(...o)=>{h(n)&&(n=setTimeout((()=>{n=null,e.apply(null,o)}),t))}}},Ca=(e,t)=>{let n=null;const o=()=>{h(n)||(clearTimeout(n),n=null)};return{cancel:o,throttle:(...r)=>{o(),n=setTimeout((()=>{n=null,e.apply(null,r)}),t)}}},wa=N("mce-annotation"),Ea=N("data-mce-annotation"),xa=N("data-mce-annotation-uid"),_a=N("data-mce-annotation-active"),Sa=N("data-mce-annotation-classes"),ka=N("data-mce-annotation-attrs"),Na=e=>t=>_n(t,e),Ra=(e,t)=>{const n=e.selection.getRng(),o=yn(n.startContainer),r=yn(e.getBody()),s=t.fold((()=>"."+wa()),(e=>`[${Ea()}="${e}"]`)),a=In(o,n.startOffset).getOr(o);return no(a,s,Na(r)).bind((t=>tn(t,`${xa()}`).bind((n=>tn(t,`${Ea()}`).map((t=>{const o=Ta(e,n);return{uid:n,name:t,elements:o}}))))))},Aa=(e,t)=>nn(e,"data-mce-bogus")||((e,t,n)=>eo(e,'[data-mce-bogus="all"]',n).isSome())(e,0,Na(t)),Ta=(e,t)=>{const n=yn(e.getBody()),o=Uo(n,`[${xa()}="${t}"]`);return Y(o,(e=>!Aa(e,n)))},Oa=(e,t)=>{const n=yn(e.getBody()),o=Uo(n,`[${Ea()}="${t}"]`),r={};return V(o,(e=>{if(!Aa(e,n)){const t=en(e,xa()),n=xe(r,t).getOr([]);r[t]=n.concat([e])}})),r};let Ba=0;const Pa=e=>{const t=(new Date).getTime(),n=Math.floor(1e9*Math.random());return Ba++,e+"_"+n+Ba+String(t)},Da=(e,t)=>yn(e.dom.cloneNode(t)),La=e=>Da(e,!1),Ma=e=>Da(e,!0),Ia=(e,t,n=L)=>{const o=new $o(e,t),r=e=>{let t;do{t=o[e]()}while(t&&!lr(t)&&!n(t));return I.from(t).filter(lr)};return{current:()=>I.from(o.current()).filter(lr),next:()=>r("next"),prev:()=>r("prev"),prev2:()=>r("prev2")}},Fa=(e,t)=>{const n=t||(t=>e.isBlock(t)||gr(t)||br(t)),o=(e,t,n,r)=>{if(lr(e)){const n=r(e,t,e.data);if(-1!==n)return I.some({container:e,offset:n})}return n().bind((e=>o(e.container,e.offset,n,r)))};return{backwards:(t,r,s,a)=>{const i=Ia(t,null!=a?a:e.getRoot(),n);return o(t,r,(()=>i.prev().map((e=>({container:e,offset:e.length})))),s).getOrNull()},forwards:(t,r,s,a)=>{const i=Ia(t,null!=a?a:e.getRoot(),n);return o(t,r,(()=>i.next().map((e=>({container:e,offset:0})))),s).getOrNull()}}},Ua=((e,t)=>{const n=t=>e(t)?I.from(t.dom.nodeValue):I.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return n(t).getOr("")},getOption:n,set:(t,n)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=n}}})(Kt),za=e=>Ua.get(e),ja=e=>{let t;return n=>(t=t||se(e,M),_e(t,Ht(n)))},Ha=e=>Wt(e)&&"br"===Ht(e),$a=ja(["h1","h2","h3","h4","h5","h6","p","div","address","pre","form","blockquote","center","dir","fieldset","header","footer","article","section","hgroup","aside","nav","figure"]),qa=ja(["ul","ol","dl"]),Va=ja(["li","dd","dt"]),Wa=ja(["thead","tbody","tfoot"]),Ka=ja(["td","th"]),Ya=ja(["pre","script","textarea","style"]),Ga=()=>{const e=bn("br");return Qt(e,"data-mce-bogus","1"),e},Xa=e=>{wo(e),vo(e,Ga())},Za=qo,Qa=Wo,Ja=e=>e.replace(/\uFEFF/g,""),ei=Jo,ti=lr,ni=e=>(ti(e)&&(e=e.parentNode),ei(e)&&e.hasAttribute("data-mce-caret")),oi=e=>ti(e)&&Qa(e.data),ri=e=>ni(e)||oi(e),si=e=>e.firstChild!==e.lastChild||!gr(e.firstChild),ai=e=>{const t=e.container();return!!lr(t)&&(t.data.charAt(e.offset())===Za||e.isAtStart()&&oi(t.previousSibling))},ii=e=>{const t=e.container();return!!lr(t)&&(t.data.charAt(e.offset()-1)===Za||e.isAtEnd()&&oi(t.nextSibling))},li=e=>ti(e)&&e.data[0]===Za,di=e=>ti(e)&&e.data[e.data.length-1]===Za,ci=e=>e&&e.hasAttribute("data-mce-caret")?((e=>{var t;const n=e.getElementsByTagName("br"),o=n[n.length-1];rr(o)&&(null===(t=o.parentNode)||void 0===t||t.removeChild(o))})(e),e.removeAttribute("data-mce-caret"),e.removeAttribute("data-mce-bogus"),e.removeAttribute("style"),e.removeAttribute("data-mce-style"),e.removeAttribute("_moz_abspos"),e):null,ui=e=>ni(e.startContainer),mi=Math.round,fi=e=>e?{left:mi(e.left),top:mi(e.top),bottom:mi(e.bottom),right:mi(e.right),width:mi(e.width),height:mi(e.height)}:{left:0,top:0,bottom:0,right:0,width:0,height:0},gi=(e,t)=>(e=fi(e),t||(e.left=e.left+e.width),e.right=e.left,e.width=0,e),pi=(e,t,n)=>e>=0&&e<=Math.min(t.height,n.height)/2,hi=(e,t)=>{const n=Math.min(t.height/2,e.height/2);return e.bottom-n<t.top||!(e.top>t.bottom)&&pi(t.top-e.bottom,e,t)},bi=(e,t)=>e.top>t.bottom||!(e.bottom<t.top)&&pi(t.bottom-e.top,e,t),vi=(e,t,n)=>{const o=Math.max(Math.min(t,e.left+e.width),e.left),r=Math.max(Math.min(n,e.top+e.height),e.top);return Math.sqrt((t-o)*(t-o)+(n-r)*(n-r))},yi=e=>{const t=e.startContainer,n=e.startOffset;return t===e.endContainer&&t.hasChildNodes()&&e.endOffset===n+1?t.childNodes[n]:null},Ci=(e,t)=>{if(Jo(e)&&e.hasChildNodes()){const n=e.childNodes,o=((e,t,n)=>Math.min(Math.max(e,0),n))(t,0,n.length-1);return n[o]}return e},wi=new RegExp("[\u0300-\u036f\u0483-\u0487\u0488-\u0489\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u0610-\u061a\u064b-\u065f\u0670\u06d6-\u06dc\u06df-\u06e4\u06e7-\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0859-\u085b\u08e3-\u0902\u093a\u093c\u0941-\u0948\u094d\u0951-\u0957\u0962-\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2-\u09e3\u0a01-\u0a02\u0a3c\u0a41-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a70-\u0a71\u0a75\u0a81-\u0a82\u0abc\u0ac1-\u0ac5\u0ac7-\u0ac8\u0acd\u0ae2-\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62-\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c00\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c62-\u0c63\u0c81\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc-\u0ccd\u0cd5-\u0cd6\u0ce2-\u0ce3\u0d01\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62-\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb-\u0ebc\u0ec8-\u0ecd\u0f18-\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86-\u0f87\u0f8d-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039-\u103a\u103d-\u103e\u1058-\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085-\u1086\u108d\u109d\u135d-\u135f\u1712-\u1714\u1732-\u1734\u1752-\u1753\u1772-\u1773\u17b4-\u17b5\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927-\u1928\u1932\u1939-\u193b\u1a17-\u1a18\u1a1b\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1ab0-\u1abd\u1abe\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80-\u1b81\u1ba2-\u1ba5\u1ba8-\u1ba9\u1bab-\u1bad\u1be6\u1be8-\u1be9\u1bed\u1bef-\u1bf1\u1c2c-\u1c33\u1c36-\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1cf4\u1cf8-\u1cf9\u1dc0-\u1df5\u1dfc-\u1dff\u200c-\u200d\u20d0-\u20dc\u20dd-\u20e0\u20e1\u20e2-\u20e4\u20e5-\u20f0\u2cef-\u2cf1\u2d7f\u2de0-\u2dff\u302a-\u302d\u302e-\u302f\u3099-\u309a\ua66f\ua670-\ua672\ua674-\ua67d\ua69e-\ua69f\ua6f0-\ua6f1\ua802\ua806\ua80b\ua825-\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\ua9e5\uaa29-\uaa2e\uaa31-\uaa32\uaa35-\uaa36\uaa43\uaa4c\uaa7c\uaab0\uaab2-\uaab4\uaab7-\uaab8\uaabe-\uaabf\uaac1\uaaec-\uaaed\uaaf6\uabe5\uabe8\uabed\ufb1e\ufe00-\ufe0f\ufe20-\ufe2f\uff9e-\uff9f]"),Ei=e=>m(e)&&e.charCodeAt(0)>=768&&wi.test(e),xi=hr,_i=br,Si=gr,ki=lr,Ni=nr(["script","style","textarea"]),Ri=nr(["img","input","textarea","hr","iframe","video","audio","object","embed"]),Ai=nr(["table"]),Ti=ri,Oi=e=>!Ti(e)&&(ki(e)?!Ni(e.parentNode):Ri(e)||Si(e)||Ai(e)||Bi(e)),Bi=e=>!(e=>Jo(e)&&"true"===e.getAttribute("unselectable"))(e)&&_i(e),Pi=(e,t)=>Oi(e)&&((e,t)=>{for(let n=e.parentNode;n&&n!==t;n=n.parentNode){if(Bi(n))return!1;if(xi(n))return!0}return!0})(e,t),Di=Jo,Li=Oi,Mi=or("display","block table"),Ii=or("float","left right"),Fi=((...e)=>t=>{for(let n=0;n<e.length;n++)if(!e[n](t))return!1;return!0})(Di,Li,O(Ii)),Ui=O(or("white-space","pre pre-line pre-wrap")),zi=lr,ji=gr,Hi=la.nodeIndex,$i=(e,t)=>t<0&&Jo(e)&&e.hasChildNodes()?void 0:Ci(e,t),qi=e=>e?e.createRange():la.DOM.createRng(),Vi=e=>m(e)&&/[\r\n\t ]/.test(e),Wi=e=>!!e.setStart&&!!e.setEnd,Ki=e=>{const t=e.startContainer,n=e.startOffset;if(Vi(e.toString())&&Ui(t.parentNode)&&lr(t)){const e=t.data;if(Vi(e[n-1])||Vi(e[n+1]))return!0}return!1},Yi=e=>0===e.left&&0===e.right&&0===e.top&&0===e.bottom,Gi=e=>{var t;let n;const o=e.getClientRects();return n=o.length>0?fi(o[0]):fi(e.getBoundingClientRect()),!Wi(e)&&ji(e)&&Yi(n)?(e=>{const t=e.ownerDocument,n=qi(t),o=t.createTextNode(Vo),r=e.parentNode;r.insertBefore(o,e),n.setStart(o,0),n.setEnd(o,1);const s=fi(n.getBoundingClientRect());return r.removeChild(o),s})(e):Yi(n)&&Wi(e)&&null!==(t=(e=>{const t=e.startContainer,n=e.endContainer,o=e.startOffset,r=e.endOffset;if(t===n&&lr(n)&&0===o&&1===r){const t=e.cloneRange();return t.setEndAfter(n),Gi(t)}return null})(e))&&void 0!==t?t:n},Xi=(e,t)=>{const n=gi(e,t);return n.width=1,n.right=n.left+1,n},Zi=(e,t,n)=>{const o=()=>(n||(n=(e=>{const t=[],n=e=>{var n,o;0!==e.height&&(t.length>0&&(n=e,o=t[t.length-1],n.left===o.left&&n.top===o.top&&n.bottom===o.bottom&&n.right===o.right)||t.push(e))},o=(e,t)=>{const o=qi(e.ownerDocument);if(t<e.data.length){if(Ei(e.data[t]))return;if(Ei(e.data[t-1])&&(o.setStart(e,t),o.setEnd(e,t+1),!Ki(o)))return void n(Xi(Gi(o),!1))}t>0&&(o.setStart(e,t-1),o.setEnd(e,t),Ki(o)||n(Xi(Gi(o),!1))),t<e.data.length&&(o.setStart(e,t),o.setEnd(e,t+1),Ki(o)||n(Xi(Gi(o),!0)))},r=e.container(),s=e.offset();if(zi(r))return o(r,s),t;if(Di(r))if(e.isAtEnd()){const e=$i(r,s);zi(e)&&o(e,e.data.length),Fi(e)&&!ji(e)&&n(Xi(Gi(e),!1))}else{const a=$i(r,s);if(zi(a)&&o(a,0),Fi(a)&&e.isAtEnd())return n(Xi(Gi(a),!1)),t;const i=$i(e.container(),e.offset()-1);Fi(i)&&!ji(i)&&(Mi(i)||Mi(a)||!Fi(a))&&n(Xi(Gi(i),!1)),Fi(a)&&n(Xi(Gi(a),!0))}return t})(Zi(e,t))),n);return{container:N(e),offset:N(t),toRange:()=>{const n=qi(e.ownerDocument);return n.setStart(e,t),n.setEnd(e,t),n},getClientRects:o,isVisible:()=>o().length>0,isAtStart:()=>(zi(e),0===t),isAtEnd:()=>zi(e)?t>=e.data.length:t>=e.childNodes.length,isEqual:n=>n&&e===n.container()&&t===n.offset(),getNode:n=>$i(e,n?t-1:t)}};Zi.fromRangeStart=e=>Zi(e.startContainer,e.startOffset),Zi.fromRangeEnd=e=>Zi(e.endContainer,e.endOffset),Zi.after=e=>Zi(e.parentNode,Hi(e)+1),Zi.before=e=>Zi(e.parentNode,Hi(e)),Zi.isAbove=(e,t)=>Mt(le(t.getClientRects()),de(e.getClientRects()),hi).getOr(!1),Zi.isBelow=(e,t)=>Mt(de(t.getClientRects()),le(e.getClientRects()),bi).getOr(!1),Zi.isAtStart=e=>!!e&&e.isAtStart(),Zi.isAtEnd=e=>!!e&&e.isAtEnd(),Zi.isTextPosition=e=>!!e&&lr(e.container()),Zi.isElementPosition=e=>!Zi.isTextPosition(e);const Qi=(e,t)=>{lr(t)&&0===t.data.length&&e.remove(t)},Ji=(e,t,n)=>{fr(n)?((e,t,n)=>{const o=I.from(n.firstChild),r=I.from(n.lastChild);t.insertNode(n),o.each((t=>Qi(e,t.previousSibling))),r.each((t=>Qi(e,t.nextSibling)))})(e,t,n):((e,t,n)=>{t.insertNode(n),Qi(e,n.previousSibling),Qi(e,n.nextSibling)})(e,t,n)},el=lr,tl=rr,nl=la.nodeIndex,ol=e=>{const t=e.parentNode;return tl(t)?ol(t):t},rl=e=>e?Be(e.childNodes,((e,t)=>(tl(t)&&"BR"!==t.nodeName?e=e.concat(rl(t)):e.push(t),e)),[]):[],sl=e=>t=>e===t,al=e=>(el(e)?"text()":e.nodeName.toLowerCase())+"["+(e=>{let t,n;t=rl(ol(e)),n=Pe(t,sl(e),e),t=t.slice(0,n+1);const o=Be(t,((e,n,o)=>(el(n)&&el(t[o-1])&&e++,e)),0);return t=Oe(t,nr([e.nodeName])),n=Pe(t,sl(e),e),n-o})(e)+"]",il=(e,t)=>{let n,o=[],r=t.container(),s=t.offset();if(el(r))n=((e,t)=>{let n=e;for(;(n=n.previousSibling)&&el(n);)t+=n.data.length;return t})(r,s);else{const e=r.childNodes;s>=e.length?(n="after",s=e.length-1):n="before",r=e[s]}o.push(al(r));let a=((e,t,n)=>{const o=[];for(let n=t.parentNode;n&&n!==e;n=n.parentNode)o.push(n);return o})(e,r);return a=Oe(a,O(rr)),o=o.concat(Te(a,(e=>al(e)))),o.reverse().join("/")+","+n},ll=(e,t)=>{if(!t)return null;const n=t.split(","),o=n[0].split("/"),r=n.length>1?n[1]:"before",s=Be(o,((e,t)=>{const n=/([\w\-\(\)]+)\[([0-9]+)\]/.exec(t);return n?("text()"===n[1]&&(n[1]="#text"),((e,t,n)=>{let o=rl(e);return o=Oe(o,((e,t)=>!el(e)||!el(o[t-1]))),o=Oe(o,nr([t])),o[n]})(e,n[1],parseInt(n[2],10))):null}),e);if(!s)return null;if(!el(s)&&s.parentNode){let e;return e="after"===r?nl(s)+1:nl(s),Zi(s.parentNode,e)}return((e,t)=>{let n=e,o=0;for(;el(n);){const r=n.data.length;if(t>=o&&t<=o+r){e=n,t-=o;break}if(!el(n.nextSibling)){e=n,t=r;break}o+=r,n=n.nextSibling}return el(e)&&t>e.data.length&&(t=e.data.length),Zi(e,t)})(s,parseInt(r,10))},dl=br,cl=(e,t,n,o,r)=>{const s=r?o.startContainer:o.endContainer;let a=r?o.startOffset:o.endOffset;const i=[],l=e.getRoot();if(lr(s))i.push(n?((e,t,n)=>{let o=e(t.data.slice(0,n)).length;for(let n=t.previousSibling;n&&lr(n);n=n.previousSibling)o+=e(n.data).length;return o})(t,s,a):a);else{let t=0;const o=s.childNodes;a>=o.length&&o.length&&(t=1,a=Math.max(0,o.length-1)),i.push(e.nodeIndex(o[a],n)+t)}for(let t=s;t&&t!==l;t=t.parentNode)i.push(e.nodeIndex(t,n));return i},ul=(e,t,n)=>{let o=0;return Pt.each(e.select(t),(e=>"all"===e.getAttribute("data-mce-bogus")?void 0:e!==n&&void o++)),o},ml=(e,t)=>{let n=t?e.startContainer:e.endContainer,o=t?e.startOffset:e.endOffset;if(Jo(n)&&"TR"===n.nodeName){const r=n.childNodes;n=r[Math.min(t?o:o-1,r.length-1)],n&&(o=t?0:n.childNodes.length,t?e.setStart(n,o):e.setEnd(n,o))}},fl=e=>(ml(e,!0),ml(e,!1),e),gl=(e,t)=>{if(Jo(e)&&(e=Ci(e,t),dl(e)))return e;if(ri(e)){lr(e)&&ni(e)&&(e=e.parentNode);let t=e.previousSibling;if(dl(t))return t;if(t=e.nextSibling,dl(t))return t}},pl=(e,t,n)=>{const o=n.getNode(),r=n.getRng();if("IMG"===o.nodeName||dl(o)){const e=o.nodeName;return{name:e,index:ul(n.dom,e,o)}}const s=(e=>gl(e.startContainer,e.startOffset)||gl(e.endContainer,e.endOffset))(r);if(s){const e=s.tagName;return{name:e,index:ul(n.dom,e,s)}}return((e,t,n,o)=>{const r=t.dom,s=cl(r,e,n,o,!0),a=t.isForward(),i=ui(o)?{isFakeCaret:!0}:{};return t.isCollapsed()?{start:s,forward:a,...i}:{start:s,end:cl(r,e,n,o,!1),forward:a,...i}})(e,n,t,r)},hl=(e,t,n)=>{const o={"data-mce-type":"bookmark",id:t,style:"overflow:hidden;line-height:0px"};return n?e.create("span",o,"&#xFEFF;"):e.create("span",o)},bl=(e,t)=>{const n=e.dom;let o=e.getRng();const r=n.uniqueId(),s=e.isCollapsed(),a=e.getNode(),i=a.nodeName,l=e.isForward();if("IMG"===i)return{name:i,index:ul(n,i,a)};const d=fl(o.cloneRange());if(!s){d.collapse(!1);const e=hl(n,r+"_end",t);Ji(n,d,e)}o=fl(o),o.collapse(!0);const c=hl(n,r+"_start",t);return Ji(n,o,c),e.moveToBookmark({id:r,keep:!0,forward:l}),{id:r,forward:l}},vl=T(pl,R,!0),yl=e=>{const t=t=>t(e),n=N(e),o=()=>r,r={tag:!0,inner:e,fold:(t,n)=>n(e),isValue:M,isError:L,map:t=>wl.value(t(e)),mapError:o,bind:t,exists:t,forall:t,getOr:n,or:o,getOrThunk:n,orThunk:o,getOrDie:n,each:t=>{t(e)},toOptional:()=>I.some(e)};return r},Cl=e=>{const t=()=>n,n={tag:!1,inner:e,fold:(t,n)=>t(e),isValue:L,isError:M,map:t,mapError:t=>wl.error(t(e)),bind:t,exists:L,forall:M,getOr:R,or:R,getOrThunk:P,orThunk:P,getOrDie:B(String(e)),each:_,toOptional:I.none};return n},wl={value:yl,error:Cl,fromOption:(e,t)=>e.fold((()=>Cl(t)),yl)},El=e=>{if(!p(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],n={};return V(e,((o,r)=>{const s=fe(o);if(1!==s.length)throw new Error("one and only one name per case");const a=s[0],i=o[a];if(void 0!==n[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!p(i))throw new Error("case arguments must be an array");t.push(a),n[a]=(...n)=>{const o=n.length;if(o!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+o);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,n)},match:e=>{const o=fe(e);if(t.length!==o.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+o.join(","));if(!ne(t,(e=>H(o,e))))throw new Error("Not all branches were specified when using match. Specified: "+o.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,n)},log:e=>{console.log(e,{constructors:t,constructor:a,params:n})}}}})),n};El([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const xl=e=>"inline-command"===e.type||"inline-format"===e.type,_l=e=>"block-command"===e.type||"block-format"===e.type,Sl=e=>{var t;const n=t=>wl.error({message:t,pattern:e}),o=(t,o,r)=>{if(void 0!==e.format){let r;if(p(e.format)){if(!ne(e.format,m))return n(t+" pattern has non-string items in the `format` array");r=e.format}else{if(!m(e.format))return n(t+" pattern has non-string `format` parameter");r=[e.format]}return wl.value(o(r))}return void 0!==e.cmd?m(e.cmd)?wl.value(r(e.cmd,e.value)):n(t+" pattern has non-string `cmd` parameter"):n(t+" pattern is missing both `format` and `cmd` parameters")};if(!f(e))return n("Raw pattern is not an object");if(!m(e.start))return n("Raw pattern is missing `start` parameter");if(void 0!==e.end){if(!m(e.end))return n("Inline pattern has non-string `end` parameter");if(0===e.start.length&&0===e.end.length)return n("Inline pattern has empty `start` and `end` parameters");let t=e.start,r=e.end;return 0===r.length&&(r=t,t=""),o("Inline",(e=>({type:"inline-format",start:t,end:r,format:e})),((e,n)=>({type:"inline-command",start:t,end:r,cmd:e,value:n})))}if(void 0!==e.replacement)return m(e.replacement)?0===e.start.length?n("Replacement pattern has empty `start` parameter"):wl.value({type:"inline-command",start:"",end:e.start,cmd:"mceInsertContent",value:e.replacement}):n("Replacement pattern has non-string `replacement` parameter");{const r=null!==(t=e.trigger)&&void 0!==t?t:"space";return 0===e.start.length?n("Block pattern has empty `start` parameter"):o("Block",(t=>({type:"block-format",start:e.start,format:t[0],trigger:r})),((t,n)=>({type:"block-command",start:e.start,cmd:t,value:n,trigger:r})))}},kl=e=>Y(e,_l),Nl=e=>Y(e,xl),Rl=(e,t)=>({...e,blockPatterns:Y(e.blockPatterns,(e=>((e,t)=>("block-command"===e.type||"block-format"===e.type)&&e.trigger===t)(e,t)))}),Al=e=>{const t=(e=>{const t=[],n=[];return V(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{n.push(e)}))})),{errors:t,values:n}})(q(e,Sl));return V(t.errors,(e=>console.error(e.message,e.pattern))),t.values},Tl=xt().deviceType,Ol=Tl.isTouch(),Bl=la.DOM,Pl=e=>u(e,RegExp),Dl=e=>t=>t.options.get(e),Ll=e=>m(e)||f(e),Ml=(e,t="")=>n=>{const o=m(n);if(o){if(-1!==n.indexOf("=")){const r=(e=>{const t=e.indexOf("=")>0?e.split(/[;,](?![^=;,]*(?:[;,]|$))/):e.split(",");return X(t,((e,t)=>{const n=t.split("="),o=n[0],r=n.length>1?n[1]:o;return e[We(o)]=We(r),e}),{})})(n);return{value:xe(r,e.id).getOr(t),valid:o}}return{value:n,valid:o}}return{valid:!1,message:"Must be a string."}},Il=Dl("iframe_attrs"),Fl=Dl("doctype"),Ul=Dl("document_base_url"),zl=Dl("body_id"),jl=Dl("body_class"),Hl=Dl("content_security_policy"),$l=Dl("br_in_pre"),ql=Dl("forced_root_block"),Vl=Dl("forced_root_block_attrs"),Wl=Dl("newline_behavior"),Kl=Dl("br_newline_selector"),Yl=Dl("no_newline_selector"),Gl=Dl("keep_styles"),Xl=Dl("end_container_on_empty_block"),Zl=Dl("automatic_uploads"),Ql=Dl("images_reuse_filename"),Jl=Dl("images_replace_blob_uris"),ed=Dl("icons"),td=Dl("icons_url"),nd=Dl("images_upload_url"),od=Dl("images_upload_base_path"),rd=Dl("images_upload_credentials"),sd=Dl("images_upload_handler"),ad=Dl("content_css_cors"),id=Dl("referrer_policy"),ld=Dl("language"),dd=Dl("language_url"),cd=Dl("indent_use_margin"),ud=Dl("indentation"),md=Dl("content_css"),fd=Dl("content_style"),gd=Dl("font_css"),pd=Dl("directionality"),hd=Dl("inline_boundaries_selector"),bd=Dl("object_resizing"),vd=Dl("resize_img_proportional"),yd=Dl("placeholder"),Cd=Dl("event_root"),wd=Dl("service_message"),Ed=Dl("theme"),xd=Dl("theme_url"),_d=Dl("model"),Sd=Dl("model_url"),kd=Dl("inline_boundaries"),Nd=Dl("formats"),Rd=Dl("preview_styles"),Ad=Dl("format_empty_lines"),Td=Dl("format_noneditable_selector"),Od=Dl("custom_ui_selector"),Bd=Dl("inline"),Pd=Dl("hidden_input"),Dd=Dl("submit_patch"),Ld=Dl("add_form_submit_trigger"),Md=Dl("add_unload_trigger"),Id=Dl("custom_undo_redo_levels"),Fd=Dl("disable_nodechange"),Ud=Dl("readonly"),zd=Dl("editable_root"),jd=Dl("content_css_cors"),Hd=Dl("plugins"),$d=Dl("external_plugins"),qd=Dl("block_unsupported_drop"),Vd=Dl("visual"),Wd=Dl("visual_table_class"),Kd=Dl("visual_anchor_class"),Yd=Dl("iframe_aria_text"),Gd=Dl("setup"),Xd=Dl("init_instance_callback"),Zd=Dl("urlconverter_callback"),Qd=Dl("auto_focus"),Jd=Dl("browser_spellcheck"),ec=Dl("protect"),tc=Dl("paste_block_drop"),nc=Dl("paste_data_images"),oc=Dl("paste_preprocess"),rc=Dl("paste_postprocess"),sc=Dl("newdocument_content"),ac=Dl("paste_webkit_styles"),ic=Dl("paste_remove_styles_if_webkit"),lc=Dl("paste_merge_formats"),dc=Dl("smart_paste"),cc=Dl("paste_as_text"),uc=Dl("paste_tab_spaces"),mc=Dl("allow_html_data_urls"),fc=Dl("text_patterns"),gc=Dl("text_patterns_lookup"),pc=Dl("noneditable_class"),hc=Dl("editable_class"),bc=Dl("noneditable_regexp"),vc=Dl("preserve_cdata"),yc=Dl("highlight_on_focus"),Cc=Dl("xss_sanitization"),wc=Dl("init_content_sync"),Ec=e=>Pt.explode(e.options.get("images_file_types")),xc=Dl("table_tab_navigation"),_c=Dl("details_initial_state"),Sc=Dl("details_serialized_state"),kc=Dl("force_hex_color"),Nc=Dl("sandbox_iframes"),Rc=e=>e.options.get("sandbox_iframes_exclusions"),Ac=Dl("convert_unsafe_embeds"),Tc=Jo,Oc=lr,Bc=e=>{const t=e.parentNode;t&&t.removeChild(e)},Pc=e=>{const t=Ja(e);return{count:e.length-t.length,text:t}},Dc=e=>{let t;for(;-1!==(t=e.data.lastIndexOf(Za));)e.deleteData(t,1)},Lc=(e,t)=>(Ic(e),t),Mc=(e,t)=>Zi.isTextPosition(t)?((e,t)=>Oc(e)&&t.container()===e?((e,t)=>{const n=Pc(e.data.substr(0,t.offset())),o=Pc(e.data.substr(t.offset()));return(n.text+o.text).length>0?(Dc(e),Zi(e,t.offset()-n.count)):t})(e,t):Lc(e,t))(e,t):((e,t)=>t.container()===e.parentNode?((e,t)=>{const n=t.container(),o=((e,t)=>{const n=j(e,t);return-1===n?I.none():I.some(n)})(ce(n.childNodes),e).map((e=>e<t.offset()?Zi(n,t.offset()-1):t)).getOr(t);return Ic(e),o})(e,t):Lc(e,t))(e,t),Ic=e=>{Tc(e)&&ri(e)&&(si(e)?e.removeAttribute("data-mce-caret"):Bc(e)),Oc(e)&&(Dc(e),0===e.data.length&&Bc(e))},Fc=br,Uc=Cr,zc=vr,jc=(e,t,n)=>{const o=gi(t.getBoundingClientRect(),n);let r,s;if("BODY"===e.tagName){const t=e.ownerDocument.documentElement;r=e.scrollLeft||t.scrollLeft,s=e.scrollTop||t.scrollTop}else{const t=e.getBoundingClientRect();r=e.scrollLeft-t.left,s=e.scrollTop-t.top}o.left+=r,o.right+=r,o.top+=s,o.bottom+=s,o.width=1;let a=t.offsetWidth-t.clientWidth;return a>0&&(n&&(a*=-1),o.left+=a,o.right+=a),o},Hc=(e,t,n,o)=>{const r=va();let s,a;const i=ql(e),l=e.dom,d=()=>{(e=>{var t,n;const o=Uo(yn(e),"*[contentEditable=false],video,audio,embed,object");for(let e=0;e<o.length;e++){const r=o[e].dom;let s=r.previousSibling;if(di(s)){const e=s.data;1===e.length?null===(t=s.parentNode)||void 0===t||t.removeChild(s):s.deleteData(e.length-1,1)}s=r.nextSibling,li(s)&&(1===s.data.length?null===(n=s.parentNode)||void 0===n||n.removeChild(s):s.deleteData(0,1))}})(t),a&&(Ic(a),a=null),r.on((e=>{l.remove(e.caret),r.clear()})),s&&(clearInterval(s),s=void 0)};return{show:(e,c)=>{let u;if(d(),zc(c))return null;if(!n(c))return a=((e,t)=>{var n;const o=(null!==(n=e.ownerDocument)&&void 0!==n?n:document).createTextNode(Za),r=e.parentNode;if(t){const t=e.previousSibling;if(ti(t)){if(ri(t))return t;if(di(t))return t.splitText(t.data.length-1)}null==r||r.insertBefore(o,e)}else{const t=e.nextSibling;if(ti(t)){if(ri(t))return t;if(li(t))return t.splitText(1),t}e.nextSibling?null==r||r.insertBefore(o,e.nextSibling):null==r||r.appendChild(o)}return o})(c,e),u=c.ownerDocument.createRange(),qc(a.nextSibling)?(u.setStart(a,0),u.setEnd(a,0)):(u.setStart(a,1),u.setEnd(a,1)),u;{const n=((e,t,n)=>{var o;const r=(null!==(o=t.ownerDocument)&&void 0!==o?o:document).createElement(e);r.setAttribute("data-mce-caret",n?"before":"after"),r.setAttribute("data-mce-bogus","all"),r.appendChild(Ga().dom);const s=t.parentNode;return n?null==s||s.insertBefore(r,t):t.nextSibling?null==s||s.insertBefore(r,t.nextSibling):null==s||s.appendChild(r),r})(i,c,e),d=jc(t,c,e);l.setStyle(n,"top",d.top),l.setStyle(n,"caret-color","transparent"),a=n;const m=l.create("div",{class:"mce-visual-caret","data-mce-bogus":"all"});l.setStyles(m,{...d}),l.add(t,m),r.set({caret:m,element:c,before:e}),e&&l.addClass(m,"mce-visual-caret-before"),s=setInterval((()=>{r.on((e=>{o()?l.toggleClass(e.caret,"mce-visual-caret-hidden"):l.addClass(e.caret,"mce-visual-caret-hidden")}))}),500),u=c.ownerDocument.createRange(),u.setStart(n,0),u.setEnd(n,0)}return u},hide:d,getCss:()=>".mce-visual-caret {position: absolute;background-color: black;background-color: currentcolor;}.mce-visual-caret-hidden {display: none;}*[data-mce-caret] {position: absolute;left: -1000px;right: auto;top: 0;margin: 0;padding: 0;}",reposition:()=>{r.on((e=>{const n=jc(t,e.element,e.before);l.setStyles(e.caret,{...n})}))},destroy:()=>clearInterval(s)}},$c=()=>Tt.browser.isFirefox(),qc=e=>Fc(e)||Uc(e),Vc=e=>(qc(e)||sr(e)&&$c())&&Tn(yn(e)).exists(oo),Wc=hr,Kc=br,Yc=Cr,Gc=or("display","block table table-cell table-caption list-item"),Xc=ri,Zc=ni,Qc=Jo,Jc=lr,eu=Oi,tu=e=>e>0,nu=e=>e<0,ou=(e,t)=>{let n;for(;n=e(t);)if(!Zc(n))return n;return null},ru=(e,t,n,o,r)=>{const s=new $o(e,o),a=Kc(e)||Zc(e);let i;if(nu(t)){if(a&&(i=ou(s.prev.bind(s),!0),n(i)))return i;for(;i=ou(s.prev.bind(s),r);)if(n(i))return i}if(tu(t)){if(a&&(i=ou(s.next.bind(s),!0),n(i)))return i;for(;i=ou(s.next.bind(s),r);)if(n(i))return i}return null},su=(e,t)=>{for(;e&&e!==t;){if(Gc(e))return e;e=e.parentNode}return null},au=(e,t,n)=>su(e.container(),n)===su(t.container(),n),iu=(e,t)=>{if(!t)return I.none();const n=t.container(),o=t.offset();return Qc(n)?I.from(n.childNodes[o+e]):I.none()},lu=(e,t)=>{var n;const o=(null!==(n=t.ownerDocument)&&void 0!==n?n:document).createRange();return e?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)),o},du=(e,t,n)=>su(t,e)===su(n,e),cu=(e,t,n)=>{const o=e?"previousSibling":"nextSibling";let r=n;for(;r&&r!==t;){let e=r[o];if(e&&Xc(e)&&(e=e[o]),Kc(e)||Yc(e)){if(du(t,e,r))return e;break}if(eu(e))break;r=r.parentNode}return null},uu=T(lu,!0),mu=T(lu,!1),fu=(e,t,n)=>{let o;const r=T(cu,!0,t),s=T(cu,!1,t),a=n.startContainer,i=n.startOffset;if(ni(a)){const e=Jc(a)?a.parentNode:a,t=e.getAttribute("data-mce-caret");if("before"===t&&(o=e.nextSibling,Vc(o)))return uu(o);if("after"===t&&(o=e.previousSibling,Vc(o)))return mu(o)}if(!n.collapsed)return n;if(lr(a)){if(Xc(a)){if(1===e){if(o=s(a),o)return uu(o);if(o=r(a),o)return mu(o)}if(-1===e){if(o=r(a),o)return mu(o);if(o=s(a),o)return uu(o)}return n}if(di(a)&&i>=a.data.length-1)return 1===e&&(o=s(a),o)?uu(o):n;if(li(a)&&i<=1)return-1===e&&(o=r(a),o)?mu(o):n;if(i===a.data.length)return o=s(a),o?uu(o):n;if(0===i)return o=r(a),o?mu(o):n}return n},gu=(e,t)=>iu(e?0:-1,t).filter(Kc),pu=(e,t,n)=>{const o=fu(e,t,n);return-1===e?Zi.fromRangeStart(o):Zi.fromRangeEnd(o)},hu=e=>I.from(e.getNode()).map(yn),bu=(e,t)=>{let n=t;for(;n=e(n);)if(n.isVisible())return n;return n},vu=(e,t)=>{const n=au(e,t);return!(n||!gr(e.getNode()))||n};var yu;!function(e){e[e.Backwards=-1]="Backwards",e[e.Forwards=1]="Forwards"}(yu||(yu={}));const Cu=br,wu=lr,Eu=Jo,xu=gr,_u=Oi,Su=e=>Ri(e)||(e=>!!Bi(e)&&!X(ce(e.getElementsByTagName("*")),((e,t)=>e||xi(t)),!1))(e),ku=Pi,Nu=(e,t)=>e.hasChildNodes()&&t<e.childNodes.length?e.childNodes[t]:null,Ru=(e,t)=>{if(tu(e)){if(_u(t.previousSibling)&&!wu(t.previousSibling))return Zi.before(t);if(wu(t))return Zi(t,0)}if(nu(e)){if(_u(t.nextSibling)&&!wu(t.nextSibling))return Zi.after(t);if(wu(t))return Zi(t,t.data.length)}return nu(e)?xu(t)?Zi.before(t):Zi.after(t):Zi.before(t)},Au=(e,t,n)=>{let o,r,s,a;if(!Eu(n)||!t)return null;if(t.isEqual(Zi.after(n))&&n.lastChild){if(a=Zi.after(n.lastChild),nu(e)&&_u(n.lastChild)&&Eu(n.lastChild))return xu(n.lastChild)?Zi.before(n.lastChild):a}else a=t;const i=a.container();let l=a.offset();if(wu(i)){if(nu(e)&&l>0)return Zi(i,--l);if(tu(e)&&l<i.length)return Zi(i,++l);o=i}else{if(nu(e)&&l>0&&(r=Nu(i,l-1),_u(r)))return!Su(r)&&(s=ru(r,e,ku,r),s)?wu(s)?Zi(s,s.data.length):Zi.after(s):wu(r)?Zi(r,r.data.length):Zi.before(r);if(tu(e)&&l<i.childNodes.length&&(r=Nu(i,l),_u(r)))return xu(r)?((e,t)=>{const n=t.nextSibling;return n&&_u(n)?wu(n)?Zi(n,0):Zi.before(n):Au(yu.Forwards,Zi.after(t),e)})(n,r):!Su(r)&&(s=ru(r,e,ku,r),s)?wu(s)?Zi(s,0):Zi.before(s):wu(r)?Zi(r,0):Zi.after(r);o=r||a.getNode()}if(o&&(tu(e)&&a.isAtEnd()||nu(e)&&a.isAtStart())&&(o=ru(o,e,M,n,!0),ku(o,n)))return Ru(e,o);r=o?ru(o,e,ku,n):o;const d=De(Y(((e,t)=>{const n=[];let o=e;for(;o&&o!==t;)n.push(o),o=o.parentNode;return n})(i,n),Cu));return!d||r&&d.contains(r)?r?Ru(e,r):null:(a=tu(e)?Zi.after(d):Zi.before(d),a)},Tu=e=>({next:t=>Au(yu.Forwards,t,e),prev:t=>Au(yu.Backwards,t,e)}),Ou=e=>Zi.isTextPosition(e)?0===e.offset():Oi(e.getNode()),Bu=e=>{if(Zi.isTextPosition(e)){const t=e.container();return e.offset()===t.data.length}return Oi(e.getNode(!0))},Pu=(e,t)=>!Zi.isTextPosition(e)&&!Zi.isTextPosition(t)&&e.getNode()===t.getNode(!0),Du=(e,t,n)=>{const o=Tu(t);return I.from(e?o.next(n):o.prev(n))},Lu=(e,t,n)=>Du(e,t,n).bind((o=>au(n,o,t)&&((e,t,n)=>{return e?!Pu(t,n)&&(o=t,!(!Zi.isTextPosition(o)&&gr(o.getNode())))&&Bu(t)&&Ou(n):!Pu(n,t)&&Ou(t)&&Bu(n);var o})(e,n,o)?Du(e,t,o):I.some(o))),Mu=(e,t,n,o)=>Lu(e,t,n).bind((n=>o(n)?Mu(e,t,n,o):I.some(n))),Iu=(e,t)=>{const n=e?t.firstChild:t.lastChild;return lr(n)?I.some(Zi(n,e?0:n.data.length)):n?Oi(n)?I.some(e?Zi.before(n):gr(o=n)?Zi.before(o):Zi.after(o)):((e,t,n)=>{const o=e?Zi.before(n):Zi.after(n);return Du(e,t,o)})(e,t,n):I.none();var o},Fu=T(Du,!0),Uu=T(Du,!1),zu=T(Iu,!0),ju=T(Iu,!1),Hu="_mce_caret",$u=e=>Jo(e)&&e.id===Hu,qu=(e,t)=>{let n=t;for(;n&&n!==e;){if($u(n))return n;n=n.parentNode}return null},Vu=e=>_e(e,"name"),Wu=e=>Pt.isArray(e.start),Ku=e=>!(!Vu(e)&&b(e.forward))||e.forward,Yu=(e,t)=>(Jo(t)&&e.isBlock(t)&&!t.innerHTML&&(t.innerHTML='<br data-mce-bogus="1" />'),t),Gu=(e,t)=>ju(e).fold(L,(e=>(t.setStart(e.container(),e.offset()),t.setEnd(e.container(),e.offset()),!0))),Xu=(e,t,n)=>!(!(e=>!e.hasChildNodes())(t)||!qu(e,t)||(((e,t)=>{var n;const o=(null!==(n=e.ownerDocument)&&void 0!==n?n:document).createTextNode(Za);e.appendChild(o),t.setStart(o,0),t.setEnd(o,0)})(t,n),0)),Zu=(e,t,n,o)=>{const r=n[t?"start":"end"],s=e.getRoot();if(r){let e=s,n=r[0];for(let t=r.length-1;e&&t>=1;t--){const n=e.childNodes;if(Xu(s,e,o))return!0;if(r[t]>n.length-1)return!!Xu(s,e,o)||Gu(e,o);e=n[r[t]]}lr(e)&&(n=Math.min(r[0],e.data.length)),Jo(e)&&(n=Math.min(r[0],e.childNodes.length)),t?o.setStart(e,n):o.setEnd(e,n)}return!0},Qu=e=>lr(e)&&e.data.length>0,Ju=(e,t,n)=>{const o=e.get(n.id+"_"+t),r=null==o?void 0:o.parentNode,s=n.keep;if(o&&r){let a,i;if("start"===t?s?o.hasChildNodes()?(a=o.firstChild,i=1):Qu(o.nextSibling)?(a=o.nextSibling,i=0):Qu(o.previousSibling)?(a=o.previousSibling,i=o.previousSibling.data.length):(a=r,i=e.nodeIndex(o)+1):(a=r,i=e.nodeIndex(o)):s?o.hasChildNodes()?(a=o.firstChild,i=1):Qu(o.previousSibling)?(a=o.previousSibling,i=o.previousSibling.data.length):(a=r,i=e.nodeIndex(o)):(a=r,i=e.nodeIndex(o)),!s){const r=o.previousSibling,s=o.nextSibling;let l;for(Pt.each(Pt.grep(o.childNodes),(e=>{lr(e)&&(e.data=e.data.replace(/\uFEFF/g,""))}));l=e.get(n.id+"_"+t);)e.remove(l,!0);if(lr(s)&&lr(r)&&!Tt.browser.isOpera()){const t=r.data.length;r.appendData(s.data),e.remove(s),a=r,i=t}}return I.some(Zi(a,i))}return I.none()},em=(e,t,n)=>((e,t,n=!1)=>2===t?pl(Ja,n,e):3===t?(e=>{const t=e.getRng();return{start:il(e.dom.getRoot(),Zi.fromRangeStart(t)),end:il(e.dom.getRoot(),Zi.fromRangeEnd(t)),forward:e.isForward()}})(e):t?(e=>({rng:e.getRng(),forward:e.isForward()}))(e):bl(e,!1))(e,t,n),tm=(e,t)=>{((e,t)=>{const n=e.dom;if(t){if(Wu(t))return((e,t)=>{const n=e.createRng();return Zu(e,!0,t,n)&&Zu(e,!1,t,n)?I.some({range:n,forward:Ku(t)}):I.none()})(n,t);if((e=>m(e.start))(t))return((e,t)=>{const n=I.from(ll(e.getRoot(),t.start)),o=I.from(ll(e.getRoot(),t.end));return Mt(n,o,((n,o)=>{const r=e.createRng();return r.setStart(n.container(),n.offset()),r.setEnd(o.container(),o.offset()),{range:r,forward:Ku(t)}}))})(n,t);if((e=>_e(e,"id"))(t))return((e,t)=>{const n=Ju(e,"start",t),o=Ju(e,"end",t);return Mt(n,o.or(n),((n,o)=>{const r=e.createRng();return r.setStart(Yu(e,n.container()),n.offset()),r.setEnd(Yu(e,o.container()),o.offset()),{range:r,forward:Ku(t)}}))})(n,t);if(Vu(t))return((e,t)=>I.from(e.select(t.name)[t.index]).map((t=>{const n=e.createRng();return n.selectNode(t),{range:n,forward:!0}})))(n,t);if((e=>_e(e,"rng"))(t))return I.some({range:t.rng,forward:Ku(t)})}return I.none()})(e,t).each((({range:t,forward:n})=>{e.setRng(t,n)}))},nm=e=>Jo(e)&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type"),om=(rm=Vo,e=>rm===e);var rm;const sm=e=>""!==e&&-1!==" \f\n\r\t\v".indexOf(e),am=e=>!sm(e)&&!om(e)&&!Wo(e),im=e=>{const t=[];if(e)for(let n=0;n<e.rangeCount;n++)t.push(e.getRangeAt(n));return t},lm=(e,t)=>{const n=Uo(t,"td[data-mce-selected],th[data-mce-selected]");return n.length>0?n:(e=>Y((e=>te(e,(e=>{const t=yi(e);return t?[yn(t)]:[]})))(e),Ka))(e)},dm=e=>lm(im(e.selection.getSel()),yn(e.getBody())),cm=(e,t)=>eo(e,"table",t),um=e=>Fn(e).fold(N([e]),(t=>[e].concat(um(t)))),mm=e=>Un(e).fold(N([e]),(t=>"br"===Ht(t)?Bn(t).map((t=>[e].concat(mm(t)))).getOr([]):[e].concat(mm(t)))),fm=(e,t)=>Mt((e=>{const t=e.startContainer,n=e.startOffset;return lr(t)?0===n?I.some(yn(t)):I.none():I.from(t.childNodes[n]).map(yn)})(t),(e=>{const t=e.endContainer,n=e.endOffset;return lr(t)?n===t.data.length?I.some(yn(t)):I.none():I.from(t.childNodes[n-1]).map(yn)})(t),((t,n)=>{const o=Q(um(e),T(_n,t)),r=Q(mm(e),T(_n,n));return o.isSome()&&r.isSome()})).getOr(!1),gm=(e,t,n,o)=>{const r=n,s=new $o(n,r),a=Ce(e.schema.getMoveCaretBeforeOnEnterElements(),((e,t)=>!H(["td","th","table"],t.toLowerCase())));let i=n;do{if(lr(i)&&0!==Pt.trim(i.data).length)return void(o?t.setStart(i,0):t.setEnd(i,i.data.length));if(a[i.nodeName])return void(o?t.setStartBefore(i):"BR"===i.nodeName?t.setEndBefore(i):t.setEndAfter(i))}while(i=o?s.next():s.prev());"BODY"===r.nodeName&&(o?t.setStart(r,0):t.setEnd(r,r.childNodes.length))},pm=e=>{const t=e.selection.getSel();return C(t)&&t.rangeCount>0},hm=(e,t)=>{const n=dm(e);n.length>0?V(n,(n=>{const o=n.dom,r=e.dom.createRng();r.setStartBefore(o),r.setEndAfter(o),t(r,!0)})):t(e.selection.getRng(),!1)},bm=(e,t,n)=>{const o=bl(e,t);n(o),e.moveToBookmark(o)},vm=e=>E(null==e?void 0:e.nodeType),ym=e=>Jo(e)&&!nm(e)&&!$u(e)&&!rr(e),Cm=(e,t,n)=>{const{selection:o,dom:r}=e,s=o.getNode(),a=br(s);bm(o,!0,(()=>{t()})),a&&br(s)&&r.isChildOf(s,e.getBody())?e.selection.select(s):n(o.getStart())&&wm(r,o)},wm=(e,t)=>{var n,o;const r=t.getRng(),{startContainer:s,startOffset:a}=r;if(!((e,t)=>{if(ym(t)&&!/^(TD|TH)$/.test(t.nodeName)){const n=e.getAttrib(t,"data-mce-selected"),o=parseInt(n,10);return!isNaN(o)&&o>0}return!1})(e,t.getNode())&&Jo(s)){const i=s.childNodes,l=e.getRoot();let d;if(a<i.length){const t=i[a];d=new $o(t,null!==(n=e.getParent(t,e.isBlock))&&void 0!==n?n:l)}else{const t=i[i.length-1];d=new $o(t,null!==(o=e.getParent(t,e.isBlock))&&void 0!==o?o:l),d.next(!0)}for(let n=d.current();n;n=d.next()){if("false"===e.getContentEditable(n))return;if(lr(n)&&!Sm(n))return r.setStart(n,0),void t.setRng(r)}}},Em=(e,t,n)=>{if(e){const o=t?"nextSibling":"previousSibling";for(e=n?e:e[o];e;e=e[o])if(Jo(e)||!Sm(e))return e}},xm=(e,t)=>!!e.getTextBlockElements()[t.nodeName.toLowerCase()]||Wr(e,t),_m=(e,t,n)=>e.schema.isValidChild(t,n),Sm=(e,t=!1)=>{if(C(e)&&lr(e)){const n=t?e.data.replace(/ /g,"\xa0"):e.data;return Yo(n)}return!1},km=(e,t)=>{const n=e.dom;return ym(t)&&"false"===n.getContentEditable(t)&&((e,t)=>{const n="[data-mce-cef-wrappable]",o=Td(e),r=Xe(o)?n:`${n},${o}`;return En(yn(t),r)})(e,t)&&0===n.select('[contenteditable="true"]',t).length},Nm=(e,t)=>w(e)?e(t):(C(t)&&(e=e.replace(/%(\w+)/g,((e,n)=>t[n]||e))),e),Rm=(e,t)=>(t=t||"",e=""+((e=e||"").nodeName||e),t=""+(t.nodeName||t),e.toLowerCase()===t.toLowerCase()),Am=(e,t)=>{if(y(e))return null;{let n=String(e);return"color"!==t&&"backgroundColor"!==t||(n=Hs(n)),"fontWeight"===t&&700===e&&(n="bold"),"fontFamily"===t&&(n=n.replace(/[\'\"]/g,"").replace(/,\s+/g,",")),n}},Tm=(e,t,n)=>{const o=e.getStyle(t,n);return Am(o,n)},Om=(e,t)=>{let n;return e.getParent(t,(t=>!!Jo(t)&&(n=e.getStyle(t,"text-decoration"),!!n&&"none"!==n))),n},Bm=(e,t,n)=>e.getParents(t,n,e.getRoot()),Pm=(e,t,n)=>{const o=e.formatter.get(t);return C(o)&&$(o,n)},Dm=e=>Se(e,"block"),Lm=e=>Se(e,"selector"),Mm=e=>Se(e,"inline"),Im=e=>Lm(e)&&!1!==e.expand&&!Mm(e),Fm=e=>(e=>{const t=[];let n=e;for(;n;){if(lr(n)&&n.data!==Za||n.childNodes.length>1)return[];Jo(n)&&t.push(n),n=n.firstChild}return t})(e).length>0,Um=e=>$u(e.dom)&&Fm(e.dom),zm=nm,jm=Bm,Hm=Sm,$m=xm,qm=(e,t)=>{let n=t;for(;n;){if(Jo(n)&&e.getContentEditable(n))return"false"===e.getContentEditable(n)?n:t;n=n.parentNode}return t},Vm=(e,t,n,o)=>{const r=t.data;if(e){for(let e=n;e>0;e--)if(o(r.charAt(e-1)))return e}else for(let e=n;e<r.length;e++)if(o(r.charAt(e)))return e;return-1},Wm=(e,t,n)=>Vm(e,t,n,(e=>om(e)||sm(e))),Km=(e,t,n)=>Vm(e,t,n,am),Ym=(e,t,n,o,r,s)=>{let a;const i=e.getParent(n,e.isBlock)||t,l=(t,n,o)=>{const s=Fa(e),l=r?s.backwards:s.forwards;return I.from(l(t,n,((e,t)=>zm(e.parentNode)?-1:(a=e,o(r,e,t))),i))};return l(n,o,Wm).bind((e=>s?l(e.container,e.offset+(r?-1:0),Km):I.some(e))).orThunk((()=>a?I.some({container:a,offset:r?0:a.length}):I.none()))},Gm=(e,t,n,o,r)=>{const s=o[r];lr(o)&&Xe(o.data)&&s&&(o=s);const a=jm(e,o);for(let o=0;o<a.length;o++)for(let r=0;r<t.length;r++){const s=t[r];if((!C(s.collapsed)||s.collapsed===n.collapsed)&&Lm(s)&&e.is(a[o],s.selector))return a[o]}return o},Xm=(e,t,n,o)=>{var r;let s=n;const a=e.getRoot(),i=t[0];if(Dm(i)&&(s=i.wrapper?null:e.getParent(n,i.block,a)),!s){const t=null!==(r=e.getParent(n,"LI,TD,TH,SUMMARY"))&&void 0!==r?r:a;s=e.getParent(lr(n)?n.parentNode:n,(t=>t!==a&&$m(e.schema,t)),t)}if(s&&Dm(i)&&i.wrapper&&(s=jm(e,s,"ul,ol").reverse()[0]||s),!s)for(s=n;s&&s[o]&&!e.isBlock(s[o])&&(s=s[o],!Rm(s,"br")););return s||n},Zm=(e,t,n,o)=>{const r=n.parentNode;return!C(n[o])&&(!(r!==t&&!y(r)&&!e.isBlock(r))||Zm(e,t,r,o))},Qm=(e,t,n,o,r)=>{let s=n;const a=r?"previousSibling":"nextSibling",i=e.getRoot();if(lr(n)&&!Hm(n)&&(r?o>0:o<n.data.length))return n;for(;s;){if(!t[0].block_expand&&e.isBlock(s))return s;for(let t=s[a];t;t=t[a]){const n=lr(t)&&!Zm(e,i,t,a);if(!zm(t)&&(!gr(l=t)||!l.getAttribute("data-mce-bogus")||l.nextSibling)&&!Hm(t,n))return s}if(s===i||s.parentNode===i){n=s;break}s=s.parentNode}var l;return n},Jm=e=>zm(e.parentNode)||zm(e),ef=(e,t,n,o=!1)=>{let{startContainer:r,startOffset:s,endContainer:a,endOffset:i}=t;const l=n[0];return Jo(r)&&r.hasChildNodes()&&(r=Ci(r,s),lr(r)&&(s=0)),Jo(a)&&a.hasChildNodes()&&(a=Ci(a,t.collapsed?i:i-1),lr(a)&&(i=a.data.length)),r=qm(e,r),a=qm(e,a),Jm(r)&&(r=zm(r)?r:r.parentNode,r=t.collapsed?r.previousSibling||r:r.nextSibling||r,lr(r)&&(s=t.collapsed?r.length:0)),Jm(a)&&(a=zm(a)?a:a.parentNode,a=t.collapsed?a.nextSibling||a:a.previousSibling||a,lr(a)&&(i=t.collapsed?0:a.length)),t.collapsed&&(Ym(e,e.getRoot(),r,s,!0,o).each((({container:e,offset:t})=>{r=e,s=t})),Ym(e,e.getRoot(),a,i,!1,o).each((({container:e,offset:t})=>{a=e,i=t}))),(Mm(l)||l.block_expand)&&(Mm(l)&&lr(r)&&0!==s||(r=Qm(e,n,r,s,!0)),Mm(l)&&lr(a)&&i!==a.data.length||(a=Qm(e,n,a,i,!1))),Im(l)&&(r=Gm(e,n,t,r,"previousSibling"),a=Gm(e,n,t,a,"nextSibling")),(Dm(l)||Lm(l))&&(r=Xm(e,n,r,"previousSibling"),a=Xm(e,n,a,"nextSibling"),Dm(l)&&(e.isBlock(r)||(r=Qm(e,n,r,s,!0),lr(r)&&(s=0)),e.isBlock(a)||(a=Qm(e,n,a,i,!1),lr(a)&&(i=a.data.length)))),Jo(r)&&r.parentNode&&(s=e.nodeIndex(r),r=r.parentNode),Jo(a)&&a.parentNode&&(i=e.nodeIndex(a)+1,a=a.parentNode),{startContainer:r,startOffset:s,endContainer:a,endOffset:i}},tf=(e,t,n)=>{var o;const r=t.startOffset,s=Ci(t.startContainer,r),a=t.endOffset,i=Ci(t.endContainer,a-1),l=e=>{const t=e[0];lr(t)&&t===s&&r>=t.data.length&&e.splice(0,1);const n=e[e.length-1];return 0===a&&e.length>0&&n===i&&lr(n)&&e.splice(e.length-1,1),e},d=(e,t,n)=>{const o=[];for(;e&&e!==n;e=e[t])o.push(e);return o},c=(t,n)=>e.getParent(t,(e=>e.parentNode===n),n),u=(e,t,o)=>{const r=o?"nextSibling":"previousSibling";for(let s=e,a=s.parentNode;s&&s!==t;s=a){a=s.parentNode;const t=d(s===e?s:s[r],r);t.length&&(o||t.reverse(),n(l(t)))}};if(s===i)return n(l([s]));const m=null!==(o=e.findCommonAncestor(s,i))&&void 0!==o?o:e.getRoot();if(e.isChildOf(s,i))return u(s,m,!0);if(e.isChildOf(i,s))return u(i,m);const f=c(s,m)||s,g=c(i,m)||i;u(s,f,!0);const p=d(f===s?f:f.nextSibling,"nextSibling",g===i?g.nextSibling:g);p.length&&n(l(p)),u(i,g)},nf=['pre[class*=language-][contenteditable="false"]',"figure.image","div[data-ephox-embed-iri]","div.tiny-pageembed","div.mce-toc","div[data-mce-toc]"],of=(e,t,n,o,r,s)=>{const{uid:a=t,...i}=n;un(e,wa()),Qt(e,`${xa()}`,a),Qt(e,`${Ea()}`,o);const{attributes:l={},classes:d=[]}=r(a,i);if(Jt(e,l),((e,t)=>{V(t,(t=>{un(e,t)}))})(e,d),s){d.length>0&&Qt(e,`${Sa()}`,d.join(","));const t=fe(l);t.length>0&&Qt(e,`${ka()}`,t.join(","))}},rf=(e,t,n,o,r)=>{const s=bn("span",e);return of(s,t,n,o,r,!1),s},sf=(e,t,n,o,r,s)=>{const a=[],i=rf(e.getDoc(),n,s,o,r),l=va(),d=()=>{l.clear()},c=e=>{V(e,u)},u=t=>{switch(((e,t,n,o)=>An(t).fold((()=>"skipping"),(r=>"br"===o||(e=>Kt(e)&&za(e)===Za)(t)?"valid":(e=>Wt(e)&&gn(e,wa()))(t)?"existing":$u(t.dom)?"caret":$(nf,(e=>En(t,e)))?"valid-block":_m(e,n,o)&&_m(e,Ht(r),n)?"valid":"invalid-child")))(e,t,"span",Ht(t))){case"invalid-child":{d();const e=Mn(t);c(e),d();break}case"valid-block":d(),of(t,n,s,o,r,!0);break;case"valid":{const e=l.get().getOrThunk((()=>{const e=La(i);return a.push(e),l.set(e),e}));yo(t,e);break}}};return tf(e.dom,t,(e=>{d(),(e=>{const t=q(e,yn);c(t)})(e)})),a},af=e=>{const t=(()=>{const e={};return{register:(t,n)=>{e[t]={name:t,settings:n}},lookup:t=>xe(e,t).map((e=>e.settings)),getNames:()=>fe(e)}})();((e,t)=>{const n=Ea(),o=e=>I.from(e.attr(n)).bind(t.lookup),r=e=>{var t,n;e.attr(xa(),null),e.attr(Ea(),null),e.attr(_a(),null);const o=I.from(e.attr(ka())).map((e=>e.split(","))).getOr([]),r=I.from(e.attr(Sa())).map((e=>e.split(","))).getOr([]);V(o,(t=>e.attr(t,null)));const s=null!==(n=null===(t=e.attr("class"))||void 0===t?void 0:t.split(" "))&&void 0!==n?n:[],a=re(s,[wa()].concat(r));e.attr("class",a.length>0?a.join(" "):null),e.attr(Sa(),null),e.attr(ka(),null)};e.serializer.addTempAttr(_a()),e.serializer.addAttributeFilter(n,(e=>{for(const t of e)o(t).each((e=>{!1===e.persistent&&("span"===t.name?t.unwrap():r(t))}))}))})(e,t);const n=((e,t)=>{const n=ua({}),o=()=>({listeners:[],previous:va()}),r=(e,t)=>{s(e,(e=>(t(e),e)))},s=(e,t)=>{const r=n.get(),s=t(xe(r,e).getOrThunk(o));r[e]=s,n.set(r)},a=(t,n)=>{V(Ta(e,t),(e=>{n?Qt(e,_a(),"true"):on(e,_a())}))},i=Ca((()=>{const n=ae(t.getNames());V(n,(t=>{s(t,(n=>{const o=n.previous.get();return Ra(e,I.some(t)).fold((()=>{o.each((e=>{(e=>{r(e,(t=>{V(t.listeners,(t=>t(!1,e)))}))})(t),n.previous.clear(),a(e,!1)}))}),(({uid:e,name:t,elements:s})=>{Dt(o,e)||(o.each((e=>a(e,!1))),((e,t,n)=>{r(e,(o=>{V(o.listeners,(o=>o(!0,e,{uid:t,nodes:q(n,(e=>e.dom))})))}))})(t,e,s),n.previous.set(e),a(e,!0))})),{previous:n.previous,listeners:n.listeners}}))}))}),30);return e.on("remove",(()=>{i.cancel()})),e.on("NodeChange",(()=>{i.throttle()})),{addListener:(e,t)=>{s(e,(e=>({previous:e.previous,listeners:e.listeners.concat([t])})))}}})(e,t),o=Xt("span"),r=e=>{V(e,(e=>{o(e)?xo(e):(e=>{fn(e,wa()),on(e,`${xa()}`),on(e,`${Ea()}`),on(e,`${_a()}`);const t=tn(e,`${ka()}`).map((e=>e.split(","))).getOr([]),n=tn(e,`${Sa()}`).map((e=>e.split(","))).getOr([]);var o;V(t,(t=>on(e,t))),o=e,V(n,(e=>{fn(o,e)})),on(e,`${Sa()}`),on(e,`${ka()}`)})(e)}))};return{register:(e,n)=>{t.register(e,n)},annotate:(n,o)=>{t.lookup(n).each((t=>{((e,t,n,o)=>{e.undoManager.transact((()=>{const r=e.selection,s=r.getRng(),a=dm(e).length>0,i=Pa("mce-annotation");if(s.collapsed&&!a&&((e,t)=>{const n=ef(e.dom,t,[{inline:"span"}]);t.setStart(n.startContainer,n.startOffset),t.setEnd(n.endContainer,n.endOffset),e.selection.setRng(t)})(e,s),r.getRng().collapsed&&!a){const s=rf(e.getDoc(),i,o,t,n.decorate);ko(s,Vo),r.getRng().insertNode(s.dom),r.select(s.dom)}else bm(r,!1,(()=>{hm(e,(r=>{sf(e,r,i,t,n.decorate,o)}))}))}))})(e,n,t,o)}))},annotationChanged:(e,t)=>{n.addListener(e,t)},remove:t=>{Ra(e,I.some(t)).each((({elements:t})=>{const n=e.selection.getBookmark();r(t),e.selection.moveToBookmark(n)}))},removeAll:t=>{const n=e.selection.getBookmark();pe(Oa(e,t),((e,t)=>{r(e)})),e.selection.moveToBookmark(n)},getAll:t=>{const n=Oa(e,t);return he(n,(e=>q(e,(e=>e.dom))))}}},lf=e=>({getBookmark:T(em,e),moveToBookmark:T(tm,e)});lf.isBookmarkNode=nm;const df=(e,t,n)=>!n.collapsed&&$(n.getClientRects(),(n=>((e,t,n)=>t>=e.left&&t<=e.right&&n>=e.top&&n<=e.bottom)(n,e,t))),cf=(e,t,n)=>{e.dispatch(t,n)},uf=(e,t,n,o)=>{e.dispatch("FormatApply",{format:t,node:n,vars:o})},mf=(e,t,n,o)=>{e.dispatch("FormatRemove",{format:t,node:n,vars:o})},ff=(e,t)=>e.dispatch("SetContent",t),gf=(e,t)=>e.dispatch("GetContent",t),pf=(e,t)=>{e.dispatch("AutocompleterUpdateActiveRange",t)},hf=(e,t)=>e.dispatch("PastePlainTextToggle",{state:t}),bf={BACKSPACE:8,DELETE:46,DOWN:40,ENTER:13,ESC:27,LEFT:37,RIGHT:39,SPACEBAR:32,TAB:9,UP:38,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,modifierPressed:e=>e.shiftKey||e.ctrlKey||e.altKey||bf.metaKeyPressed(e),metaKeyPressed:e=>Tt.os.isMacOS()||Tt.os.isiOS()?e.metaKey:e.ctrlKey&&!e.altKey},vf="data-mce-selected",yf=Math.abs,Cf=Math.round,wf={nw:[0,0,-1,-1],ne:[1,0,1,-1],se:[1,1,1,1],sw:[0,1,-1,1]},Ef=(e,t)=>{const n=t.dom,o=t.getDoc(),r=document,s=t.getBody();let a,i,l,d,c,u,m,f,g,p,h,b,v,y,w;const E=e=>C(e)&&(pr(e)||n.is(e,"figure.image")),x=e=>Cr(e)||n.hasClass(e,"mce-preview-object"),_=e=>{const n=e.target;((e,t)=>{if((e=>"longpress"===e.type||0===e.type.indexOf("touch"))(e)){const n=e.touches[0];return E(e.target)&&!df(n.clientX,n.clientY,t)}return E(e.target)&&!df(e.clientX,e.clientY,t)})(e,t.selection.getRng())&&!e.isDefaultPrevented()&&t.selection.select(n)},S=e=>n.hasClass(e,"mce-preview-object")&&C(e.firstElementChild)?[e,e.firstElementChild]:n.is(e,"figure.image")?[e.querySelector("img")]:[e],k=e=>{const o=bd(t);return!!o&&"false"!==e.getAttribute("data-mce-resize")&&e!==t.getBody()&&(n.hasClass(e,"mce-preview-object")&&C(e.firstElementChild)?En(yn(e.firstElementChild),o):En(yn(e),o))},N=(e,o,r)=>{if(C(r)){const s=S(e);V(s,(e=>{e.style[o]||!t.schema.isValid(e.nodeName.toLowerCase(),o)?n.setStyle(e,o,r):n.setAttrib(e,o,""+r)}))}},R=(e,t,n)=>{N(e,"width",t),N(e,"height",n)},A=e=>{let o,r,c,C,_;o=e.screenX-u,r=e.screenY-m,b=o*d[2]+f,v=r*d[3]+g,b=b<5?5:b,v=v<5?5:v,c=(E(a)||x(a))&&!1!==vd(t)?!bf.modifierPressed(e):bf.modifierPressed(e),c&&(yf(o)>yf(r)?(v=Cf(b*p),b=Cf(v/p)):(b=Cf(v/p),v=Cf(b*p))),R(i,b,v),C=d.startPos.x+o,_=d.startPos.y+r,C=C>0?C:0,_=_>0?_:0,n.setStyles(l,{left:C,top:_,display:"block"}),l.innerHTML=b+" &times; "+v,d[2]<0&&i.clientWidth<=b&&n.setStyle(i,"left",void 0+(f-b)),d[3]<0&&i.clientHeight<=v&&n.setStyle(i,"top",void 0+(g-v)),o=s.scrollWidth-y,r=s.scrollHeight-w,o+r!==0&&n.setStyles(l,{left:C-o,top:_-r}),h||(((e,t,n,o,r)=>{e.dispatch("ObjectResizeStart",{target:t,width:n,height:o,origin:r})})(t,a,f,g,"corner-"+d.name),h=!0)},T=()=>{const e=h;h=!1,e&&(N(a,"width",b),N(a,"height",v)),n.unbind(o,"mousemove",A),n.unbind(o,"mouseup",T),r!==o&&(n.unbind(r,"mousemove",A),n.unbind(r,"mouseup",T)),n.remove(i),n.remove(l),n.remove(c),O(a),e&&(((e,t,n,o,r)=>{e.dispatch("ObjectResized",{target:t,width:n,height:o,origin:r})})(t,a,b,v,"corner-"+d.name),n.setAttrib(a,"style",n.getAttrib(a,"style"))),t.nodeChanged()},O=e=>{M();const h=n.getPos(e,s),C=h.x,E=h.y,_=e.getBoundingClientRect(),N=_.width||_.right-_.left,O=_.height||_.bottom-_.top;a!==e&&(P(),a=e,b=v=0);const B=t.dispatch("ObjectSelected",{target:e});k(e)&&!B.isDefaultPrevented()?pe(wf,((e,t)=>{let h=n.get("mceResizeHandle"+t);h&&n.remove(h),h=n.add(s,"div",{id:"mceResizeHandle"+t,"data-mce-bogus":"all",class:"mce-resizehandle",unselectable:!0,style:"cursor:"+t+"-resize; margin:0; padding:0"}),n.bind(h,"mousedown",(h=>{h.stopImmediatePropagation(),h.preventDefault(),(h=>{const b=S(a)[0];u=h.screenX,m=h.screenY,f=b.clientWidth,g=b.clientHeight,p=g/f,d=e,d.name=t,d.startPos={x:N*e[0]+C,y:O*e[1]+E},y=s.scrollWidth,w=s.scrollHeight,c=n.add(s,"div",{class:"mce-resize-backdrop","data-mce-bogus":"all"}),n.setStyles(c,{position:"fixed",left:"0",top:"0",width:"100%",height:"100%"}),i=((e,t)=>{if(x(t))return e.create("img",{src:Tt.transparentSrc});if(sr(t)){const n=t.cloneNode(!0);return de(e.select("tr",n)).each((t=>{const n=e.select("td,th",t);e.setStyle(t,"height",null),V(n,(t=>e.setStyle(t,"height",null)))})),n}return t.cloneNode(!0)})(n,a),n.addClass(i,"mce-clonedresizable"),n.setAttrib(i,"data-mce-bogus","all"),i.contentEditable="false",n.setStyles(i,{left:C,top:E,margin:0}),R(i,N,O),i.removeAttribute(vf),s.appendChild(i),n.bind(o,"mousemove",A),n.bind(o,"mouseup",T),r!==o&&(n.bind(r,"mousemove",A),n.bind(r,"mouseup",T)),l=n.add(s,"div",{class:"mce-resize-helper","data-mce-bogus":"all"},f+" &times; "+g)})(h)})),e.elm=h,n.setStyles(h,{left:N*e[0]+C-h.offsetWidth/2,top:O*e[1]+E-h.offsetHeight/2})})):P(!1)},B=ya(O,0),P=(e=!0)=>{B.cancel(),M(),a&&e&&a.removeAttribute(vf),pe(wf,((e,t)=>{const o=n.get("mceResizeHandle"+t);o&&(n.unbind(o),n.remove(o))}))},D=(e,t)=>n.isChildOf(e,t),L=o=>{if(h||t.removed||t.composing)return;const r="mousedown"===o.type?o.target:e.getNode(),a=no(yn(r),"table,img,figure.image,hr,video,span.mce-preview-object,details").map((e=>e.dom)).filter((e=>n.isEditable(e.parentElement)||"IMG"===e.nodeName&&n.isEditable(e))).getOrUndefined(),i=C(a)?n.getAttrib(a,vf,"1"):"1";if(V(n.select(`img[${vf}],hr[${vf}]`),(e=>{e.removeAttribute(vf)})),C(a)&&D(a,s)&&t.hasFocus()){I();const t=e.getStart(!0);if(D(t,a)&&D(e.getEnd(!0),a))return n.setAttrib(a,vf,i),void B.throttle(a)}P()},M=()=>{pe(wf,(e=>{e.elm&&(n.unbind(e.elm),delete e.elm)}))},I=()=>{try{t.getDoc().execCommand("enableObjectResizing",!1,"false")}catch(e){}};return t.on("init",(()=>{I(),t.on("NodeChange ResizeEditor ResizeWindow ResizeContent drop",L),t.on("keyup compositionend",(e=>{a&&"TABLE"===a.nodeName&&L(e)})),t.on("hide blur",P),t.on("contextmenu longpress",_,!0)})),t.on("remove",M),{isResizable:k,showResizeRect:O,hideResizeRect:P,updateResizeRect:L,destroy:()=>{B.cancel(),a=i=c=null}}},xf=(e,t,n)=>{const o=e.document.createRange();var r;return r=o,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,n)=>{e.setEnd(t.dom,n)}),(t=>{e.setEndAfter(t.dom)}))})(o,n),o},_f=(e,t,n,o,r)=>{const s=e.document.createRange();return s.setStart(t.dom,n),s.setEnd(o.dom,r),s},Sf=El([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),kf=(e,t,n)=>t(yn(n.startContainer),n.startOffset,yn(n.endContainer),n.endOffset);Sf.ltr,Sf.rtl;const Nf=(e,t,n,o)=>({start:e,soffset:t,finish:n,foffset:o}),Rf=document.caretPositionFromPoint?(e,t,n)=>{var o,r;return I.from(null===(r=(o=e.dom).caretPositionFromPoint)||void 0===r?void 0:r.call(o,t,n)).bind((t=>{if(null===t.offsetNode)return I.none();const n=e.dom.createRange();return n.setStart(t.offsetNode,t.offset),n.collapse(),I.some(n)}))}:document.caretRangeFromPoint?(e,t,n)=>{var o,r;return I.from(null===(r=(o=e.dom).caretRangeFromPoint)||void 0===r?void 0:r.call(o,t,n))}:I.none,Af=El([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Tf={before:Af.before,on:Af.on,after:Af.after,cata:(e,t,n,o)=>e.fold(t,n,o),getStart:e=>e.fold(R,R,R)},Of=El([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Bf={domRange:Of.domRange,relative:Of.relative,exact:Of.exact,exactFromRange:e=>Of.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>yn(e.startContainer),relative:(e,t)=>Tf.getStart(e),exact:(e,t,n,o)=>e}))(e);return Rn(t)},range:Nf},Pf=(e,t)=>{const n=Ht(e);return"input"===n?Tf.after(e):H(["br","img"],n)?0===t?Tf.before(e):Tf.after(e):Tf.on(e,t)},Df=(e,t)=>{const n=e.fold(Tf.before,Pf,Tf.after),o=t.fold(Tf.before,Pf,Tf.after);return Bf.relative(n,o)},Lf=(e,t,n,o)=>{const r=Pf(e,t),s=Pf(n,o);return Bf.relative(r,s)},Mf=(e,t)=>{const n=(t||document).createDocumentFragment();return V(e,(e=>{n.appendChild(e.dom)})),yn(n)},If=e=>{const t=Bf.getWin(e).dom,n=(e,n,o,r)=>_f(t,e,n,o,r),o=(e=>e.match({domRange:e=>{const t=yn(e.startContainer),n=yn(e.endContainer);return Lf(t,e.startOffset,n,e.endOffset)},relative:Df,exact:Lf}))(e);return((e,t)=>{const n=((e,t)=>t.match({domRange:e=>({ltr:N(e),rtl:I.none}),relative:(t,n)=>({ltr:Le((()=>xf(e,t,n))),rtl:Le((()=>I.some(xf(e,n,t))))}),exact:(t,n,o,r)=>({ltr:Le((()=>_f(e,t,n,o,r))),rtl:Le((()=>I.some(_f(e,o,r,t,n))))})}))(e,t);return((e,t)=>{const n=t.ltr();return n.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Sf.rtl(yn(e.endContainer),e.endOffset,yn(e.startContainer),e.startOffset))).getOrThunk((()=>kf(0,Sf.ltr,n))):kf(0,Sf.ltr,n)})(0,n)})(t,o).match({ltr:n,rtl:n})},Ff=(e,t,n)=>((e,t,n)=>((e,t,n)=>{const o=yn(e.document);return Rf(o,t,n).map((e=>Nf(yn(e.startContainer),e.startOffset,yn(e.endContainer),e.endOffset)))})(e,t,n))(Rn(yn(n)).dom,e,t).map((e=>{const t=n.createRange();return t.setStart(e.start.dom,e.soffset),t.setEnd(e.finish.dom,e.foffset),t})).getOrUndefined(),Uf=(e,t)=>C(e)&&C(t)&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset,zf=(e,t,n)=>null!==((e,t,n)=>{let o=e;for(;o&&o!==t;){if(n(o))return o;o=o.parentNode}return null})(e,t,n),jf=(e,t,n)=>zf(e,t,(e=>e.nodeName===n)),Hf=(e,t)=>ri(e)&&!zf(e,t,$u),$f=(e,t,n)=>{const o=t.parentNode;if(o){const r=new $o(t,e.getParent(o,e.isBlock)||e.getRoot());let s;for(;s=r[n?"prev":"next"]();)if(gr(s))return!0}return!1},qf=(e,t,n,o,r)=>{const s=e.getRoot(),a=e.schema.getNonEmptyElements(),i=r.parentNode;let l,d;if(!i)return I.none();const c=e.getParent(i,e.isBlock)||s;if(o&&gr(r)&&t&&e.isEmpty(c))return I.some(Zi(i,e.nodeIndex(r)));const u=new $o(r,c);for(;d=u[o?"prev":"next"]();){if("false"===e.getContentEditableParent(d)||Hf(d,s))return I.none();if(lr(d)&&d.data.length>0)return jf(d,s,"A")?I.none():I.some(Zi(d,o?d.data.length:0));if(e.isBlock(d)||a[d.nodeName.toLowerCase()])return I.none();l=d}return ur(l)?I.none():n&&l?I.some(Zi(l,0)):I.none()},Vf=(e,t,n,o)=>{const r=e.getRoot();let s,a=!1,i=n?o.startContainer:o.endContainer,l=n?o.startOffset:o.endOffset;const d=Jo(i)&&l===i.childNodes.length,c=e.schema.getNonEmptyElements();let u=n;if(ri(i))return I.none();if(Jo(i)&&l>i.childNodes.length-1&&(u=!1),mr(i)&&(i=r,l=0),i===r){if(u&&(s=i.childNodes[l>0?l-1:0],s)){if(ri(s))return I.none();if(c[s.nodeName]||sr(s))return I.none()}if(i.hasChildNodes()){if(l=Math.min(!u&&l>0?l-1:l,i.childNodes.length-1),i=i.childNodes[l],l=lr(i)&&d?i.data.length:0,!t&&i===r.lastChild&&sr(i))return I.none();if(((e,t)=>{let n=t;for(;n&&n!==e;){if(br(n))return!0;n=n.parentNode}return!1})(r,i)||ri(i))return I.none();if(Er(i))return I.none();if(i.hasChildNodes()&&!sr(i)){s=i;const t=new $o(i,r);do{if(br(s)||ri(s)){a=!1;break}if(lr(s)&&s.data.length>0){l=u?0:s.data.length,i=s,a=!0;break}if(c[s.nodeName.toLowerCase()]&&!yr(s)){l=e.nodeIndex(s),i=s.parentNode,u||l++,a=!0;break}}while(s=u?t.next():t.prev())}}}return t&&(lr(i)&&0===l&&qf(e,d,t,!0,i).each((e=>{i=e.container(),l=e.offset(),a=!0})),Jo(i)&&(s=i.childNodes[l],s||(s=i.childNodes[l-1]),!s||!gr(s)||((e,t)=>{var n;return"A"===(null===(n=e.previousSibling)||void 0===n?void 0:n.nodeName)})(s)||$f(e,s,!1)||$f(e,s,!0)||qf(e,d,t,!0,s).each((e=>{i=e.container(),l=e.offset(),a=!0})))),u&&!t&&lr(i)&&l===i.data.length&&qf(e,d,t,!1,i).each((e=>{i=e.container(),l=e.offset(),a=!0})),a&&i?I.some(Zi(i,l)):I.none()},Wf=(e,t)=>{const n=t.collapsed,o=t.cloneRange(),r=Zi.fromRangeStart(t);return Vf(e,n,!0,o).each((e=>{n&&Zi.isAbove(r,e)||o.setStart(e.container(),e.offset())})),n||Vf(e,n,!1,o).each((e=>{o.setEnd(e.container(),e.offset())})),n&&o.collapse(!0),Uf(t,o)?I.none():I.some(o)},Kf=(e,t)=>e.splitText(t),Yf=e=>{let t=e.startContainer,n=e.startOffset,o=e.endContainer,r=e.endOffset;if(t===o&&lr(t)){if(n>0&&n<t.data.length)if(o=Kf(t,n),t=o.previousSibling,r>n){r-=n;const e=Kf(o,r).previousSibling;t=o=e,r=e.data.length,n=0}else r=0}else if(lr(t)&&n>0&&n<t.data.length&&(t=Kf(t,n),n=0),lr(o)&&r>0&&r<o.data.length){const e=Kf(o,r).previousSibling;o=e,r=e.data.length}return{startContainer:t,startOffset:n,endContainer:o,endOffset:r}},Gf=e=>({walk:(t,n)=>tf(e,t,n),split:Yf,expand:(t,n={type:"word"})=>{if("word"===n.type){const n=ef(e,t,[{inline:"span"}]),o=e.createRng();return o.setStart(n.startContainer,n.startOffset),o.setEnd(n.endContainer,n.endOffset),o}return t},normalize:t=>Wf(e,t).fold(L,(e=>(t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),!0)))});Gf.compareRanges=Uf,Gf.getCaretRangeFromPoint=Ff,Gf.getSelectedNode=yi,Gf.getNode=Ci;const Xf=((e,t)=>{const n=t=>{const n=(e=>{const t=e.dom;return Gn(e)?t.getBoundingClientRect().height:t.offsetHeight})(t);if(n<=0||null===n){const n=co(t,e);return parseFloat(n)||0}return n},o=(e,t)=>X(t,((t,n)=>{const o=co(e,n),r=void 0===o?0:parseInt(o,10);return isNaN(r)?t:t+r}),0);return{set:(t,n)=>{if(!E(n)&&!n.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+n);const o=t.dom;so(o)&&(o.style[e]=n+"px")},get:n,getOuter:n,aggregate:o,max:(e,t,n)=>{const r=o(e,n);return t>r?t-r:0}}})("height"),Zf=()=>yn(document),Qf=(e,t)=>e.view(t).fold(N([]),(t=>{const n=e.owner(t),o=Qf(e,n);return[t].concat(o)}));var Jf=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?I.none():I.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(yn)},owner:e=>Nn(e)});const eg=e=>"textarea"===Ht(e),tg=(e,t)=>{const n=(e=>{const t=e.dom.ownerDocument,n=t.body,o=t.defaultView,r=t.documentElement;if(n===e.dom)return To(n.offsetLeft,n.offsetTop);const s=Oo(null==o?void 0:o.pageYOffset,r.scrollTop),a=Oo(null==o?void 0:o.pageXOffset,r.scrollLeft),i=Oo(r.clientTop,n.clientTop),l=Oo(r.clientLeft,n.clientLeft);return Bo(e).translate(a-l,s-i)})(e),o=(e=>Xf.get(e))(e);return{element:e,bottom:n.top+o,height:o,pos:n,cleanup:t}},ng=(e,t,n,o)=>{ag(e,((r,s)=>rg(e,t,n,o)),n)},og=(e,t,n,o,r)=>{const s={elm:o.element.dom,alignToTop:r};((e,t)=>e.dispatch("ScrollIntoView",t).isDefaultPrevented())(e,s)||(n(e,t,Po(t).top,o,r),((e,t)=>{e.dispatch("AfterScrollIntoView",t)})(e,s))},rg=(e,t,n,o)=>{const r=yn(e.getBody()),s=yn(e.getDoc());r.dom.offsetWidth;const a=((e,t)=>{const n=((e,t)=>{const n=Mn(e);if(0===n.length||eg(e))return{element:e,offset:t};if(t<n.length&&!eg(n[t]))return{element:n[t],offset:0};{const o=n[n.length-1];return eg(o)?{element:e,offset:t}:"img"===Ht(o)?{element:o,offset:1}:Kt(o)?{element:o,offset:za(o).length}:{element:o,offset:Mn(o).length}}})(e,t),o=hn('<span data-mce-bogus="all" style="display: inline-block;">\ufeff</span>');return po(n.element,o),tg(o,(()=>Eo(o)))})(yn(n.startContainer),n.startOffset);og(e,s,t,a,o),a.cleanup()},sg=(e,t,n,o)=>{const r=yn(e.getDoc());og(e,r,n,(e=>tg(yn(e),_))(t),o)},ag=(e,t,n)=>{const o=n.startContainer,r=n.startOffset,s=n.endContainer,a=n.endOffset;t(yn(o),yn(s));const i=e.dom.createRng();i.setStart(o,r),i.setEnd(s,a),e.selection.setRng(n)},ig=(e,t,n,o,r)=>{const s=t.pos;if(o)Do(s.left,s.top,r);else{const o=s.top-n+t.height;Do(-e.getBody().getBoundingClientRect().left,o,r)}},lg=(e,t,n,o,r,s)=>{const a=o+n,i=r.pos.top,l=r.bottom,d=l-i>=o;i<n?ig(e,r,o,!1!==s,t):i>a?ig(e,r,o,d?!1!==s:!0===s,t):l>a&&!d&&ig(e,r,o,!0===s,t)},dg=(e,t,n,o,r)=>{const s=Rn(t).dom.innerHeight;lg(e,t,n,s,o,r)},cg=(e,t,n,o,r)=>{const s=Rn(t).dom.innerHeight;lg(e,t,n,s,o,r);const a=(e=>{const t=Zf(),n=Po(t),o=((e,t)=>{const n=t.owner(e);return Qf(t,n)})(e,Jf),r=Bo(e),s=G(o,((e,t)=>{const n=Bo(t);return{left:e.left+n.left,top:e.top+n.top}}),{left:0,top:0});return To(s.left+r.left+n.left,s.top+r.top+n.top)})(o.element),i=Io(window);a.top<i.y?Lo(o.element,!1!==r):a.top>i.bottom&&Lo(o.element,!0===r)},ug=(e,t,n)=>ng(e,dg,t,n),mg=(e,t,n)=>sg(e,t,dg,n),fg=(e,t,n)=>ng(e,cg,t,n),gg=(e,t,n)=>sg(e,t,cg,n),pg=(e,t,n)=>{(e.inline?ug:fg)(e,t,n)},hg=(e,t=!1)=>e.dom.focus({preventScroll:t}),bg=e=>{const t=qn(e).dom;return e.dom===t.activeElement},vg=(e=Zf())=>I.from(e.dom.activeElement).map(yn),yg=(e,t)=>{const n=Kt(t)?za(t).length:Mn(t).length+1;return e>n?n:e<0?0:e},Cg=e=>Bf.range(e.start,yg(e.soffset,e.start),e.finish,yg(e.foffset,e.finish)),wg=(e,t)=>!Qo(t.dom)&&(Sn(e,t)||_n(e,t)),Eg=e=>t=>wg(e,t.start)&&wg(e,t.finish),xg=e=>Bf.range(yn(e.startContainer),e.startOffset,yn(e.endContainer),e.endOffset),_g=e=>{const t=document.createRange();try{return t.setStart(e.start.dom,e.soffset),t.setEnd(e.finish.dom,e.foffset),I.some(t)}catch(e){return I.none()}},Sg=e=>{const t=(e=>e.inline||Tt.browser.isFirefox())(e)?(n=yn(e.getBody()),(e=>{const t=e.getSelection();return(t&&0!==t.rangeCount?I.from(t.getRangeAt(0)):I.none()).map(xg)})(Rn(n).dom).filter(Eg(n))):I.none();var n;e.bookmark=t.isSome()?t:e.bookmark},kg=e=>(e.bookmark?e.bookmark:I.none()).bind((t=>{return n=yn(e.getBody()),o=t,I.from(o).filter(Eg(n)).map(Cg);var n,o})).bind(_g),Ng={isEditorUIElement:e=>{const t=e.className.toString();return-1!==t.indexOf("tox-")||-1!==t.indexOf("mce-")}},Rg={setEditorTimeout:(e,t,n)=>((e,t)=>(E(t)||(t=0),setTimeout(e,t)))((()=>{e.removed||t()}),n),setEditorInterval:(e,t,n)=>{const o=((e,t)=>(E(t)||(t=0),setInterval(e,t)))((()=>{e.removed?clearInterval(o):t()}),n);return o}};let Ag;const Tg=la.DOM,Og=e=>{const t=e.classList;return void 0!==t&&(t.contains("tox-edit-area")||t.contains("tox-edit-area__iframe")||t.contains("mce-content-body"))},Bg=(e,t)=>{const n=Od(e),o=Tg.getParent(t,(t=>(e=>Jo(e)&&Ng.isEditorUIElement(e))(t)||!!n&&e.dom.is(t,n)));return null!==o},Pg=e=>{try{const t=qn(yn(e.getElement()));return vg(t).fold((()=>document.body),(e=>e.dom))}catch(e){return document.body}},Dg=(e,t)=>{const n=t.editor;(e=>{const t=ya((()=>{Sg(e)}),0);e.on("init",(()=>{e.inline&&((e,t)=>{const n=()=>{t.throttle()};la.DOM.bind(document,"mouseup",n),e.on("remove",(()=>{la.DOM.unbind(document,"mouseup",n)}))})(e,t),((e,t)=>{((e,t)=>{e.on("mouseup touchend",(e=>{t.throttle()}))})(e,t),e.on("keyup NodeChange AfterSetSelectionRange",(t=>{(e=>"nodechange"===e.type&&e.selectionChange)(t)||Sg(e)}))})(e,t)})),e.on("remove",(()=>{t.cancel()}))})(n);const o=(e,t)=>{yc(e)&&!0!==e.inline&&t(yn(e.getContainer()),"tox-edit-focus")};n.on("focusin",(()=>{const t=e.focusedEditor;Og(Pg(n))&&o(n,un),t!==n&&(t&&t.dispatch("blur",{focusedEditor:n}),e.setActive(n),e.focusedEditor=n,n.dispatch("focus",{blurredEditor:t}),n.focus(!0))})),n.on("focusout",(()=>{Rg.setEditorTimeout(n,(()=>{const t=e.focusedEditor;Og(Pg(n))&&t===n||o(n,fn),Bg(n,Pg(n))||t!==n||(n.dispatch("blur",{focusedEditor:null}),e.focusedEditor=null)}))})),Ag||(Ag=t=>{const n=e.activeEditor;n&&Kn(t).each((t=>{const o=t;o.ownerDocument===document&&(o===document.body||Bg(n,o)||e.focusedEditor!==n||(n.dispatch("blur",{focusedEditor:null}),e.focusedEditor=null))}))},Tg.bind(document,"focusin",Ag))},Lg=(e,t)=>{e.focusedEditor===t.editor&&(e.focusedEditor=null),!e.activeEditor&&Ag&&(Tg.unbind(document,"focusin",Ag),Ag=null)},Mg=(e,t)=>{((e,t)=>(e=>e.collapsed?I.from(Ci(e.startContainer,e.startOffset)).map(yn):I.none())(t).bind((t=>Wa(t)?I.some(t):Sn(e,t)?I.none():I.some(e))))(yn(e.getBody()),t).bind((e=>zu(e.dom))).fold((()=>{e.selection.normalize()}),(t=>e.selection.setRng(t.toRange())))},Ig=e=>{if(e.setActive)try{e.setActive()}catch(t){e.focus()}else e.focus()},Fg=e=>e.inline?(e=>{const t=e.getBody();return t&&(n=yn(t),bg(n)||(o=n,vg(qn(o)).filter((e=>o.dom.contains(e.dom)))).isSome());var n,o})(e):(e=>C(e.iframeElement)&&bg(yn(e.iframeElement)))(e),Ug=e=>Fg(e)||(e=>{const t=qn(yn(e.getElement()));return vg(t).filter((t=>!Og(t.dom)&&Bg(e,t.dom))).isSome()})(e),zg=e=>e.editorManager.setActive(e),jg=(e,t)=>t.collapsed?e.isEditable(t.startContainer):e.isEditable(t.startContainer)&&e.isEditable(t.endContainer),Hg=(e,t,n,o,r)=>{const s=n?t.startContainer:t.endContainer,a=n?t.startOffset:t.endOffset;return I.from(s).map(yn).map((e=>o&&t.collapsed?e:In(e,r(e,a)).getOr(e))).bind((e=>Wt(e)?I.some(e):An(e).filter(Wt))).map((e=>e.dom)).getOr(e)},$g=(e,t,n=!1)=>Hg(e,t,!0,n,((e,t)=>Math.min(zn(e),t))),qg=(e,t,n=!1)=>Hg(e,t,!1,n,((e,t)=>t>0?t-1:t)),Vg=(e,t)=>{const n=e;for(;e&&lr(e)&&0===e.length;)e=t?e.nextSibling:e.previousSibling;return e||n},Wg=(e,t)=>q(t,(t=>{const n=e.dispatch("GetSelectionRange",{range:t});return n.range!==t?n.range:t})),Kg={"#text":3,"#comment":8,"#cdata":4,"#pi":7,"#doctype":10,"#document-fragment":11},Yg=(e,t,n)=>{const o=n?"lastChild":"firstChild",r=n?"prev":"next";if(e[o])return e[o];if(e!==t){let n=e[r];if(n)return n;for(let o=e.parent;o&&o!==t;o=o.parent)if(n=o[r],n)return n}},Gg=e=>{var t;const n=null!==(t=e.value)&&void 0!==t?t:"";if(!Yo(n))return!1;const o=e.parent;return!o||"span"===o.name&&!o.attr("style")||!/^[ ]+$/.test(n)},Xg=e=>{const t="a"===e.name&&!e.attr("href")&&e.attr("id");return e.attr("name")||e.attr("id")&&!e.firstChild||e.attr("data-mce-bookmark")||t};class Zg{static create(e,t){const n=new Zg(e,Kg[e]||1);return t&&pe(t,((e,t)=>{n.attr(t,e)})),n}constructor(e,t){this.name=e,this.type=t,1===t&&(this.attributes=[],this.attributes.map={})}replace(e){const t=this;return e.parent&&e.remove(),t.insert(e,t),t.remove(),t}attr(e,t){const n=this;if(!m(e))return C(e)&&pe(e,((e,t)=>{n.attr(t,e)})),n;const o=n.attributes;if(o){if(void 0!==t){if(null===t){if(e in o.map){delete o.map[e];let t=o.length;for(;t--;)if(o[t].name===e)return o.splice(t,1),n}return n}if(e in o.map){let n=o.length;for(;n--;)if(o[n].name===e){o[n].value=t;break}}else o.push({name:e,value:t});return o.map[e]=t,n}return o.map[e]}}clone(){const e=this,t=new Zg(e.name,e.type),n=e.attributes;if(n){const e=[];e.map={};for(let t=0,o=n.length;t<o;t++){const o=n[t];"id"!==o.name&&(e[e.length]={name:o.name,value:o.value},e.map[o.name]=o.value)}t.attributes=e}return t.value=e.value,t}wrap(e){const t=this;return t.parent&&(t.parent.insert(e,t),e.append(t)),t}unwrap(){const e=this;for(let t=e.firstChild;t;){const n=t.next;e.insert(t,e,!0),t=n}e.remove()}remove(){const e=this,t=e.parent,n=e.next,o=e.prev;return t&&(t.firstChild===e?(t.firstChild=n,n&&(n.prev=null)):o&&(o.next=n),t.lastChild===e?(t.lastChild=o,o&&(o.next=null)):n&&(n.prev=o),e.parent=e.next=e.prev=null),e}append(e){const t=this;e.parent&&e.remove();const n=t.lastChild;return n?(n.next=e,e.prev=n,t.lastChild=e):t.lastChild=t.firstChild=e,e.parent=t,e}insert(e,t,n){e.parent&&e.remove();const o=t.parent||this;return n?(t===o.firstChild?o.firstChild=e:t.prev&&(t.prev.next=e),e.prev=t.prev,e.next=t,t.prev=e):(t===o.lastChild?o.lastChild=e:t.next&&(t.next.prev=e),e.next=t.next,e.prev=t,t.next=e),e.parent=o,e}getAll(e){const t=this,n=[];for(let o=t.firstChild;o;o=Yg(o,t))o.name===e&&n.push(o);return n}children(){const e=[];for(let t=this.firstChild;t;t=t.next)e.push(t);return e}empty(){const e=this;if(e.firstChild){const t=[];for(let n=e.firstChild;n;n=Yg(n,e))t.push(n);let n=t.length;for(;n--;){const e=t[n];e.parent=e.firstChild=e.lastChild=e.next=e.prev=null}}return e.firstChild=e.lastChild=null,e}isEmpty(e,t={},n){var o;const r=this;let s=r.firstChild;if(Xg(r))return!1;if(s)do{if(1===s.type){if(s.attr("data-mce-bogus"))continue;if(e[s.name])return!1;if(Xg(s))return!1}if(8===s.type)return!1;if(3===s.type&&!Gg(s))return!1;if(3===s.type&&s.parent&&t[s.parent.name]&&Yo(null!==(o=s.value)&&void 0!==o?o:""))return!1;if(n&&n(s))return!1}while(s=Yg(s,r));return!0}walk(e){return Yg(this,null,e)}}const Qg=Pt.makeMap("NOSCRIPT STYLE SCRIPT XMP IFRAME NOEMBED NOFRAMES PLAINTEXT"," "),Jg=e=>m(e.nodeValue)&&e.nodeValue.includes(Za),ep=e=>(0===e.length?"":`${q(e,(e=>`[${e}]`)).join(",")},`)+'[data-mce-bogus="all"]',tp=e=>document.createTreeWalker(e,NodeFilter.SHOW_COMMENT,(e=>Jg(e)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP)),np=e=>document.createTreeWalker(e,NodeFilter.SHOW_TEXT,(e=>{if(Jg(e)){const t=e.parentNode;return t&&_e(Qg,t.nodeName)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}return NodeFilter.FILTER_SKIP})),op=e=>null!==tp(e).nextNode(),rp=e=>null!==np(e).nextNode(),sp=(e,t)=>null!==t.querySelector(ep(e)),ap=(e,t)=>{V(((e,t)=>t.querySelectorAll(ep(e)))(e,t),(t=>{const n=yn(t);"all"===en(n,"data-mce-bogus")?Eo(n):V(e,(e=>{nn(n,e)&&on(n,e)}))}))},ip=e=>{let t=e.nextNode();for(;null!==t;)t.nodeValue=null,t=e.nextNode()},lp=S(ip,tp),dp=S(ip,np),cp=(e,t)=>{const n=[{condition:T(sp,t),action:T(ap,t)},{condition:op,action:lp},{condition:rp,action:dp}];let o=e,r=!1;return V(n,(({condition:t,action:n})=>{t(o)&&(r||(o=e.cloneNode(!0),r=!0),n(o))})),o},up=e=>{const t=Uo(e,"[data-mce-bogus]");V(t,(e=>{"all"===en(e,"data-mce-bogus")?Eo(e):Ha(e)?(po(e,vn(qo)),Eo(e)):xo(e)}))},mp=e=>{const t=Uo(e,"input");V(t,(e=>{on(e,"name")}))},fp=(e,t,n)=>{let o;return o="raw"===t.format?Pt.trim(Ja(cp(n,e.serializer.getTempAttrs()).innerHTML)):"text"===t.format?((e,t)=>{const n=e.getDoc(),o=qn(yn(e.getBody())),r=bn("div",n);Qt(r,"data-mce-bogus","all"),lo(r,{position:"fixed",left:"-9999999px",top:"0"}),ko(r,t.innerHTML),up(r),mp(r);const s=(e=>jn(e)?e:yn(Nn(e).dom.body))(o);vo(s,r);const a=Ja(r.dom.innerText);return Eo(r),a})(e,n):"tree"===t.format?e.serializer.serialize(n,t):((e,t)=>{const n=ql(e),o=new RegExp(`^(<${n}[^>]*>(&nbsp;|&#160;|\\s|\xa0|<br \\/>|)<\\/${n}>[\r\n]*|<br \\/>[\r\n]*)$`);return t.replace(o,"")})(e,e.serializer.serialize(n,t)),"text"!==t.format&&!Ya(yn(n))&&m(o)?Pt.trim(o):o},gp=Pt.makeMap,pp=e=>{const t=[],n=(e=e||{}).indent,o=gp(e.indent_before||""),r=gp(e.indent_after||""),s=hs.getEncodeFunc(e.entity_encoding||"raw",e.entities),a="xhtml"!==e.element_format;return{start:(e,i,l)=>{if(n&&o[e]&&t.length>0){const e=t[t.length-1];e.length>0&&"\n"!==e&&t.push("\n")}if(t.push("<",e),i)for(let e=0,n=i.length;e<n;e++){const n=i[e];t.push(" ",n.name,'="',s(n.value,!0),'"')}if(t[t.length]=!l||a?">":" />",l&&n&&r[e]&&t.length>0){const e=t[t.length-1];e.length>0&&"\n"!==e&&t.push("\n")}},end:e=>{let o;t.push("</",e,">"),n&&r[e]&&t.length>0&&(o=t[t.length-1],o.length>0&&"\n"!==o&&t.push("\n"))},text:(e,n)=>{e.length>0&&(t[t.length]=n?e:s(e))},cdata:e=>{t.push("<![CDATA[",e,"]]>")},comment:e=>{t.push("\x3c!--",e,"--\x3e")},pi:(e,o)=>{o?t.push("<?",e," ",s(o),"?>"):t.push("<?",e,"?>"),n&&t.push("\n")},doctype:e=>{t.push("<!DOCTYPE",e,">",n?"\n":"")},reset:()=>{t.length=0},getContent:()=>t.join("").replace(/\n$/,"")}},hp=(e={},t=Ps())=>{const n=pp(e);return e.validate=!("validate"in e)||e.validate,{serialize:o=>{const r=e.validate,s={3:e=>{var t;n.text(null!==(t=e.value)&&void 0!==t?t:"",e.raw)},8:e=>{var t;n.comment(null!==(t=e.value)&&void 0!==t?t:"")},7:e=>{n.pi(e.name,e.value)},10:e=>{var t;n.doctype(null!==(t=e.value)&&void 0!==t?t:"")},4:e=>{var t;n.cdata(null!==(t=e.value)&&void 0!==t?t:"")},11:e=>{let t=e;if(t=t.firstChild)do{a(t)}while(t=t.next)}};n.reset();const a=e=>{var o;const i=s[e.type];if(i)i(e);else{const s=e.name,i=s in t.getVoidElements();let l=e.attributes;if(r&&l&&l.length>1){const n=[];n.map={};const o=t.getElementRule(e.name);if(o){for(let e=0,t=o.attributesOrder.length;e<t;e++){const t=o.attributesOrder[e];if(t in l.map){const e=l.map[t];n.map[t]=e,n.push({name:t,value:e})}}for(let e=0,t=l.length;e<t;e++){const t=l[e].name;if(!(t in n.map)){const e=l.map[t];n.map[t]=e,n.push({name:t,value:e})}}l=n}}if(n.start(s,l,i),Or(s))m(e.value)&&n.text(e.value,!0),n.end(s);else if(!i){let t=e.firstChild;if(t){"pre"!==s&&"textarea"!==s||3!==t.type||"\n"!==(null===(o=t.value)||void 0===o?void 0:o[0])||n.text("\n",!0);do{a(t)}while(t=t.next)}n.end(s)}}};return 1!==o.type||e.inner?3===o.type?s[3](o):s[11](o):a(o),n.getContent()}}},bp=new Set;V(["margin","margin-left","margin-right","margin-top","margin-bottom","padding","padding-left","padding-right","padding-top","padding-bottom","border","border-width","border-style","border-color","background","background-attachment","background-clip","background-color","background-image","background-origin","background-position","background-repeat","background-size","float","position","left","right","top","bottom","z-index","display","transform","width","max-width","min-width","height","max-height","min-height","overflow","overflow-x","overflow-y","text-overflow","vertical-align","transition","transition-delay","transition-duration","transition-property","transition-timing-function"],(e=>{bp.add(e)}));const vp=["font","text-decoration","text-emphasis"],yp=(e,t)=>fe(e.parseStyle(e.getAttrib(t,"style"))),Cp=(e,t,n)=>{const o=yp(e,t),r=yp(e,n),s=o=>{var r,s;const a=null!==(r=e.getStyle(t,o))&&void 0!==r?r:"",i=null!==(s=e.getStyle(n,o))&&void 0!==s?s:"";return Ge(a)&&Ge(i)&&a!==i};return $(o,(e=>{const t=t=>$(t,(t=>t===e));if(!t(r)&&t(vp)){const e=Y(r,(e=>$(vp,(t=>$e(e,t)))));return $(e,s)}return s(e)}))},wp=(e,t,n)=>I.from(n.container()).filter(lr).exists((o=>{const r=e?0:-1;return t(o.data.charAt(n.offset()+r))})),Ep=T(wp,!0,sm),xp=T(wp,!1,sm),_p=e=>{const t=e.container();return lr(t)&&(0===t.data.length||Qa(t.data)&&lf.isBookmarkNode(t.parentNode))},Sp=(e,t)=>n=>iu(e?0:-1,n).filter(t).isSome(),kp=e=>pr(e)&&"block"===co(yn(e),"display"),Np=e=>br(e)&&!(e=>Jo(e)&&"all"===e.getAttribute("data-mce-bogus"))(e),Rp=Sp(!0,kp),Ap=Sp(!1,kp),Tp=Sp(!0,Cr),Op=Sp(!1,Cr),Bp=Sp(!0,sr),Pp=Sp(!1,sr),Dp=Sp(!0,Np),Lp=Sp(!1,Np),Mp=(e,t)=>((e,t,n)=>Sn(t,e)?On(e,(e=>n(e)||_n(e,t))).slice(0,-1):[])(e,t,L),Ip=(e,t)=>[e].concat(Mp(e,t)),Fp=(e,t,n)=>Mu(e,t,n,_p),Up=(e,t,n)=>Q(Ip(yn(t.container()),e),(e=>t=>e.isBlock(Ht(t)))(n)),zp=(e,t,n,o)=>Fp(e,t.dom,n).forall((e=>Up(t,n,o).fold((()=>!au(e,n,t.dom)),(o=>!au(e,n,t.dom)&&Sn(o,yn(e.container())))))),jp=(e,t,n,o)=>Up(t,n,o).fold((()=>Fp(e,t.dom,n).forall((e=>!au(e,n,t.dom)))),(t=>Fp(e,t.dom,n).isNone())),Hp=T(jp,!1),$p=T(jp,!0),qp=T(zp,!1),Vp=T(zp,!0),Wp=e=>hu(e).exists(Ha),Kp=(e,t,n,o)=>{const r=Y(Ip(yn(n.container()),t),(e=>o.isBlock(Ht(e)))),s=le(r).getOr(t);return Du(e,s.dom,n).filter(Wp)},Yp=(e,t,n)=>hu(t).exists(Ha)||Kp(!0,e,t,n).isSome(),Gp=(e,t,n)=>(e=>I.from(e.getNode(!0)).map(yn))(t).exists(Ha)||Kp(!1,e,t,n).isSome(),Xp=T(Kp,!1),Zp=T(Kp,!0),Qp=e=>Zi.isTextPosition(e)&&!e.isAtStart()&&!e.isAtEnd(),Jp=(e,t,n)=>{const o=Y(Ip(yn(t.container()),e),(e=>n.isBlock(Ht(e))));return le(o).getOr(e)},eh=(e,t,n)=>Qp(t)?xp(t):xp(t)||Uu(Jp(e,t,n).dom,t).exists(xp),th=(e,t,n)=>Qp(t)?Ep(t):Ep(t)||Fu(Jp(e,t,n).dom,t).exists(Ep),nh=e=>hu(e).bind((e=>Qn(e,Wt))).exists((e=>(e=>H(["pre","pre-wrap"],e))(co(e,"white-space")))),oh=(e,t)=>n=>{return o=new $o(n,e)[t](),C(o)&&br(o)&&Gc(o);var o},rh=(e,t,n)=>!nh(t)&&(((e,t,n)=>((e,t)=>Uu(e.dom,t).isNone())(e,t)||((e,t)=>Fu(e.dom,t).isNone())(e,t)||Hp(e,t,n)||$p(e,t,n)||Gp(e,t,n)||Yp(e,t,n))(e,t,n)||eh(e,t,n)||th(e,t,n)),sh=(e,t,n)=>!nh(t)&&(Hp(e,t,n)||qp(e,t,n)||Gp(e,t,n)||eh(e,t,n)||((e,t)=>{const n=Uu(e.dom,t).getOr(t),o=oh(e.dom,"prev");return t.isAtStart()&&(o(t.container())||o(n.container()))})(e,t)),ah=(e,t,n)=>!nh(t)&&($p(e,t,n)||Vp(e,t,n)||Yp(e,t,n)||th(e,t,n)||((e,t)=>{const n=Fu(e.dom,t).getOr(t),o=oh(e.dom,"next");return t.isAtEnd()&&(o(t.container())||o(n.container()))})(e,t)),ih=(e,t,n)=>sh(e,t,n)||ah(e,(e=>{const t=e.container(),n=e.offset();return lr(t)&&n<t.data.length?Zi(t,n+1):e})(t),n),lh=(e,t)=>om(e.charAt(t)),dh=(e,t)=>sm(e.charAt(t)),ch=(e,t,n,o)=>{const r=t.data,s=Zi(t,0);return n||!lh(r,0)||ih(e,s,o)?!!(n&&dh(r,0)&&sh(e,s,o))&&(t.data=Vo+r.slice(1),!0):(t.data=" "+r.slice(1),!0)},uh=(e,t,n,o)=>{const r=t.data,s=Zi(t,r.length-1);return n||!lh(r,r.length-1)||ih(e,s,o)?!!(n&&dh(r,r.length-1)&&ah(e,s,o))&&(t.data=r.slice(0,-1)+Vo,!0):(t.data=r.slice(0,-1)+" ",!0)},mh=(e,t,n)=>{const o=t.container();if(!lr(o))return I.none();if((e=>{const t=e.container();return lr(t)&&He(t.data,Vo)})(t)){const r=ch(e,o,!1,n)||(e=>{const t=e.data,n=(e=>{const t=e.split("");return q(t,((e,n)=>om(e)&&n>0&&n<t.length-1&&am(t[n-1])&&am(t[n+1])?" ":e)).join("")})(t);return n!==t&&(e.data=n,!0)})(o)||uh(e,o,!1,n);return It(r,t)}if(ih(e,t,n)){const r=ch(e,o,!0,n)||uh(e,o,!0,n);return It(r,t)}return I.none()},fh=(e,t,n,o)=>{if(0===n)return;const r=yn(e),s=Zn(r,(e=>o.isBlock(Ht(e)))).getOr(r),a=e.data.slice(t,t+n),i=t+n>=e.data.length&&ah(s,Zi(e,e.data.length),o),l=0===t&&sh(s,Zi(e,0),o);e.replaceData(t,n,Xo(a,4,l,i))},gh=(e,t,n)=>{const o=e.data.slice(t),r=o.length-Ke(o).length;fh(e,t,r,n)},ph=(e,t,n)=>{const o=e.data.slice(0,t),r=o.length-Ye(o).length;fh(e,t-r,r,n)},hh=(e,t,n,o,r=!0)=>{const s=Ye(e.data).length,a=r?e:t,i=r?t:e;return r?a.appendData(i.data):a.insertData(0,i.data),Eo(yn(i)),o&&gh(a,s,n),a},bh=(e,t)=>((e,t)=>{const n=e.container(),o=e.offset();return!Zi.isTextPosition(e)&&n===t.parentNode&&o>Zi.before(t).offset()})(t,e)?Zi(t.container(),t.offset()-1):t,vh=e=>{return Oi(e.previousSibling)?I.some((t=e.previousSibling,lr(t)?Zi(t,t.data.length):Zi.after(t))):e.previousSibling?ju(e.previousSibling):I.none();var t},yh=e=>{return Oi(e.nextSibling)?I.some((t=e.nextSibling,lr(t)?Zi(t,0):Zi.before(t))):e.nextSibling?zu(e.nextSibling):I.none();var t},Ch=(e,t,n)=>((e,t,n)=>e?((e,t)=>yh(t).orThunk((()=>vh(t))).orThunk((()=>((e,t)=>Fu(e,Zi.after(t)).orThunk((()=>Uu(e,Zi.before(t)))))(e,t))))(t,n):((e,t)=>vh(t).orThunk((()=>yh(t))).orThunk((()=>((e,t)=>I.from(t.previousSibling?t.previousSibling:t.parentNode).bind((t=>Uu(e,Zi.before(t)))).orThunk((()=>Fu(e,Zi.after(t)))))(e,t))))(t,n))(e,t,n).map(T(bh,n)),wh=(e,t,n)=>{n.fold((()=>{e.focus()}),(n=>{e.selection.setRng(n.toRange(),t)}))},Eh=(e,t)=>t&&_e(e.schema.getBlockElements(),Ht(t)),xh=(e,t,n,o=!0,r=!1)=>{const s=Ch(t,e.getBody(),n.dom),a=Zn(n,T(Eh,e),(i=e.getBody(),e=>e.dom===i));var i;const l=((e,t,n,o)=>{const r=Bn(e).filter(Kt),s=Pn(e).filter(Kt);return Eo(e),(a=r,i=s,l=t,d=(e,t,r)=>{const s=e.dom,a=t.dom,i=s.data.length;return hh(s,a,n,o),r.container()===a?Zi(s,i):r},a.isSome()&&i.isSome()&&l.isSome()?I.some(d(a.getOrDie(),i.getOrDie(),l.getOrDie())):I.none()).orThunk((()=>(o&&(r.each((e=>ph(e.dom,e.dom.length,n))),s.each((e=>gh(e.dom,0,n)))),t)));var a,i,l,d})(n,s,e.schema,((e,t)=>_e(e.schema.getTextInlineElements(),Ht(t)))(e,n));e.dom.isEmpty(e.getBody())?(e.setContent(""),e.selection.setCursorLocation()):a.bind((t=>((e,t,n)=>{if(Ar(e,t)){const e=hn('<br data-mce-bogus="1">');return n?V(Mn(t),(e=>{Um(e)||Eo(e)})):wo(t),vo(t,e),I.some(Zi.before(e.dom))}return I.none()})(e.schema,t,r))).fold((()=>{o&&wh(e,t,l)}),(n=>{o&&wh(e,t,I.some(n))}))},_h=/[\u0591-\u07FF\uFB1D-\uFDFF\uFE70-\uFEFC]/,Sh=(e,t)=>En(yn(t),hd(e))&&!Wr(e.schema,t)&&e.dom.isEditable(t),kh=e=>{var t;return"rtl"===la.DOM.getStyle(e,"direction",!0)||(e=>_h.test(e))(null!==(t=e.textContent)&&void 0!==t?t:"")},Nh=(e,t,n)=>{const o=((e,t,n)=>Y(la.DOM.getParents(n.container(),"*",t),e))(e,t,n);return I.from(o[o.length-1])},Rh=(e,t)=>{const n=t.container(),o=t.offset();return e?oi(n)?lr(n.nextSibling)?Zi(n.nextSibling,0):Zi.after(n):ai(t)?Zi(n,o+1):t:oi(n)?lr(n.previousSibling)?Zi(n.previousSibling,n.previousSibling.data.length):Zi.before(n):ii(t)?Zi(n,o-1):t},Ah=T(Rh,!0),Th=T(Rh,!1),Oh=(e,t)=>{const n=e=>e.stopImmediatePropagation();e.on("beforeinput input",n,!0),e.getDoc().execCommand(t),e.off("beforeinput input",n)},Bh=e=>Oh(e,"Delete"),Ph=e=>$a(e)||Va(e),Dh=(e,t)=>Sn(e,t)?Qn(t,Ph,(e=>t=>Dt(An(t),e,_n))(e)):I.none(),Lh=(e,t=!0)=>{e.dom.isEmpty(e.getBody())&&e.setContent("",{no_selection:!t})},Mh=(e,t,n)=>Mt(zu(n),ju(n),((o,r)=>{const s=Rh(!0,o),a=Rh(!1,r),i=Rh(!1,t);return e?Fu(n,i).exists((e=>e.isEqual(a)&&t.isEqual(s))):Uu(n,i).exists((e=>e.isEqual(s)&&t.isEqual(a)))})).getOr(!0),Ih=e=>{var t;return(8===$t(t=e)||"#comment"===Ht(t)?Bn(e):Un(e)).bind(Ih).orThunk((()=>I.some(e)))},Fh=(e,t,n,o=!0)=>{var r;t.deleteContents();const s=Ih(n).getOr(n),a=yn(null!==(r=e.dom.getParent(s.dom,e.dom.isBlock))&&void 0!==r?r:n.dom);if(a.dom===e.getBody()?Lh(e,o):Ar(e.schema,a,{checkRootAsContent:!1})&&(Xa(a),o&&e.selection.setCursorLocation(a.dom,0)),!_n(n,a)){const t=Dt(An(a),n)?[]:An(i=a).map(Mn).map((e=>Y(e,(e=>!_n(i,e))))).getOr([]);V(t.concat(Mn(n)),(t=>{_n(t,a)||Sn(t,a)||!Ar(e.schema,t)||Eo(t)}))}var i},Uh=e=>Uo(e,"td,th"),zh=(e,t)=>cm(yn(e),t),jh=(e,t)=>({start:e,end:t}),Hh=El([{singleCellTable:["rng","cell"]},{fullTable:["table"]},{partialTable:["cells","outsideDetails"]},{multiTable:["startTableCells","endTableCells","betweenRng"]}]),$h=(e,t)=>no(yn(e),"td,th",t),qh=e=>!_n(e.start,e.end),Vh=(e,t)=>cm(e.start,t).bind((n=>cm(e.end,t).bind((e=>It(_n(n,e),n))))),Wh=e=>t=>Vh(t,e).map((e=>((e,t,n)=>({rng:e,table:t,cells:n}))(t,e,Uh(e)))),Kh=(e,t,n,o)=>{if(n.collapsed||!e.forall(qh))return I.none();if(t.isSameTable){const t=e.bind(Wh(o));return I.some({start:t,end:t})}{const e=$h(n.startContainer,o),t=$h(n.endContainer,o),r=e.bind((e=>t=>cm(t,e).bind((e=>de(Uh(e)).map((e=>jh(t,e))))))(o)).bind(Wh(o)),s=t.bind((e=>t=>cm(t,e).bind((e=>le(Uh(e)).map((e=>jh(e,t))))))(o)).bind(Wh(o));return I.some({start:r,end:s})}},Yh=(e,t)=>J(e,(e=>_n(e,t))),Gh=e=>Mt(Yh(e.cells,e.rng.start),Yh(e.cells,e.rng.end),((t,n)=>e.cells.slice(t,n+1))),Xh=(e,t)=>{const{startTable:n,endTable:o}=t,r=e.cloneRange();return n.each((e=>r.setStartAfter(e.dom))),o.each((e=>r.setEndBefore(e.dom))),r},Zh=(e,t)=>{const n=(e=>t=>_n(e,t))(e),o=((e,t)=>{const n=$h(e.startContainer,t),o=$h(e.endContainer,t);return Mt(n,o,jh)})(t,n),r=((e,t)=>{const n=zh(e.startContainer,t),o=zh(e.endContainer,t),r=n.isSome(),s=o.isSome(),a=Mt(n,o,_n).getOr(!1);return(e=>Mt(e.startTable,e.endTable,((t,n)=>{const o=Ho(t,(e=>_n(e,n))),r=Ho(n,(e=>_n(e,t)));return o||r?{...e,startTable:o?I.none():e.startTable,endTable:r?I.none():e.endTable,isSameTable:!1,isMultiTable:!1}:e})).getOr(e))({startTable:n,endTable:o,isStartInTable:r,isEndInTable:s,isSameTable:a,isMultiTable:!a&&r&&s})})(t,n);return((e,t,n)=>e.exists((e=>((e,t)=>!qh(e)&&Vh(e,t).exists((e=>{const t=e.dom.rows;return 1===t.length&&1===t[0].cells.length})))(e,n)&&fm(e.start,t))))(o,t,n)?o.map((e=>Hh.singleCellTable(t,e.start))):r.isMultiTable?((e,t,n,o)=>Kh(e,t,n,o).bind((({start:e,end:o})=>{const r=e.bind(Gh).getOr([]),s=o.bind(Gh).getOr([]);if(r.length>0&&s.length>0){const e=Xh(n,t);return I.some(Hh.multiTable(r,s,e))}return I.none()})))(o,r,t,n):((e,t,n,o)=>Kh(e,t,n,o).bind((({start:e,end:t})=>e.or(t))).bind((e=>{const{isSameTable:o}=t,r=Gh(e).getOr([]);if(o&&e.cells.length===r.length)return I.some(Hh.fullTable(e.table));if(r.length>0){if(o)return I.some(Hh.partialTable(r,I.none()));{const e=Xh(n,t);return I.some(Hh.partialTable(r,I.some({...t,rng:e})))}}return I.none()})))(o,r,t,n)},Qh=e=>V(e,(e=>{on(e,"contenteditable"),Xa(e)})),Jh=(e,t,n,o)=>{const r=n.cloneRange();o?(r.setStart(n.startContainer,n.startOffset),r.setEndAfter(t.dom.lastChild)):(r.setStartBefore(t.dom.firstChild),r.setEnd(n.endContainer,n.endOffset)),ob(e,r,t,!1).each((e=>e()))},eb=e=>{const t=dm(e),n=yn(e.selection.getNode());vr(n.dom)&&Ar(e.schema,n)?e.selection.setCursorLocation(n.dom,0):e.selection.collapse(!0),t.length>1&&$(t,(e=>_n(e,n)))&&Qt(n,"data-mce-selected","1")},tb=(e,t,n)=>I.some((()=>{const o=e.selection.getRng(),r=n.bind((({rng:n,isStartInTable:r})=>{const s=((e,t)=>I.from(e.dom.getParent(t,e.dom.isBlock)).map(yn))(e,r?n.endContainer:n.startContainer);n.deleteContents(),((e,t,n)=>{n.each((n=>{t?Eo(n):(Xa(n),e.selection.setCursorLocation(n.dom,0))}))})(e,r,s.filter(T(Ar,e.schema)));const a=r?t[0]:t[t.length-1];return Jh(e,a,o,r),Ar(e.schema,a)?I.none():I.some(r?t.slice(1):t.slice(0,-1))})).getOr(t);Qh(r),eb(e)})),nb=(e,t,n,o)=>I.some((()=>{const r=e.selection.getRng(),s=t[0],a=n[n.length-1];Jh(e,s,r,!0),Jh(e,a,r,!1);const i=Ar(e.schema,s)?t:t.slice(1),l=Ar(e.schema,a)?n:n.slice(0,-1);Qh(i.concat(l)),o.deleteContents(),eb(e)})),ob=(e,t,n,o=!0)=>I.some((()=>{Fh(e,t,n,o)})),rb=(e,t)=>I.some((()=>xh(e,!1,t))),sb=(e,t)=>Q(Ip(t,e),Ka),ab=(e,t)=>Q(Ip(t,e),Xt("caption")),ib=(e,t)=>I.some((()=>{Xa(t),e.selection.setCursorLocation(t.dom,0)})),lb=(e,t)=>e?Bp(t):Pp(t),db=(e,t,n)=>{const o=yn(e.getBody());return ab(o,n).fold((()=>((e,t,n,o)=>{const r=Zi.fromRangeStart(e.selection.getRng());return sb(n,o).bind((o=>Ar(e.schema,o,{checkRootAsContent:!1})?ib(e,o):((e,t,n,o,r)=>Lu(n,e.getBody(),r).bind((e=>sb(t,yn(e.getNode())).bind((e=>_n(e,o)?I.none():I.some(_))))))(e,n,t,o,r)))})(e,t,o,n).orThunk((()=>It(((e,t)=>{const n=Zi.fromRangeStart(e.selection.getRng());return lb(t,n)||Du(t,e.getBody(),n).exists((e=>lb(t,e)))})(e,t),_)))),(n=>((e,t,n,o)=>{const r=Zi.fromRangeStart(e.selection.getRng());return Ar(e.schema,o)?ib(e,o):((e,t,n,o,r)=>Lu(n,e.getBody(),r).fold((()=>I.some(_)),(s=>((e,t,n,o)=>zu(e.dom).bind((r=>ju(e.dom).map((e=>t?n.isEqual(r)&&o.isEqual(e):n.isEqual(e)&&o.isEqual(r))))).getOr(!0))(o,n,r,s)?((e,t)=>ib(e,t))(e,o):((e,t,n)=>ab(e,yn(n.getNode())).fold((()=>I.some(_)),(e=>It(!_n(e,t),_))))(t,o,s))))(e,n,t,o,r)})(e,t,o,n)))},cb=(e,t)=>{const n=yn(e.selection.getStart(!0)),o=dm(e);return e.selection.isCollapsed()&&0===o.length?db(e,t,n):((e,t,n)=>{const o=yn(e.getBody()),r=e.selection.getRng();return 0!==n.length?tb(e,n,I.none()):((e,t,n,o)=>ab(t,o).fold((()=>((e,t,n)=>Zh(t,n).bind((t=>t.fold(T(ob,e),T(rb,e),T(tb,e),T(nb,e)))))(e,t,n)),(t=>((e,t)=>ib(e,t))(e,t))))(e,o,r,t)})(e,n,o)},ub=(e,t)=>{let n=t;for(;n&&n!==e;){if(hr(n)||br(n))return n;n=n.parentNode}return null},mb=["data-ephox-","data-mce-","data-alloy-","data-snooker-","_"],fb=Pt.each,gb=e=>{const t=e.dom,n=new Set(e.serializer.getTempAttrs()),o=e=>$(mb,(t=>$e(e,t)))||n.has(e);return{compare:(e,n)=>{if(e.nodeName!==n.nodeName||e.nodeType!==n.nodeType)return!1;const r=e=>{const n={};return fb(t.getAttribs(e),(r=>{const s=r.nodeName.toLowerCase();"style"===s||o(s)||(n[s]=t.getAttrib(e,s))})),n},s=(e,t)=>{for(const n in e)if(_e(e,n)){const o=t[n];if(v(o))return!1;if(e[n]!==o)return!1;delete t[n]}for(const e in t)if(_e(t,e))return!1;return!0};if(Jo(e)&&Jo(n)){if(!s(r(e),r(n)))return!1;if(!s(t.parseStyle(t.getAttrib(e,"style")),t.parseStyle(t.getAttrib(n,"style"))))return!1}return!nm(e)&&!nm(n)},isAttributeInternal:o}},pb=e=>["h1","h2","h3","h4","h5","h6"].includes(e.name),hb=(e,t,n,o)=>{const r=n.name;for(let t=0,s=e.length;t<s;t++){const s=e[t];if(s.name===r){const e=o.nodes[r];e?e.nodes.push(n):o.nodes[r]={filter:s,nodes:[n]}}}if(n.attributes)for(let e=0,r=t.length;e<r;e++){const r=t[e],s=r.name;if(s in n.attributes.map){const e=o.attributes[s];e?e.nodes.push(n):o.attributes[s]={filter:r,nodes:[n]}}}},bb=(e,t)=>{const n=(e,n)=>{pe(e,(e=>{const o=ce(e.nodes);V(e.filter.callbacks,(r=>{for(let t=o.length-1;t>=0;t--){const r=o[t];(n?void 0!==r.attr(e.filter.name):r.name===e.filter.name)&&!y(r.parent)||o.splice(t,1)}o.length>0&&r(o,e.filter.name,t)}))}))};n(e.nodes,!1),n(e.attributes,!0)},vb=(e,t,n,o={})=>{const r=((e,t,n)=>{const o={nodes:{},attributes:{}};return n.firstChild&&((n,r)=>{let s=n;for(;s=s.walk();)hb(e,t,s,o)})(n),o})(e,t,n);bb(r,o)},yb=(e,t,n,o)=>{if((e.pad_empty_with_br||t.insert)&&n(o)){const e=new Zg("br",1);t.insert&&e.attr("data-mce-bogus","1"),o.empty().append(e)}else o.empty().append(new Zg("#text",3)).value=Vo},Cb=(e,t)=>{const n=null==e?void 0:e.firstChild;return C(n)&&n===e.lastChild&&n.name===t},wb=(e,t,n,o)=>o.isEmpty(t,n,(t=>((e,t)=>{const n=e.getElementRule(t.name);return!0===(null==n?void 0:n.paddEmpty)})(e,t))),Eb=e=>{let t;for(let n=e;n;n=n.parent){const e=n.attr("contenteditable");if("false"===e)break;"true"===e&&(t=n)}return I.from(t)},xb=(e,t,n=e.parent)=>{if(t.getSpecialElements()[e.name])e.empty().remove();else{const o=e.children();for(const e of o)n&&!t.isValidChild(n.name,e.name)&&xb(e,t,n);e.unwrap()}},_b=(e,t,n,o=_)=>{const r=t.getTextBlockElements(),s=t.getNonEmptyElements(),a=t.getWhitespaceElements(),i=Pt.makeMap("tr,td,th,tbody,thead,tfoot,table,summary"),l=new Set,d=e=>e!==n&&!i[e.name];for(let n=0;n<e.length;n++){const i=e[n];let c,u,m;if(!i.parent||l.has(i))continue;if(r[i.name]&&"li"===i.parent.name){let e=i.next;for(;e&&r[e.name];)e.name="li",l.add(e),i.parent.insert(e,i.parent),e=e.next;i.unwrap();continue}const f=[i];for(c=i.parent;c&&!t.isValidChild(c.name,i.name)&&d(c);c=c.parent)f.push(c);if(c&&f.length>1)if(Sb(t,i,c))xb(i,t);else{f.reverse(),u=f[0].clone(),o(u);let e=u;for(let n=0;n<f.length-1;n++){t.isValidChild(e.name,f[n].name)&&n>0?(m=f[n].clone(),o(m),e.append(m)):m=e;for(let e=f[n].firstChild;e&&e!==f[n+1];){const t=e.next;m.append(e),e=t}e=m}wb(t,s,a,u)?c.insert(i,f[0],!0):(c.insert(u,f[0],!0),c.insert(i,u)),c=f[0],(wb(t,s,a,c)||Cb(c,"br"))&&c.empty().remove()}else if(i.parent){if("li"===i.name){let e=i.prev;if(e&&("ul"===e.name||"ol"===e.name)){e.append(i);continue}if(e=i.next,e&&("ul"===e.name||"ol"===e.name)&&e.firstChild){e.insert(i,e.firstChild,!0);continue}const t=new Zg("ul",1);o(t),i.wrap(t);continue}if(t.isValidChild(i.parent.name,"div")&&t.isValidChild("div",i.name)){const e=new Zg("div",1);o(e),i.wrap(e)}else xb(i,t)}}},Sb=(e,t,n=t.parent)=>!(!n||(!e.children[t.name]||e.isValidChild(n.name,t.name))&&("a"!==t.name||!((e,t)=>{let n=e;for(;n;){if("a"===n.name)return!0;n=n.parent}return!1})(n))&&(!(e=>"summary"===e.name)(n)||!pb(t)||(null==n?void 0:n.firstChild)===t&&(null==n?void 0:n.lastChild)===t)),kb=e=>e.collapsed?e:(e=>{const t=Zi.fromRangeStart(e),n=Zi.fromRangeEnd(e),o=e.commonAncestorContainer;return Du(!1,o,n).map((r=>!au(t,n,o)&&au(t,r,o)?((e,t,n,o)=>{const r=document.createRange();return r.setStart(e,t),r.setEnd(n,o),r})(t.container(),t.offset(),r.container(),r.offset()):e)).getOr(e)})(e),Nb=(e,t)=>{let n=t.firstChild,o=t.lastChild;return n&&"meta"===n.name&&(n=n.next),o&&"mce_marker"===o.attr("id")&&(o=o.prev),((e,t)=>{const n=e.getNonEmptyElements();return C(t)&&(t.isEmpty(n)||((e,t)=>e.getBlockElements()[t.name]&&(e=>C(e.firstChild)&&e.firstChild===e.lastChild)(t)&&(e=>"br"===e.name||e.value===Vo)(t.firstChild))(e,t))})(e,o)&&(o=null==o?void 0:o.prev),!(!n||n!==o||"ul"!==n.name&&"ol"!==n.name)},Rb=e=>{return e.length>0&&(!(n=e[e.length-1]).firstChild||C(null==(t=n)?void 0:t.firstChild)&&t.firstChild===t.lastChild&&(e=>e.data===Vo||gr(e))(t.firstChild))?e.slice(0,-1):e;var t,n},Ab=(e,t)=>{const n=e.getParent(t,e.isBlock);return n&&"LI"===n.nodeName?n:null},Tb=(e,t)=>{const n=Zi.after(e),o=Tu(t).prev(n);return o?o.toRange():null},Ob=(e,t,n,o)=>{const r=((e,t,n)=>{const o=t.serialize(n);return(e=>{var t,n;const o=e.firstChild,r=e.lastChild;return o&&"META"===o.nodeName&&(null===(t=o.parentNode)||void 0===t||t.removeChild(o)),r&&"mce_marker"===r.id&&(null===(n=r.parentNode)||void 0===n||n.removeChild(r)),e})(e.createFragment(o))})(t,e,o),s=Ab(t,n.startContainer),a=Rb((i=r.firstChild,Y(null!==(l=null==i?void 0:i.childNodes)&&void 0!==l?l:[],(e=>"LI"===e.nodeName))));var i,l;const d=t.getRoot(),c=e=>{const o=Zi.fromRangeStart(n),r=Tu(t.getRoot()),a=1===e?r.prev(o):r.next(o),i=null==a?void 0:a.getNode();return!i||Ab(t,i)!==s};return s?c(1)?((e,t,n)=>{const o=e.parentNode;return o&&Pt.each(t,(t=>{o.insertBefore(t,e)})),((e,t)=>{const n=Zi.before(e),o=Tu(t).next(n);return o?o.toRange():null})(e,n)})(s,a,d):c(2)?((e,t,n,o)=>(o.insertAfter(t.reverse(),e),Tb(t[0],n)))(s,a,d,t):((e,t,n,o)=>{const r=((e,t)=>{const n=t.cloneRange(),o=t.cloneRange();return n.setStartBefore(e),o.setEndAfter(e),[n.cloneContents(),o.cloneContents()]})(e,o),s=e.parentNode;return s&&(s.insertBefore(r[0],e),Pt.each(t,(t=>{s.insertBefore(t,e)})),s.insertBefore(r[1],e),s.removeChild(e)),Tb(t[t.length-1],n)})(s,a,d,n):null},Bb=["pre"],Pb=vr,Db=(e,t,n)=>{var o,r;const s=e.selection,a=e.dom,i=e.parser,l=n.merge,d=hp({validate:!0},e.schema),c='<span id="mce_marker" data-mce-type="bookmark">&#xFEFF;</span>';n.preserve_zwsp||(t=Ja(t)),-1===t.indexOf("{$caret}")&&(t+="{$caret}"),t=t.replace(/\{\$caret\}/,c);let u=s.getRng();const m=u.startContainer,f=e.getBody();m===f&&s.isCollapsed()&&a.isBlock(f.firstChild)&&((e,t)=>C(t)&&!e.schema.getVoidElements()[t.nodeName])(e,f.firstChild)&&a.isEmpty(f.firstChild)&&(u=a.createRng(),u.setStart(f.firstChild,0),u.setEnd(f.firstChild,0),s.setRng(u)),s.isCollapsed()||(e=>{const t=e.dom,n=kb(e.selection.getRng());e.selection.setRng(n);const o=t.getParent(n.startContainer,Pb);((e,t,n)=>!!C(n)&&n===e.getParent(t.endContainer,Pb)&&fm(yn(n),t))(t,n,o)?ob(e,n,yn(o)):n.startContainer===n.endContainer&&n.endOffset-n.startOffset==1&&lr(n.startContainer.childNodes[n.startOffset])?n.deleteContents():e.getDoc().execCommand("Delete",!1)})(e);const g=s.getNode(),p={context:g.nodeName.toLowerCase(),data:n.data,insert:!0},h=i.parse(t,p);if(!0===n.paste&&Nb(e.schema,h)&&((e,t)=>!!Ab(e,t))(a,g))return u=Ob(d,a,s.getRng(),h),u&&s.setRng(u),t;!0===n.paste&&((e,t,n,o)=>{var r;const s=t.firstChild,a=t.lastChild,i=s===("bookmark"===a.attr("data-mce-type")?a.prev:a),l=H(Bb,s.name);if(i&&l){const t="false"!==s.attr("contenteditable"),a=(null===(r=e.getParent(n,e.isBlock))||void 0===r?void 0:r.nodeName.toLowerCase())===s.name,i=I.from(ub(o,n)).forall(hr);return t&&a&&i}return!1})(a,h,g,e.getBody())&&(null===(o=h.firstChild)||void 0===o||o.unwrap()),(e=>{let t=e;for(;t=t.walk();)1===t.type&&t.attr("data-mce-fragment","1")})(h);let b=h.lastChild;if(b&&"mce_marker"===b.attr("id")){const t=b;for(b=b.prev;b;b=b.walk(!0))if(3===b.type||!a.isBlock(b.name)){b.parent&&e.schema.isValidChild(b.parent.name,"span")&&b.parent.insert(t,b,"br"===b.name);break}}if(e._selectionOverrides.showBlockCaretContainer(g),p.invalid||((e,t,n)=>{var o;return $(n.children(),pb)&&"SUMMARY"===(null===(o=e.getParent(t,e.isBlock))||void 0===o?void 0:o.nodeName)})(a,g,h)){e.selection.setContent(c);let n,o=s.getNode();const l=e.getBody();for(mr(o)?o=n=l:n=o;n&&n!==l;)o=n,n=n.parentNode;t=o===l?l.innerHTML:a.getOuterHTML(o);const u=i.parse(t),m=(e=>{for(let t=e;t;t=t.walk())if("mce_marker"===t.attr("id"))return I.some(t);return I.none()})(u),f=m.bind(Eb).getOr(u);m.each((e=>e.replace(h)));const g=h.children(),p=null!==(r=h.parent)&&void 0!==r?r:u;h.unwrap();const b=Y(g,(t=>Sb(e.schema,t,p)));_b(b,e.schema,f),vb(i.getNodeFilters(),i.getAttributeFilters(),u),t=d.serialize(u),o===l?a.setHTML(l,t):a.setOuterHTML(o,t)}else t=d.serialize(h),((e,t,n)=>{var o;if("all"===n.getAttribute("data-mce-bogus"))null===(o=n.parentNode)||void 0===o||o.insertBefore(e.dom.createFragment(t),n);else{const o=n.firstChild,r=n.lastChild;!o||o===r&&"BR"===o.nodeName?e.dom.setHTML(n,t):e.selection.setContent(t,{no_events:!0})}})(e,t,g);var v;return((e,t)=>{const n=e.schema.getTextInlineElements(),o=e.dom;if(t){const t=e.getBody(),r=gb(e);Pt.each(o.select("*[data-mce-fragment]"),(e=>{if(C(n[e.nodeName.toLowerCase()])&&((e,t)=>ne(yp(e,t),(e=>!(e=>bp.has(e))(e))))(o,e))for(let n=e.parentElement;C(n)&&n!==t&&!Cp(o,e,n);n=n.parentElement)if(r.compare(n,e)){o.remove(e,!0);break}}))}})(e,l),((e,t)=>{var n,o,r;let s;const a=e.dom,i=e.selection;if(!t)return;i.scrollIntoView(t);const l=ub(e.getBody(),t);if(l&&"false"===a.getContentEditable(l))return a.remove(t),void i.select(l);let d=a.createRng();const c=t.previousSibling;if(lr(c)){d.setStart(c,null!==(o=null===(n=c.nodeValue)||void 0===n?void 0:n.length)&&void 0!==o?o:0);const e=t.nextSibling;lr(e)&&(c.appendData(e.data),null===(r=e.parentNode)||void 0===r||r.removeChild(e))}else d.setStartBefore(t),d.setEndBefore(t);const u=a.getParent(t,a.isBlock);if(a.remove(t),u&&a.isEmpty(u)){const t=Pb(u);wo(yn(u)),d.setStart(u,0),d.setEnd(u,0),t||(e=>!!e.getAttribute("data-mce-fragment"))(u)||!(s=(t=>{let n=Zi.fromRangeStart(t);return n=Tu(e.getBody()).next(n),null==n?void 0:n.toRange()})(d))?a.add(u,a.create("br",t?{}:{"data-mce-bogus":"1"})):(d=s,a.remove(u))}i.setRng(d)})(e,a.get("mce_marker")),v=e.getBody(),Pt.each(v.getElementsByTagName("*"),(e=>{e.removeAttribute("data-mce-fragment")})),((e,t,n)=>{I.from(e.getParent(t,"td,th")).map(yn).each((e=>((e,t)=>{Un(e).each((n=>{Bn(n).each((o=>{t.isBlock(Ht(e))&&Ha(n)&&t.isBlock(Ht(o))&&Eo(n)}))}))})(e,n)))})(a,s.getStart(),e.schema),((e,t,n)=>{const o=On(yn(n),(e=>_n(e,yn(t))));ie(o,o.length-2).filter(Wt).fold((()=>jr(e,t)),(t=>jr(e,t.dom)))})(e.schema,e.getBody(),s.getStart()),t},Lb=e=>e instanceof Zg,Mb=(e,t,n)=>{e.dom.setHTML(e.getBody(),t),!0!==n&&(e=>{Fg(e)&&zu(e.getBody()).each((t=>{const n=t.getNode(),o=sr(n)?zu(n).getOr(t):t;e.selection.setRng(o.toRange())}))})(e)},Ib=e=>w(e)?e:L,Fb=(e,t,n)=>{const o=t(e),r=Ib(n);return o.orThunk((()=>r(e)?I.none():((e,t,n)=>{let o=e.dom;const r=Ib(n);for(;o.parentNode;){o=o.parentNode;const e=yn(o),n=t(e);if(n.isSome())return n;if(r(e))break}return I.none()})(e,t,r)))},Ub=Rm,zb=(e,t,n)=>{const o=e.formatter.get(n);if(o)for(let n=0;n<o.length;n++){const r=o[n];if(Lm(r)&&!1===r.inherit&&e.dom.is(t,r.selector))return!0}return!1},jb=(e,t,n,o,r)=>{const s=e.dom.getRoot();if(t===s)return!1;const a=e.dom.getParent(t,(t=>!!zb(e,t,n)||t.parentNode===s||!!qb(e,t,n,o,!0)));return!!qb(e,a,n,o,r)},Hb=(e,t,n)=>!(!Mm(n)||!Ub(t,n.inline))||!(!Dm(n)||!Ub(t,n.block))||!!Lm(n)&&Jo(t)&&e.is(t,n.selector),$b=(e,t,n,o,r,s)=>{const a=n[o],i="attributes"===o;if(w(n.onmatch))return n.onmatch(t,n,o);if(a)if(Ne(a)){for(let n=0;n<a.length;n++)if(i?e.getAttrib(t,a[n]):Tm(e,t,a[n]))return!0}else for(const o in a)if(_e(a,o)){const l=i?e.getAttrib(t,o):Tm(e,t,o),d=Nm(a[o],s),c=y(l)||Xe(l);if(c&&y(d))continue;if(r&&c&&!n.exact)return!1;if((!r||n.exact)&&!Ub(l,Am(d,o)))return!1}return!0},qb=(e,t,n,o,r)=>{const s=e.formatter.get(n),a=e.dom;if(s&&Jo(t))for(let n=0;n<s.length;n++){const i=s[n];if(Hb(e.dom,t,i)&&$b(a,t,i,"attributes",r,o)&&$b(a,t,i,"styles",r,o)){const n=i.classes;if(n)for(let r=0;r<n.length;r++)if(!e.dom.hasClass(t,Nm(n[r],o)))return;return i}}},Vb=(e,t,n,o,r)=>{if(o)return jb(e,o,t,n,r);if(o=e.selection.getNode(),jb(e,o,t,n,r))return!0;const s=e.selection.getStart();return!(s===o||!jb(e,s,t,n,r))},Wb=Za,Kb=e=>{if(e){const t=new $o(e,e);for(let e=t.current();e;e=t.next())if(lr(e))return e}return null},Yb=e=>{const t=bn("span");return Jt(t,{id:Hu,"data-mce-bogus":"1","data-mce-type":"format-caret"}),e&&vo(t,vn(Wb)),t},Gb=(e,t,n)=>{const o=e.dom,r=e.selection;if(Fm(t))xh(e,!1,yn(t),n,!0);else{const e=r.getRng(),n=o.getParent(t,o.isBlock),s=e.startContainer,a=e.startOffset,i=e.endContainer,l=e.endOffset,d=(e=>{const t=Kb(e);return t&&t.data.charAt(0)===Wb&&t.deleteData(0,1),t})(t);o.remove(t,!0),s===d&&a>0&&e.setStart(d,a-1),i===d&&l>0&&e.setEnd(d,l-1),n&&o.isEmpty(n)&&Xa(yn(n)),r.setRng(e)}},Xb=(e,t,n)=>{const o=e.dom,r=e.selection;if(t)Gb(e,t,n);else if(!(t=qu(e.getBody(),r.getStart())))for(;t=o.get(Hu);)Gb(e,t,n)},Zb=(e,t)=>(e.appendChild(t),t),Qb=(e,t)=>{var n;const o=G(e,((e,t)=>Zb(e,t.cloneNode(!1))),t),r=null!==(n=o.ownerDocument)&&void 0!==n?n:document;return Zb(o,r.createTextNode(Wb))},Jb=(e,t,n,o)=>{const a=e.dom,i=e.selection;let l=!1;const d=e.formatter.get(t);if(!d)return;const c=i.getRng(),u=c.startContainer,m=c.startOffset;let f=u;lr(u)&&(m!==u.data.length&&(l=!0),f=f.parentNode);const g=[];let h;for(;f;){if(qb(e,f,t,n,o)){h=f;break}f.nextSibling&&(l=!0),g.push(f),f=f.parentNode}if(h)if(l){const r=i.getBookmark();c.collapse(!0);let s=ef(a,c,d,!0);s=Yf(s),e.formatter.remove(t,n,s,o),i.moveToBookmark(r)}else{const l=qu(e.getBody(),h),d=C(l)?a.getParents(h.parentNode,M,l):[],c=Yb(!1).dom;((e,t,n)=>{var o,r;const s=e.dom,a=s.getParent(n,T(xm,e.schema));a&&s.isEmpty(a)?null===(o=n.parentNode)||void 0===o||o.replaceChild(t,n):((e=>{const t=Uo(e,"br"),n=Y((e=>{const t=[];let n=e.dom;for(;n;)t.push(yn(n)),n=n.lastChild;return t})(e).slice(-1),Ha);t.length===n.length&&V(n,Eo)})(yn(n)),s.isEmpty(n)?null===(r=n.parentNode)||void 0===r||r.replaceChild(t,n):s.insertAfter(t,n))})(e,c,null!=l?l:h);const u=((e,t,n,o,a,i)=>{const l=e.formatter,d=e.dom,c=Y(fe(l.get()),(e=>e!==o&&!He(e,"removeformat"))),u=((e,t,n)=>X(n,((n,o)=>{const r=((e,t)=>Pm(e,t,(e=>{const t=e=>w(e)||e.length>1&&"%"===e.charAt(0);return $(["styles","attributes"],(n=>xe(e,n).exists((e=>{const n=p(e)?e:Ee(e);return $(n,t)}))))})))(e,o);return e.formatter.matchNode(t,o,{},r)?n.concat([o]):n}),[]))(e,n,c);if(Y(u,(t=>!((e,t,n)=>{const o=["inline","block","selector","attributes","styles","classes"],a=e=>Ce(e,((e,t)=>$(o,(e=>e===t))));return Pm(e,t,(t=>{const o=a(t);return Pm(e,n,(e=>{const t=a(e);return((e,t,n=s)=>r(n).eq(e,t))(o,t)}))}))})(e,t,o))).length>0){const e=n.cloneNode(!1);return d.add(t,e),l.remove(o,a,e,i),d.remove(e),I.some(e)}return I.none()})(e,c,h,t,n,o),m=Qb([...g,...u.toArray(),...d],c);l&&Gb(e,l,C(l)),i.setCursorLocation(m,1),a.isEmpty(h)&&a.remove(h)}},ev=e=>{const t=Yb(!1),n=Qb(e,t.dom);return{caretContainer:t,caretPosition:Zi(n,0)}},tv=(e,t)=>{const{caretContainer:n,caretPosition:o}=ev(t);return po(yn(e),n),Eo(yn(e)),o},nv=(e,t)=>{if($u(t.dom))return!1;const n=e.schema.getTextInlineElements();return _e(n,Ht(t))&&!$u(t.dom)&&!rr(t.dom)},ov={},rv=nr(["pre"]);((e,t)=>{ov[e]||(ov[e]=[]),ov[e].push((e=>{if(!e.selection.getRng().collapsed){const t=e.selection.getSelectedBlocks(),n=Y(Y(t,rv),(e=>t=>{const n=t.previousSibling;return rv(n)&&H(e,n)})(t));V(n,(e=>{((e,t)=>{const n=yn(t),o=Nn(n).dom;Eo(n),Co(yn(e),[bn("br",o),bn("br",o),...Mn(n)])})(e.previousSibling,e)}))}}))})("pre");const sv=["fontWeight","fontStyle","color","fontSize","fontFamily"],av=(e,t)=>{const n=e.get(t);return p(n)?Q(n,(e=>Mm(e)&&"span"===e.inline&&(e=>f(e.styles)&&$(fe(e.styles),(e=>H(sv,e))))(e))):I.none()},iv=(e,t)=>Uu(t,Zi.fromRangeStart(e)).isNone(),lv=(e,t)=>!1===Fu(t,Zi.fromRangeEnd(e)).exists((e=>!gr(e.getNode())||Fu(t,e).isSome())),dv=e=>t=>wr(t)&&e.isEditable(t),cv=e=>Y(e.getSelectedBlocks(),dv(e.dom)),uv=Pt.each,mv=e=>Jo(e)&&!nm(e)&&!$u(e)&&!rr(e),fv=(e,t)=>{for(let n=e;n;n=n[t]){if(lr(n)&&Ge(n.data))return e;if(Jo(n)&&!nm(n))return n}return e},gv=(e,t,n)=>{const o=gb(e),r=er(t)&&e.dom.isEditable(t),s=er(n)&&e.dom.isEditable(n);if(r&&s){const r=fv(t,"previousSibling"),s=fv(n,"nextSibling");if(o.compare(r,s)){for(let e=r.nextSibling;e&&e!==s;){const t=e;e=e.nextSibling,r.appendChild(t)}return e.dom.remove(s),Pt.each(Pt.grep(s.childNodes),(e=>{r.appendChild(e)})),r}}return n},pv=(e,t,n,o)=>{var r;if(o&&!1!==t.merge_siblings){const t=null!==(r=gv(e,Em(o),o))&&void 0!==r?r:o;gv(e,t,Em(t,!0))}},hv=(e,t,n)=>{uv(e.childNodes,(e=>{mv(e)&&(t(e)&&n(e),e.hasChildNodes()&&hv(e,t,n))}))},bv=(e,t)=>n=>!(!n||!Tm(e,n,t)),vv=(e,t,n)=>o=>{e.setStyle(o,t,n),""===o.getAttribute("style")&&o.removeAttribute("style"),((e,t)=>{"SPAN"===t.nodeName&&0===e.getAttribs(t).length&&e.remove(t,!0)})(e,o)},yv=El([{keep:[]},{rename:["name"]},{removed:[]}]),Cv=/^(src|href|style)$/,wv=Pt.each,Ev=Rm,xv=(e,t,n)=>e.isChildOf(t,n)&&t!==n&&!e.isBlock(n),_v=(e,t,n)=>{let o=t[n?"startContainer":"endContainer"],r=t[n?"startOffset":"endOffset"];if(Jo(o)){const e=o.childNodes.length-1;!n&&r&&r--,o=o.childNodes[r>e?e:r]}return lr(o)&&n&&r>=o.data.length&&(o=new $o(o,e.getBody()).next()||o),lr(o)&&!n&&0===r&&(o=new $o(o,e.getBody()).prev()||o),o},Sv=(e,t)=>{const n=t?"firstChild":"lastChild",o=e[n];return(e=>/^(TR|TH|TD)$/.test(e.nodeName))(e)&&o?"TR"===e.nodeName&&o[n]||o:e},kv=(e,t,n,o)=>{var r;const s=e.create(n,o);return null===(r=t.parentNode)||void 0===r||r.insertBefore(s,t),s.appendChild(t),s},Nv=(e,t,n,o,r)=>{const s=yn(t),a=yn(e.create(o,r)),i=n?Ln(s):Dn(s);return Co(a,i),n?(po(s,a),bo(a,s)):(ho(s,a),vo(a,s)),a.dom},Rv=(e,t,n)=>{const o=t.parentNode;let r;const s=e.dom,a=ql(e);Dm(n)&&o===s.getRoot()&&(n.list_block&&Ev(t,n.list_block)||V(ce(t.childNodes),(t=>{_m(e,a,t.nodeName.toLowerCase())?r?r.appendChild(t):(r=kv(s,t,a),s.setAttribs(r,Vl(e))):r=null}))),(e=>Lm(e)&&Mm(e)&&Dt(xe(e,"mixed"),!0))(n)&&!Ev(n.inline,t)||s.remove(t,!0)},Av=(e,t,n)=>E(e)?{name:t,value:null}:{name:e,value:Nm(t,n)},Tv=(e,t)=>{""===e.getAttrib(t,"style")&&(t.removeAttribute("style"),t.removeAttribute("data-mce-style"))},Ov=(e,t,n,o,r)=>{let s=!1;wv(n.styles,((a,i)=>{const{name:l,value:d}=Av(i,a,o),c=Am(d,l);(n.remove_similar||h(d)||!Jo(r)||Ev(Tm(e,r,l),c))&&e.setStyle(t,l,""),s=!0})),s&&Tv(e,t)},Bv=(e,t,n,o,r)=>{const s=e.dom,a=gb(e),i=e.schema;if(Mm(t)&&qr(i,t.inline)&&Wr(i,o)&&o.parentElement===e.getBody())return Rv(e,o,t),yv.removed();if(!t.ceFalseOverride&&o&&"false"===s.getContentEditableParent(o))return yv.keep();if(o&&!Hb(s,o,t)&&!((e,t)=>t.links&&"A"===e.nodeName)(o,t))return yv.keep();const l=o,d=t.preserve_attributes;if(Mm(t)&&"all"===t.remove&&p(d)){const e=Y(s.getAttribs(l),(e=>H(d,e.name.toLowerCase())));if(s.removeAllAttribs(l),V(e,(e=>s.setAttrib(l,e.name,e.value))),e.length>0)return yv.rename("span")}if("all"!==t.remove){Ov(s,l,t,n,r),wv(t.attributes,((e,o)=>{const{name:a,value:i}=Av(o,e,n);if(t.remove_similar||h(i)||!Jo(r)||Ev(s.getAttrib(r,a),i)){if("class"===a){const e=s.getAttrib(l,a);if(e){let t="";if(V(e.split(/\s+/),(e=>{/mce\-\w+/.test(e)&&(t+=(t?" ":"")+e)})),t)return void s.setAttrib(l,a,t)}}if(Cv.test(a)&&l.removeAttribute("data-mce-"+a),"style"===a&&nr(["li"])(l)&&"none"===s.getStyle(l,"list-style-type"))return l.removeAttribute(a),void s.setStyle(l,"list-style-type","none");"class"===a&&l.removeAttribute("className"),l.removeAttribute(a)}})),wv(t.classes,(e=>{e=Nm(e,n),Jo(r)&&!s.hasClass(r,e)||s.removeClass(l,e)}));const e=s.getAttribs(l);for(let t=0;t<e.length;t++){const n=e[t].nodeName;if(!a.isAttributeInternal(n))return yv.keep()}}return"none"!==t.remove?(Rv(e,l,t),yv.removed()):yv.keep()},Pv=(e,t,n,o)=>Bv(e,t,n,o,o).fold(N(o),(t=>(e.dom.createFragment().appendChild(o),e.dom.rename(o,t))),N(null)),Dv=(e,t,n,o,r)=>{(o||e.selection.isEditable())&&((e,t,n,o,r)=>{const s=e.formatter.get(t),a=s[0],i=e.dom,l=e.selection,d=o=>{const i=((e,t,n,o,r)=>{let s;return t.parentNode&&V(Bm(e.dom,t.parentNode).reverse(),(t=>{if(!s&&Jo(t)&&"_start"!==t.id&&"_end"!==t.id){const a=qb(e,t,n,o,r);a&&!1!==a.split&&(s=t)}})),s})(e,o,t,n,r);return((e,t,n,o,r,s,a,i)=>{var l,d;let c,u;const m=e.dom;if(n){const s=n.parentNode;for(let n=o.parentNode;n&&n!==s;n=n.parentNode){let o=m.clone(n,!1);for(let n=0;n<t.length&&(o=Pv(e,t[n],i,o),null!==o);n++);o&&(c&&o.appendChild(c),u||(u=o),c=o)}a.mixed&&m.isBlock(n)||(o=null!==(l=m.split(n,o))&&void 0!==l?l:o),c&&u&&(null===(d=r.parentNode)||void 0===d||d.insertBefore(c,r),u.appendChild(r),Mm(a)&&pv(e,a,0,c))}return o})(e,s,i,o,o,0,a,n)},c=t=>$(s,(o=>Lv(e,o,n,t,t))),u=t=>{const n=ce(t.childNodes),o=c(t)||$(s,(e=>Hb(i,t,e))),r=t.parentNode;if(!o&&C(r)&&Im(a)&&c(r),a.deep&&n.length)for(let e=0;e<n.length;e++)u(n[e]);V(["underline","line-through","overline"],(n=>{Jo(t)&&e.dom.getStyle(t,"text-decoration")===n&&t.parentNode&&Om(i,t.parentNode)===n&&Lv(e,{deep:!1,exact:!0,inline:"span",styles:{textDecoration:n}},void 0,t)}))},m=e=>{const t=i.get(e?"_start":"_end");if(t){let n=t[e?"firstChild":"lastChild"];return(e=>nm(e)&&Jo(e)&&("_start"===e.id||"_end"===e.id))(n)&&(n=n[e?"firstChild":"lastChild"]),lr(n)&&0===n.data.length&&(n=e?t.previousSibling||t.nextSibling:t.nextSibling||t.previousSibling),i.remove(t,!0),n}return null},f=t=>{let n,o,r=ef(i,t,s,t.collapsed);if(a.split){if(r=Yf(r),n=_v(e,r,!0),o=_v(e,r),n!==o){if(n=Sv(n,!0),o=Sv(o,!1),xv(i,n,o)){const e=I.from(n.firstChild).getOr(n);return d(Nv(i,e,!0,"span",{id:"_start","data-mce-type":"bookmark"})),void m(!0)}if(xv(i,o,n)){const e=I.from(o.lastChild).getOr(o);return d(Nv(i,e,!1,"span",{id:"_end","data-mce-type":"bookmark"})),void m(!1)}n=kv(i,n,"span",{id:"_start","data-mce-type":"bookmark"}),o=kv(i,o,"span",{id:"_end","data-mce-type":"bookmark"});const e=i.createRng();e.setStartAfter(n),e.setEndBefore(o),tf(i,e,(e=>{V(e,(e=>{nm(e)||nm(e.parentNode)||d(e)}))})),d(n),d(o),n=m(!0),o=m()}else n=o=d(n);r.startContainer=n.parentNode?n.parentNode:n,r.startOffset=i.nodeIndex(n),r.endContainer=o.parentNode?o.parentNode:o,r.endOffset=i.nodeIndex(o)+1}tf(i,r,(e=>{V(e,u)}))};if(o){if(vm(o)){const e=i.createRng();e.setStartBefore(o),e.setEndAfter(o),f(e)}else f(o);mf(e,t,o,n)}else l.isCollapsed()&&Mm(a)&&!dm(e).length?Jb(e,t,n,r):(Cm(e,(()=>hm(e,f)),(o=>Mm(a)&&Vb(e,t,n,o))),e.nodeChanged()),((e,t,n)=>{"removeformat"===t?V(cv(e.selection),(t=>{V(sv,(n=>e.dom.setStyle(t,n,""))),Tv(e.dom,t)})):av(e.formatter,t).each((t=>{V(cv(e.selection),(o=>Ov(e.dom,o,t,n,null)))}))})(e,t,n),mf(e,t,o,n)})(e,t,n,o,r)},Lv=(e,t,n,o,r)=>Bv(e,t,n,o,r).fold(L,(t=>(e.dom.rename(o,t),!0)),M),Mv=Pt.each,Iv=Pt.each,Fv=(e,t,n,o)=>{if(Iv(n.styles,((n,r)=>{e.setStyle(t,r,Nm(n,o))})),n.styles){const n=e.getAttrib(t,"style");n&&e.setAttrib(t,"data-mce-style",n)}},Uv=(e,t,n,o)=>{const r=e.formatter.get(t),s=r[0],a=!o&&e.selection.isCollapsed(),i=e.dom,l=e.selection,d=(e,t=s)=>{w(t.onformat)&&t.onformat(e,t,n,o),Fv(i,e,t,n),Iv(t.attributes,((t,o)=>{i.setAttrib(e,o,Nm(t,n))})),Iv(t.classes,(t=>{const o=Nm(t,n);i.hasClass(e,o)||i.addClass(e,o)}))},c=(e,t)=>{let n=!1;return Iv(e,(e=>!(!Lm(e)||("false"!==i.getContentEditable(t)||e.ceFalseOverride)&&(!C(e.collapsed)||e.collapsed===a)&&i.is(t,e.selector)&&!$u(t)&&(d(t,e),n=!0,1)))),n},u=e=>{if(m(e)){const t=i.create(e);return d(t),t}return null},f=(o,a,i)=>{const l=[];let m=!0;const f=s.inline||s.block,g=u(f);tf(o,a,(a=>{let u;const p=a=>{let h=!1,b=m,v=!1;const y=a.parentNode,w=y.nodeName.toLowerCase(),E=o.getContentEditable(a);C(E)&&(b=m,m="true"===E,h=!0,v=km(e,a));const x=m&&!h;if(gr(a)&&!((e,t,n,o)=>{if(Ad(e)&&Mm(t)&&n.parentNode){const t=Os(e.schema),r=jo(yn(n),(e=>$u(e.dom)));return Se(t,o)&&Rr(e.schema,n.parentNode,{skipBogus:!1,includeZwsp:!0})&&!r}return!1})(e,s,a,w))return u=null,void(Dm(s)&&o.remove(a));if((o=>(e=>Dm(e)&&!0===e.wrapper)(s)&&qb(e,o,t,n))(a))u=null;else{if(((t,n,o)=>{const r=(e=>Dm(e)&&!0!==e.wrapper)(s)&&xm(e.schema,t)&&_m(e,n,f);return o&&r})(a,w,x)){const e=o.rename(a,f);return d(e),l.push(e),void(u=null)}if(Lm(s)){let e=c(r,a);if(!e&&C(y)&&Im(s)&&(e=c(r,y)),!Mm(s)||e)return void(u=null)}C(g)&&((t,n,r,a)=>{const l=t.nodeName.toLowerCase(),d=_m(e,f,l)&&_m(e,n,f),c=!i&&lr(t)&&Qa(t.data),u=$u(t),m=!Mm(s)||!o.isBlock(t);return(r||a)&&d&&!c&&!u&&m})(a,w,x,v)?(u||(u=o.clone(g,!1),y.insertBefore(u,a),l.push(u)),v&&h&&(m=b),u.appendChild(a)):(u=null,V(ce(a.childNodes),p),h&&(m=b),u=null)}};V(a,p)})),!0===s.links&&V(l,(e=>{const t=e=>{"A"===e.nodeName&&d(e,s),V(ce(e.childNodes),t)};t(e)})),V(l,(a=>{const i=(e=>{let t=0;return V(e.childNodes,(e=>{(e=>C(e)&&lr(e)&&0===e.length)(e)||nm(e)||t++})),t})(a);!(l.length>1)&&o.isBlock(a)||0!==i?(Mm(s)||Dm(s)&&s.wrapper)&&(s.exact||1!==i||(a=(e=>{const t=Q(e.childNodes,ym).filter((e=>"false"!==o.getContentEditable(e)&&Hb(o,e,s)));return t.map((t=>{const n=o.clone(t,!1);return d(n),o.replace(n,e,!0),o.remove(t,!0),n})).getOr(e)})(a)),((e,t,n,o)=>{Mv(t,(t=>{Mm(t)&&Mv(e.dom.select(t.inline,o),(o=>{mv(o)&&Lv(e,t,n,o,t.exact?o:null)})),((e,t,n)=>{if(t.clear_child_styles){const o=t.links?"*:not(a)":"*";uv(e.select(o,n),(n=>{mv(n)&&e.isEditable(n)&&uv(t.styles,((t,o)=>{e.setStyle(n,o,"")}))}))}})(e.dom,t,o)}))})(e,r,n,a),((e,t,n,o,r)=>{const s=r.parentNode;qb(e,s,n,o)&&Lv(e,t,o,r)||t.merge_with_parents&&s&&e.dom.getParent(s,(s=>!!qb(e,s,n,o)&&(Lv(e,t,o,r),!0)))})(e,s,t,n,a),((e,t,n,o)=>{if(t.styles&&t.styles.backgroundColor){const r=bv(e,"fontSize");hv(o,(t=>r(t)&&e.isEditable(t)),vv(e,"backgroundColor",Nm(t.styles.backgroundColor,n)))}})(o,s,n,a),((e,t,n,o)=>{const r=t=>{if(er(t)&&Jo(t.parentNode)&&e.isEditable(t)){const n=Om(e,t.parentNode);e.getStyle(t,"color")&&n?e.setStyle(t,"text-decoration",n):e.getStyle(t,"text-decoration")===n&&e.setStyle(t,"text-decoration",null)}};t.styles&&(t.styles.color||t.styles.textDecoration)&&(Pt.walk(o,r,"childNodes"),r(o))})(o,s,0,a),((e,t,n,o)=>{if(Mm(t)&&("sub"===t.inline||"sup"===t.inline)){const n=bv(e,"fontSize");hv(o,(t=>n(t)&&e.isEditable(t)),vv(e,"fontSize",""));const r=Y(e.select("sup"===t.inline?"sub":"sup",o),e.isEditable);e.remove(r,!0)}})(o,s,0,a),pv(e,s,0,a)):o.remove(a,!0)}))},g=vm(o)?o:l.getNode();if("false"===i.getContentEditable(g)&&!km(e,g))return c(r,o=g),void uf(e,t,o,n);if(s){if(o)if(vm(o)){if(!c(r,o)){const e=i.createRng();e.setStartBefore(o),e.setEndAfter(o),f(i,ef(i,e,r),!0)}}else f(i,o,!0);else a&&Mm(s)&&!dm(e).length?((e,t,n)=>{let o;const r=e.selection,s=e.formatter.get(t);if(!s)return;const a=r.getRng();let i=a.startOffset;const l=a.startContainer.nodeValue;o=qu(e.getBody(),r.getStart());const d=/[^\s\u00a0\u00ad\u200b\ufeff]/;if(l&&i>0&&i<l.length&&d.test(l.charAt(i))&&d.test(l.charAt(i-1))){const o=r.getBookmark();a.collapse(!0);let i=ef(e.dom,a,s);i=Yf(i),e.formatter.apply(t,n,i),r.moveToBookmark(o)}else{let s=o?Kb(o):null;o&&(null==s?void 0:s.data)===Wb||(c=e.getDoc(),u=Yb(!0).dom,o=c.importNode(u,!0),s=o.firstChild,a.insertNode(o),i=1),e.formatter.apply(t,n,o),r.setCursorLocation(s,i)}var c,u})(e,t,n):(l.setRng(kb(l.getRng())),Cm(e,(()=>{hm(e,((e,t)=>{const n=t?e:ef(i,e,r);f(i,n,!1)}))}),M),e.nodeChanged()),av(e.formatter,t).each((t=>{V((e=>Y((e=>{const t=e.getSelectedBlocks(),n=e.getRng();if(e.isCollapsed())return[];if(1===t.length)return iv(n,t[0])&&lv(n,t[0])?t:[];{const e=le(t).filter((e=>iv(n,e))).toArray(),o=de(t).filter((e=>lv(n,e))).toArray(),r=t.slice(1,-1);return e.concat(r).concat(o)}})(e),dv(e.dom)))(e.selection),(e=>Fv(i,e,t,n)))}));((e,t)=>{_e(ov,e)&&V(ov[e],(e=>{e(t)}))})(t,e)}uf(e,t,o,n)},zv=(e,t,n,o)=>{(o||e.selection.isEditable())&&Uv(e,t,n,o)},jv=e=>_e(e,"vars"),Hv=e=>e.selection.getStart(),$v=(e,t,n,o,r)=>Z(t,(t=>{const s=e.formatter.matchNode(t,n,null!=r?r:{},o);return!v(s)}),(t=>!!zb(e,t,n)||!o&&C(e.formatter.matchNode(t,n,r,!0)))),qv=(e,t)=>{const n=null!=t?t:Hv(e);return Y(Bm(e.dom,n),(e=>Jo(e)&&!rr(e)))},Vv=(e,t,n)=>{const o=qv(e,t);pe(n,((n,r)=>{const s=n=>{const s=$v(e,o,r,n.similar,jv(n)?n.vars:void 0),a=s.isSome();if(n.state.get()!==a){n.state.set(a);const e=s.getOr(t);jv(n)?n.callback(a,{node:e,format:r,parents:o}):V(n.callbacks,(t=>t(a,{node:e,format:r,parents:o})))}};V([n.withSimilar,n.withoutSimilar],s),V(n.withVars,s)}))},Wv=Pt.explode,Kv=()=>{const e={};return{addFilter:(t,n)=>{V(Wv(t),(t=>{_e(e,t)||(e[t]={name:t,callbacks:[]}),e[t].callbacks.push(n)}))},getFilters:()=>Ee(e),removeFilter:(t,n)=>{V(Wv(t),(t=>{if(_e(e,t))if(C(n)){const o=e[t],r=Y(o.callbacks,(e=>e!==n));r.length>0?o.callbacks=r:delete e[t]}else delete e[t]}))}}},Yv=(e,t,n)=>{var o;const r=$s();t.convert_fonts_to_spans&&((e,t,n)=>{e.addNodeFilter("font",(e=>{V(e,(e=>{const o=t.parse(e.attr("style")),r=e.attr("color"),s=e.attr("face"),a=e.attr("size");r&&(o.color=r),s&&(o["font-family"]=s),a&&Ze(a).each((e=>{o["font-size"]=n[e-1]})),e.name="span",e.attr("style",t.serialize(o)),((e,t)=>{V(["color","face","size"],(t=>{e.attr(t,null)}))})(e)}))}))})(e,r,Pt.explode(null!==(o=t.font_size_legacy_values)&&void 0!==o?o:"")),((e,t,n)=>{e.addNodeFilter("strike",(e=>{const o="html4"!==t.type;V(e,(e=>{if(o)e.name="s";else{const t=n.parse(e.attr("style"));t["text-decoration"]="line-through",e.name="span",e.attr("style",n.serialize(t))}}))}))})(e,n,r)},Gv=e=>{const[t,...n]=e.split(","),o=n.join(","),r=/data:([^/]+\/[^;]+)(;.+)?/.exec(t);if(r){const e=";base64"===r[2],t=e?(e=>{const t=/([a-z0-9+\/=\s]+)/i.exec(e);return t?t[1]:""})(o):decodeURIComponent(o);return I.some({type:r[1],data:t,base64Encoded:e})}return I.none()},Xv=(e,t,n=!0)=>{let o=t;if(n)try{o=atob(t)}catch(e){return I.none()}const r=new Uint8Array(o.length);for(let e=0;e<r.length;e++)r[e]=o.charCodeAt(e);return I.some(new Blob([r],{type:e}))},Zv=e=>new Promise(((t,n)=>{const o=new FileReader;o.onloadend=()=>{t(o.result)},o.onerror=()=>{var e;n(null===(e=o.error)||void 0===e?void 0:e.message)},o.readAsDataURL(e)}));let Qv=0;const Jv=(e,t,n)=>Gv(e).bind((({data:e,type:o,base64Encoded:r})=>{if(t&&!r)return I.none();{const t=r?e:btoa(e);return n(t,o)}})),ey=(e,t,n)=>{const o=e.create("blobid"+Qv++,t,n);return e.add(o),o},ty=(e,t,n=!1)=>Jv(t,n,((t,n)=>I.from(e.getByData(t,n)).orThunk((()=>Xv(n,t).map((n=>ey(e,n,t))))))),ny=/^(?:(?:(?:[A-Za-z][A-Za-z\d.+-]{0,14}:\/\/(?:[-.~*+=!&;:'%@?^${}(),\w]+@)?|www\.|[-;:&=+$,.\w]+@)([A-Za-z\d-]+(?:\.[A-Za-z\d-]+)*))(?::\d+)?(?:\/(?:[-.~*+=!;:'%@$(),\/\w]*[-~*+=%@$()\/\w])?)?(?:\?(?:[-.~*+=!&;:'%@?^${}(),\/\w]+)?)?(?:#(?:[-.~*+=!&;:'%@?^${}(),\/\w]+)?)?)$/,oy=e=>I.from(e.match(ny)).bind((e=>ie(e,1))).map((e=>$e(e,"www.")?e.substring(4):e)),ry=(e,t)=>{I.from(e.attr("src")).bind(oy).forall((e=>!H(t,e)))&&e.attr("sandbox","")},sy=(e,t)=>$e(e,`${t}/`),{entries:ay,setPrototypeOf:iy,isFrozen:ly,getPrototypeOf:dy,getOwnPropertyDescriptor:cy}=Object;let{freeze:uy,seal:my,create:fy}=Object,{apply:gy,construct:py}="undefined"!=typeof Reflect&&Reflect;// eslint-disable-line import/no-mutable-exports
uy||(uy=function(e){return e}),my||(my=function(e){return e}),gy||(gy=function(e,t,n){return e.apply(t,n)}),py||(py=function(e,t){return new e(...t)});const hy=Oy(Array.prototype.forEach),by=Oy(Array.prototype.lastIndexOf),vy=Oy(Array.prototype.pop),yy=Oy(Array.prototype.push),Cy=Oy(Array.prototype.splice),wy=Oy(String.prototype.toLowerCase),Ey=Oy(String.prototype.toString),xy=Oy(String.prototype.match),_y=Oy(String.prototype.replace),Sy=Oy(String.prototype.indexOf),ky=Oy(String.prototype.trim),Ny=Oy(Object.prototype.hasOwnProperty),Ry=Oy(RegExp.prototype.test),Ay=(Ty=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return py(Ty,t)});
/**
     * Creates a new function that constructs an instance of the given constructor function with the provided arguments.
     *
     * @param func - The constructor function to be wrapped and called.
     * @returns A new function that constructs an instance of the given constructor function with the provided arguments.
     */
var Ty;
/**
     * Add properties to a lookup table
     *
     * @param set - The set to which elements will be added.
     * @param array - The array containing elements to be added to the set.
     * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.
     * @returns The modified set with added elements.
     */
/**
     * Creates a new function that calls the given function with a specified thisArg and arguments.
     *
     * @param func - The function to be wrapped and called.
     * @returns A new function that calls the given function with a specified thisArg and arguments.
     */
function Oy(e){return function(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return gy(e,t,o)}}function By(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:wy;iy&&
// Make 'in' and truthy checks like Boolean(set.constructor)
// independent of any properties defined on Object.prototype.
// Prevent prototype setters from intercepting set as a this value.
iy(e,null);let o=t.length;for(;o--;){let r=t[o];if("string"==typeof r){const e=n(r);e!==r&&(
// Config presets (e.g. tags.js, attrs.js) are immutable.
ly(t)||(t[o]=e),r=e)}e[r]=!0}return e}
/**
     * Clean up an array to harden against CSPP
     *
     * @param array - The array to be cleaned.
     * @returns The cleaned version of the array
     */function Py(e){for(let t=0;t<e.length;t++)Ny(e,t)||(e[t]=null);return e}
/**
     * Shallow clone an object
     *
     * @param object - The object to be cloned.
     * @returns A new object that copies the original.
     */function Dy(e){const t=fy(null);for(const[n,o]of ay(e))Ny(e,n)&&(Array.isArray(o)?t[n]=Py(o):o&&"object"==typeof o&&o.constructor===Object?t[n]=Dy(o):t[n]=o);return t}
/**
     * This method automatically checks if the prop is function or getter and behaves accordingly.
     *
     * @param object - The object to look up the getter function in its prototype chain.
     * @param prop - The property name for which to find the getter function.
     * @returns The getter function found in the prototype chain or a fallback function.
     */function Ly(e,t){for(;null!==e;){const n=cy(e,t);if(n){if(n.get)return Oy(n.get);if("function"==typeof n.value)return Oy(n.value)}e=dy(e)}return function(){return null}}const My=uy(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Iy=uy(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Fy=uy(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Uy=uy(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),zy=uy(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),jy=uy(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Hy=uy(["#text"]),$y=uy(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),qy=uy(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Vy=uy(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Wy=uy(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Ky=my(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Yy=my(/<%[\w\W]*|[\w\W]*%>/gm),Gy=my(/\$\{[\w\W]*/gm),Xy=my(/^data-[\-\w.\u00B7-\uFFFF]+$/),Zy=my(/^aria-[\-\w]+$/),Qy=my(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Jy=my(/^(?:\w+script|data):/i),eC=my(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),tC=my(/^html$/i),nC=my(/^[a-z][.\w]*(-[.\w]+)+$/i);var oC=Object.freeze({__proto__:null,ARIA_ATTR:Zy,ATTR_WHITESPACE:eC,CUSTOM_ELEMENT:nC,DATA_ATTR:Xy,DOCTYPE_NAME:tC,ERB_EXPR:Yy,IS_ALLOWED_URI:Qy,IS_SCRIPT_OR_DATA:Jy,MUSTACHE_EXPR:Ky,TMPLIT_EXPR:Gy});
/* eslint-disable @typescript-eslint/indent */
// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType
const rC=function(){return"undefined"==typeof window?null:window};var sC=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:rC();const n=t=>e(t);if(n.version="3.2.4",n.removed=[],!t||!t.document||9!==t.document.nodeType||!t.Element)
// Not running in a browser, provide a factory function
// so that you can pass your own Window
return n.isSupported=!1,n;let{document:o}=t;const r=o,s=r.currentScript,{DocumentFragment:a,HTMLTemplateElement:i,Node:l,Element:d,NodeFilter:c,NamedNodeMap:u=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:m,DOMParser:f,trustedTypes:g}=t,p=d.prototype,h=Ly(p,"cloneNode"),b=Ly(p,"remove"),v=Ly(p,"nextSibling"),y=Ly(p,"childNodes"),C=Ly(p,"parentNode");
// As per issue #47, the web-components registry is inherited by a
// new document created via createHTMLDocument. As per the spec
// (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)
// a new empty registry is used when creating a template contents owner
// document, so we use that as our parent document to ensure nothing
// is inherited.
if("function"==typeof i){const e=o.createElement("template");e.content&&e.content.ownerDocument&&(o=e.content.ownerDocument)}let w,E="";const{implementation:x,createNodeIterator:_,createDocumentFragment:S,getElementsByTagName:k}=o,{importNode:N}=r;let R={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};
/**
       * Expose whether this browser supports running the full DOMPurify.
       */n.isSupported="function"==typeof ay&&"function"==typeof C&&x&&void 0!==x.createHTMLDocument;const{MUSTACHE_EXPR:A,ERB_EXPR:T,TMPLIT_EXPR:O,DATA_ATTR:B,ARIA_ATTR:P,IS_SCRIPT_OR_DATA:D,ATTR_WHITESPACE:L,CUSTOM_ELEMENT:M}=oC;let{IS_ALLOWED_URI:I}=oC,F=null;
/**
       * We consider the elements and attributes below to be safe. Ideally
       * don't add any new ones but feel free to remove unwanted ones.
       */
/* allowed element names */const U=By({},[...My,...Iy,...Fy,...zy,...Hy]);
/* Allowed attribute names */let z=null;const j=By({},[...$y,...qy,...Vy,...Wy]);
/*
       * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.
       * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)
       * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)
       * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.
       */let H=Object.seal(fy(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),$=null,q=null,V=!0,W=!0,K=!1,Y=!0,G=!1,X=!0,Z=!1,Q=!1,J=!1,ee=!1,te=!1,ne=!1,oe=!0,re=!1,se=!0,ae=!1,ie={},le=null;
/* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */const de=By({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);
/* Tags that are safe for data: URIs */let ce=null;const ue=By({},["audio","video","img","source","image","track"]);
/* Attributes safe for values like "javascript:" */let me=null;const fe=By({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ge="http://www.w3.org/1998/Math/MathML",pe="http://www.w3.org/2000/svg",he="http://www.w3.org/1999/xhtml";
/* Document namespace */
let be=he,ve=!1,ye=null;const Ce=By({},[ge,pe,he],Ey);let we=By({},["mi","mo","mn","ms","mtext"]),Ee=By({},["annotation-xml"]);
// Certain elements are allowed in both SVG and HTML
// namespace. We need to specify them explicitly
// so that they don't get erroneously deleted from
// HTML namespace.
const xe=By({},["title","style","font","a","script"]);
/* Parsing of strict XHTML documents */let _e=null;const Se=["application/xhtml+xml","text/html"];let ke=null,Ne=null;
/* Keep a reference to config to pass to hooks */
/* Ideally, do not touch anything below this line */
/* ______________________________________________ */
const Re=o.createElement("form"),Ae=function(e){return e instanceof RegExp||e instanceof Function},Te=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!Ne||Ne!==e){if(
/* Shield configuration object from tampering */
e&&"object"==typeof e||(e={})
/* Shield configuration object from prototype pollution */,e=Dy(e),_e=
// eslint-disable-next-line unicorn/prefer-includes
-1===Se.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,
// HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.
ke="application/xhtml+xml"===_e?Ey:wy,
/* Set configuration parameters */
F=Ny(e,"ALLOWED_TAGS")?By({},e.ALLOWED_TAGS,ke):U,z=Ny(e,"ALLOWED_ATTR")?By({},e.ALLOWED_ATTR,ke):j,ye=Ny(e,"ALLOWED_NAMESPACES")?By({},e.ALLOWED_NAMESPACES,Ey):Ce,me=Ny(e,"ADD_URI_SAFE_ATTR")?By(Dy(fe),e.ADD_URI_SAFE_ATTR,ke):fe,ce=Ny(e,"ADD_DATA_URI_TAGS")?By(Dy(ue),e.ADD_DATA_URI_TAGS,ke):ue,le=Ny(e,"FORBID_CONTENTS")?By({},e.FORBID_CONTENTS,ke):de,$=Ny(e,"FORBID_TAGS")?By({},e.FORBID_TAGS,ke):{},q=Ny(e,"FORBID_ATTR")?By({},e.FORBID_ATTR,ke):{},ie=!!Ny(e,"USE_PROFILES")&&e.USE_PROFILES,V=!1!==e.ALLOW_ARIA_ATTR,// Default true
W=!1!==e.ALLOW_DATA_ATTR,// Default true
K=e.ALLOW_UNKNOWN_PROTOCOLS||!1,// Default false
Y=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,// Default true
G=e.SAFE_FOR_TEMPLATES||!1,// Default false
X=!1!==e.SAFE_FOR_XML,// Default true
Z=e.WHOLE_DOCUMENT||!1,// Default false
ee=e.RETURN_DOM||!1,// Default false
te=e.RETURN_DOM_FRAGMENT||!1,// Default false
ne=e.RETURN_TRUSTED_TYPE||!1,// Default false
J=e.FORCE_BODY||!1,// Default false
oe=!1!==e.SANITIZE_DOM,// Default true
re=e.SANITIZE_NAMED_PROPS||!1,// Default false
se=!1!==e.KEEP_CONTENT,// Default true
ae=e.IN_PLACE||!1,// Default false
I=e.ALLOWED_URI_REGEXP||Qy,be=e.NAMESPACE||he,we=e.MATHML_TEXT_INTEGRATION_POINTS||we,Ee=e.HTML_INTEGRATION_POINTS||Ee,H=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&Ae(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(H.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Ae(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(H.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(H.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),G&&(W=!1),te&&(ee=!0)
/* Parse profile info */,ie&&(F=By({},Hy),z=[],!0===ie.html&&(By(F,My),By(z,$y)),!0===ie.svg&&(By(F,Iy),By(z,qy),By(z,Wy)),!0===ie.svgFilters&&(By(F,Fy),By(z,qy),By(z,Wy)),!0===ie.mathMl&&(By(F,zy),By(z,Vy),By(z,Wy)))
/* Merge configuration parameters */,e.ADD_TAGS&&(F===U&&(F=Dy(F)),By(F,e.ADD_TAGS,ke)),e.ADD_ATTR&&(z===j&&(z=Dy(z)),By(z,e.ADD_ATTR,ke)),e.ADD_URI_SAFE_ATTR&&By(me,e.ADD_URI_SAFE_ATTR,ke),e.FORBID_CONTENTS&&(le===de&&(le=Dy(le)),By(le,e.FORBID_CONTENTS,ke))
/* Add #text in case KEEP_CONTENT is set to true */,se&&(F["#text"]=!0)
/* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */,Z&&By(F,["html","head","body"])
/* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */,F.table&&(By(F,["tbody"]),delete $.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw Ay('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw Ay('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');
// Overwrite existing TrustedTypes policy.
w=e.TRUSTED_TYPES_POLICY,
// Sign local variables required by `sanitize`.
E=w.createHTML("")}else
// Uninitialized policy, attempt to initialize the internal dompurify policy.
void 0===w&&(w=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;
// Allow the callers to control the unique policy name
// by adding a data-tt-policy-suffix to the script element with the DOMPurify.
// Policy creation with duplicate names throws in Trusted Types.
let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){
// Policy creation failed (most likely another DOMPurify script has
// already run). Skip creating the policy, as this will only cause errors
// if TT are enforced.
return console.warn("TrustedTypes policy "+r+" could not be created."),null}}(g,s)),
// If creating the internal policy succeeded sign internal variables.
null!==w&&"string"==typeof E&&(E=w.createHTML(""));
// Prevent further manipulation of configuration.
// Not available in IE8, Safari 5, etc.
uy&&uy(e),Ne=e}},Oe=By({},[...Iy,...Fy,...Uy]),Be=By({},[...zy,...jy]),Pe=function(e){yy(n.removed,{element:e});try{
// eslint-disable-next-line unicorn/prefer-dom-node-remove
C(e).removeChild(e)}catch(t){b(e)}},De=function(e,t){try{yy(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){yy(n.removed,{attribute:null,from:t})}
// We void attribute values for unremovable "is" attributes
if(t.removeAttribute(e),"is"===e)if(ee||te)try{Pe(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Le=function(e){
/* Create a HTML document */
let t=null,n=null;if(J)e="<remove></remove>"+e;else{
/* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */
const t=xy(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===_e&&be===he&&(
// Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)
e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const r=w?w.createHTML(e):e;
/*
         * Use the DOMParser API by default, fallback later if needs be
         * DOMParser not work for svg when has multiple root element.
         */if(be===he)try{t=(new f).parseFromString(r,_e)}catch(e){}
/* Use createHTMLDocument in case DOMParser is not available */if(!t||!t.documentElement){t=x.createDocument(be,"template",null);try{t.documentElement.innerHTML=ve?E:r}catch(e){
// Syntax error if dirtyPayload is invalid xml
}}const s=t.body||t.documentElement;
/* Work on whole document or just its body */
return e&&n&&s.insertBefore(o.createTextNode(n),s.childNodes[0]||null),be===he?k.call(t,Z?"html":"body")[0]:Z?t.documentElement:s},Me=function(e){return _.call(e.ownerDocument||e,e,
// eslint-disable-next-line no-bitwise
c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT|c.SHOW_PROCESSING_INSTRUCTION|c.SHOW_CDATA_SECTION,null)},Ie=function(e){return e instanceof m&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof u)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Fe=function(e){return"function"==typeof l&&e instanceof l};function Ue(e,t,o){hy(e,(e=>{e.call(n,t,o,Ne)}))}
/**
       * _sanitizeElements
       *
       * @protect nodeName
       * @protect textContent
       * @protect removeChild
       * @param currentNode to check for permission to exist
       * @return true if node was killed, false if left alive
       */const ze=function(e){let t=null;
/* Execute a hook if present */
/* Check if element is clobbered or can clobber */
if(Ue(R.beforeSanitizeElements,e,null),Ie(e))return Pe(e),!0;
/* Now let's check the element's type and name */const o=ke(e.nodeName);
/* Execute a hook if present */
/* Detect mXSS attempts abusing namespace confusion */
if(Ue(R.uponSanitizeElement,e,{tagName:o,allowedTags:F}),e.hasChildNodes()&&!Fe(e.firstElementChild)&&Ry(/<[/\w]/g,e.innerHTML)&&Ry(/<[/\w]/g,e.textContent))return Pe(e),!0;
/* Remove any occurrence of processing instructions */if(7===e.nodeType)return Pe(e),!0;
/* Remove any kind of possibly harmful comments */if(X&&8===e.nodeType&&Ry(/<[/\w]/g,e.data))return Pe(e),!0;
/* Remove element if anything forbids its presence */if(!F[o]||$[o]){
/* Check if we have a custom element to handle */
if(!$[o]&&He(o)){if(H.tagNameCheck instanceof RegExp&&Ry(H.tagNameCheck,o))return!1;if(H.tagNameCheck instanceof Function&&H.tagNameCheck(o))return!1}
/* Keep content except for bad-listed elements */if(se&&!le[o]){const t=C(e)||e.parentNode,n=y(e)||e.childNodes;if(n&&t)for(let o=n.length-1;o>=0;--o){const r=h(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,v(e))}}return Pe(e),!0}
/* Check whether element has a valid namespace */return e instanceof d&&!function(e){let t=C(e);
// In JSDOM, if we're inside shadow DOM, then parentNode
// can be null. We just simulate parent in this case.
t&&t.tagName||(t={namespaceURI:be,tagName:"template"});const n=wy(e.tagName),o=wy(t.tagName);return!!ye[e.namespaceURI]&&(e.namespaceURI===pe?
// The only way to switch from HTML namespace to SVG
// is via <svg>. If it happens via any other tag, then
// it should be killed.
t.namespaceURI===he?"svg"===n:
// The only way to switch from MathML to SVG is via`
// svg if parent is either <annotation-xml> or MathML
// text integration points.
t.namespaceURI===ge?"svg"===n&&("annotation-xml"===o||we[o]):Boolean(Oe[n]):e.namespaceURI===ge?
// The only way to switch from HTML namespace to MathML
// is via <math>. If it happens via any other tag, then
// it should be killed.
t.namespaceURI===he?"math"===n:
// The only way to switch from SVG to MathML is via
// <math> and HTML integration points
t.namespaceURI===pe?"math"===n&&Ee[o]:Boolean(Be[n]):e.namespaceURI===he?
// The only way to switch from SVG to HTML is via
// HTML integration points, and from MathML to HTML
// is via MathML text integration points
!(t.namespaceURI===pe&&!Ee[o])&&!(t.namespaceURI===ge&&!we[o])&&!Be[n]&&(xe[n]||!Oe[n]):!("application/xhtml+xml"!==_e||!ye[e.namespaceURI]))}(e)?(Pe(e),!0):
/* Make sure that older browsers don't get fallback-tag mXSS */
"noscript"!==o&&"noembed"!==o&&"noframes"!==o||!Ry(/<\/no(script|embed|frames)/i,e.innerHTML)?(
/* Sanitize element content to be template-safe */
G&&3===e.nodeType&&(
/* Get the element's text content */
t=e.textContent,hy([A,T,O],(e=>{t=_y(t,e," ")})),e.textContent!==t&&(yy(n.removed,{element:e.cloneNode()}),e.textContent=t))
/* Execute a hook if present */,Ue(R.afterSanitizeElements,e,null),!1):(Pe(e),!0)},je=function(e,t,n){
/* Make sure attribute cannot clobber */
if(oe&&("id"===t||"name"===t)&&(n in o||n in Re))return!1;
/* Allow valid data-* attributes: At least one character after "-"
            (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)
            XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)
            We don't need to check the value; it's always URI safe. */if(W&&!q[t]&&Ry(B,t));else if(V&&Ry(P,t));else if(!z[t]||q[t]){if(
// First condition does a very basic check if a) it's basically a valid custom element tagname AND
// b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck
// and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck
!(He(e)&&(H.tagNameCheck instanceof RegExp&&Ry(H.tagNameCheck,e)||H.tagNameCheck instanceof Function&&H.tagNameCheck(e))&&(H.attributeNameCheck instanceof RegExp&&Ry(H.attributeNameCheck,t)||H.attributeNameCheck instanceof Function&&H.attributeNameCheck(t))||
// Alternative, second condition checks if it's an `is`-attribute, AND
// the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck
"is"===t&&H.allowCustomizedBuiltInElements&&(H.tagNameCheck instanceof RegExp&&Ry(H.tagNameCheck,n)||H.tagNameCheck instanceof Function&&H.tagNameCheck(n))))return!1;
/* Check value is safe. First, is attr inert? If so, is safe */}else if(me[t]);else if(Ry(I,_y(n,L,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==Sy(n,"data:")||!ce[e])if(K&&!Ry(D,_y(n,L,"")));else if(n)return!1;return!0},He=function(e){return"annotation-xml"!==e&&xy(e,M)},$e=function(e){
/* Execute a hook if present */
Ue(R.beforeSanitizeAttributes,e,null);const{attributes:t}=e;
/* Check if we have attributes; if not we might have a text node */if(!t||Ie(e))return;const o={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:z,forceKeepAttr:void 0};let r=t.length;
/* Go backwards over all attributes; safely remove bad ones */for(;r--;){const s=t[r],{name:a,namespaceURI:i,value:l}=s,d=ke(a);let c="value"===a?l:ky(l);const u=c;
/* Execute a hook if present */
/* Work around a security issue with comments inside attributes */
if(o.attrName=d,o.attrValue=c,o.keepAttr=!0,o.forceKeepAttr=void 0,// Allows developers to see this is a property they can set
Ue(R.uponSanitizeAttribute,e,o),c=o.attrValue,
/* Full DOM Clobbering protection via namespace isolation,
           * Prefix id and name attributes with `user-content-`
           */
!re||"id"!==d&&"name"!==d||(
// Remove the attribute with this value
De(a,e),
// Prefix the value and later re-create the attribute with the sanitized value
c="user-content-"+c),X&&Ry(/((--!?|])>)|<\/(style|title)/i,c)){De(a,e);continue}
/* Did the hooks approve of the attribute? */if(o.forceKeepAttr)continue;
/* Did the hooks approve of the attribute? */if(!o.keepAttr){De(a,e);continue}
/* Work around a security issue in jQuery 3.0 */if(!Y&&Ry(/\/>/i,c)){De(a,e);continue}
/* Sanitize attribute content to be template-safe */G&&hy([A,T,O],(e=>{c=_y(c,e," ")}))
/* Is `value` valid for this attribute? */;const m=ke(e.nodeName);if(je(m,d,c)){
/* Handle attributes that require Trusted Types */
if(w&&"object"==typeof g&&"function"==typeof g.getAttributeType)if(i);else switch(g.getAttributeType(m,d)){case"TrustedHTML":c=w.createHTML(c);break;case"TrustedScriptURL":c=w.createScriptURL(c)}
/* Handle invalid data-* attribute set by try-catching it */if(c!==u)try{i?e.setAttributeNS(i,a,c):
/* Fallback to setAttribute() for browser-unrecognized namespaces e.g. "x-schema". */
e.setAttribute(a,c),Ie(e)?Pe(e):vy(n.removed)}catch(e){}}else De(a,e)}
/* Execute a hook if present */Ue(R.afterSanitizeAttributes,e,null)},qe=function e(t){let n=null;const o=Me(t);
/* Execute a hook if present */for(Ue(R.beforeSanitizeShadowDOM,t,null);n=o.nextNode();)
/* Execute a hook if present */
Ue(R.uponSanitizeShadowNode,n,null),
/* Sanitize tags and elements */
ze(n),
/* Check attributes next */
$e(n),
/* Deep shadow DOM detected */
n.content instanceof a&&e(n.content);
/* Execute a hook if present */Ue(R.afterSanitizeShadowDOM,t,null)};
/**
       * _isValidAttribute
       *
       * @param lcTag Lowercase tag name of containing element.
       * @param lcName Lowercase attribute name.
       * @param value Attribute value.
       * @return Returns true if `value` is valid, otherwise false.
       */
// eslint-disable-next-line complexity
// eslint-disable-next-line complexity
return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=null,s=null,i=null,d=null;
/* Stringify, in case dirty is an object */
if(
/* Make sure we have a string to sanitize.
          DO NOT return early, as this will return the wrong type if
          the user has requested a DOM object rather than a string */
ve=!e,ve&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Fe(e)){if("function"!=typeof e.toString)throw Ay("toString is not a function");if("string"!=typeof(e=e.toString()))throw Ay("dirty is not a string, aborting")}
/* Return dirty HTML if DOMPurify cannot run */if(!n.isSupported)return e;
/* Assign config vars */if(Q||Te(t)
/* Clean up removed elements */,n.removed=[],
/* Check if dirty is correctly typed for IN_PLACE */
"string"==typeof e&&(ae=!1),ae){
/* Do some early pre-sanitization to avoid unsafe root nodes */
if(e.nodeName){const t=ke(e.nodeName);if(!F[t]||$[t])throw Ay("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)
/* If dirty is a DOM element, append to an empty document to avoid
             elements being stripped by the parser */
o=Le("\x3c!----\x3e"),s=o.ownerDocument.importNode(e,!0),1===s.nodeType&&"BODY"===s.nodeName||"HTML"===s.nodeName?
/* Node is already a body, use as is */
o=s:
// eslint-disable-next-line unicorn/prefer-dom-node-append
o.appendChild(s);else{
/* Exit directly if we have nothing to do */
if(!ee&&!G&&!Z&&
// eslint-disable-next-line unicorn/prefer-includes
-1===e.indexOf("<"))return w&&ne?w.createHTML(e):e;
/* Initialize the document to work on */
/* Check we have a DOM node from the data */
if(o=Le(e),!o)return ee?null:ne?E:""}
/* Remove first element node (ours) if FORCE_BODY is set */o&&J&&Pe(o.firstChild)
/* Get node iterator */;const c=Me(ae?e:o);
/* Now start iterating over the created document */for(;i=c.nextNode();)
/* Sanitize tags and elements */
ze(i),
/* Check attributes next */
$e(i),
/* Shadow DOM detected, sanitize it */
i.content instanceof a&&qe(i.content);
/* If we sanitized `dirty` in-place, return it. */if(ae)return e;
/* Return sanitized string or DOM */if(ee){if(te)for(d=S.call(o.ownerDocument);o.firstChild;)
// eslint-disable-next-line unicorn/prefer-dom-node-append
d.appendChild(o.firstChild);else d=o;return(z.shadowroot||z.shadowrootmode)&&(
/*
              AdoptNode() is not used because internal state is not reset
              (e.g. the past names map of a HTMLFormElement), this is safe
              in theory but we would rather not risk another attack vector.
              The state that is cloned by importNode() is explicitly defined
              by the specs.
            */
d=N.call(r,d,!0)),d}let u=Z?o.outerHTML:o.innerHTML;
/* Serialize doctype if allowed */return Z&&F["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&Ry(tC,o.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+o.ownerDocument.doctype.name+">\n"+u)
/* Sanitize final string template-safe */,G&&hy([A,T,O],(e=>{u=_y(u,e," ")})),w&&ne?w.createHTML(u):u},n.setConfig=function(){Te(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Q=!0},n.clearConfig=function(){Ne=null,Q=!1},n.isValidAttribute=function(e,t,n){
/* Initialize shared config vars if necessary. */
Ne||Te({});const o=ke(e),r=ke(t);return je(o,r,n)},n.addHook=function(e,t){"function"==typeof t&&yy(R[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=by(R[e],t);return-1===n?void 0:Cy(R[e],n,1)[0]}return vy(R[e])},n.removeHooks=function(e){R[e]=[]},n.removeAllHooks=function(){R={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}();const aC=Pt.each,iC=Pt.trim,lC=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],dC={ftp:21,http:80,https:443,mailto:25},cC=["img","video"],uC=(e,t,n)=>{const o=(e=>{try{return decodeURIComponent(e)}catch(t){return unescape(e)}})(t).replace(/\s/g,"");return!e.allow_script_urls&&(!!/((java|vb)script|mhtml):/i.test(o)||!e.allow_html_data_urls&&(/^data:image\//i.test(o)?((e,t)=>C(e)?!e:!C(t)||!H(cC,t))(e.allow_svg_data_urls,n)&&/^data:image\/svg\+xml/i.test(o):/^data:/i.test(o)))};class mC{static parseDataUri(e){let t;const n=decodeURIComponent(e).split(","),o=/data:([^;]+)/.exec(n[0]);return o&&(t=o[1]),{type:t,data:n[1]}}static isDomSafe(e,t,n={}){if(n.allow_script_urls)return!0;{const o=hs.decode(e).replace(/[\s\u0000-\u001F]+/g,"");return!uC(n,o,t)}}static getDocumentBaseUrl(e){var t;let n;return n=0!==e.protocol.indexOf("http")&&"file:"!==e.protocol?null!==(t=e.href)&&void 0!==t?t:"":e.protocol+"//"+e.host+e.pathname,/^[^:]+:\/\/\/?[^\/]+\//.test(n)&&(n=n.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(n)||(n+="/")),n}constructor(e,t={}){this.path="",this.directory="",e=iC(e),this.settings=t;const n=t.base_uri,o=this;if(/^([\w\-]+):([^\/]{2})/i.test(e)||/^\s*#/.test(e))return void(o.source=e);const r=0===e.indexOf("//");if(0!==e.indexOf("/")||r||(e=(n&&n.protocol||"http")+"://mce_host"+e),!/^[\w\-]*:?\/\//.test(e)){const t=n?n.path:new mC(document.location.href).directory;if(""===(null==n?void 0:n.protocol))e="//mce_host"+o.toAbsPath(t,e);else{const r=/([^#?]*)([#?]?.*)/.exec(e);r&&(e=(n&&n.protocol||"http")+"://mce_host"+o.toAbsPath(t,r[1])+r[2])}}e=e.replace(/@@/g,"(mce_at)");const s=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?(\[[a-zA-Z0-9:.%]+\]|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(e);s&&aC(lC,((e,t)=>{let n=s[t];n&&(n=n.replace(/\(mce_at\)/g,"@@")),o[e]=n})),n&&(o.protocol||(o.protocol=n.protocol),o.userInfo||(o.userInfo=n.userInfo),o.port||"mce_host"!==o.host||(o.port=n.port),o.host&&"mce_host"!==o.host||(o.host=n.host),o.source=""),r&&(o.protocol="")}setPath(e){const t=/^(.*?)\/?(\w+)?$/.exec(e);t&&(this.path=t[0],this.directory=t[1],this.file=t[2]),this.source="",this.getURI()}toRelative(e){if("./"===e)return e;const t=new mC(e,{base_uri:this});if("mce_host"!==t.host&&this.host!==t.host&&t.host||this.port!==t.port||this.protocol!==t.protocol&&""!==t.protocol)return t.getURI();const n=this.getURI(),o=t.getURI();if(n===o||"/"===n.charAt(n.length-1)&&n.substr(0,n.length-1)===o)return n;let r=this.toRelPath(this.path,t.path);return t.query&&(r+="?"+t.query),t.anchor&&(r+="#"+t.anchor),r}toAbsolute(e,t){const n=new mC(e,{base_uri:this});return n.getURI(t&&this.isSameOrigin(n))}isSameOrigin(e){if(this.host==e.host&&this.protocol==e.protocol){if(this.port==e.port)return!0;const t=this.protocol?dC[this.protocol]:null;if(t&&(this.port||t)==(e.port||t))return!0}return!1}toRelPath(e,t){let n,o,r=0,s="";const a=e.substring(0,e.lastIndexOf("/")).split("/"),i=t.split("/");if(a.length>=i.length)for(n=0,o=a.length;n<o;n++)if(n>=i.length||a[n]!==i[n]){r=n+1;break}if(a.length<i.length)for(n=0,o=i.length;n<o;n++)if(n>=a.length||a[n]!==i[n]){r=n+1;break}if(1===r)return t;for(n=0,o=a.length-(r-1);n<o;n++)s+="../";for(n=r-1,o=i.length;n<o;n++)s+=n!==r-1?"/"+i[n]:i[n];return s}toAbsPath(e,t){let n=0;const o=/\/$/.test(t)?"/":"",r=e.split("/"),s=t.split("/"),a=[];aC(r,(e=>{e&&a.push(e)}));const i=[];for(let e=s.length-1;e>=0;e--)0!==s[e].length&&"."!==s[e]&&(".."!==s[e]?n>0?n--:i.push(s[e]):n++);const l=a.length-n;let d;return d=l<=0?oe(i).join("/"):a.slice(0,l).join("/")+"/"+oe(i).join("/"),0!==d.indexOf("/")&&(d="/"+d),o&&d.lastIndexOf("/")!==d.length-1&&(d+=o),d}getURI(e=!1){let t;return this.source&&!e||(t="",e||(this.protocol?t+=this.protocol+"://":t+="//",this.userInfo&&(t+=this.userInfo+"@"),this.host&&(t+=this.host),this.port&&(t+=":"+this.port)),this.path&&(t+=this.path),this.query&&(t+="?"+this.query),this.anchor&&(t+="#"+this.anchor),this.source=t),this.source}}const fC=Pt.makeMap("src,href,data,background,action,formaction,poster,xlink:href"),gC="data-mce-type";let pC=0;const hC=(e,t,n,o,r)=>{var s,a,i,l;const d=t.validate,c=n.getSpecialElements();8===e.nodeType&&!t.allow_conditional_comments&&/^\[if/i.test(null!==(s=e.nodeValue)&&void 0!==s?s:"")&&(e.nodeValue=" "+e.nodeValue);const u=null!==(a=null==r?void 0:r.tagName)&&void 0!==a?a:e.nodeName.toLowerCase();if("html"!==o&&n.isValid(o))return void(C(r)&&(r.allowedTags[u]=!0));if(1!==e.nodeType||"body"===u)return;const f=yn(e),g=nn(f,gC),p=en(f,"data-mce-bogus");if(!g&&m(p))return void("all"===p?Eo(f):xo(f));const h=n.getElementRule(u);if(!d||h){if(C(r)&&(r.allowedTags[u]=!0),d&&h&&!g){if(V(null!==(i=h.attributesForced)&&void 0!==i?i:[],(e=>{Qt(f,e.name,"{$uid}"===e.value?"mce_"+pC++:e.value)})),V(null!==(l=h.attributesDefault)&&void 0!==l?l:[],(e=>{nn(f,e.name)||Qt(f,e.name,"{$uid}"===e.value?"mce_"+pC++:e.value)})),h.attributesRequired&&!$(h.attributesRequired,(e=>nn(f,e))))return void xo(f);if(h.removeEmptyAttrs&&(e=>{const t=e.dom.attributes;return null==t||0===t.length})(f))return void xo(f);h.outputName&&h.outputName!==u&&((e,t)=>{const n=((e,t)=>{const n=bn(t),o=rn(e);return Jt(n,o),n})(e,t);ho(e,n);const o=Mn(e);Co(n,o),Eo(e)})(f,h.outputName)}}else _e(c,u)?Eo(f):xo(f)},bC=(e,t,n,o,r,s)=>"html"!==n&&!Or(o)||!(r in fC&&uC(e,s,o))&&(!e.validate||t.isValid(o,r)||$e(r,"data-")||$e(r,"aria-")),vC=(e,t)=>e.hasAttribute(gC)&&("id"===t||"class"===t||"style"===t),yC=(e,t)=>e in t.getBoolAttrs(),CC=(e,t,n,o)=>{const{attributes:r}=e;for(let s=r.length-1;s>=0;s--){const a=r[s],i=a.name,l=a.value;bC(t,n,o,e.tagName.toLowerCase(),i,l)||vC(e,i)?yC(i,n)&&e.setAttribute(i,i):e.removeAttribute(i)}},wC=(e,t,n)=>{const o=sC();return o.addHook("uponSanitizeElement",((o,r)=>{hC(o,e,t,n.track(o),r)})),o.addHook("uponSanitizeAttribute",((o,r)=>{((e,t,n,o,r)=>{const s=e.tagName.toLowerCase(),{attrName:a,attrValue:i}=r;r.keepAttr=bC(t,n,o,s,a,i),r.keepAttr?(r.allowedAttributes[a]=!0,yC(a,n)&&(r.attrValue=a),t.allow_svg_data_urls&&$e(i,"data:image/svg+xml")&&(r.forceKeepAttr=!0)):vC(e,a)&&(r.forceKeepAttr=!0)})(o,e,t,n.current(),r)})),o},EC=e=>{const t=["type","href","role","arcrole","title","show","actuate","label","from","to"].map((e=>`xlink:${e}`)),n={IN_PLACE:!0,USE_PROFILES:{html:!0,svg:!0,svgFilters:!0},ALLOWED_ATTR:t};return sC().sanitize(e,n),e.innerHTML},xC=Pt.makeMap,_C=Pt.extend,SC=(e,t,n,o)=>{const r=e.name,s=r in n&&"title"!==r&&"textarea"!==r&&"noscript"!==r,a=t.childNodes;for(let t=0,r=a.length;t<r;t++){const r=a[t],i=new Zg(r.nodeName.toLowerCase(),r.nodeType);if(Jo(r)){const e=r.attributes;for(let t=0,n=e.length;t<n;t++){const n=e[t];i.attr(n.name,n.value)}Or(i.name)&&(o(r),i.value=r.innerHTML)}else lr(r)?(i.value=r.data,s&&(i.raw=!0)):(ur(r)||dr(r)||cr(r))&&(i.value=r.data);Or(i.name)||SC(i,r,n,o),e.append(i)}},kC=(e={},t=Ps())=>{const n=Kv(),o=Kv(),r={validate:!0,root_name:"body",sanitize:!0,...e},s=new DOMParser,a=((e,t)=>{const n=(()=>{let e=[];const t=()=>e[e.length-1];return{track:n=>{Br(n)&&e.push(n);let o=t();return o&&!o.contains(n)&&(e.pop(),o=t()),Pr(o)},current:()=>Pr(t()),reset:()=>{e=[]}}})();if(e.sanitize){const o=wC(e,t,n),r=(t,r)=>{o.sanitize(t,((e,t)=>{const n={IN_PLACE:!0,ALLOW_UNKNOWN_PROTOCOLS:!0,ALLOWED_TAGS:["#comment","#cdata-section","body"],ALLOWED_ATTR:[],SAFE_FOR_XML:!1};return n.PARSER_MEDIA_TYPE=t,e.allow_script_urls?n.ALLOWED_URI_REGEXP=/.*/:e.allow_html_data_urls&&(n.ALLOWED_URI_REGEXP=/^(?!(\w+script|mhtml):)/i),n})(e,r)),o.removed=[],n.reset()};return{sanitizeHtmlElement:r,sanitizeNamespaceElement:EC}}return{sanitizeHtmlElement:(o,r)=>{const s=document.createNodeIterator(o,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_COMMENT|NodeFilter.SHOW_TEXT);let a;for(;a=s.nextNode();){const o=n.track(a);hC(a,e,t,o),Jo(a)&&CC(a,e,t,o)}n.reset()},sanitizeNamespaceElement:_}})(r,t),i=n.addFilter,l=n.getFilters,d=n.removeFilter,c=o.addFilter,u=o.getFilters,f=o.removeFilter,g=(e,n)=>{const o=m(n.attr(gC)),r=1===n.type&&!_e(e,n.name)&&!Yr(t,n)&&!Or(n.name);return 3===n.type||r&&!o},p={schema:t,addAttributeFilter:c,getAttributeFilters:u,removeAttributeFilter:f,addNodeFilter:i,getNodeFilters:l,removeNodeFilter:d,parse:(e,n={})=>{var o;const i=r.validate,d=null!==(o=n.context)&&void 0!==o?o:r.root_name,c=((e,n,o="html")=>{const r="xhtml"===o?"application/xhtml+xml":"text/html",i=_e(t.getSpecialElements(),n.toLowerCase()),l=i?`<${n}>${e}</${n}>`:e,d="xhtml"===o?`<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>${l}</body></html>`:`<body>${l}</body>`,c=s.parseFromString(d,r).body;return a.sanitizeHtmlElement(c,r),i?c.firstChild:c})(e,d,n.format);jr(t,c);const m=new Zg(d,11);SC(m,c,t.getSpecialElements(),a.sanitizeNamespaceElement),c.innerHTML="";const[f,p]=((e,t,n,o)=>{const r=n.validate,s=t.getNonEmptyElements(),a=t.getWhitespaceElements(),i=_C(xC("script,style,head,html,body,title,meta,param"),t.getBlockElements()),l=Os(t),d=/[ \t\r\n]+/g,c=/^[ \t\r\n]+/,u=/[ \t\r\n]+$/,m=e=>{let t=e.parent;for(;C(t);){if(t.name in a)return!0;t=t.parent}return!1},f=n=>n.name in i||Yr(t,n)||Or(n.name)&&n.parent===e,g=(t,n)=>{const r=n?t.prev:t.next;return!C(r)&&!y(t.parent)&&f(t.parent)&&(t.parent!==e||!0===o.isRootContent)};return[e=>{var t;if(3===e.type&&!m(e)){let n=null!==(t=e.value)&&void 0!==t?t:"";n=n.replace(d," "),(((e,t)=>C(e)&&(t(e)||"br"===e.name))(e.prev,f)||g(e,!0))&&(n=n.replace(c,"")),0===n.length?e.remove():e.value=n}},e=>{var i;if(1===e.type){const i=t.getElementRule(e.name);if(r&&i){const r=wb(t,s,a,e);i.paddInEmptyBlock&&r&&(e=>{let n=e;for(;C(n);){if(n.name in l)return wb(t,s,a,n);n=n.parent}return!1})(e)?yb(n,o,f,e):i.removeEmpty&&r?f(e)?e.remove():e.unwrap():i.paddEmpty&&(r||(e=>{var t;return Cb(e,"#text")&&(null===(t=null==e?void 0:e.firstChild)||void 0===t?void 0:t.value)===Vo})(e))&&yb(n,o,f,e)}}else if(3===e.type&&!m(e)){let t=null!==(i=e.value)&&void 0!==i?i:"";(e.next&&f(e.next)||g(e,!1))&&(t=t.replace(u,"")),0===t.length?e.remove():e.value=t}}]})(m,t,r,n),h=[],b=i?e=>((e,n)=>{Sb(t,e)&&n.push(e)})(e,h):_,v={nodes:{},attributes:{}},w=e=>hb(l(),u(),e,v);if(((e,t,n)=>{const o=[];for(let n=e,r=n;n;r=n,n=n.walk()){const s=n;V(t,(e=>e(s))),y(s.parent)&&s!==e?n=r:o.push(s)}for(let e=o.length-1;e>=0;e--){const t=o[e];V(n,(e=>e(t)))}})(m,[f,w],[p,b]),h.reverse(),i&&h.length>0)if(n.context){const{pass:e,fail:o}=K(h,(e=>e.parent===m));_b(o,t,m,w),n.invalid=e.length>0}else _b(h,t,m,w);const E=((e,t)=>{var n;const o=null!==(n=t.forced_root_block)&&void 0!==n?n:e.forced_root_block;return!1===o?"":!0===o?"p":o})(r,n);return E&&("body"===m.name||n.isRootContent)&&((e,n)=>{const o=_C(xC("script,style,head,html,body,title,meta,param"),t.getBlockElements()),s=/^[ \t\r\n]+/,a=/[ \t\r\n]+$/;let i=e.firstChild,l=null;const d=e=>{var t,n;e&&(i=e.firstChild,i&&3===i.type&&(i.value=null===(t=i.value)||void 0===t?void 0:t.replace(s,"")),i=e.lastChild,i&&3===i.type&&(i.value=null===(n=i.value)||void 0===n?void 0:n.replace(a,"")))};if(t.isValidChild(e.name,n.toLowerCase())){for(;i;){const t=i.next;g(o,i)?(l||(l=new Zg(n,1),l.attr(r.forced_root_block_attrs),e.insert(l,i)),l.append(i)):(d(l),l=null),i=t}d(l)}})(m,E),n.invalid||bb(v,n),m}};return((e,t)=>{var n,o;const r=e.schema;e.addAttributeFilter("href",(e=>{let n=e.length;const o=e=>{const t=e?Pt.trim(e):"";return/\b(noopener)\b/g.test(t)?t:(e=>e.split(" ").filter((e=>e.length>0)).concat(["noopener"]).sort().join(" "))(t)};if(!t.allow_unsafe_link_target)for(;n--;){const t=e[n];"a"===t.name&&"_blank"===t.attr("target")&&t.attr("rel",o(t.attr("rel")))}})),t.allow_html_in_named_anchor||e.addAttributeFilter("id,name",(e=>{let t,n,o,r,s=e.length;for(;s--;)if(r=e[s],"a"===r.name&&r.firstChild&&!r.attr("href"))for(o=r.parent,t=r.lastChild;t&&o;)n=t.prev,o.insert(t,r),t=n})),t.fix_list_elements&&e.addNodeFilter("ul,ol",(e=>{let t,n,o=e.length;for(;o--;)if(t=e[o],n=t.parent,n&&("ul"===n.name||"ol"===n.name))if(t.prev&&"li"===t.prev.name)t.prev.append(t);else{const e=new Zg("li",1);e.attr("style","list-style-type: none"),t.wrap(e)}}));const s=r.getValidClasses();t.validate&&s&&e.addAttributeFilter("class",(e=>{var t;let n=e.length;for(;n--;){const o=e[n],r=null!==(t=o.attr("class"))&&void 0!==t?t:"",a=Pt.explode(r," ");let i="";for(let e=0;e<a.length;e++){const t=a[e];let n=!1,r=s["*"];r&&r[t]&&(n=!0),r=s[o.name],!n&&r&&r[t]&&(n=!0),n&&(i&&(i+=" "),i+=t)}i.length||(i=null),o.attr("class",i)}})),((e,t)=>{const{blob_cache:n}=t;if(n){const t=e=>{const t=e.attr("src");(e=>e.attr("src")===Tt.transparentSrc||C(e.attr("data-mce-placeholder")))(e)||(e=>C(e.attr("data-mce-bogus")))(e)||y(t)||ty(n,t,!0).each((t=>{e.attr("src",t.blobUri())}))};e.addAttributeFilter("src",(e=>V(e,t)))}})(e,t);const a=null!==(n=t.sandbox_iframes)&&void 0!==n&&n,i=me(null!==(o=t.sandbox_iframes_exclusions)&&void 0!==o?o:[]);t.convert_unsafe_embeds&&e.addNodeFilter("object,embed",(e=>V(e,(e=>{e.replace((({type:e,src:t,width:n,height:o}={},r,s)=>{const a=(e=>v(e)?"iframe":sy(e,"image")?"img":sy(e,"video")?"video":sy(e,"audio")?"audio":"iframe")(e),i=new Zg(a,1);return i.attr("audio"===a?{src:t}:{src:t,width:n,height:o}),"audio"!==a&&"video"!==a||i.attr("controls",""),"iframe"===a&&r&&ry(i,s),i})({type:e.attr("type"),src:"object"===e.name?e.attr("data"):e.attr("src"),width:e.attr("width"),height:e.attr("height")},a,i))})))),a&&e.addNodeFilter("iframe",(e=>V(e,(e=>ry(e,i)))))})(p,r),((e,t,n)=>{t.inline_styles&&Yv(e,t,n)})(p,r,t),p},NC=(e,t,n)=>{const o=(e=>Lb(e)?hp({validate:!1}).serialize(e):e)(e),r=t(o);if(r.isDefaultPrevented())return r;if(Lb(e)){if(r.content!==o){const t=kC({validate:!1,forced_root_block:!1,...n}).parse(r.content,{context:e.name});return{...r,content:t}}return{...r,content:e}}return r},RC=e=>({sanitize:Cc(e),sandbox_iframes:Nc(e),sandbox_iframes_exclusions:Rc(e)}),AC=(e,t)=>{if(t.no_events)return wl.value(t);{const n=((e,t)=>e.dispatch("BeforeGetContent",t))(e,t);return n.isDefaultPrevented()?wl.error(gf(e,{content:"",...n}).content):wl.value(n)}},TC=(e,t,n)=>{if(n.no_events)return t;{const o=NC(t,(t=>gf(e,{...n,content:t})),RC(e));return o.content}},OC=(e,t)=>{if(t.no_events)return wl.value(t);{const n=NC(t.content,(n=>((e,t)=>e.dispatch("BeforeSetContent",t))(e,{...t,content:n})),RC(e));return n.isDefaultPrevented()?(ff(e,n),wl.error(void 0)):wl.value(n)}},BC=(e,t,n)=>{n.no_events||ff(e,{...n,content:t})},PC=(e,t,n)=>({element:e,width:t,rows:n}),DC=(e,t)=>({element:e,cells:t}),LC=(e,t)=>({x:e,y:t}),MC=(e,t)=>tn(e,t).bind(Ze).getOr(1),IC=(e,t,n)=>{const o=e.rows;return!!(o[n]?o[n].cells:[])[t]},FC=e=>X(e,((e,t)=>t.cells.length>e?t.cells.length:e),0),UC=(e,t)=>{const n=e.rows;for(let e=0;e<n.length;e++){const o=n[e].cells;for(let n=0;n<o.length;n++)if(_n(o[n],t))return I.some(LC(n,e))}return I.none()},zC=(e,t,n,o,r)=>{const s=[],a=e.rows;for(let e=n;e<=r;e++){const n=a[e].cells,r=t<o?n.slice(t,o+1):n.slice(o,t+1);s.push(DC(a[e].element,r))}return s},jC=e=>((e,t)=>{const n=La(e.element),o=bn("tbody");return Co(o,t),vo(n,o),n})(e,(e=>q(e.rows,(e=>{const t=q(e.cells,(e=>{const t=Ma(e);return on(t,"colspan"),on(t,"rowspan"),t})),n=La(e.element);return Co(n,t),n})))(e)),HC=(e,t,n)=>{const o=yn(t.commonAncestorContainer),r=Ip(o,e),s=Y(r,(e=>n.isWrapper(Ht(e)))),a=((e,t)=>Q(e,(e=>"li"===Ht(e)&&fm(e,t))).fold(N([]),(t=>(e=>Q(e,(e=>"ul"===Ht(e)||"ol"===Ht(e))))(e).map((e=>{const t=bn(Ht(e)),n=Ce(fo(e),((e,t)=>$e(t,"list-style")));return lo(t,n),[bn("li"),t]})).getOr([]))))(r,t),i=s.concat(a.length?a:(e=>Va(e)?An(e).filter(qa).fold(N([]),(t=>[e,t])):qa(e)?[e]:[])(o));return q(i,La)},$C=()=>Mf([]),qC=(e,t)=>((e,t)=>eo(t,"table",T(_n,e)))(e,t[0]).bind((e=>{const n=t[0],o=t[t.length-1],r=(e=>{const t=PC(La(e),0,[]);return V(Uo(e,"tr"),((e,n)=>{V(Uo(e,"td,th"),((o,r)=>{((e,t,n,o,r)=>{const s=MC(r,"rowspan"),a=MC(r,"colspan"),i=e.rows;for(let e=n;e<n+s;e++){i[e]||(i[e]=DC(Ma(o),[]));for(let o=t;o<t+a;o++)i[e].cells[o]=e===n&&o===t?r:La(r)}})(t,((e,t,n)=>{for(;IC(e,t,n);)t++;return t})(t,r,n),n,e,o)}))})),PC(t.element,FC(t.rows),t.rows)})(e);return((e,t,n)=>UC(e,t).bind((t=>UC(e,n).map((n=>((e,t,n)=>{const o=t.x,r=t.y,s=n.x,a=n.y,i=r<a?zC(e,o,r,s,a):zC(e,o,a,s,r);return PC(e.element,FC(i),i)})(e,t,n))))))(r,n,o).map((e=>Mf([jC(e)])))})).getOrThunk($C),VC=(e,t,n)=>{const o=lm(t,e);return o.length>0?qC(e,o):((e,t,n)=>t.length>0&&t[0].collapsed?$C():((e,t,n)=>((e,t)=>{const n=X(t,((e,t)=>(vo(t,e),t)),e);return t.length>0?Mf([n]):n})(yn(t.cloneContents()),HC(e,t,n)))(e,t[0],n))(e,t,n)},WC=(e,t)=>t>=0&&t<e.length&&sm(e.charAt(t)),KC=e=>Ja(e.innerText),YC=e=>Jo(e)?e.outerHTML:lr(e)?hs.encodeRaw(e.data,!1):ur(e)?"\x3c!--"+e.data+"--\x3e":"",GC=(e,t)=>(((e,t)=>{let n=0;V(e,(e=>{0===e[0]?n++:1===e[0]?(((e,t,n)=>{const o=(e=>{let t;const n=document.createElement("div"),o=document.createDocumentFragment();for(e&&(n.innerHTML=e);t=n.firstChild;)o.appendChild(t);return o})(t);if(e.hasChildNodes()&&n<e.childNodes.length){const t=e.childNodes[n];e.insertBefore(o,t)}else e.appendChild(o)})(t,e[1],n),n++):2===e[0]&&((e,t)=>{if(e.hasChildNodes()&&t<e.childNodes.length){const n=e.childNodes[t];e.removeChild(n)}})(t,n)}))})(((e,t)=>{const n=e.length+t.length+2,o=new Array(n),r=new Array(n),s=(n,o,r,a,l)=>{const d=i(n,o,r,a);if(null===d||d.start===o&&d.diag===o-a||d.end===n&&d.diag===n-r){let s=n,i=r;for(;s<o||i<a;)s<o&&i<a&&e[s]===t[i]?(l.push([0,e[s]]),++s,++i):o-n>a-r?(l.push([2,e[s]]),++s):(l.push([1,t[i]]),++i)}else{s(n,d.start,r,d.start-d.diag,l);for(let t=d.start;t<d.end;++t)l.push([0,e[t]]);s(d.end,o,d.end-d.diag,a,l)}},a=(n,o,r,s)=>{let a=n;for(;a-o<s&&a<r&&e[a]===t[a-o];)++a;return((e,t,n)=>({start:e,end:t,diag:n}))(n,a,o)},i=(n,s,i,l)=>{const d=s-n,c=l-i;if(0===d||0===c)return null;const u=d-c,m=c+d,f=(m%2==0?m:m+1)/2;let g,p,h,b,v;for(o[1+f]=n,r[1+f]=s+1,g=0;g<=f;++g){for(p=-g;p<=g;p+=2){for(h=p+f,p===-g||p!==g&&o[h-1]<o[h+1]?o[h]=o[h+1]:o[h]=o[h-1]+1,b=o[h],v=b-n+i-p;b<s&&v<l&&e[b]===t[v];)o[h]=++b,++v;if(u%2!=0&&u-g<=p&&p<=u+g&&r[h-u]<=o[h])return a(r[h-u],p+n-i,s,l)}for(p=u-g;p<=u+g;p+=2){for(h=p+f-u,p===u-g||p!==u+g&&r[h+1]<=r[h-1]?r[h]=r[h+1]-1:r[h]=r[h-1],b=r[h]-1,v=b-n+i-p;b>=n&&v>=i&&e[b]===t[v];)r[h]=b--,v--;if(u%2==0&&-g<=p&&p<=g&&r[h]<=o[h+u])return a(r[h],p+n-i,s,l)}}return null},l=[];return s(0,e.length,0,t.length,l),l})(q(ce(t.childNodes),YC),e),t),t),XC=Le((()=>document.implementation.createHTMLDocument("undo"))),ZC=e=>{const t=e.serializer.getTempAttrs(),n=cp(e.getBody(),t);return(e=>null!==e.querySelector("iframe"))(n)?{type:"fragmented",fragments:Y(q(ce(n.childNodes),S(Ja,YC)),(e=>e.length>0)),content:"",bookmark:null,beforeBookmark:null}:{type:"complete",fragments:null,content:Ja(n.innerHTML),bookmark:null,beforeBookmark:null}},QC=(e,t,n)=>{const o=n?t.beforeBookmark:t.bookmark;"fragmented"===t.type?GC(t.fragments,e.getBody()):e.setContent(t.content,{format:"raw",no_selection:!C(o)||!Wu(o)||!o.isFakeCaret}),o&&(e.selection.moveToBookmark(o),e.selection.scrollIntoView())},JC=e=>"fragmented"===e.type?e.fragments.join(""):e.content,ew=e=>{const t=bn("body",XC());return ko(t,JC(e)),V(Uo(t,"*[data-mce-bogus]"),xo),So(t)},tw=(e,t)=>!(!e||!t)&&(!!((e,t)=>JC(e)===JC(t))(e,t)||((e,t)=>ew(e)===ew(t))(e,t)),nw=e=>0===e.get(),ow=(e,t,n)=>{nw(n)&&(e.typing=t)},rw=(e,t)=>{e.typing&&(ow(e,!1,t),e.add())},sw=e=>({init:{bindEvents:_},undoManager:{beforeChange:(t,n)=>((e,t,n)=>{nw(t)&&n.set(vl(e.selection))})(e,t,n),add:(t,n,o,r,s,a)=>((e,t,n,o,r,s,a)=>{const i=ZC(e),l=Pt.extend(s||{},i);if(!nw(o)||e.removed)return null;const d=t.data[n.get()];if(e.dispatch("BeforeAddUndo",{level:l,lastLevel:d,originalEvent:a}).isDefaultPrevented())return null;if(d&&tw(d,l))return null;t.data[n.get()]&&r.get().each((e=>{t.data[n.get()].beforeBookmark=e}));const c=Id(e);if(c&&t.data.length>c){for(let e=0;e<t.data.length-1;e++)t.data[e]=t.data[e+1];t.data.length--,n.set(t.data.length)}l.bookmark=vl(e.selection),n.get()<t.data.length-1&&(t.data.length=n.get()+1),t.data.push(l),n.set(t.data.length-1);const u={level:l,lastLevel:d,originalEvent:a};return n.get()>0?(e.setDirty(!0),e.dispatch("AddUndo",u),e.dispatch("change",u)):e.dispatch("AddUndo",u),l})(e,t,n,o,r,s,a),undo:(t,n,o)=>((e,t,n,o)=>{let r;return t.typing&&(t.add(),t.typing=!1,ow(t,!1,n)),o.get()>0&&(o.set(o.get()-1),r=t.data[o.get()],QC(e,r,!0),e.setDirty(!0),e.dispatch("Undo",{level:r})),r})(e,t,n,o),redo:(t,n)=>((e,t,n)=>{let o;return t.get()<n.length-1&&(t.set(t.get()+1),o=n[t.get()],QC(e,o,!1),e.setDirty(!0),e.dispatch("Redo",{level:o})),o})(e,t,n),clear:(t,n)=>((e,t,n)=>{t.data=[],n.set(0),t.typing=!1,e.dispatch("ClearUndos")})(e,t,n),reset:e=>(e=>{e.clear(),e.add()})(e),hasUndo:(t,n)=>((e,t,n)=>n.get()>0||t.typing&&t.data[0]&&!tw(ZC(e),t.data[0]))(e,t,n),hasRedo:(e,t)=>((e,t)=>t.get()<e.data.length-1&&!e.typing)(e,t),transact:(e,t,n)=>((e,t,n)=>(rw(e,t),e.beforeChange(),e.ignore(n),e.add()))(e,t,n),ignore:(e,t)=>((e,t)=>{try{e.set(e.get()+1),t()}finally{e.set(e.get()-1)}})(e,t),extra:(t,n,o,r)=>((e,t,n,o,r)=>{if(t.transact(o)){const o=t.data[n.get()].bookmark,s=t.data[n.get()-1];QC(e,s,!0),t.transact(r)&&(t.data[n.get()-1].beforeBookmark=o)}})(e,t,n,o,r)},formatter:{match:(t,n,o,r)=>Vb(e,t,n,o,r),matchAll:(t,n)=>((e,t,n)=>{const o=[],r={},s=e.selection.getStart();return e.dom.getParent(s,(s=>{for(let a=0;a<t.length;a++){const i=t[a];!r[i]&&qb(e,s,i,n)&&(r[i]=!0,o.push(i))}}),e.dom.getRoot()),o})(e,t,n),matchNode:(t,n,o,r)=>qb(e,t,n,o,r),canApply:t=>((e,t)=>{const n=e.formatter.get(t),o=e.dom;if(n&&e.selection.isEditable()){const t=e.selection.getStart(),r=Bm(o,t);for(let e=n.length-1;e>=0;e--){const t=n[e];if(!Lm(t))return!0;for(let e=r.length-1;e>=0;e--)if(o.is(r[e],t.selector))return!0}}return!1})(e,t),closest:t=>((e,t)=>{const n=t=>_n(t,yn(e.getBody()));return I.from(e.selection.getStart(!0)).bind((o=>Fb(yn(o),(n=>ue(t,(t=>((t,n)=>qb(e,t.dom,n)?I.some(n):I.none())(n,t)))),n))).getOrNull()})(e,t),apply:(t,n,o)=>zv(e,t,n,o),remove:(t,n,o,r)=>Dv(e,t,n,o,r),toggle:(t,n,o)=>((e,t,n,o)=>{const r=e.formatter.get(t);r&&(!Vb(e,t,n,o)||"toggle"in r[0]&&!r[0].toggle?zv(e,t,n,o):Dv(e,t,n,o))})(e,t,n,o),formatChanged:(t,n,o,r,s)=>((e,t,n,o,r,s)=>(((e,t,n,o,r,s)=>{const a=t.get();V(n.split(","),(t=>{const n=xe(a,t).getOrThunk((()=>{const e={withSimilar:{state:ua(!1),similar:!0,callbacks:[]},withoutSimilar:{state:ua(!1),similar:!1,callbacks:[]},withVars:[]};return a[t]=e,e})),i=()=>{const n=qv(e);return $v(e,n,t,r,s).isSome()};if(v(s)){const e=r?n.withSimilar:n.withoutSimilar;e.callbacks.push(o),1===e.callbacks.length&&e.state.set(i())}else n.withVars.push({state:ua(i()),similar:r,vars:s,callback:o})})),t.set(a)})(e,t,n,o,r,s),{unbind:()=>((e,t,n)=>{const o=e.get();V(t.split(","),(e=>xe(o,e).each((t=>{o[e]={withSimilar:{...t.withSimilar,callbacks:Y(t.withSimilar.callbacks,(e=>e!==n))},withoutSimilar:{...t.withoutSimilar,callbacks:Y(t.withoutSimilar.callbacks,(e=>e!==n))},withVars:Y(t.withVars,(e=>e.callback!==n))}})))),e.set(o)})(t,n,o)}))(e,t,n,o,r,s)},editor:{getContent:t=>((e,t)=>I.from(e.getBody()).fold(N("tree"===t.format?new Zg("body",11):""),(n=>fp(e,t,n))))(e,t),setContent:(t,n)=>((e,t,n)=>I.from(e.getBody()).map((o=>Lb(t)?((e,t,n,o)=>{vb(e.parser.getNodeFilters(),e.parser.getAttributeFilters(),n);const r=hp({validate:!1},e.schema).serialize(n),s=Ja(Ya(yn(t))?r:Pt.trim(r));return Mb(e,s,o.no_selection),{content:n,html:s}})(e,o,t,n):((e,t,n,o)=>{if(0===(n=Ja(n)).length||/^\s+$/.test(n)){const r='<br data-mce-bogus="1">';"TABLE"===t.nodeName?n="<tr><td>"+r+"</td></tr>":/^(UL|OL)$/.test(t.nodeName)&&(n="<li>"+r+"</li>");const s=ql(e);return e.schema.isValidChild(t.nodeName.toLowerCase(),s.toLowerCase())?(n=r,n=e.dom.createHTML(s,Vl(e),n)):n||(n=r),Mb(e,n,o.no_selection),{content:n,html:n}}{"raw"!==o.format&&(n=hp({validate:!1},e.schema).serialize(e.parser.parse(n,{isRootContent:!0,insert:!0})));const r=Ya(yn(t))?n:Pt.trim(n);return Mb(e,r,o.no_selection),{content:r,html:r}}})(e,o,t,n))).getOr({content:t,html:Lb(n.content)?"":n.content}))(e,t,n),insertContent:(t,n)=>Db(e,t,n),addVisual:t=>((e,t)=>{const n=e.dom,o=C(t)?t:e.getBody();V(n.select("table,a",o),(t=>{switch(t.nodeName){case"TABLE":const o=Wd(e),r=n.getAttrib(t,"border");r&&"0"!==r||!e.hasVisual?n.removeClass(t,o):n.addClass(t,o);break;case"A":if(!n.getAttrib(t,"href")){const o=n.getAttrib(t,"name")||t.id,r=Kd(e);o&&e.hasVisual?n.addClass(t,r):n.removeClass(t,r)}}})),e.dispatch("VisualAid",{element:t,hasVisual:e.hasVisual})})(e,t)},selection:{getContent:(t,n)=>((e,t,n={})=>{const o=((e,t)=>({...e,format:t,get:!0,selection:!0,getInner:!0}))(n,t);return AC(e,o).fold(R,(t=>{const n=((e,t)=>{if("text"===t.format)return(e=>I.from(e.selection.getRng()).map((t=>{var n;const o=I.from(e.dom.getParent(t.commonAncestorContainer,e.dom.isBlock)),r=e.getBody(),s=(e=>e.map((e=>e.nodeName)).getOr("div").toLowerCase())(o),a=yn(t.cloneContents());up(a),mp(a);const i=e.dom.add(r,s,{"data-mce-bogus":"all",style:"overflow: hidden; opacity: 0;"},a.dom),l=KC(i),d=Ja(null!==(n=i.textContent)&&void 0!==n?n:"");if(e.dom.remove(i),WC(d,0)||WC(d,d.length-1)){const e=o.getOr(r),t=KC(e),n=t.indexOf(l);return-1===n?l:(WC(t,n-1)?" ":"")+l+(WC(t,n+l.length)?" ":"")}return l})).getOr(""))(e);{const n=((e,t)=>{const n=e.selection.getRng(),o=e.dom.create("body"),r=e.selection.getSel(),s=Wg(e,im(r)),a=t.contextual?VC(yn(e.getBody()),s,e.schema).dom:n.cloneContents();return a&&o.appendChild(a),e.selection.serializer.serialize(o,t)})(e,t);return"tree"===t.format?n:e.selection.isCollapsed()?"":n}})(e,t);return TC(e,n,t)}))})(e,t,n)},autocompleter:{addDecoration:_,removeDecoration:_},raw:{getModel:()=>I.none()}}),aw=e=>_e(e.plugins,"rtc"),iw=e=>e.rtcInstance?e.rtcInstance:sw(e),lw=e=>{const t=e.rtcInstance;if(t)return t;throw new Error("Failed to get RTC instance not yet initialized.")},dw=e=>lw(e).init.bindEvents(),cw=e=>0===e.dom.length?(Eo(e),I.none()):I.some(e),uw=(e,t,n,o,r)=>{e.bind((e=>((o?ph:gh)(e.dom,o?e.dom.length:0,r),t.filter(Kt).map((t=>((e,t,n,o,r)=>{const s=e.dom,a=t.dom,i=o?s.length:a.length;o?(hh(s,a,r,!1,!o),n.setStart(a,i)):(hh(a,s,r,!1,!o),n.setEnd(a,i))})(e,t,n,o,r)))))).orThunk((()=>{const e=((e,t)=>e.filter((e=>lf.isBookmarkNode(e.dom))).bind(t?Pn:Bn))(t,o).or(t).filter(Kt);return e.map((e=>((e,t,n)=>{An(e).each((o=>{const r=e.dom;t&&sh(o,Zi(r,0),n)?gh(r,0,n):!t&&ah(o,Zi(r,r.length),n)&&ph(r,r.length,n)}))})(e,o,r)))}))},mw=(e,t,n)=>{if(_e(e,t)){const o=Y(e[t],(e=>e!==n));0===o.length?delete e[t]:e[t]=o}};const fw=e=>!(!e||!e.ownerDocument)&&Sn(yn(e.ownerDocument),yn(e)),gw=(e,t,n,o)=>{let r,s;const{selectorChangedWithUnbind:a}=((e,t)=>{let n,o;const r=(t,n)=>Q(n,(n=>e.is(n,t))),s=t=>e.getParents(t,void 0,e.getRoot());return{selectorChangedWithUnbind:(e,a)=>(n||(n={},o={},t.on("NodeChange",(e=>{const t=e.element,a=s(t),i={};pe(n,((e,t)=>{r(t,a).each((n=>{o[t]||(V(e,(e=>{e(!0,{node:n,selector:t,parents:a})})),o[t]=e),i[t]=e}))})),pe(o,((e,n)=>{i[n]||(delete o[n],V(e,(e=>{e(!1,{node:t,selector:n,parents:a})})))}))}))),n[e]||(n[e]=[]),n[e].push(a),r(e,s(t.selection.getStart())).each((()=>{o[e]=n[e]})),{unbind:()=>{mw(n,e,a),mw(o,e,a)}})}})(e,o),i=(e,t)=>((e,t,n={})=>{const o=((e,t)=>({format:"html",...e,set:!0,selection:!0,content:t}))(n,t);OC(e,o).each((t=>{const n=((e,t)=>{if("raw"!==t.format){const n=e.selection.getRng(),o=e.dom.getParent(n.commonAncestorContainer,e.dom.isBlock),r=o?{context:o.nodeName.toLowerCase()}:{},s=e.parser.parse(t.content,{forced_root_block:!1,...r,...t});return hp({validate:!1},e.schema).serialize(s)}return t.content})(e,t),o=e.selection.getRng();((e,t,n)=>{const o=I.from(t.firstChild).map(yn),r=I.from(t.lastChild).map(yn);e.deleteContents(),e.insertNode(t);const s=o.bind(Bn).filter(Kt).bind(cw),a=r.bind(Pn).filter(Kt).bind(cw);uw(s,o,e,!0,n),uw(a,r,e,!1,n),e.collapse(!1)})(o,o.createContextualFragment(n),e.schema),e.selection.setRng(o),pg(e,o),BC(e,n,t)}))})(o,e,t),l=e=>{const t=c();t.collapse(!!e),u(t)},d=()=>t.getSelection?t.getSelection():t.document.selection,c=()=>{let n;const a=(e,t,n)=>{try{return t.compareBoundaryPoints(e,n)}catch(e){return-1}},i=t.document;if(C(o.bookmark)&&!Fg(o)){const e=kg(o);if(e.isSome())return e.map((e=>Wg(o,[e])[0])).getOr(i.createRange())}try{const e=d();e&&!Qo(e.anchorNode)&&(n=e.rangeCount>0?e.getRangeAt(0):i.createRange(),n=Wg(o,[n])[0])}catch(e){}if(n||(n=i.createRange()),mr(n.startContainer)&&n.collapsed){const t=e.getRoot();n.setStart(t,0),n.setEnd(t,0)}return r&&s&&(0===a(n.START_TO_START,n,r)&&0===a(n.END_TO_END,n,r)?n=s:(r=null,s=null)),n},u=(e,t)=>{if(!(e=>!!e&&fw(e.startContainer)&&fw(e.endContainer))(e))return;const n=d();if(e=o.dispatch("SetSelectionRange",{range:e,forward:t}).range,n){s=e;try{n.removeAllRanges(),n.addRange(e)}catch(e){}!1===t&&n.extend&&(n.collapse(e.endContainer,e.endOffset),n.extend(e.startContainer,e.startOffset)),r=n.rangeCount>0?n.getRangeAt(0):null}if(!e.collapsed&&e.startContainer===e.endContainer&&(null==n?void 0:n.setBaseAndExtent)&&e.endOffset-e.startOffset<2&&e.startContainer.hasChildNodes()){const t=e.startContainer.childNodes[e.startOffset];t&&"IMG"===t.nodeName&&(n.setBaseAndExtent(e.startContainer,e.startOffset,e.endContainer,e.endOffset),n.anchorNode===e.startContainer&&n.focusNode===e.endContainer||n.setBaseAndExtent(t,0,t,1))}o.dispatch("AfterSetSelectionRange",{range:e,forward:t})},m=()=>{const t=d(),n=null==t?void 0:t.anchorNode,o=null==t?void 0:t.focusNode;if(!t||!n||!o||Qo(n)||Qo(o))return!0;const r=e.createRng(),s=e.createRng();try{r.setStart(n,t.anchorOffset),r.collapse(!0),s.setStart(o,t.focusOffset),s.collapse(!0)}catch(e){return!0}return r.compareBoundaryPoints(r.START_TO_START,s)<=0},f={dom:e,win:t,serializer:n,editor:o,expand:(t={type:"word"})=>u(Gf(e).expand(c(),t)),collapse:l,setCursorLocation:(t,n)=>{const r=e.createRng();C(t)&&C(n)?(r.setStart(t,n),r.setEnd(t,n),u(r),l(!1)):(gm(e,r,o.getBody(),!0),u(r))},getContent:e=>((e,t={})=>((e,t,n)=>lw(e).selection.getContent(t,n))(e,t.format?t.format:"html",t))(o,e),setContent:i,getBookmark:(e,t)=>g.getBookmark(e,t),moveToBookmark:e=>g.moveToBookmark(e),select:(t,n)=>(((e,t,n)=>I.from(t).bind((t=>I.from(t.parentNode).map((o=>{const r=e.nodeIndex(t),s=e.createRng();return s.setStart(o,r),s.setEnd(o,r+1),n&&(gm(e,s,t,!0),gm(e,s,t,!1)),s})))))(e,t,n).each(u),t),isCollapsed:()=>{const e=c(),t=d();return!(!e||e.item)&&(e.compareEndPoints?0===e.compareEndPoints("StartToEnd",e):!t||e.collapsed)},isEditable:()=>{const t=c(),n=o.getBody().querySelectorAll('[data-mce-selected="1"]');return n.length>0?ne(n,(t=>e.isEditable(t.parentElement))):jg(e,t)},isForward:m,setNode:t=>(i(e.getOuterHTML(t)),t),getNode:()=>((e,t)=>{if(!t)return e;let n=t.startContainer,o=t.endContainer;const r=t.startOffset,s=t.endOffset;let a=t.commonAncestorContainer;t.collapsed||(n===o&&s-r<2&&n.hasChildNodes()&&(a=n.childNodes[r]),lr(n)&&lr(o)&&(n=n.length===r?Vg(n.nextSibling,!0):n.parentNode,o=0===s?Vg(o.previousSibling,!1):o.parentNode,n&&n===o&&(a=n)));const i=lr(a)?a.parentNode:a;return er(i)?i:e})(o.getBody(),c()),getSel:d,setRng:u,getRng:c,getStart:e=>$g(o.getBody(),c(),e),getEnd:e=>qg(o.getBody(),c(),e),getSelectedBlocks:(t,n)=>((e,t,n,o)=>{const r=[],s=e.getRoot(),a=e.getParent(n||$g(s,t,t.collapsed),e.isBlock),i=e.getParent(o||qg(s,t,t.collapsed),e.isBlock);if(a&&a!==s&&r.push(a),a&&i&&a!==i){let t;const n=new $o(a,s);for(;(t=n.next())&&t!==i;)e.isBlock(t)&&r.push(t)}return i&&a!==i&&i!==s&&r.push(i),r})(e,c(),t,n),normalize:()=>{const t=c(),n=d();if(!(im(n).length>1)&&pm(o)){const n=Wf(e,t);return n.each((e=>{u(e,m())})),n.getOr(t)}return t},selectorChanged:(e,t)=>(a(e,t),f),selectorChangedWithUnbind:a,getScrollContainer:()=>{let t,n=e.getRoot();for(;n&&"BODY"!==n.nodeName;){if(n.scrollHeight>n.clientHeight){t=n;break}n=n.parentNode}return t},scrollIntoView:(e,t)=>{C(e)?((e,t,n)=>{(e.inline?mg:gg)(e,t,n)})(o,e,t):pg(o,c(),t)},placeCaretAt:(e,t)=>u(Ff(e,t,o.getDoc())),getBoundingClientRect:()=>{const e=c();return e.collapsed?Zi.fromRangeStart(e).getClientRects()[0]:e.getBoundingClientRect()},destroy:()=>{t=r=s=null,p.destroy()}},g=lf(f),p=Ef(f,o);return f.bookmarkManager=g,f.controlSelection=p,f},pw=(e,t,n)=>{-1===Pt.inArray(t,n)&&(e.addAttributeFilter(n,((e,t)=>{let n=e.length;for(;n--;)e[n].attr(t,null)})),t.push(n))},hw=(e,t)=>{const n=["data-mce-selected"],o={entity_encoding:"named",remove_trailing_brs:!0,pad_empty_with_br:!1,...e},r=t&&t.dom?t.dom:la.DOM,s=t&&t.schema?t.schema:Ps(o),a=kC(o,s);return((e,t,n)=>{e.addAttributeFilter("data-mce-tabindex",((e,t)=>{let n=e.length;for(;n--;){const o=e[n];o.attr("tabindex",o.attr("data-mce-tabindex")),o.attr(t,null)}})),e.addAttributeFilter("src,href,style",((e,o)=>{const r="data-mce-"+o,s=t.url_converter,a=t.url_converter_scope;let i=e.length;for(;i--;){const t=e[i];let l=t.attr(r);void 0!==l?(t.attr(o,l.length>0?l:null),t.attr(r,null)):(l=t.attr(o),"style"===o?l=n.serializeStyle(n.parseStyle(l),t.name):s&&(l=s.call(a,l,o,t.name)),t.attr(o,l.length>0?l:null))}})),e.addAttributeFilter("class",(e=>{let t=e.length;for(;t--;){const n=e[t];let o=n.attr("class");o&&(o=o.replace(/(?:^|\s)mce-item-\w+(?!\S)/g,""),n.attr("class",o.length>0?o:null))}})),e.addAttributeFilter("data-mce-type",((e,t,n)=>{let o=e.length;for(;o--;){const t=e[o];if("bookmark"===t.attr("data-mce-type")&&!n.cleanup){const e=I.from(t.firstChild).exists((e=>{var t;return!Qa(null!==(t=e.value)&&void 0!==t?t:"")}));e?t.unwrap():t.remove()}}})),e.addNodeFilter("script,style",((e,n)=>{var o;const r=e=>e.replace(/(<!--\[CDATA\[|\]\]-->)/g,"\n").replace(/^[\r\n]*|[\r\n]*$/g,"").replace(/^\s*((<!--)?(\s*\/\/)?\s*<!\[CDATA\[|(<!--\s*)?\/\*\s*<!\[CDATA\[\s*\*\/|(\/\/)?\s*<!--|\/\*\s*<!--\s*\*\/)\s*[\r\n]*/gi,"").replace(/\s*(\/\*\s*\]\]>\s*\*\/(-->)?|\s*\/\/\s*\]\]>(-->)?|\/\/\s*(-->)?|\]\]>|\/\*\s*-->\s*\*\/|\s*-->\s*)\s*$/g,"");let s=e.length;for(;s--;){const a=e[s],i=a.firstChild,l=null!==(o=null==i?void 0:i.value)&&void 0!==o?o:"";if("script"===n){const e=a.attr("type");e&&a.attr("type","mce-no/type"===e?null:e.replace(/^mce\-/,"")),"xhtml"===t.element_format&&i&&l.length>0&&(i.value="// <![CDATA[\n"+r(l)+"\n// ]]>")}else"xhtml"===t.element_format&&i&&l.length>0&&(i.value="\x3c!--\n"+r(l)+"\n--\x3e")}})),e.addNodeFilter("#comment",(e=>{let o=e.length;for(;o--;){const r=e[o],s=r.value;t.preserve_cdata&&0===(null==s?void 0:s.indexOf("[CDATA["))?(r.name="#cdata",r.type=4,r.value=n.decode(s.replace(/^\[CDATA\[|\]\]$/g,""))):0===(null==s?void 0:s.indexOf("mce:protected "))&&(r.name="#text",r.type=3,r.raw=!0,r.value=unescape(s).substr(14))}})),e.addNodeFilter("xml:namespace,input",((e,t)=>{let n=e.length;for(;n--;){const o=e[n];7===o.type?o.remove():1===o.type&&("input"!==t||o.attr("type")||o.attr("type","text"))}})),e.addAttributeFilter("data-mce-type",(t=>{V(t,(t=>{"format-caret"===t.attr("data-mce-type")&&(t.isEmpty(e.schema.getNonEmptyElements())?t.remove():t.unwrap())}))})),e.addAttributeFilter("data-mce-src,data-mce-href,data-mce-style,data-mce-selected,data-mce-expando,data-mce-block,data-mce-type,data-mce-resize,data-mce-placeholder",((e,t)=>{let n=e.length;for(;n--;)e[n].attr(t,null)})),t.remove_trailing_brs&&((e,t,n)=>{t.addNodeFilter("br",((t,o,r)=>{const s=Pt.extend({},n.getBlockElements()),a=n.getNonEmptyElements(),i=n.getWhitespaceElements();s.body=1;const l=e=>e.name in s||Yr(n,e);for(let o=0,d=t.length;o<d;o++){let d=t[o],c=d.parent;if(c&&l(c)&&d===c.lastChild){let t=d.prev;for(;t;){const e=t.name;if("span"!==e||"bookmark"!==t.attr("data-mce-type")){"br"===e&&(d=null);break}t=t.prev}if(d&&(d.remove(),wb(n,a,i,c))){const t=n.getElementRule(c.name);t&&(t.removeEmpty?c.remove():t.paddEmpty&&yb(e,r,l,c))}}else{let e=d;for(;c&&c.firstChild===e&&c.lastChild===e&&(e=c,!s[c.name]);)c=c.parent;if(e===c){const e=new Zg("#text",3);e.value=Vo,d.replace(e)}}}}))})(t,e,e.schema)})(a,o,r),{schema:s,addNodeFilter:a.addNodeFilter,addAttributeFilter:a.addAttributeFilter,serialize:(e,n={})=>{const i={format:"html",...n},l=((e,t,n)=>((e,t)=>C(e)&&e.hasEventListeners("PreProcess")&&!t.no_events)(e,n)?((e,t,n)=>{let o;const r=e.dom;let s=t.cloneNode(!0);const a=document.implementation;if(a.createHTMLDocument){const e=a.createHTMLDocument("");Pt.each("BODY"===s.nodeName?s.childNodes:[s],(t=>{e.body.appendChild(e.importNode(t,!0))})),s="BODY"!==s.nodeName?e.body.firstChild:e.body,o=r.doc,r.doc=e}return((e,t)=>{e.dispatch("PreProcess",t)})(e,{...n,node:s}),o&&(r.doc=o),s})(e,t,n):t)(t,e,i),d=((e,t,n)=>{const o=Ja(n.getInner?t.innerHTML:e.getOuterHTML(t));return n.selection||Ya(yn(t))?o:Pt.trim(o)})(r,l,i),c=((e,t,n)=>{const o=n.selection?{forced_root_block:!1,...n}:n,r=e.parse(t,o);return(e=>{const t=e=>"br"===(null==e?void 0:e.name),n=e.lastChild;if(t(n)){const e=n.prev;t(e)&&(n.remove(),e.remove())}})(r),r})(a,d,i);return"tree"===i.format?c:((e,t,n,o,r)=>{const s=((e,t,n)=>hp(e,t).serialize(n))(t,n,o);return((e,t,n)=>{if(!t.no_events&&e){const o=((e,t)=>e.dispatch("PostProcess",t))(e,{...t,content:n});return o.content}return n})(e,r,s)})(t,o,s,c,i)},addRules:s.addValidElements,setRules:s.setValidElements,addTempAttr:T(pw,a,n),getTempAttrs:N(n),getNodeFilters:a.getNodeFilters,getAttributeFilters:a.getAttributeFilters,removeNodeFilter:a.removeNodeFilter,removeAttributeFilter:a.removeAttributeFilter}},bw=(e,t)=>{const n=hw(e,t);return{schema:n.schema,addNodeFilter:n.addNodeFilter,addAttributeFilter:n.addAttributeFilter,serialize:n.serialize,addRules:n.addRules,setRules:n.setRules,addTempAttr:n.addTempAttr,getTempAttrs:n.getTempAttrs,getNodeFilters:n.getNodeFilters,getAttributeFilters:n.getAttributeFilters,removeNodeFilter:n.removeNodeFilter,removeAttributeFilter:n.removeAttributeFilter}},vw=(e,t,n={})=>{const o=((e,t)=>({format:"html",...e,set:!0,content:t}))(n,t);return OC(e,o).map((t=>{const n=((e,t,n)=>iw(e).editor.setContent(t,n))(e,t.content,t);return BC(e,n.html,t),n.content})).getOr(t)},yw="autoresize_on_init,content_editable_state,padd_empty_with_br,block_elements,boolean_attributes,editor_deselector,editor_selector,elements,file_browser_callback_types,filepicker_validator_handler,force_hex_style_colors,force_p_newlines,gecko_spellcheck,images_dataimg_filter,media_scripts,mode,move_caret_before_on_enter_elements,non_empty_elements,self_closing_elements,short_ended_elements,special,spellchecker_select_languages,spellchecker_whitelist,tab_focus,tabfocus_elements,table_responsive_width,text_block_elements,text_inline_elements,toolbar_drawer,types,validate,whitespace_elements,paste_enable_default_filters,paste_filter_drop,paste_word_valid_elements,paste_retain_style_properties,paste_convert_word_fake_lists".split(","),Cw="".split(","),ww="bbcode,colorpicker,contextmenu,fullpage,legacyoutput,spellchecker,textcolor".split(","),Ew=[{name:"rtc",replacedWith:!1}],xw=(e,t)=>{const n=Y(t,(t=>_e(e,t)));return ae(n)},_w=e=>{const t=xw(e,yw),n=e.forced_root_block;return!1!==n&&""!==n||t.push("forced_root_block (false only)"),ae(t)},Sw=e=>xw(e,Cw),kw=(e,t)=>{const n=Pt.makeMap(e.plugins," "),o=Y(t,(e=>_e(n,e)));return ae(o)},Nw=e=>kw(e,ww),Rw=e=>kw(e,Ew.map((e=>e.name))),Aw=e=>Q(Ew,(t=>t.name===e)).fold((()=>e),(t=>t.replacedWith?`${e}, replaced by ${t.replacedWith}`:e)),Tw=la.DOM,Ow=e=>I.from(e).each((e=>e.destroy())),Bw=(()=>{const e={};return{add:(t,n)=>{e[t]=n},get:t=>e[t]?e[t]:{icons:{}},has:t=>_e(e,t)}})(),Pw=ha.ModelManager,Dw=(e,t)=>t.dom[e],Lw=(e,t)=>parseInt(co(t,e),10),Mw=T(Dw,"clientWidth"),Iw=T(Dw,"clientHeight"),Fw=T(Lw,"margin-top"),Uw=T(Lw,"margin-left"),zw=e=>{const t=[],n=()=>{const t=e.theme;return t&&t.getNotificationManagerImpl?t.getNotificationManagerImpl():(()=>{const e=()=>{throw new Error("Theme did not provide a NotificationManager implementation.")};return{open:e,close:e,getArgs:e}})()},o=()=>I.from(t[0]),r=()=>{V(t,(e=>{e.reposition()}))},s=e=>{J(t,(t=>t===e)).each((e=>{t.splice(e,1)}))},a=(a,i=!0)=>e.removed||!(e=>{return(t=e.inline?e.getBody():e.getContentAreaContainer(),I.from(t).map(yn)).map(Gn).getOr(!1);var t})(e)?{}:(i&&e.dispatch("BeforeOpenNotification",{notification:a}),Q(t,(e=>{return t=n().getArgs(e),o=a,!(t.type!==o.type||t.text!==o.text||t.progressBar||t.timeout||o.progressBar||o.timeout);var t,o})).getOrThunk((()=>{e.editorManager.setActive(e);const i=n().open(a,(()=>{s(i),r(),Ug(e)&&o().fold((()=>e.focus()),(e=>hg(yn(e.getEl()))))}));return(e=>{t.push(e)})(i),r(),e.dispatch("OpenNotification",{notification:{...i}}),i}))),i=N(t);return(e=>{e.on("SkinLoaded",(()=>{const t=wd(e);t&&a({text:t,type:"warning",timeout:0},!1),r()})),e.on("show ResizeEditor ResizeWindow NodeChange",(()=>{requestAnimationFrame(r)})),e.on("remove",(()=>{V(t.slice(),(e=>{n().close(e)}))}))})(e),{open:a,close:()=>{o().each((e=>{n().close(e),s(e),r()}))},getNotifications:i}},jw=ha.PluginManager,Hw=ha.ThemeManager,$w=e=>{let t=[];const n=()=>{const t=e.theme;return t&&t.getWindowManagerImpl?t.getWindowManagerImpl():(()=>{const e=()=>{throw new Error("Theme did not provide a WindowManager implementation.")};return{open:e,openUrl:e,alert:e,confirm:e,close:e}})()},o=(e,t)=>(...n)=>t?t.apply(e,n):void 0,r=n=>{(t=>{e.dispatch("CloseWindow",{dialog:t})})(n),t=Y(t,(e=>e!==n)),0===t.length&&e.focus()},s=n=>{e.editorManager.setActive(e),Sg(e),e.ui.show();const o=n();return(n=>{t.push(n),(t=>{e.dispatch("OpenWindow",{dialog:t})})(n)})(o),o};return e.on("remove",(()=>{V(t,(e=>{n().close(e)}))})),{open:(e,t)=>s((()=>n().open(e,t,r))),openUrl:e=>s((()=>n().openUrl(e,r))),alert:(e,t,r)=>{const s=n();s.alert(e,o(r||s,t))},confirm:(e,t,r)=>{const s=n();s.confirm(e,o(r||s,t))},close:()=>{I.from(t[t.length-1]).each((e=>{n().close(e),r(e)}))}}},qw=(e,t)=>{e.notificationManager.open({type:"error",text:t})},Vw=(e,t)=>{e._skinLoaded?qw(e,t):e.on("SkinLoaded",(()=>{qw(e,t)}))},Ww=(e,t,n)=>{cf(e,t,{message:n}),console.error(n)},Kw=(e,t,n)=>n?`Failed to load ${e}: ${n} from url ${t}`:`Failed to load ${e} url: ${t}`,Yw=(e,...t)=>{const n=window.console;n&&(n.error?n.error(e,...t):n.log(e,...t))},Gw=e=>"content/"+e+"/content.css",Xw=(e,t)=>{const n=e.editorManager.baseURL+"/skins/content",o=`content${e.editorManager.suffix}.css`;return q(t,(t=>(e=>hugerte.Resource.has(Gw(e)))(t)?Gw(t):(e=>/^[a-z0-9\-]+$/i.test(e))(t)&&!e.inline?`${n}/${t}/${o}`:e.documentBaseURI.toAbsolute(t)))},Zw=(e,t)=>{const n={};return{findAll:(o,r=M)=>{const s=Y((e=>e?ce(e.getElementsByTagName("img")):[])(o),(t=>{const n=t.src;return!t.hasAttribute("data-mce-bogus")&&!t.hasAttribute("data-mce-placeholder")&&!(!n||n===Tt.transparentSrc)&&($e(n,"blob:")?!e.isUploaded(n)&&r(t):!!$e(n,"data:")&&r(t))})),a=q(s,(e=>{const o=e.src;if(_e(n,o))return n[o].then((t=>m(t)?t:{image:e,blobInfo:t.blobInfo}));{const r=((e,t)=>{const n=()=>Promise.reject("Invalid data URI");if($e(t,"blob:")){const s=e.getByUri(t);return C(s)?Promise.resolve(s):(o=t,$e(o,"blob:")?(e=>fetch(e).then((e=>e.ok?e.blob():Promise.reject())).catch((()=>Promise.reject({message:`Cannot convert ${e} to Blob. Resource might not exist or is inaccessible.`,uriType:"blob"}))))(o):$e(o,"data:")?(r=o,new Promise(((e,t)=>{Gv(r).bind((({type:e,data:t,base64Encoded:n})=>Xv(e,t,n))).fold((()=>t("Invalid data URI")),e)}))):Promise.reject("Unknown URI format")).then((t=>Zv(t).then((o=>Jv(o,!1,(n=>I.some(ey(e,t,n)))).getOrThunk(n)))))}var o,r;return $e(t,"data:")?ty(e,t).fold(n,(e=>Promise.resolve(e))):Promise.reject("Unknown image data format")})(t,o).then((t=>(delete n[o],{image:e,blobInfo:t}))).catch((e=>(delete n[o],e)));return n[o]=r,r}}));return Promise.all(a)}}},Qw=()=>{let e={};const t=(e,t)=>({status:e,resultUri:t}),n=t=>t in e;return{hasBlobUri:n,getResultUri:t=>{const n=e[t];return n?n.resultUri:null},isPending:t=>!!n(t)&&1===e[t].status,isUploaded:t=>!!n(t)&&2===e[t].status,markPending:n=>{e[n]=t(1,null)},markUploaded:(n,o)=>{e[n]=t(2,o)},removeFailed:t=>{delete e[t]},destroy:()=>{e={}}}};let Jw=0;const eE=(e,t)=>{const n={},o=(e,n)=>new Promise(((o,r)=>{const s=new XMLHttpRequest;s.open("POST",t.url),s.withCredentials=t.credentials,s.upload.onprogress=e=>{n(e.loaded/e.total*100)},s.onerror=()=>{r("Image upload failed due to a XHR Transport error. Code: "+s.status)},s.onload=()=>{if(s.status<200||s.status>=300)return void r("HTTP Error: "+s.status);const e=JSON.parse(s.responseText);var n,a;e&&m(e.location)?o((n=t.basePath,a=e.location,n?n.replace(/\/$/,"")+"/"+a.replace(/^\//,""):a)):r("Invalid JSON: "+s.responseText)};const a=new FormData;a.append("file",e.blob(),e.filename()),s.send(a)})),r=w(t.handler)?t.handler:o,s=(e,t)=>({url:t,blobInfo:e,status:!0}),a=(e,t)=>({url:"",blobInfo:e,status:!1,error:t}),i=(e,t)=>{Pt.each(n[e],(e=>{e(t)})),delete n[e]};return{upload:(l,d)=>t.url||r!==o?((t,o)=>(t=Pt.grep(t,(t=>!e.isUploaded(t.blobUri()))),Promise.all(Pt.map(t,(t=>e.isPending(t.blobUri())?(e=>{const t=e.blobUri();return new Promise((e=>{n[t]=n[t]||[],n[t].push(e)}))})(t):((t,n,o)=>(e.markPending(t.blobUri()),new Promise((r=>{let l,d;try{const c=()=>{l&&(l.close(),d=_)},u=n=>{c(),e.markUploaded(t.blobUri(),n),i(t.blobUri(),s(t,n)),r(s(t,n))},f=n=>{c(),e.removeFailed(t.blobUri()),i(t.blobUri(),a(t,n)),r(a(t,n))};d=e=>{e<0||e>100||I.from(l).orThunk((()=>I.from(o).map(P))).each((t=>{l=t,t.progressBar.value(e)}))},n(t,d).then(u,(e=>{f(m(e)?{message:e}:e)}))}catch(e){r(a(t,e))}}))))(t,r,o))))))(l,d):new Promise((e=>{e([])}))}},tE=e=>()=>e.notificationManager.open({text:e.translate("Image uploading..."),type:"info",timeout:-1,progressBar:!0}),nE=(e,t)=>eE(t,{url:nd(e),basePath:od(e),credentials:rd(e),handler:sd(e)}),oE=e=>{const t=(()=>{let e=[];const t=e=>{if(!e.blob||!e.base64)throw new Error("blob and base64 representations of the image are required for BlobInfo to be created");const t=e.id||"blobid"+Jw+++(()=>{const e=()=>Math.round(4294967295*Math.random()).toString(36);return"s"+(new Date).getTime().toString(36)+e()+e()+e()})(),n=e.name||t,o=e.blob;var r;return{id:N(t),name:N(n),filename:N(e.filename||n+"."+(r=o.type,{"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png","image/apng":"apng","image/avif":"avif","image/svg+xml":"svg","image/webp":"webp","image/bmp":"bmp","image/tiff":"tiff"}[r.toLowerCase()]||"dat")),blob:N(o),base64:N(e.base64),blobUri:N(e.blobUri||URL.createObjectURL(o)),uri:N(e.uri)}},n=t=>Q(e,t).getOrUndefined(),o=e=>n((t=>t.id()===e));return{create:(e,n,o,r,s)=>{if(m(e))return t({id:e,name:r,filename:s,blob:n,base64:o});if(f(e))return t(e);throw new Error("Unknown input type")},add:t=>{o(t.id())||e.push(t)},get:o,getByUri:e=>n((t=>t.blobUri()===e)),getByData:(e,t)=>n((n=>n.base64()===e&&n.blob().type===t)),findFirst:n,removeByUri:t=>{e=Y(e,(e=>e.blobUri()!==t||(URL.revokeObjectURL(e.blobUri()),!1)))},destroy:()=>{V(e,(e=>{URL.revokeObjectURL(e.blobUri())})),e=[]}}})();let n,o;const r=Qw(),s=[],a=t=>n=>e.selection?t(n):[],i=(e,t,n)=>{let o=0;do{o=e.indexOf(t,o),-1!==o&&(e=e.substring(0,o)+n+e.substr(o+t.length),o+=n.length-t.length+1)}while(-1!==o);return e},l=(e,t,n)=>{const o=`src="${n}"${n===Tt.transparentSrc?' data-mce-placeholder="1"':""}`;return e=i(e,`src="${t}"`,o),i(e,'data-mce-src="'+t+'"','data-mce-src="'+n+'"')},d=(t,n)=>{V(e.undoManager.data,(e=>{"fragmented"===e.type?e.fragments=q(e.fragments,(e=>l(e,t,n))):e.content=l(e.content,t,n)}))},c=()=>(n||(n=nE(e,r)),p().then(a((o=>{const r=q(o,(e=>e.blobInfo));return n.upload(r,tE(e)).then(a((n=>{const r=[];let s=!1;const a=q(n,((n,a)=>{const{blobInfo:i,image:l}=o[a];let c=!1;return n.status&&Jl(e)?(n.url&&!He(l.src,n.url)&&(s=!0),t.removeByUri(l.src),aw(e)||((t,n)=>{const o=e.convertURL(n,"src");var r;d(t.src,n),Jt(yn(t),{src:Ql(e)?(r=n,r+(-1===r.indexOf("?")?"?":"&")+(new Date).getTime()):n,"data-mce-src":o})})(l,n.url)):n.error&&(n.error.remove&&(d(l.src,Tt.transparentSrc),r.push(l),c=!0),((e,t)=>{Vw(e,pa.translate(["Failed to upload image: {0}",t]))})(e,n.error.message)),{element:l,status:n.status,uploadUri:n.url,blobInfo:i,removed:c}}));return r.length>0&&!aw(e)?e.undoManager.transact((()=>{V(_o(r),(n=>{const o=An(n);Eo(n),o.each((e=>t=>{((e,t)=>e.dom.isEmpty(t.dom)&&C(e.schema.getTextBlockElements()[Ht(t)]))(e,t)&&vo(t,hn('<br data-mce-bogus="1" />'))})(e)),t.removeByUri(n.dom.src)}))})):s&&e.undoManager.dispatchChange(),a})))})))),u=()=>Zl(e)?c():Promise.resolve([]),g=e=>ne(s,(t=>t(e))),p=()=>(o||(o=Zw(r,t)),o.findAll(e.getBody(),g).then(a((t=>{const n=Y(t,(t=>m(t)?(Vw(e,t),!1):"blob"!==t.uriType));return aw(e)||V(n,(e=>{d(e.image.src,e.blobInfo.blobUri()),e.image.src=e.blobInfo.blobUri(),e.image.removeAttribute("data-mce-src")})),n})))),h=n=>n.replace(/src="(blob:[^"]+)"/g,((n,o)=>{const s=r.getResultUri(o);if(s)return'src="'+s+'"';let a=t.getByUri(o);return a||(a=X(e.editorManager.get(),((e,t)=>e||t.editorUpload&&t.editorUpload.blobCache.getByUri(o)),void 0)),a?'src="data:'+a.blob().type+";base64,"+a.base64()+'"':n}));return e.on("SetContent",(()=>{Zl(e)?u():p()})),e.on("RawSaveContent",(e=>{e.content=h(e.content)})),e.on("GetContent",(e=>{e.source_view||"raw"===e.format||"tree"===e.format||(e.content=h(e.content))})),e.on("PostRender",(()=>{e.parser.addNodeFilter("img",(e=>{V(e,(e=>{const n=e.attr("src");if(!n||t.getByUri(n))return;const o=r.getResultUri(n);o&&e.attr("src",o)}))}))})),{blobCache:t,addFilter:e=>{s.push(e)},uploadImages:c,uploadImagesAuto:u,scanForImages:p,destroy:()=>{t.destroy(),r.destroy(),o=n=null}}},rE={remove_similar:!0,inherit:!1},sE={selector:"td,th",...rE},aE={tablecellbackgroundcolor:{styles:{backgroundColor:"%value"},...sE},tablecellverticalalign:{styles:{"vertical-align":"%value"},...sE},tablecellbordercolor:{styles:{borderColor:"%value"},...sE},tablecellclass:{classes:["%value"],...sE},tableclass:{selector:"table",classes:["%value"],...rE},tablecellborderstyle:{styles:{borderStyle:"%value"},...sE},tablecellborderwidth:{styles:{borderWidth:"%value"},...sE}},iE=N(aE),lE=Pt.each,dE=la.DOM,cE=e=>C(e)&&f(e),uE=(e,t)=>{const n=t&&t.schema||Ps({}),o=e=>{const t=m(e)?{name:e,classes:[],attrs:{}}:e,n=dE.create(t.name);return((e,t)=>{t.classes.length>0&&dE.addClass(e,t.classes.join(" ")),dE.setAttribs(e,t.attrs)})(n,t),n},r=(e,t,s)=>{let a;const i=t[0],l=cE(i)?i.name:void 0,d=((e,t)=>{const o=n.getElementRule(e.nodeName.toLowerCase()),r=null==o?void 0:o.parentsRequired;return!(!r||!r.length)&&(t&&H(r,t)?t:r[0])})(e,l);if(d)l===d?(a=i,t=t.slice(1)):a=d;else if(i)a=i,t=t.slice(1);else if(!s)return e;const c=a?o(a):dE.create("div");c.appendChild(e),s&&Pt.each(s,(t=>{const n=o(t);c.insertBefore(n,e)}));const u=cE(a)?a.siblings:void 0;return r(c,t,u)},s=dE.create("div");if(e.length>0){const t=e[0],n=o(t),a=cE(t)?t.siblings:void 0;s.appendChild(r(n,e.slice(1),a))}return s},mE=e=>{let t="div";const n={name:t,classes:[],attrs:{},selector:e=Pt.trim(e)};return"*"!==e&&(t=e.replace(/(?:([#\.]|::?)([\w\-]+)|(\[)([^\]]+)\]?)/g,((e,t,o,r,s)=>{switch(t){case"#":n.attrs.id=o;break;case".":n.classes.push(o);break;case":":-1!==Pt.inArray("checked disabled enabled read-only required".split(" "),o)&&(n.attrs[o]=o)}if("["===r){const e=s.match(/([\w\-]+)(?:\=\"([^\"]+))?/);e&&(n.attrs[e[1]]=e[2])}return""}))),n.name=t||"div",n},fE=(e,t)=>{let n="",o=Rd(e);if(""===o)return"";const r=e=>m(e)?e.replace(/%(\w+)/g,""):"",s=(t,n)=>dE.getStyle(null!=n?n:e.getBody(),t,!0);if(m(t)){const n=e.formatter.get(t);if(!n)return"";t=n[0]}if("preview"in t){const e=t.preview;if(!1===e)return"";o=e||o}let a,i=t.block||t.inline||"span";const l=(d=t.selector,m(d)?(d=(d=d.split(/\s*,\s*/)[0]).replace(/\s*(~\+|~|\+|>)\s*/g,"$1"),Pt.map(d.split(/(?:>|\s+(?![^\[\]]+\]))/),(e=>{const t=Pt.map(e.split(/(?:~\+|~|\+)/),mE),n=t.pop();return t.length&&(n.siblings=t),n})).reverse()):[]);var d;l.length>0?(l[0].name||(l[0].name=i),i=t.selector,a=uE(l,e)):a=uE([i],e);const c=dE.select(i,a)[0]||a.firstChild;lE(t.styles,((e,t)=>{const n=r(e);n&&dE.setStyle(c,t,n)})),lE(t.attributes,((e,t)=>{const n=r(e);n&&dE.setAttrib(c,t,n)})),lE(t.classes,(e=>{const t=r(e);dE.hasClass(c,t)||dE.addClass(c,t)})),e.dispatch("PreviewFormats"),dE.setStyles(a,{position:"absolute",left:-65535}),e.getBody().appendChild(a);const u=s("fontSize"),f=/px$/.test(u)?parseInt(u,10):0;return lE(o.split(" "),(e=>{let t=s(e,c);if(!("background-color"===e&&/transparent|rgba\s*\([^)]+,\s*0\)/.test(t)&&(t=s(e),"#ffffff"===Hs(t).toLowerCase())||"color"===e&&"#000000"===Hs(t).toLowerCase())){if("font-size"===e&&/em|%$/.test(t)){if(0===f)return;t=parseFloat(t)/(/%$/.test(t)?100:1)*f+"px"}"border"===e&&t&&(n+="padding:0 2px;"),n+=e+":"+t+";"}})),e.dispatch("AfterPreviewFormats"),dE.remove(a),n},gE=e=>{const t=(e=>{const t={},n=(e,o)=>{e&&(m(e)?(p(o)||(o=[o]),V(o,(e=>{v(e.deep)&&(e.deep=!Lm(e)),v(e.split)&&(e.split=!Lm(e)||Mm(e)),v(e.remove)&&Lm(e)&&!Mm(e)&&(e.remove="none"),Lm(e)&&Mm(e)&&(e.mixed=!0,e.block_expand=!0),m(e.classes)&&(e.classes=e.classes.split(/\s+/))})),t[e]=o):pe(e,((e,t)=>{n(t,e)})))};return n((e=>{const t=e.dom,n=e.schema.type,o={valigntop:[{selector:"td,th",styles:{verticalAlign:"top"}}],valignmiddle:[{selector:"td,th",styles:{verticalAlign:"middle"}}],valignbottom:[{selector:"td,th",styles:{verticalAlign:"bottom"}}],alignleft:[{selector:"figure.image",collapsed:!1,classes:"align-left",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"left"},inherit:!1,preview:!1},{selector:"img,audio,video",collapsed:!1,styles:{float:"left"},preview:"font-family font-size"},{selector:"table",collapsed:!1,styles:{marginLeft:"0px",marginRight:"auto"},onformat:e=>{t.setStyle(e,"float",null)},preview:"font-family font-size"},{selector:".mce-preview-object,[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{float:"left"}}],aligncenter:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"center"},inherit:!1,preview:"font-family font-size"},{selector:"figure.image",collapsed:!1,classes:"align-center",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{display:"block",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"table",collapsed:!1,styles:{marginLeft:"auto",marginRight:"auto"},preview:"font-family font-size"},{selector:".mce-preview-object",ceFalseOverride:!0,styles:{display:"table",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{marginLeft:"auto",marginRight:"auto"},preview:!1}],alignright:[{selector:"figure.image",collapsed:!1,classes:"align-right",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"right"},inherit:!1,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{float:"right"},preview:"font-family font-size"},{selector:"table",collapsed:!1,styles:{marginRight:"0px",marginLeft:"auto"},onformat:e=>{t.setStyle(e,"float",null)},preview:"font-family font-size"},{selector:".mce-preview-object,[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{float:"right"},preview:!1}],alignjustify:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"justify"},inherit:!1,preview:"font-family font-size"}],bold:[{inline:"strong",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontWeight:"bold"}},{inline:"b",remove:"all",preserve_attributes:["class","style"]}],italic:[{inline:"em",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontStyle:"italic"}},{inline:"i",remove:"all",preserve_attributes:["class","style"]}],underline:[{inline:"span",styles:{textDecoration:"underline"},exact:!0},{inline:"u",remove:"all",preserve_attributes:["class","style"]}],strikethrough:(()=>{const e={inline:"span",styles:{textDecoration:"line-through"},exact:!0},t={inline:"strike",remove:"all",preserve_attributes:["class","style"]},o={inline:"s",remove:"all",preserve_attributes:["class","style"]};return"html4"!==n?[o,e,t]:[e,o,t]})(),forecolor:{inline:"span",styles:{color:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},hilitecolor:{inline:"span",styles:{backgroundColor:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},fontname:{inline:"span",toggle:!1,styles:{fontFamily:"%value"},clear_child_styles:!0},fontsize:{inline:"span",toggle:!1,styles:{fontSize:"%value"},clear_child_styles:!0},lineheight:{selector:"h1,h2,h3,h4,h5,h6,p,li,td,th,div",styles:{lineHeight:"%value"}},fontsize_class:{inline:"span",attributes:{class:"%value"}},blockquote:{block:"blockquote",wrapper:!0,remove:"all"},subscript:{inline:"sub"},superscript:{inline:"sup"},code:{inline:"code"},link:{inline:"a",selector:"a",remove:"all",split:!0,deep:!0,onmatch:(e,t,n)=>Jo(e)&&e.hasAttribute("href"),onformat:(e,n,o)=>{Pt.each(o,((n,o)=>{t.setAttrib(e,o,n)}))}},lang:{inline:"span",clear_child_styles:!0,remove_similar:!0,attributes:{lang:"%value","data-mce-lang":e=>{var t;return null!==(t=null==e?void 0:e.customValue)&&void 0!==t?t:null}}},removeformat:[{selector:"b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small",remove:"all",split:!0,expand:!1,block_expand:!0,deep:!0},{selector:"span",attributes:["style","class"],remove:"empty",split:!0,expand:!1,deep:!0},{selector:"*",attributes:["style","class"],split:!1,expand:!1,deep:!0}]};return Pt.each("p h1 h2 h3 h4 h5 h6 div address pre dt dd samp".split(/\s/),(e=>{o[e]={block:e,remove:"all"}})),o})(e)),n(iE()),n(Nd(e)),{get:e=>C(e)?t[e]:t,has:e=>_e(t,e),register:n,unregister:e=>(e&&t[e]&&delete t[e],t)}})(e),n=ua({});return(e=>{e.addShortcut("meta+b","","Bold"),e.addShortcut("meta+i","","Italic"),e.addShortcut("meta+u","","Underline");for(let t=1;t<=6;t++)e.addShortcut("access+"+t,"",["FormatBlock",!1,"h"+t]);e.addShortcut("access+7","",["FormatBlock",!1,"p"]),e.addShortcut("access+8","",["FormatBlock",!1,"div"]),e.addShortcut("access+9","",["FormatBlock",!1,"address"])})(e),(e=>{e.on("mouseup keydown",(t=>{var n;((e,t,n)=>{const o=e.selection,r=e.getBody();Xb(e,null,n),8!==t&&46!==t||!o.isCollapsed()||o.getStart().innerHTML!==Wb||Xb(e,qu(r,o.getStart()),!0),37!==t&&39!==t||Xb(e,qu(r,o.getStart()),!0)})(e,t.keyCode,(n=e.selection.getRng().endContainer,lr(n)&&qe(n.data,Vo)))}))})(e),aw(e)||((e,t)=>{e.set({}),t.on("NodeChange",(n=>{Vv(t,n.element,e.get())})),t.on("FormatApply FormatRemove",(n=>{const o=I.from(n.node).map((e=>vm(e)?e:e.startContainer)).bind((e=>Jo(e)?I.some(e):I.from(e.parentElement))).getOrThunk((()=>Hv(t)));Vv(t,o,e.get())}))})(n,e),{get:t.get,has:t.has,register:t.register,unregister:t.unregister,apply:(t,n,o)=>{((e,t,n,o)=>{lw(e).formatter.apply(t,n,o)})(e,t,n,o)},remove:(t,n,o,r)=>{((e,t,n,o,r)=>{lw(e).formatter.remove(t,n,o,r)})(e,t,n,o,r)},toggle:(t,n,o)=>{((e,t,n,o)=>{lw(e).formatter.toggle(t,n,o)})(e,t,n,o)},match:(t,n,o,r)=>((e,t,n,o,r)=>lw(e).formatter.match(t,n,o,r))(e,t,n,o,r),closest:t=>((e,t)=>lw(e).formatter.closest(t))(e,t),matchAll:(t,n)=>((e,t,n)=>lw(e).formatter.matchAll(t,n))(e,t,n),matchNode:(t,n,o,r)=>((e,t,n,o,r)=>lw(e).formatter.matchNode(t,n,o,r))(e,t,n,o,r),canApply:t=>((e,t)=>lw(e).formatter.canApply(t))(e,t),formatChanged:(t,o,r,s)=>((e,t,n,o,r,s)=>lw(e).formatter.formatChanged(t,n,o,r,s))(e,n,t,o,r,s),getCssText:T(fE,e)}},pE=e=>{switch(e.toLowerCase()){case"undo":case"redo":case"mcefocus":return!0;default:return!1}},hE=e=>{const t=va(),n=ua(0),o=ua(0),r={data:[],typing:!1,beforeChange:()=>{((e,t,n)=>{lw(e).undoManager.beforeChange(t,n)})(e,n,t)},add:(s,a)=>((e,t,n,o,r,s,a)=>lw(e).undoManager.add(t,n,o,r,s,a))(e,r,o,n,t,s,a),dispatchChange:()=>{e.setDirty(!0);const t=ZC(e);t.bookmark=vl(e.selection),e.dispatch("change",{level:t,lastLevel:ie(r.data,o.get()).getOrUndefined()})},undo:()=>((e,t,n,o)=>lw(e).undoManager.undo(t,n,o))(e,r,n,o),redo:()=>((e,t,n)=>lw(e).undoManager.redo(t,n))(e,o,r.data),clear:()=>{((e,t,n)=>{lw(e).undoManager.clear(t,n)})(e,r,o)},reset:()=>{((e,t)=>{lw(e).undoManager.reset(t)})(e,r)},hasUndo:()=>((e,t,n)=>lw(e).undoManager.hasUndo(t,n))(e,r,o),hasRedo:()=>((e,t,n)=>lw(e).undoManager.hasRedo(t,n))(e,r,o),transact:t=>((e,t,n,o)=>lw(e).undoManager.transact(t,n,o))(e,r,n,t),ignore:t=>{((e,t,n)=>{lw(e).undoManager.ignore(t,n)})(e,n,t)},extra:(t,n)=>{((e,t,n,o,r)=>{lw(e).undoManager.extra(t,n,o,r)})(e,r,o,t,n)}};return aw(e)||((e,t,n)=>{const o=ua(!1),r=e=>{ow(t,!1,n),t.add({},e)};e.on("init",(()=>{t.add()})),e.on("BeforeExecCommand",(e=>{const o=e.command;pE(o)||(rw(t,n),t.beforeChange())})),e.on("ExecCommand",(e=>{const t=e.command;pE(t)||r(e)})),e.on("ObjectResizeStart cut",(()=>{t.beforeChange()})),e.on("SaveContent ObjectResized blur",r),e.on("dragend",r),e.on("keyup",(n=>{const s=n.keyCode;if(n.isDefaultPrevented())return;const a=Tt.os.isMacOS()&&"Meta"===n.key;(s>=33&&s<=36||s>=37&&s<=40||45===s||n.ctrlKey||a)&&(r(),e.nodeChanged()),46!==s&&8!==s||e.nodeChanged(),o.get()&&t.typing&&!tw(ZC(e),t.data[0])&&(e.isDirty()||e.setDirty(!0),e.dispatch("TypingUndo"),o.set(!1),e.nodeChanged())})),e.on("keydown",(e=>{const s=e.keyCode;if(e.isDefaultPrevented())return;if(s>=33&&s<=36||s>=37&&s<=40||45===s)return void(t.typing&&r(e));const a=e.ctrlKey&&!e.altKey||e.metaKey;if((s<16||s>20)&&224!==s&&91!==s&&!t.typing&&!a)return t.beforeChange(),ow(t,!0,n),t.add({},e),void o.set(!0);(Tt.os.isMacOS()?e.metaKey:e.ctrlKey&&!e.altKey)&&t.beforeChange()})),e.on("mousedown",(e=>{t.typing&&r(e)})),e.on("input",(e=>{var t;e.inputType&&("insertReplacementText"===e.inputType||"insertText"===(t=e).inputType&&null===t.data||(e=>"insertFromPaste"===e.inputType||"insertFromDrop"===e.inputType)(e))&&r(e)})),e.on("AddUndo Undo Redo ClearUndos",(t=>{t.isDefaultPrevented()||e.nodeChanged()}))})(e,r,n),(e=>{e.addShortcut("meta+z","","Undo"),e.addShortcut("meta+y,meta+shift+z","","Redo")})(e),r},bE=[9,27,bf.HOME,bf.END,19,20,44,144,145,33,34,45,16,17,18,91,92,93,bf.DOWN,bf.UP,bf.LEFT,bf.RIGHT].concat(Tt.browser.isFirefox()?[224]:[]),vE="data-mce-placeholder",yE=e=>"keydown"===e.type||"keyup"===e.type,CE=e=>{const t=e.keyCode;return t===bf.BACKSPACE||t===bf.DELETE},wE=(e,t)=>({from:e,to:t}),EE=(e,t)=>{const n=yn(e),o=yn(t.container());return Dh(n,o).map((e=>((e,t)=>({block:e,position:t}))(e,t)))},xE=(e,t)=>Qn(t,(e=>Ka(e)||hr(e.dom)),(t=>_n(t,e))).filter(Wt).getOr(e),_E=(e,t)=>{const n=((e,t)=>{const n=Mn(e);return J(n,(e=>t.isBlock(Ht(e)))).fold(N(n),(e=>n.slice(0,e)))})(e,t);return V(n,Eo),n},SE=(e,t,n)=>{const o=Ip(n,t);return Q(o.reverse(),(t=>Ar(e,t))).each(Eo)},kE=(e,t,n,o,r)=>{if(Ar(o,n))return Xa(n),zu(n.dom);((e,t)=>0===Y(Dn(t),(t=>!Ar(e,t))).length)(o,r)&&Ar(o,t)&&po(r,bn("br"));const s=Uu(n.dom,Zi.before(r.dom));return V(_E(t,o),(e=>{po(r,e)})),SE(o,e,t),s},NE=(e,t,n,o)=>{if(Ar(o,n)){if(Ar(o,t)){const e=e=>{const t=(e,n)=>Fn(e).fold((()=>n),(e=>((e,t)=>e.isInline(Ht(t)))(o,e)?t(e,n.concat(La(e))):n));return t(e,[])},r=G(e(n),((e,t)=>(yo(e,t),t)),Ga());wo(t),vo(t,r)}return Eo(n),zu(t.dom)}const r=ju(n.dom);return V(_E(t,o),(e=>{vo(n,e)})),SE(o,e,t),r},RE=(e,t)=>{Iu(e,t.dom).bind((e=>I.from(e.getNode()))).map(yn).filter(Ha).each(Eo)},AE=(e,t,n,o)=>(RE(!0,t),RE(!1,n),((e,t)=>Sn(t,e)?((e,t)=>{const n=Ip(t,e);return I.from(n[n.length-1])})(t,e):I.none())(t,n).fold(T(NE,e,t,n,o),T(kE,e,t,n,o))),TE=(e,t,n,o,r)=>t?AE(e,o,n,r):AE(e,n,o,r),OE=(e,t)=>{const n=yn(e.getBody()),o=((e,t,n,o)=>o.collapsed?((e,t,n,o)=>{const r=EE(t,Zi.fromRangeStart(o)),s=r.bind((o=>Du(n,t,o.position).bind((o=>EE(t,o).map((o=>((e,t,n,o)=>gr(o.position.getNode())&&!Ar(e,o.block)?Iu(!1,o.block.dom).bind((e=>e.isEqual(o.position)?Du(n,t,e).bind((e=>EE(t,e))):I.some(o))).getOr(o):o)(e,t,n,o)))))));return Mt(r,s,wE).filter((e=>(e=>!_n(e.from.block,e.to.block))(e)&&((e,t)=>{const n=yn(e);return _n(xE(n,t.from.block),xE(n,t.to.block))})(t,e)&&(e=>!1===br(e.from.block.dom)&&!1===br(e.to.block.dom))(e)&&(e=>{const t=e=>$a(e)||$r(e.dom);return t(e.from.block)&&t(e.to.block)})(e)))})(e,t,n,o):I.none())(e.schema,n.dom,t,e.selection.getRng()).map((o=>()=>{TE(n,t,o.from.block,o.to.block,e.schema).each((t=>{e.selection.setRng(t.toRange())}))}));return o},BE=(e,t)=>{const n=yn(t),o=T(_n,e);return Zn(n,Ka,o).isSome()},PE=e=>{const t=yn(e.getBody());return((e,t)=>{const n=Uu(e.dom,Zi.fromRangeStart(t)).isNone(),o=Fu(e.dom,Zi.fromRangeEnd(t)).isNone();return!((e,t)=>BE(e,t.startContainer)||BE(e,t.endContainer))(e,t)&&n&&o})(t,e.selection.getRng())?(e=>I.some((()=>{e.setContent(""),e.selection.setCursorLocation()})))(e):((e,t,n)=>{const o=t.getRng();return Mt(Dh(e,yn(o.startContainer)),Dh(e,yn(o.endContainer)),((r,s)=>_n(r,s)?I.none():I.some((()=>{o.deleteContents(),TE(e,!0,r,s,n).each((e=>{t.setRng(e.toRange())}))})))).getOr(I.none())})(t,e.selection,e.schema)},DE=(e,t)=>e.selection.isCollapsed()?I.none():PE(e),LE=(e,t,n,o,r)=>I.from(t._selectionOverrides.showCaret(e,n,o,r)),ME=(e,t)=>e.dispatch("BeforeObjectSelected",{target:t}).isDefaultPrevented()?I.none():I.some((e=>{const t=e.ownerDocument.createRange();return t.selectNode(e),t})(t)),IE=(e,t,n)=>t.collapsed?((e,t,n)=>{const o=fu(1,e.getBody(),t),r=Zi.fromRangeStart(o),s=r.getNode();if(qc(s))return LE(1,e,s,!r.isAtEnd(),!1);const a=r.getNode(!0);if(qc(a))return LE(1,e,a,!1,!1);const i=ub(e.dom.getRoot(),r.getNode());return qc(i)?LE(1,e,i,!1,n):I.none()})(e,t,n).getOr(t):t,FE=e=>Dp(e)||Tp(e),UE=e=>Lp(e)||Op(e),zE=(e,t,n,o,r,s)=>{LE(o,e,s.getNode(!r),r,!0).each((n=>{if(t.collapsed){const e=t.cloneRange();r?e.setEnd(n.startContainer,n.startOffset):e.setStart(n.endContainer,n.endOffset),e.deleteContents()}else t.deleteContents();e.selection.setRng(n)})),((e,t)=>{lr(t)&&0===t.data.length&&e.remove(t)})(e.dom,n)},jE=(e,t)=>((e,t)=>{const n=e.selection.getRng();if(!lr(n.commonAncestorContainer))return I.none();const o=t?yu.Forwards:yu.Backwards,r=Tu(e.getBody()),s=T(bu,t?r.next:r.prev),a=t?FE:UE,i=pu(o,e.getBody(),n),l=s(i),d=l?Rh(t,l):l;if(!d||!vu(i,d))return I.none();if(a(d))return I.some((()=>zE(e,n,i.getNode(),o,t,d)));const c=s(d);return c&&a(c)&&vu(d,c)?I.some((()=>zE(e,n,i.getNode(),o,t,c))):I.none()})(e,t),HE=(e,t)=>{const n=e.getBody();return t?zu(n).filter(Dp):ju(n).filter(Lp)},$E=e=>{const t=e.selection.getRng();return!t.collapsed&&(HE(e,!0).exists((e=>e.isEqual(Zi.fromRangeStart(t))))||HE(e,!1).exists((e=>e.isEqual(Zi.fromRangeEnd(t)))))},qE=El([{remove:["element"]},{moveToElement:["element"]},{moveToPosition:["position"]}]),VE=(e,t,n,o)=>Du(t,e,n).bind((r=>{return s=r.getNode(),C(s)&&(Ka(yn(s))||Va(yn(s)))||((e,t,n,o,r)=>{const s=t=>r.isInline(t.nodeName.toLowerCase())&&!au(n,o,e);return gu(!t,n).fold((()=>gu(t,o).fold(L,s)),s)})(e,t,n,r,o)?I.none():t&&br(r.getNode())||!t&&br(r.getNode(!0))?((e,t,n,o,r)=>{const s=r.getNode(!n);return Dh(yn(t),yn(o.getNode())).map((t=>Ar(e,t)?qE.remove(t.dom):qE.moveToElement(s))).orThunk((()=>I.some(qE.moveToElement(s))))})(o,e,t,n,r):t&&Lp(n)||!t&&Dp(n)?I.some(qE.moveToPosition(r)):I.none();var s})),WE=(e,t)=>I.from(ub(e.getBody(),t)),KE=(e,t)=>{const n=e.selection.getNode();return WE(e,n).filter(br).fold((()=>((e,t,n,o)=>{const r=fu(t?1:-1,e,n),s=Zi.fromRangeStart(r),a=yn(e);return!t&&Lp(s)?I.some(qE.remove(s.getNode(!0))):t&&Dp(s)?I.some(qE.remove(s.getNode())):!t&&Dp(s)&&Gp(a,s,o)?Xp(a,s,o).map((e=>qE.remove(e.getNode()))):t&&Lp(s)&&Yp(a,s,o)?Zp(a,s,o).map((e=>qE.remove(e.getNode()))):((e,t,n,o)=>((e,t)=>{const n=t.getNode(!e),o=e?"after":"before";return Jo(n)&&n.getAttribute("data-mce-caret")===o})(t,n)?((e,t)=>y(t)?I.none():e&&br(t.nextSibling)?I.some(qE.moveToElement(t.nextSibling)):!e&&br(t.previousSibling)?I.some(qE.moveToElement(t.previousSibling)):I.none())(t,n.getNode(!t)).orThunk((()=>VE(e,t,n,o))):VE(e,t,n,o).bind((t=>((e,t,n)=>n.fold((e=>I.some(qE.remove(e))),(e=>I.some(qE.moveToElement(e))),(n=>au(t,n,e)?I.none():I.some(qE.moveToPosition(n)))))(e,n,t))))(e,t,s,o)})(e.getBody(),t,e.selection.getRng(),e.schema).map((n=>()=>n.fold(((e,t)=>n=>(e._selectionOverrides.hideFakeCaret(),xh(e,t,yn(n)),!0))(e,t),((e,t)=>n=>{const o=t?Zi.before(n):Zi.after(n);return e.selection.setRng(o.toRange()),!0})(e,t),(e=>t=>(e.selection.setRng(t.toRange()),!0))(e))))),(()=>I.some(_)))},YE=e=>{const t=e.dom,n=e.selection,o=ub(e.getBody(),n.getNode());if(hr(o)&&t.isBlock(o)&&t.isEmpty(o)){const e=t.create("br",{"data-mce-bogus":"1"});t.setHTML(o,""),o.appendChild(e),n.setRng(Zi.before(e).toRange())}return!0},GE=(e,t)=>e.selection.isCollapsed()?KE(e,t):((e,t)=>{const n=e.selection.getNode();return br(n)&&!vr(n)?WE(e,n.parentNode).filter(br).fold((()=>I.some((()=>{var n;n=yn(e.getBody()),V(Uo(n,".mce-offscreen-selection"),Eo),xh(e,t,yn(e.selection.getNode())),Lh(e)}))),(()=>I.some(_))):$E(e)?I.some((()=>{Fh(e,e.selection.getRng(),yn(e.getBody()))})):I.none()})(e,t),XE=(e,t)=>e.selection.isCollapsed()?((e,t)=>{const n=Zi.fromRangeStart(e.selection.getRng());return Du(t,e.getBody(),n).filter((e=>t?Rp(e):Ap(e))).bind((e=>iu(t?0:-1,e))).map((t=>()=>e.selection.select(t)))})(e,t):I.none(),ZE=lr,QE=e=>ZE(e)&&e.data[0]===Za,JE=e=>ZE(e)&&e.data[e.data.length-1]===Za,ex=e=>{var t;return(null!==(t=e.ownerDocument)&&void 0!==t?t:document).createTextNode(Za)},tx=(e,t)=>e?(e=>{var t;if(ZE(e.previousSibling))return JE(e.previousSibling)||e.previousSibling.appendData(Za),e.previousSibling;if(ZE(e))return QE(e)||e.insertData(0,Za),e;{const n=ex(e);return null===(t=e.parentNode)||void 0===t||t.insertBefore(n,e),n}})(t):(e=>{var t,n;if(ZE(e.nextSibling))return QE(e.nextSibling)||e.nextSibling.insertData(0,Za),e.nextSibling;if(ZE(e))return JE(e)||e.appendData(Za),e;{const o=ex(e);return e.nextSibling?null===(t=e.parentNode)||void 0===t||t.insertBefore(o,e.nextSibling):null===(n=e.parentNode)||void 0===n||n.appendChild(o),o}})(t),nx=T(tx,!0),ox=T(tx,!1),rx=(e,t)=>lr(e.container())?tx(t,e.container()):tx(t,e.getNode()),sx=(e,t)=>{const n=t.get();return n&&e.container()===n&&oi(n)},ax=(e,t)=>t.fold((t=>{Ic(e.get());const n=nx(t);return e.set(n),I.some(Zi(n,n.length-1))}),(t=>zu(t).map((t=>{if(sx(t,e)){const t=e.get();return Zi(t,1)}{Ic(e.get());const n=rx(t,!0);return e.set(n),Zi(n,1)}}))),(t=>ju(t).map((t=>{if(sx(t,e)){const t=e.get();return Zi(t,t.length-1)}{Ic(e.get());const n=rx(t,!1);return e.set(n),Zi(n,n.length-1)}}))),(t=>{Ic(e.get());const n=ox(t);return e.set(n),I.some(Zi(n,1))})),ix=(e,t)=>{for(let n=0;n<e.length;n++){const o=e[n].apply(null,t);if(o.isSome())return o}return I.none()},lx=El([{before:["element"]},{start:["element"]},{end:["element"]},{after:["element"]}]),dx=(e,t)=>su(t,e)||e,cx=(e,t,n)=>{const o=Ah(n),r=dx(t,o.container());return Nh(e,r,o).fold((()=>Fu(r,o).bind(T(Nh,e,r)).map((e=>lx.before(e)))),I.none)},ux=(e,t)=>null===qu(e,t),mx=(e,t,n)=>Nh(e,t,n).filter(T(ux,t)),fx=(e,t,n)=>{const o=Th(n);return mx(e,t,o).bind((e=>Uu(e,o).isNone()?I.some(lx.start(e)):I.none()))},gx=(e,t,n)=>{const o=Ah(n);return mx(e,t,o).bind((e=>Fu(e,o).isNone()?I.some(lx.end(e)):I.none()))},px=(e,t,n)=>{const o=Th(n),r=dx(t,o.container());return Nh(e,r,o).fold((()=>Uu(r,o).bind(T(Nh,e,r)).map((e=>lx.after(e)))),I.none)},hx=e=>!kh(vx(e)),bx=(e,t,n)=>ix([cx,fx,gx,px],[e,t,n]).filter(hx),vx=e=>e.fold(R,R,R,R),yx=e=>e.fold(N("before"),N("start"),N("end"),N("after")),Cx=e=>e.fold(lx.before,lx.before,lx.after,lx.after),wx=e=>e.fold(lx.start,lx.start,lx.end,lx.end),Ex=(e,t,n,o,r,s)=>Mt(Nh(t,n,o),Nh(t,n,r),((t,o)=>t!==o&&((e,t,n)=>{const o=su(t,e),r=su(n,e);return C(o)&&o===r})(n,t,o)?lx.after(e?t:o):s)).getOr(s),xx=(e,t)=>e.fold(M,(e=>{return o=t,!(yx(n=e)===yx(o)&&vx(n)===vx(o));var n,o})),_x=(e,t)=>e?t.fold(S(I.some,lx.start),I.none,S(I.some,lx.after),I.none):t.fold(I.none,S(I.some,lx.before),I.none,S(I.some,lx.end)),Sx=(e,t,n)=>{const o=e?1:-1;return t.setRng(Zi(n.container(),n.offset()+o).toRange()),t.getSel().modify("move",e?"forward":"backward","word"),!0};var kx;!function(e){e[e.Br=0]="Br",e[e.Block=1]="Block",e[e.Wrap=2]="Wrap",e[e.Eol=3]="Eol"}(kx||(kx={}));const Nx=(e,t)=>e===yu.Backwards?oe(t):t,Rx=(e,t,n)=>e===yu.Forwards?t.next(n):t.prev(n),Ax=(e,t,n,o)=>gr(o.getNode(t===yu.Forwards))?kx.Br:!1===au(n,o)?kx.Block:kx.Wrap,Tx=(e,t,n,o)=>{const r=Tu(n);let s=o;const a=[];for(;s;){const n=Rx(t,r,s);if(!n)break;if(gr(n.getNode(!1)))return t===yu.Forwards?{positions:Nx(t,a).concat([n]),breakType:kx.Br,breakAt:I.some(n)}:{positions:Nx(t,a),breakType:kx.Br,breakAt:I.some(n)};if(n.isVisible()){if(e(s,n)){const e=Ax(0,t,s,n);return{positions:Nx(t,a),breakType:e,breakAt:I.some(n)}}a.push(n),s=n}else s=n}return{positions:Nx(t,a),breakType:kx.Eol,breakAt:I.none()}},Ox=(e,t,n,o)=>t(n,o).breakAt.map((o=>{const r=t(n,o).positions;return e===yu.Backwards?r.concat(o):[o].concat(r)})).getOr([]),Bx=(e,t)=>X(e,((e,n)=>e.fold((()=>I.some(n)),(o=>Mt(le(o.getClientRects()),le(n.getClientRects()),((e,r)=>{const s=Math.abs(t-e.left);return Math.abs(t-r.left)<=s?n:o})).or(e)))),I.none()),Px=(e,t)=>le(t.getClientRects()).bind((t=>Bx(e,t.left))),Dx=T(Tx,Zi.isAbove,-1),Lx=T(Tx,Zi.isBelow,1),Mx=T(Ox,-1,Dx),Ix=T(Ox,1,Lx),Fx=(e,t)=>Dx(e,t).breakAt.isNone(),Ux=(e,t)=>Lx(e,t).breakAt.isNone(),zx=(e,t)=>Px(Mx(e,t),t),jx=(e,t)=>Px(Ix(e,t),t),Hx=br,$x=(e,t)=>Math.abs(e.left-t),qx=(e,t)=>Math.abs(e.right-t),Vx=(e,t)=>Be(e,((e,n)=>{const o=Math.min($x(e,t),qx(e,t)),r=Math.min($x(n,t),qx(n,t));return r===o&&Se(n,"node")&&Hx(n.node)||r<o?n:e})),Wx=e=>{const t=t=>q(t,(t=>{const n=fi(t);return n.node=e,n}));if(Jo(e))return t(e.getClientRects());if(lr(e)){const n=e.ownerDocument.createRange();return n.setStart(e,0),n.setEnd(e,e.data.length),t(n.getClientRects())}return[]},Kx=e=>te(e,Wx);var Yx;!function(e){e[e.Up=-1]="Up",e[e.Down=1]="Down"}(Yx||(Yx={}));const Gx=(e,t,n,o,r,s)=>{let a=0;const i=[],l=o=>{let s=Kx([o]);-1===e&&(s=s.reverse());for(let e=0;e<s.length;e++){const o=s[e];if(!n(o,d)){if(i.length>0&&t(o,De(i))&&a++,o.line=a,r(o))return!0;i.push(o)}}return!1},d=De(s.getClientRects());if(!d)return i;const c=s.getNode();return c&&(l(c),((e,t,n,o)=>{let r=o;for(;r=ru(r,e,Pi,t);)if(n(r))return})(e,o,l,c)),i},Xx=T(Gx,Yx.Up,hi,bi),Zx=T(Gx,Yx.Down,bi,hi),Qx=e=>De(e.getClientRects()),Jx=e=>t=>((e,t)=>t.line>e)(e,t),e_=e=>t=>((e,t)=>t.line===e)(e,t),t_=(e,t)=>{e.selection.setRng(t),pg(e,e.selection.getRng())},n_=(e,t,n)=>I.some(IE(e,t,n)),o_=(e,t,n,o,r,s)=>{const a=t===yu.Forwards,i=Tu(e.getBody()),l=T(bu,a?i.next:i.prev),d=a?o:r;if(!n.collapsed){const o=yi(n);if(s(o))return LE(t,e,o,t===yu.Backwards,!1);if($E(e)){const e=n.cloneRange();return e.collapse(t===yu.Backwards),I.from(e)}}const c=pu(t,e.getBody(),n);if(d(c))return ME(e,c.getNode(!a));let u=l(c);const m=ui(n);if(!u)return m?I.some(n):I.none();if(u=Rh(a,u),d(u))return LE(t,e,u.getNode(!a),a,!1);const f=l(u);return f&&d(f)&&vu(u,f)?LE(t,e,f.getNode(!a),a,!1):m?n_(e,u.toRange(),!1):I.none()},r_=(e,t,n,o,r,s)=>{const a=pu(t,e.getBody(),n),i=De(a.getClientRects()),l=t===Yx.Down,d=e.getBody();if(!i)return I.none();if($E(e)){const e=l?Zi.fromRangeEnd(n):Zi.fromRangeStart(n);return(l?jx:zx)(d,e).orThunk((()=>I.from(e))).map((e=>e.toRange()))}const c=(l?Zx:Xx)(d,Jx(1),a),u=Y(c,e_(1)),m=i.left,f=Vx(u,m);if(f&&s(f.node)){const n=Math.abs(m-f.left),o=Math.abs(m-f.right);return LE(t,e,f.node,n<o,!1)}let g;if(g=o(a)?a.getNode():r(a)?a.getNode(!0):yi(n),g){const n=((e,t,n,o)=>{const r=Tu(t);let s,a,i,l;const d=[];let c=0;1===e?(s=r.next,a=bi,i=hi,l=Zi.after(o)):(s=r.prev,a=hi,i=bi,l=Zi.before(o));const u=Qx(l);do{if(!l.isVisible())continue;const e=Qx(l);if(i(e,u))continue;d.length>0&&a(e,De(d))&&c++;const t=fi(e);if(t.position=l,t.line=c,n(t))return d;d.push(t)}while(l=s(l));return d})(t,d,Jx(1),g);let o=Vx(Y(n,e_(1)),m);if(o)return n_(e,o.position.toRange(),!1);if(o=De(Y(n,e_(0))),o)return n_(e,o.position.toRange(),!1)}return 0===u.length?s_(e,l).filter(l?r:o).map((t=>IE(e,t.toRange(),!1))):I.none()},s_=(e,t)=>{const n=e.selection.getRng(),o=t?Zi.fromRangeEnd(n):Zi.fromRangeStart(n),r=(s=o.container(),a=e.getBody(),Zn(yn(s),(e=>Wc(e.dom)),(e=>e.dom===a)).map((e=>e.dom)).getOr(a));var s,a;if(t){const e=Lx(r,o);return de(e.positions)}{const e=Dx(r,o);return le(e.positions)}},a_=(e,t,n)=>s_(e,t).filter(n).exists((t=>(e.selection.setRng(t.toRange()),!0))),i_=(e,t)=>{const n=e.dom.createRng();n.setStart(t.container(),t.offset()),n.setEnd(t.container(),t.offset()),e.selection.setRng(n)},l_=(e,t)=>{e?t.setAttribute("data-mce-selected","inline-boundary"):t.removeAttribute("data-mce-selected")},d_=(e,t,n)=>ax(t,n).map((t=>(i_(e,t),n))),c_=(e,t,n)=>{const o=e.getBody(),r=((e,t,n)=>{const o=Zi.fromRangeStart(e);if(e.collapsed)return o;{const r=Zi.fromRangeEnd(e);return n?Uu(t,r).getOr(r):Fu(t,o).getOr(o)}})(e.selection.getRng(),o,n);return((e,t,n,o)=>{const r=Rh(e,o),s=bx(t,n,r);return bx(t,n,r).bind(T(_x,e)).orThunk((()=>((e,t,n,o,r)=>{const s=Rh(e,r);return Du(e,n,s).map(T(Rh,e)).fold((()=>o.map(Cx)),(r=>bx(t,n,r).map(T(Ex,e,t,n,s,r)).filter(T(xx,o)))).filter(hx)})(e,t,n,s,o)))})(n,T(Sh,e),o,r).bind((n=>d_(e,t,n)))},u_=(e,t,n)=>!!kd(e)&&c_(e,t,n).isSome(),m_=(e,t,n)=>!!kd(t)&&((e,t)=>{const n=t.selection.getRng(),o=e?Zi.fromRangeEnd(n):Zi.fromRangeStart(n);return!!(e=>w(e.selection.getSel().modify))(t)&&(e&&ai(o)?Sx(!0,t.selection,o):!(e||!ii(o))&&Sx(!1,t.selection,o))})(e,t),f_=e=>{const t=ua(null),n=T(Sh,e);return e.on("NodeChange",(o=>{kd(e)&&(((e,t,n)=>{const o=q(Uo(yn(t.getRoot()),'*[data-mce-selected="inline-boundary"]'),(e=>e.dom)),r=Y(o,e),s=Y(n,e);V(re(r,s),T(l_,!1)),V(re(s,r),T(l_,!0))})(n,e.dom,o.parents),((e,t)=>{const n=t.get();if(e.selection.isCollapsed()&&!e.composing&&n){const o=Zi.fromRangeStart(e.selection.getRng());Zi.isTextPosition(o)&&!(e=>ai(e)||ii(e))(o)&&(i_(e,Mc(n,o)),t.set(null))}})(e,t),((e,t,n,o)=>{if(t.selection.isCollapsed()){const r=Y(o,e);V(r,(o=>{const r=Zi.fromRangeStart(t.selection.getRng());bx(e,t.getBody(),r).bind((e=>d_(t,n,e)))}))}})(n,e,t,o.parents))})),t},g_=T(m_,!0),p_=T(m_,!1),h_=(e,t,n)=>{if(kd(e)){const o=s_(e,t).getOrThunk((()=>{const n=e.selection.getRng();return t?Zi.fromRangeEnd(n):Zi.fromRangeStart(n)}));return bx(T(Sh,e),e.getBody(),o).exists((t=>{const o=Cx(t);return ax(n,o).exists((t=>(i_(e,t),!0)))}))}return!1},b_=(e,t)=>n=>ax(t,n).map((t=>()=>i_(e,t))),v_=(e,t,n,o)=>{const r=e.getBody(),s=T(Sh,e);e.undoManager.ignore((()=>{e.selection.setRng(((e,t)=>{const n=document.createRange();return n.setStart(e.container(),e.offset()),n.setEnd(t.container(),t.offset()),n})(n,o)),Bh(e),bx(s,r,Zi.fromRangeStart(e.selection.getRng())).map(wx).bind(b_(e,t)).each(D)})),e.nodeChanged()},y_=(e,t,n)=>{if(e.selection.isCollapsed()&&kd(e)){const o=Zi.fromRangeStart(e.selection.getRng());return((e,t,n,o)=>{const r=((e,t)=>su(t,e)||e)(e.getBody(),o.container()),s=T(Sh,e),a=bx(s,r,o);return a.bind((e=>n?e.fold(N(I.some(wx(e))),I.none,N(I.some(Cx(e))),I.none):e.fold(I.none,N(I.some(Cx(e))),I.none,N(I.some(wx(e)))))).map(b_(e,t)).getOrThunk((()=>{const i=Lu(n,r,o),l=i.bind((e=>bx(s,r,e)));return Mt(a,l,(()=>Nh(s,r,o).bind((t=>(e=>Mt(zu(e),ju(e),((t,n)=>{const o=Rh(!0,t),r=Rh(!1,n);return Fu(e,o).forall((e=>e.isEqual(r)))})).getOr(!0))(t)?I.some((()=>{xh(e,n,yn(t))})):I.none())))).getOrThunk((()=>l.bind((()=>i.map((r=>()=>{n?v_(e,t,o,r):v_(e,t,r,o)}))))))}))})(e,t,n,o)}return I.none()},C_=(e,t)=>{const n=yn(e.getBody()),o=yn(e.selection.getStart()),r=Ip(o,n);return J(r,t).fold(N(r),(e=>r.slice(0,e)))},w_=e=>1===zn(e),E_=(e,t)=>{const n=T(nv,e);return te(t,(e=>n(e)?[e.dom]:[]))},x_=e=>{const t=(e=>C_(e,(t=>e.schema.isBlock(Ht(t)))))(e);return E_(e,t)},__=(e,t)=>{const n=Y((e=>C_(e,(t=>e.schema.isBlock(Ht(t))||(e=>zn(e)>1)(t))))(e),w_);return de(n).bind((o=>{const r=Zi.fromRangeStart(e.selection.getRng());return Mh(t,r,o.dom)&&!Um(o)?I.some((()=>((e,t,n,o)=>{const r=E_(t,o);if(0===r.length)xh(t,e,n);else{const e=tv(n.dom,r);t.selection.setRng(e.toRange())}})(t,e,o,n))):I.none()}))},S_=(e,t)=>{const n=e.selection.getStart(),o=((e,t)=>{const n=t.parentElement;return gr(t)&&!h(n)&&e.dom.isEmpty(n)})(e,n)||Um(yn(n))?tv(n,t):((e,t)=>{const{caretContainer:n,caretPosition:o}=ev(t);return e.insertNode(n.dom),o})(e.selection.getRng(),t);e.selection.setRng(o.toRange())},k_=e=>lr(e.startContainer),N_=e=>{const t=e.selection.getRng();return(e=>0===e.startOffset&&k_(e))(t)&&((e,t)=>{const n=t.startContainer.parentElement;return!h(n)&&nv(e,yn(n))})(e,t)&&(e=>(e=>(e=>{const t=e.startContainer.parentNode,n=e.endContainer.parentNode;return!h(t)&&!h(n)&&t.isEqualNode(n)})(e)&&(e=>{const t=e.endContainer;return e.endOffset===(lr(t)?t.length:t.childNodes.length)})(e))(e)||(e=>!e.endContainer.isEqualNode(e.commonAncestorContainer))(e))(t)},R_=(e,t)=>e.selection.isCollapsed()?__(e,t):(e=>{if(N_(e)){const t=x_(e);return I.some((()=>{Bh(e),((e,t)=>{const n=re(t,x_(e));n.length>0&&S_(e,n)})(e,t)}))}return I.none()})(e),A_=e=>((e=>{const t=e.selection.getRng();return t.collapsed&&(k_(t)||e.dom.isEmpty(t.startContainer))&&!(e=>{return t=yn(e.selection.getStart()),n=e.schema,zo(t,(e=>$u(e.dom)),(e=>n.isBlock(Ht(e))));var t,n})(e)})(e)&&S_(e,[]),!0),T_=(e,t,n)=>C(n)?I.some((()=>{e._selectionOverrides.hideFakeCaret(),xh(e,t,yn(n))})):I.none(),O_=(e,t)=>e.selection.isCollapsed()?((e,t)=>{const n=t?Tp:Op,o=t?yu.Forwards:yu.Backwards,r=pu(o,e.getBody(),e.selection.getRng());return n(r)?T_(e,t,r.getNode(!t)):I.from(Rh(t,r)).filter((e=>n(e)&&vu(r,e))).bind((n=>T_(e,t,n.getNode(!t))))})(e,t):((e,t)=>{const n=e.selection.getNode();return Cr(n)?T_(e,t,n):I.none()})(e,t),B_=e=>Ze(null!=e?e:"").getOr(0),P_=(e,t)=>(e||"table"===Ht(t)?"margin":"padding")+("rtl"===co(t,"direction")?"-right":"-left"),D_=e=>{const t=M_(e);return!e.mode.isReadOnly()&&(t.length>1||((e,t)=>ne(t,(t=>{const n=P_(cd(e),t),o=mo(t,n).map(B_).getOr(0);return"false"!==e.dom.getContentEditable(t.dom)&&o>0})))(e,t))},L_=e=>qa(e)||Va(e),M_=e=>Y(_o(e.selection.getSelectedBlocks()),(e=>!L_(e)&&!(e=>An(e).exists(L_))(e)&&Qn(e,(e=>hr(e.dom)||br(e.dom))).exists((e=>hr(e.dom))))),I_=(e,t)=>{var n,o;const{dom:r}=e,s=ud(e),a=null!==(o=null===(n=/[a-z%]+$/i.exec(s))||void 0===n?void 0:n[0])&&void 0!==o?o:"px",i=B_(s),l=cd(e);V(M_(e),(e=>{((e,t,n,o,r,s)=>{const a=P_(n,yn(s)),i=B_(e.getStyle(s,a));if("outdent"===t){const t=Math.max(0,i-o);e.setStyle(s,a,t?t+r:"")}else{const t=i+o+r;e.setStyle(s,a,t)}})(r,t,l,i,a,e.dom)}))},F_=e=>I_(e,"outdent"),U_=e=>{if(e.selection.isCollapsed()&&D_(e)){const t=e.dom,n=e.selection.getRng(),o=Zi.fromRangeStart(n),r=t.getParent(n.startContainer,t.isBlock);if(null!==r&&Hp(yn(r),o,e.schema))return I.some((()=>F_(e)))}return I.none()},z_=(e,t,n)=>ue([U_,GE,jE,(e,n)=>y_(e,t,n),OE,cb,XE,O_,DE,R_],(t=>t(e,n))).filter((t=>e.selection.isEditable())),j_=(e,t)=>{e.addCommand("delete",(()=>{((e,t)=>{z_(e,t,!1).fold((()=>{e.selection.isEditable()&&(Bh(e),Lh(e))}),D)})(e,t)})),e.addCommand("forwardDelete",(()=>{((e,t)=>{z_(e,t,!0).fold((()=>{e.selection.isEditable()&&(e=>{Oh(e,"ForwardDelete")})(e)}),D)})(e,t)}))},H_=e=>void 0===e.touches||1!==e.touches.length?I.none():I.some(e.touches[0]),$_=(e,t)=>_e(e,t.nodeName),q_=(e,t)=>!!lr(t)||!!Jo(t)&&!($_(e.getBlockElements(),t)||nm(t)||Wr(e,t)||Br(t)),V_=(e,t)=>{if(lr(t)){if(0===t.data.length)return!0;if(/^\s+$/.test(t.data))return!t.nextSibling||$_(e,t.nextSibling)||Br(t.nextSibling)}return!1},W_=e=>e.dom.create(ql(e),Vl(e)),K_=e=>{const t=e.dom,n=e.selection,o=e.schema,r=o.getBlockElements(),s=n.getStart(),a=e.getBody();let i,l,d=!1;const c=ql(e);if(!s||!Jo(s))return;const u=a.nodeName.toLowerCase();if(!o.isValidChild(u,c.toLowerCase())||((e,t,n)=>$(Mp(yn(n),yn(t)),(t=>$_(e,t.dom))))(r,a,s))return;const m=n.getRng(),{startContainer:f,startOffset:g,endContainer:p,endOffset:h}=m,b=Fg(e);let v=a.firstChild;for(;v;)if(Jo(v)&&Hr(o,v),q_(o,v)){if(V_(r,v)){l=v,v=v.nextSibling,t.remove(l);continue}i||(i=W_(e),a.insertBefore(i,v),d=!0),l=v,v=v.nextSibling,i.appendChild(l)}else i=null,v=v.nextSibling;d&&b&&(m.setStart(f,g),m.setEnd(p,h),n.setRng(m),e.nodeChanged())},Y_=(e,t,n)=>{const o=yn(W_(e)),r=Ga();vo(o,r),n(t,o);const s=document.createRange();return s.setStartBefore(r.dom),s.setEndBefore(r.dom),s},G_=e=>t=>-1!==(" "+t.attr("class")+" ").indexOf(e),X_=(e,t,n)=>function(o){const r=arguments,s=r[r.length-2],a=s>0?t.charAt(s-1):"";if('"'===a)return o;if(">"===a){const e=t.lastIndexOf("<",s);if(-1!==e&&-1!==t.substring(e,s).indexOf('contenteditable="false"'))return o}return'<span class="'+n+'" data-mce-content="'+e.dom.encode(r[0])+'">'+e.dom.encode("string"==typeof r[1]?r[1]:r[0])+"</span>"},Z_=(e,t)=>ne(e,(e=>{const n=t.match(e);return null!==n&&n[0].length===t.length})),Q_=(e,t)=>{t.hasAttribute("data-mce-caret")&&(ci(t),e.selection.setRng(e.selection.getRng()),e.selection.scrollIntoView(t))},J_=(e,t)=>{const n=(e=>to(yn(e.getBody()),"*[data-mce-caret]").map((e=>e.dom)).getOrNull())(e);if(n)return"compositionstart"===t.type?(t.preventDefault(),t.stopPropagation(),void Q_(e,n)):void(si(n)&&(Q_(e,n),e.undoManager.add()))},eS=br,tS=(e,t,n)=>{const o=Tu(e.getBody()),r=T(bu,1===t?o.next:o.prev);if(n.collapsed){const o=e.dom.getParent(n.startContainer,"PRE");if(!o)return;if(!r(Zi.fromRangeStart(n))){const n=yn((e=>{const t=e.dom.create(ql(e));return t.innerHTML='<br data-mce-bogus="1">',t})(e));1===t?ho(yn(o),n):po(yn(o),n),e.selection.select(n.dom,!0),e.selection.collapse()}}},nS=(e,t)=>((e,t)=>{const n=t?yu.Forwards:yu.Backwards,o=e.selection.getRng();return((e,t,n)=>o_(t,e,n,Dp,Lp,eS))(n,e,o).orThunk((()=>(tS(e,n,o),I.none())))})(e,((e,t)=>{const n=t?e.getEnd(!0):e.getStart(!0);return kh(n)?!t:t})(e.selection,t)).exists((t=>(t_(e,t),!0))),oS=(e,t)=>((e,t)=>{const n=t?1:-1,o=e.selection.getRng();return((e,t,n)=>r_(t,e,n,(e=>Dp(e)||Bp(e)),(e=>Lp(e)||Pp(e)),eS))(n,e,o).orThunk((()=>(tS(e,n,o),I.none())))})(e,t).exists((t=>(t_(e,t),!0))),rS=(e,t)=>a_(e,t,t?Lp:Dp),sS=(e,t)=>HE(e,!t).map((n=>{const o=n.toRange(),r=e.selection.getRng();return t?o.setStart(r.startContainer,r.startOffset):o.setEnd(r.endContainer,r.endOffset),o})).exists((t=>(t_(e,t),!0))),aS=e=>H(["figcaption"],Ht(e)),iS=(e,t)=>!!e.selection.isCollapsed()&&((e,t)=>{const n=yn(e.getBody()),o=Zi.fromRangeStart(e.selection.getRng());return((e,t,n)=>{const o=T(_n,t);return Qn(yn(e.container()),(e=>n.isBlock(Ht(e))),o).filter(aS)})(o,n,e.schema).exists((()=>{if(((e,t,n)=>t?Ux(e.dom,n):Fx(e.dom,n))(n,t,o)){const o=Y_(e,n,t?vo:bo);return e.selection.setRng(o),!0}return!1}))})(e,t),lS=(e,t)=>((e,t)=>t?I.from(e.dom.getParent(e.selection.getNode(),"details")).map((t=>((e,t)=>{const n=e.selection.getRng(),o=Zi.fromRangeStart(n);return!(e.getBody().lastChild!==t||!Ux(t,o)||(e.execCommand("InsertNewBlockAfter"),0))})(e,t))).getOr(!1):I.from(e.dom.getParent(e.selection.getNode(),"summary")).bind((t=>I.from(e.dom.getParent(t,"details")).map((n=>((e,t,n)=>{const o=e.selection.getRng(),r=Zi.fromRangeStart(o);return!(e.getBody().firstChild!==t||!Fx(n,r)||(e.execCommand("InsertNewBlockBefore"),0))})(e,n,t))))).getOr(!1))(e,t),dS={shiftKey:!1,altKey:!1,ctrlKey:!1,metaKey:!1,keyCode:0},cS=(e,t)=>t.keyCode===e.keyCode&&t.shiftKey===e.shiftKey&&t.altKey===e.altKey&&t.ctrlKey===e.ctrlKey&&t.metaKey===e.metaKey,uS=(e,...t)=>()=>e.apply(null,t),mS=(e,t)=>Q(((e,t)=>te((e=>q(e,(e=>({...dS,...e}))))(e),(e=>cS(e,t)?[e]:[])))(e,t),(e=>e.action())),fS=(e,t)=>ue(((e,t)=>te((e=>q(e,(e=>({...dS,...e}))))(e),(e=>cS(e,t)?[e]:[])))(e,t),(e=>e.action())),gS=(e,t)=>{const n=t?yu.Forwards:yu.Backwards,o=e.selection.getRng();return o_(e,n,o,Tp,Op,Cr).exists((t=>(t_(e,t),!0)))},pS=(e,t)=>{const n=t?1:-1,o=e.selection.getRng();return r_(e,n,o,Tp,Op,Cr).exists((t=>(t_(e,t),!0)))},hS=(e,t)=>a_(e,t,t?Op:Tp),bS=El([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),vS={...bS,none:e=>bS.none(e)},yS=(e,t,n)=>te(Mn(e),(e=>En(e,t)?n(e)?[e]:[]:yS(e,t,n))),CS=(e,t)=>no(e,"table",t),wS=(e,t,n,o,r=M)=>{const s=1===o;if(!s&&n<=0)return vS.first(e[0]);if(s&&n>=e.length-1)return vS.last(e[e.length-1]);{const s=n+o,a=e[s];return r(a)?vS.middle(t,a):wS(e,t,s,o,r)}},ES=(e,t)=>CS(e,t).bind((t=>{const n=yS(t,"th,td",M);return J(n,(t=>_n(e,t))).map((e=>({index:e,all:n})))})),xS=["img","br"],_S=e=>{return(t=e,Ua.getOption(t)).filter((e=>0!==e.trim().length||e.indexOf(Vo)>-1)).isSome()||H(xS,Ht(e))||(e=>Vt(e)&&"false"===en(e,"contenteditable"))(e);var t},SS=(e,t,n,o,r)=>{const s=Uo(yn(n),"td,th,caption").map((e=>e.dom)),a=Y(((e,t)=>te(t,(t=>{const n=((e,t)=>({left:e.left-t,top:e.top-t,right:e.right+-2,bottom:e.bottom+-2,width:e.width+t,height:e.height+t}))(fi(t.getBoundingClientRect()),-1);return[{x:n.left,y:e(n),cell:t},{x:n.right,y:e(n),cell:t}]})))(e,s),(e=>t(e,r)));return((e,t,n)=>X(e,((e,o)=>e.fold((()=>I.some(o)),(e=>{const r=Math.sqrt(Math.abs(e.x-t)+Math.abs(e.y-n)),s=Math.sqrt(Math.abs(o.x-t)+Math.abs(o.y-n));return I.some(s<r?o:e)}))),I.none()))(a,o,r).map((e=>e.cell))},kS=T(SS,(e=>e.bottom),((e,t)=>e.y<t)),NS=T(SS,(e=>e.top),((e,t)=>e.y>t)),RS=(e,t,n)=>{const o=e(t,n);return(e=>e.breakType===kx.Wrap&&0===e.positions.length)(o)||!gr(n.getNode())&&(e=>e.breakType===kx.Br&&1===e.positions.length)(o)?!((e,t,n)=>n.breakAt.exists((n=>e(t,n).breakAt.isSome())))(e,t,o):o.breakAt.isNone()},AS=T(RS,Dx),TS=T(RS,Lx),OS=(e,t,n,o)=>{const r=e.selection.getRng(),s=t?1:-1;return!(!$c()||!((e,t,n)=>{const o=Zi.fromRangeStart(t);return Iu(!e,n).exists((e=>e.isEqual(o)))})(t,r,n)||(LE(s,e,n,!t,!1).each((t=>{t_(e,t)})),0))},BS=(e,t,n)=>{const o=((e,t)=>{const n=t.getNode(e);return sr(n)?I.some(n):I.none()})(!!t,n),r=!1===t;o.fold((()=>t_(e,n.toRange())),(o=>Iu(r,e.getBody()).filter((e=>e.isEqual(n))).fold((()=>t_(e,n.toRange())),(n=>((e,t,n)=>{t.undoManager.transact((()=>{const o=e?ho:po,r=Y_(t,yn(n),o);t_(t,r)}))})(t,e,o)))))},PS=(e,t,n,o)=>{const r=e.selection.getRng(),s=Zi.fromRangeStart(r),a=e.getBody();if(!t&&AS(o,s)){const o=((e,t,n)=>((e,t)=>le(t.getClientRects()).bind((t=>kS(e,t.left,t.top))).bind((e=>{return Px(ju(n=e).map((e=>Dx(n,e).positions.concat(e))).getOr([]),t);var n})))(t,n).orThunk((()=>le(n.getClientRects()).bind((n=>Bx(Mx(e,Zi.before(t)),n.left))))).getOr(Zi.before(t)))(a,n,s);return BS(e,t,o),!0}if(t&&TS(o,s)){const o=((e,t,n)=>((e,t)=>de(t.getClientRects()).bind((t=>NS(e,t.left,t.top))).bind((e=>{return Px(zu(n=e).map((e=>[e].concat(Lx(n,e).positions))).getOr([]),t);var n})))(t,n).orThunk((()=>le(n.getClientRects()).bind((n=>Bx(Ix(e,Zi.after(t)),n.left))))).getOr(Zi.after(t)))(a,n,s);return BS(e,t,o),!0}return!1},DS=(e,t,n)=>I.from(e.dom.getParent(e.selection.getNode(),"td,th")).bind((o=>I.from(e.dom.getParent(o,"table")).map((r=>n(e,t,r,o))))).getOr(!1),LS=(e,t)=>DS(e,t,OS),MS=(e,t)=>DS(e,t,PS),IS=(e,t,n)=>n.fold(I.none,I.none,((e,t)=>{return(n=t,Jn(n,_S)).map((e=>(e=>{const t=Bf.exact(e,0,e,0);return If(t)})(e)));var n}),(n=>(e.execCommand("mceTableInsertRowAfter"),FS(e,t,n)))),FS=(e,t,n)=>{return IS(e,t,(r=oo,ES(o=n,void 0).fold((()=>vS.none(o)),(e=>wS(e.all,o,e.index,1,r)))));var o,r},US=(e,t,n)=>{return IS(e,t,(r=oo,ES(o=n,void 0).fold((()=>vS.none()),(e=>wS(e.all,o,e.index,-1,r)))));var o,r},zS=(e,t)=>{const n=["table","li","dl"],o=yn(e.getBody()),r=e=>{const t=Ht(e);return _n(e,o)||H(n,t)},s=e.selection.getRng();return((e,t)=>((e,t,n=L)=>n(t)?I.none():H(e,Ht(t))?I.some(t):eo(t,e.join(","),(e=>En(e,"table")||n(e))))(["td","th"],e,t))(yn(t?s.endContainer:s.startContainer),r).map((n=>(CS(n,r).each((t=>{e.model.table.clearSelectedCells(t.dom)})),e.selection.collapse(!t),(t?FS:US)(e,r,n).each((t=>{e.selection.setRng(t)})),!0))).getOr(!1)},jS=(e,t)=>({container:e,offset:t}),HS=la.DOM,$S=e=>t=>e===t?-1:0,qS=(e,t,n)=>{if(lr(e)&&t>=0)return I.some(jS(e,t));{const o=Fa(HS);return I.from(o.backwards(e,t,$S(e),n)).map((e=>jS(e.container,e.container.data.length)))}},VS=(e,t,n)=>{if(!lr(e))return I.none();const o=e.data;if(t>=0&&t<=o.length)return I.some(jS(e,t));{const o=Fa(HS);return I.from(o.backwards(e,t,$S(e),n)).bind((e=>{const o=e.container.data;return VS(e.container,t+o.length,n)}))}},WS=(e,t,n)=>{if(!lr(e))return I.none();const o=e.data;if(t<=o.length)return I.some(jS(e,t));{const r=Fa(HS);return I.from(r.forwards(e,t,$S(e),n)).bind((e=>WS(e.container,t-o.length,n)))}},KS=(e,t,n,o,r)=>{const s=Fa(e,(e=>t=>e.isBlock(t)||H(["BR","IMG","HR","INPUT"],t.nodeName)||"false"===e.getContentEditable(t))(e));return I.from(s.backwards(t,n,o,r))},YS=e=>""!==e&&-1!==" \xa0\f\n\r\t\v".indexOf(e),GS=(e,t)=>e.substring(t.length),XS=(e,t,n,o=!1)=>{if(!(r=t).collapsed||!lr(r.startContainer))return I.none();var r;const s={text:"",offset:0},a=e.getParent(t.startContainer,e.isBlock)||e.getRoot();return KS(e,t.startContainer,t.startOffset,((e,t,r)=>(s.text=r+s.text,s.offset+=t,((e,t,n,o=!1)=>{let r;const s=n.charAt(0);for(r=t-1;r>=0;r--){const a=e.charAt(r);if(!o&&YS(a))return I.none();if(s===a&&He(e,n,r,t))break}return I.some(r)})(s.text,s.offset,n,o).getOr(t))),a).bind((e=>{const o=t.cloneRange();if(o.setStart(e.container,e.offset),o.setEnd(t.endContainer,t.endOffset),o.collapsed)return I.none();const r=(e=>Ja(e.toString().replace(/\u00A0/g," ")))(o);return 0!==r.lastIndexOf(n)?I.none():I.some({text:GS(r,n),range:o,trigger:n})}))},ZS=e=>{if((e=>3===e.nodeType)(e))return jS(e,e.data.length);{const t=e.childNodes;return t.length>0?ZS(t[t.length-1]):jS(e,t.length)}},QS=(e,t)=>{const n=e.childNodes;return n.length>0&&t<n.length?QS(n[t],0):n.length>0&&(e=>1===e.nodeType)(e)&&n.length===t?ZS(n[n.length-1]):jS(e,t)},JS=(e,t,n,o={})=>{var r;const s=t(),a=null!==(r=e.selection.getRng().startContainer.nodeValue)&&void 0!==r?r:"",i=Y(s.lookupByTrigger(n.trigger),(t=>n.text.length>=t.minChars&&t.matches.getOrThunk((()=>(e=>t=>{const n=QS(t.startContainer,t.startOffset);return!((e,t)=>{var n;const o=null!==(n=e.getParent(t.container,e.isBlock))&&void 0!==n?n:e.getRoot();return KS(e,t.container,t.offset,((e,t)=>0===t?-1:t),o).filter((e=>{const t=e.container.data.charAt(e.offset-1);return!YS(t)})).isSome()})(e,n)})(e.dom)))(n.range,a,n.text)));if(0===i.length)return I.none();const l=Promise.all(q(i,(e=>e.fetch(n.text,e.maxResults,o).then((t=>({matchText:n.text,items:t,columns:e.columns,onAction:e.onAction,highlightOn:e.highlightOn}))))));return I.some({lookupData:l,context:n})};var ek;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(ek||(ek={}));const tk=(e,t,n)=>e.stype===ek.Error?t(e.serror):n(e.svalue),nk=e=>({stype:ek.Value,svalue:e}),ok=e=>({stype:ek.Error,serror:e}),rk=tk,sk=e=>f(e)&&fe(e).length>100?" removed due to size":JSON.stringify(e,null,2),ak=(e,t)=>ok([{path:e,getErrorInfo:t}]),ik=(e,t)=>({extract:(n,o)=>xe(o,e).fold((()=>((e,t)=>ak(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(n,e)),(e=>((e,t,n,o)=>xe(n,o).fold((()=>((e,t,n)=>ak(e,(()=>'The chosen schema: "'+n+'" did not exist in branches: '+sk(t))))(e,n,o)),(n=>n.extract(e.concat(["branch: "+o]),t))))(n,o,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+fe(t)}),lk=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const n={};for(let o=0;o<t.length;o++){const r=t[o];for(const t in r)_e(r,t)&&(n[t]=e(n[t],r[t]))}return n},dk=lk(((e,t)=>g(e)&&g(t)?dk(e,t):t)),ck=(lk(((e,t)=>t)),e=>({tag:"defaultedThunk",process:N(e)})),uk=e=>{const t=(e=>{const t=[],n=[];return V(e,(e=>{tk(e,(e=>n.push(e)),(e=>t.push(e)))})),{values:t,errors:n}})(e);return t.errors.length>0?(n=t.errors,S(ok,ee)(n)):nk(t.values);var n},mk=(e,t,n)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return n(e.newKey,e.instantiator)}},fk=e=>({extract:(t,n)=>{return o=e(n),r=e=>((e,t)=>ak(e,N(t)))(t,e),o.stype===ek.Error?r(o.serror):o;var o,r},toString:N("val")}),gk=fk(nk),pk=(e,t,n,o)=>o(xe(e,t).getOrThunk((()=>n(e)))),hk=(e,t,n,o,r)=>{const s=e=>r.extract(t.concat([o]),e),a=e=>e.fold((()=>nk(I.none())),(e=>{const n=r.extract(t.concat([o]),e);return s=n,a=I.some,s.stype===ek.Value?{stype:ek.Value,svalue:a(s.svalue)}:s;var s,a}));switch(e.tag){case"required":return((e,t,n,o)=>xe(t,n).fold((()=>((e,t,n)=>ak(e,(()=>'Could not find valid *required* value for "'+t+'" in '+sk(n))))(e,n,t)),o))(t,n,o,s);case"defaultedThunk":return pk(n,o,e.process,s);case"option":return((e,t,n)=>n(xe(e,t)))(n,o,a);case"defaultedOptionThunk":return((e,t,n,o)=>o(xe(e,t).map((t=>!0===t?n(e):t))))(n,o,e.process,a);case"mergeWithThunk":return pk(n,o,N({}),(t=>{const o=dk(e.process(n),t);return s(o)}))}},bk=e=>({extract:(t,n)=>((e,t,n)=>{const o={},r=[];for(const s of n)mk(s,((n,s,a,i)=>{const l=hk(a,e,t,n,i);rk(l,(e=>{r.push(...e)}),(e=>{o[s]=e}))}),((e,n)=>{o[e]=n(t)}));return r.length>0?ok(r):nk(o)})(t,n,e),toString:()=>{const t=q(e,(e=>mk(e,((e,t,n,o)=>e+" -> "+o.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),vk=e=>({extract:(t,n)=>{const o=q(n,((n,o)=>e.extract(t.concat(["["+o+"]"]),n)));return uk(o)},toString:()=>"array("+e.toString()+")"}),yk=(e,t,n)=>{return o=((e,t,n)=>((e,t)=>e.stype===ek.Error?{stype:ek.Error,serror:t(e.serror)}:e)(t.extract([e],n),(e=>({input:n,errors:e}))))(e,t,n),tk(o,wl.error,wl.value);var o},Ck=(e,t)=>ik(e,he(t,bk)),wk=N(gk),Ek=(e,t)=>fk((n=>{const o=typeof n;return e(n)?nk(n):ok(`Expected type: ${t} but got: ${o}`)})),xk=Ek(E,"number"),_k=Ek(m,"string"),Sk=Ek(b,"boolean"),kk=Ek(w,"function"),Nk=(e,t,n,o)=>({tag:"field",key:e,newKey:t,presence:n,prop:o}),Rk=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),Ak=(e,t)=>Nk(e,e,{tag:"required",process:{}},t),Tk=e=>Ak(e,_k),Ok=e=>Ak(e,kk),Bk=(e,t)=>Nk(e,e,{tag:"option",process:{}},t),Pk=e=>Bk(e,_k),Dk=(e,t,n)=>Nk(e,e,ck(t),n),Lk=(e,t)=>Dk(e,t,xk),Mk=(e,t,n)=>Dk(e,t,(e=>{return t=t=>H(e,t)?wl.value(t):wl.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`),fk((e=>t(e).fold(ok,nk)));var t})(n)),Ik=(e,t)=>Dk(e,t,Sk),Fk=(e,t)=>Dk(e,t,kk),Uk=Tk("type"),zk=Ok("fetch"),jk=Ok("onAction"),Hk=Fk("onSetup",(()=>_)),$k=Pk("text"),qk=Pk("icon"),Vk=Pk("tooltip"),Wk=Pk("label"),Kk=Ik("active",!1),Yk=Ik("enabled",!0),Gk=Ik("primary",!1),Xk=e=>((e,t)=>Dk("type",t,_k))(0,e),Zk=bk([Uk,Tk("trigger"),Lk("minChars",1),(1,((e,t)=>Nk(e,e,ck(1),wk()))("columns")),Lk("maxResults",10),("matches",Bk("matches",kk)),zk,jk,(Qk=_k,Dk("highlightOn",[],vk(Qk)))]);var Qk;const Jk=[Yk,Vk,qk,$k,Hk],eN=[Kk].concat(Jk),tN=[Fk("predicate",L),Mk("scope","node",["node","editor"]),Mk("position","selection",["node","selection","line"])],nN=Jk.concat([Xk("contextformbutton"),Gk,jk,Rk("original",R)]),oN=eN.concat([Xk("contextformbutton"),Gk,jk,Rk("original",R)]),rN=Jk.concat([Xk("contextformbutton")]),sN=eN.concat([Xk("contextformtogglebutton")]),aN=Ck("type",{contextformbutton:nN,contextformtogglebutton:oN});bk([Xk("contextform"),Fk("initValue",N("")),Wk,((e,t)=>Nk(e,e,{tag:"required",process:{}},vk(t)))("commands",aN),Bk("launch",Ck("type",{contextformbutton:rN,contextformtogglebutton:sN}))].concat(tN));const iN=e=>{const t=e.ui.registry.getAll().popups,n=he(t,(e=>{return(t=e,yk("Autocompleter",Zk,t)).fold((e=>{throw new Error("Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:N("... (only showing first ten failures)")}]):e;return q(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})((t=e).errors).join("\n")+"\n\nInput object: "+sk(t.input));var t}),R);var t})),o=ke(we(n,(e=>e.trigger))),r=Ee(n);return{dataset:n,triggers:o,lookupByTrigger:e=>Y(r,(t=>t.trigger===e))}},lN=e=>{const t=va(),n=ua(!1),o=t.isSet,r=()=>{o()&&((e=>{e.dispatch("AutocompleterEnd")})(e),n.set(!1),t.clear())},s=Le((()=>iN(e))),a=a=>{(n=>t.get().map((t=>XS(e.dom,e.selection.getRng(),t.trigger,!0).bind((t=>JS(e,s,t,n))))).getOrThunk((()=>((e,t)=>{const n=t(),o=e.selection.getRng();return((e,t,n)=>ue(n.triggers,(n=>XS(e,t,n))))(e.dom,o,n).bind((n=>JS(e,t,n)))})(e,s))))(a).fold(r,(s=>{(n=>{o()||e.composing||t.set({trigger:n.trigger,matchLength:n.text.length})})(s.context),s.lookupData.then((o=>{t.get().map((a=>{const i=s.context;a.trigger===i.trigger&&(i.text.length-a.matchLength>=10?r():(t.set({...a,matchLength:i.text.length}),n.get()?(pf(e,{range:i.range}),((e,t)=>{e.dispatch("AutocompleterUpdate",t)})(e,{lookupData:o})):(n.set(!0),pf(e,{range:i.range}),((e,t)=>{e.dispatch("AutocompleterStart",t)})(e,{lookupData:o}))))}))}))}))},i=()=>t.get().bind((({trigger:t})=>{const o=e.selection.getRng();return XS(e.dom,o,t,n.get()).filter((({range:e})=>((e,t)=>{const n=e.compareBoundaryPoints(window.Range.START_TO_START,t),o=e.compareBoundaryPoints(window.Range.END_TO_END,t);return n>=0&&o<=0})(o,e))).map((({range:e})=>e))}));e.addCommand("mceAutocompleterReload",((e,t)=>{const n=f(t)?t.fetchOptions:{};a(n)})),e.addCommand("mceAutocompleterClose",r),e.addCommand("mceAutocompleterRefreshActiveRange",(()=>{i().each((t=>{pf(e,{range:t})}))})),e.editorCommands.addQueryStateHandler("mceAutoCompleterInRange",(()=>i().isSome())),((e,t)=>{const n=Ca(t.load,50);e.on("input",(()=>{n.throttle()})),e.on("keydown",(e=>{const o=e.which;8===o?n.throttle():27===o?t.cancelIfNecessary():38!==o&&40!==o||n.cancel()})),e.on("remove",n.cancel)})(e,{cancelIfNecessary:r,load:a})},dN=xt().browser.isSafari(),cN=e=>Xa(yn(e)),uN=(e,t)=>{var n;return 0===e.startOffset&&e.endOffset===(null===(n=t.textContent)||void 0===n?void 0:n.length)},mN=(e,t)=>I.from(e.getParent(t.container(),"details")),fN=(e,t)=>mN(e,t).isSome(),gN=(e,t)=>{const n=t.getNode();v(n)||e.selection.setCursorLocation(n,t.offset())},pN=(e,t,n)=>{const o=e.dom.getParent(t.container(),"details");if(o&&!o.open){const t=e.dom.select("summary",o)[0];t&&(n?zu(t):ju(t)).each((t=>gN(e,t)))}else gN(e,t)},hN=(e,t,n)=>{const{dom:o,selection:r}=e,s=e.getBody();if("character"===n){const n=Zi.fromRangeStart(r.getRng()),a=o.getParent(n.container(),o.isBlock),i=mN(o,n),l=a&&o.isEmpty(a),d=h(null==a?void 0:a.previousSibling),c=h(null==a?void 0:a.nextSibling);return!!(l&&(t?c:d)&&Lu(!t,s,n).exists((e=>fN(o,e)&&!Lt(i,mN(o,e)))))||Lu(t,s,n).fold(L,(n=>{const r=mN(o,n);if(fN(o,n)&&!Lt(i,r)){if(t||pN(e,n,!1),a&&l){if(t&&d)return!0;if(!t&&c)return!0;pN(e,n,t),e.dom.remove(a)}return!0}return!1}))}return!1},bN=(e,t,n,o)=>{const r=e.selection.getRng(),s=Zi.fromRangeStart(r),a=e.getBody();return"selection"===o?((e,t)=>{const n=t.startSummary.exists((t=>t.contains(e.startContainer))),o=t.startSummary.exists((t=>t.contains(e.endContainer))),r=t.startDetails.forall((e=>t.endDetails.forall((t=>e!==t))));return(n||o)&&!(n&&o)||r})(r,t):n?((e,t)=>t.startSummary.exists((t=>((e,t)=>ju(t).exists((n=>gr(n.getNode())&&Uu(t,n).exists((t=>t.isEqual(e)))||n.isEqual(e))))(e,t))))(s,t)||((e,t,n)=>n.startDetails.exists((n=>Fu(e,t).forall((e=>!n.contains(e.container()))))))(a,s,t):((e,t)=>t.startSummary.exists((t=>((e,t)=>zu(t).exists((t=>t.isEqual(e))))(e,t))))(s,t)||((e,t)=>t.startDetails.exists((n=>Uu(n,e).forall((n=>t.startSummary.exists((t=>!t.contains(e.container())&&t.contains(n.container()))))))))(s,t)},vN=(e,t,n)=>((e,t,n)=>((e,t)=>{const n=I.from(e.getParent(t.startContainer,"details")),o=I.from(e.getParent(t.endContainer,"details"));if(n.isSome()||o.isSome()){const t=n.bind((t=>I.from(e.select("summary",t)[0])));return I.some({startSummary:t,startDetails:n,endDetails:o})}return I.none()})(e.dom,e.selection.getRng()).fold((()=>hN(e,t,n)),(o=>bN(e,o,t,n)||hN(e,t,n))))(e,t,n)||dN&&((e,t,n)=>{const o=e.selection,r=o.getNode(),s=o.getRng(),a=Zi.fromRangeStart(s);return!!xr(r)&&("selection"===n&&uN(s,r)||Mh(t,a,r)?cN(r):e.undoManager.transact((()=>{const s=o.getSel();let{anchorNode:a,anchorOffset:i,focusNode:l,focusOffset:d}=null!=s?s:{};const c=()=>{C(a)&&C(i)&&C(l)&&C(d)&&(null==s||s.setBaseAndExtent(a,i,l,d))},u=(e,t)=>{V(e.childNodes,(e=>{vm(e)&&t.appendChild(e)}))},m=e.dom.create("span",{"data-mce-bogus":"1"});u(r,m),r.appendChild(m),c(),"word"!==n&&"line"!==n||null==s||s.modify("extend",t?"right":"left",n),!o.isCollapsed()&&uN(o.getRng(),m)?cN(r):(e.execCommand(t?"ForwardDelete":"Delete"),a=null==s?void 0:s.anchorNode,i=null==s?void 0:s.anchorOffset,l=null==s?void 0:s.focusNode,d=null==s?void 0:s.focusOffset,u(m,r),c()),e.dom.remove(m)})),!0)})(e,t,n)?I.some(_):I.none(),yN=e=>(t,n,o={})=>{const r=t.getBody(),s={bubbles:!0,composed:!0,data:null,isComposing:!1,detail:0,view:null,target:r,currentTarget:r,eventPhase:Event.AT_TARGET,originalTarget:r,explicitOriginalTarget:r,isTrusted:!1,srcElement:r,cancelable:!1,preventDefault:_,inputType:n},a=Vs(new InputEvent(e));return t.dispatch(e,{...a,...s,...o})},CN=yN("input"),wN=yN("beforeinput"),EN=xt(),xN=EN.os,_N=xN.isMacOS()||xN.isiOS(),SN=EN.browser.isFirefox(),kN=(e,t)=>{const n=e.dom,o=e.schema.getMoveCaretBeforeOnEnterElements();if(!t)return;if(/^(LI|DT|DD)$/.test(t.nodeName)){const e=(e=>{for(;e;){if(Jo(e)||lr(e)&&e.data&&/[\r\n\s]/.test(e.data))return e;e=e.nextSibling}return null})(t.firstChild);e&&/^(UL|OL|DL)$/.test(e.nodeName)&&t.insertBefore(n.doc.createTextNode(Vo),t.firstChild)}const r=n.createRng();if(t.normalize(),t.hasChildNodes()){const e=new $o(t,t);let n,s=t;for(;n=e.current();){if(lr(n)){r.setStart(n,0),r.setEnd(n,0);break}if(o[n.nodeName.toLowerCase()]){r.setStartBefore(n),r.setEndBefore(n);break}s=n,n=e.next()}n||(r.setStart(s,0),r.setEnd(s,0))}else gr(t)?t.nextSibling&&n.isBlock(t.nextSibling)?(r.setStartBefore(t),r.setEndBefore(t)):(r.setStartAfter(t),r.setEndAfter(t)):(r.setStart(t,0),r.setEnd(t,0));e.selection.setRng(r),pg(e,r)},NN=(e,t)=>{const n=e.getRoot();let o,r=t;for(;r!==n&&r&&"false"!==e.getContentEditable(r);){if("true"===e.getContentEditable(r)){o=r;break}r=r.parentNode}return r!==n?o:n},RN=e=>I.from(e.dom.getParent(e.selection.getStart(!0),e.dom.isBlock)),AN=e=>{e.innerHTML='<br data-mce-bogus="1">'},TN=(e,t)=>{ql(e).toLowerCase()===t.tagName.toLowerCase()&&((e,t,n)=>{const o=e.dom;I.from(n.style).map(o.parseStyle).each((e=>{const n={...fo(yn(t)),...e};o.setStyles(t,n)}));const r=I.from(n.class).map((e=>e.split(/\s+/))),s=I.from(t.className).map((e=>Y(e.split(/\s+/),(e=>""!==e))));Mt(r,s,((e,n)=>{const r=Y(n,(t=>!H(e,t))),s=[...e,...r];o.setAttrib(t,"class",s.join(" "))}));const a=["style","class"],i=Ce(n,((e,t)=>!H(a,t)));o.setAttribs(t,i)})(e,t,Vl(e))},ON=(e,t,n,o,r=!0,s,a)=>{const i=e.dom,l=e.schema,d=ql(e),c=n?n.nodeName.toUpperCase():"";let u=t;const m=l.getTextInlineElements();let f;f=s||"TABLE"===c||"HR"===c?i.create(s||d,a||{}):n.cloneNode(!1);let g=f;if(r){do{if(m[u.nodeName]){if($u(u)||nm(u))continue;const e=u.cloneNode(!1);i.setAttrib(e,"id",""),f.hasChildNodes()?(e.appendChild(f.firstChild),f.appendChild(e)):(g=e,f.appendChild(e))}}while((u=u.parentNode)&&u!==o)}else i.setAttrib(f,"style",null),i.setAttrib(f,"class",null);return TN(e,f),AN(g),f},BN=(e,t)=>{const n=null==e?void 0:e.parentNode;return C(n)&&n.nodeName===t},PN=e=>C(e)&&/^(OL|UL|LI)$/.test(e.nodeName),DN=e=>C(e)&&/^(LI|DT|DD)$/.test(e.nodeName),LN=e=>{const t=e.parentNode;return DN(t)?t:e},MN=(e,t,n)=>{let o=e[n?"firstChild":"lastChild"];for(;o&&!Jo(o);)o=o[n?"nextSibling":"previousSibling"];return o===t},IN=e=>X(we(fo(yn(e)),((e,t)=>`${t}: ${e};`)),((e,t)=>e+t),""),FN=(e,t)=>t&&"A"===t.nodeName&&e.isEmpty(t),UN=(e,t)=>e.nodeName===t||e.previousSibling&&e.previousSibling.nodeName===t,zN=(e,t)=>C(t)&&e.isBlock(t)&&!/^(TD|TH|CAPTION|FORM)$/.test(t.nodeName)&&!/^(fixed|absolute)/i.test(t.style.position)&&e.isEditable(t.parentNode)&&"false"!==e.getContentEditable(t),jN=(e,t,n)=>lr(t)?e?1===n&&t.data.charAt(n-1)===Za?0:n:n===t.data.length-1&&t.data.charAt(n)===Za?t.data.length:n:n,HN={insert:(e,t)=>{let n,o,r,s,a=!1;const i=e.dom,l=e.schema.getNonEmptyElements(),d=e.selection.getRng(),c=ql(e),u=yn(d.startContainer),f=In(u,d.startOffset),g=f.exists((e=>Vt(e)&&!oo(e))),p=d.collapsed&&g,b=(t,o)=>ON(e,n,S,_,Gl(e),t,o),v=e=>{const t=jN(e,n,o);if(lr(n)&&(e?t>0:t<n.data.length))return!1;if((n.parentNode===S||n===S)&&a&&!e)return!0;if(e&&Jo(n)&&n===S.firstChild)return!0;if(UN(n,"TABLE")||UN(n,"HR"))return a&&!e||!a&&e;const r=new $o(n,S);let s;for(lr(n)&&(e&&0===t?r.prev():e||t!==n.data.length||r.next());s=r.current();){if(Jo(s)){if(!s.getAttribute("data-mce-bogus")){const e=s.nodeName.toLowerCase();if(l[e]&&"br"!==e)return!1}}else if(lr(s)&&!Yo(s.data))return!1;e?r.prev():r.next()}return!0},w=()=>{let t;return t=/^(H[1-6]|PRE|FIGURE)$/.test(r)&&"HGROUP"!==k?b(c):b(),((e,t)=>{const n=Xl(e);return!y(t)&&(m(n)?H(Pt.explode(n),t.nodeName.toLowerCase()):n)})(e,s)&&zN(i,s)&&i.isEmpty(S,void 0,{includeZwsp:!0})?t=i.split(s,S):i.insertAfter(t,S),kN(e,t),t};Wf(i,d).each((e=>{d.setStart(e.startContainer,e.startOffset),d.setEnd(e.endContainer,e.endOffset)})),n=d.startContainer,o=d.startOffset;const E=!(!t||!t.shiftKey),x=!(!t||!t.ctrlKey);Jo(n)&&n.hasChildNodes()&&!p&&(a=o>n.childNodes.length-1,n=n.childNodes[Math.min(o,n.childNodes.length-1)]||n,o=a&&lr(n)?n.data.length:0);const _=NN(i,n);if(!_||((e,t)=>{const n=e.dom.getParent(t,"ol,ul,dl");return null!==n&&"false"===e.dom.getContentEditableParent(n)})(e,n))return;E||(n=((e,t,n,o,r)=>{var s,a;const i=e.dom,l=null!==(s=NN(i,o))&&void 0!==s?s:i.getRoot();let d=i.getParent(o,i.isBlock);if(!d||!zN(i,d)){if(d=d||l,!d.hasChildNodes()){const o=i.create(t);return TN(e,o),d.appendChild(o),n.setStart(o,0),n.setEnd(o,0),o}let s,c=o;for(;c&&c.parentNode!==d;)c=c.parentNode;for(;c&&!i.isBlock(c);)s=c,c=c.previousSibling;const u=null===(a=null==s?void 0:s.parentElement)||void 0===a?void 0:a.nodeName;if(s&&u&&e.schema.isValidChild(u,t.toLowerCase())){const a=s.parentNode,l=i.create(t);for(TN(e,l),a.insertBefore(l,s),c=s;c&&!i.isBlock(c);){const e=c.nextSibling;l.appendChild(c),c=e}n.setStart(o,r),n.setEnd(o,r)}}return o})(e,c,d,n,o));let S=i.getParent(n,i.isBlock)||i.getRoot();s=C(null==S?void 0:S.parentNode)?i.getParent(S.parentNode,i.isBlock):null,r=S?S.nodeName.toUpperCase():"";const k=s?s.nodeName.toUpperCase():"";if("LI"!==k||x||(S=s,s=s.parentNode,r=k),Jo(s)&&((e,t,n)=>!t&&n.nodeName.toLowerCase()===ql(e)&&e.dom.isEmpty(n)&&((t,n,o)=>{let r=n;for(;r&&r!==t&&h(r.nextSibling);){const t=r.parentElement;if(!t||(s=t,!_e(e.schema.getTextBlockElements(),s.nodeName.toLowerCase())))return Er(t);r=t}var s;return!1})(e.getBody(),n))(e,E,S))return((e,t,n)=>{var o,r,s;const a=t(ql(e)),i=((e,t)=>e.dom.getParent(t,Er))(e,n);i&&(e.dom.insertAfter(a,i),kN(e,a),(null!==(s=null===(r=null===(o=n.parentElement)||void 0===o?void 0:o.childNodes)||void 0===r?void 0:r.length)&&void 0!==s?s:0)>1&&e.dom.remove(n))})(e,b,S);if(/^(LI|DT|DD)$/.test(r)&&Jo(s)&&i.isEmpty(S))return void((e,t,n,o,r)=>{const s=e.dom,a=e.selection.getRng(),i=n.parentNode;if(n===e.getBody()||!i)return;var l;PN(l=n)&&PN(l.parentNode)&&(r="LI");const d=DN(o)?IN(o):void 0;let c=DN(o)&&d?t(r,{style:IN(o)}):t(r);if(MN(n,o,!0)&&MN(n,o,!1))if(BN(n,"LI")){const e=LN(n);s.insertAfter(c,e),(e=>{var t;return(null===(t=e.parentNode)||void 0===t?void 0:t.firstChild)===e})(n)?s.remove(e):s.remove(n)}else s.replace(c,n);else if(MN(n,o,!0))BN(n,"LI")?(s.insertAfter(c,LN(n)),c.appendChild(s.doc.createTextNode(" ")),c.appendChild(n)):i.insertBefore(c,n),s.remove(o);else if(MN(n,o,!1))s.insertAfter(c,LN(n)),s.remove(o);else{n=LN(n);const e=a.cloneRange();e.setStartAfter(o),e.setEndAfter(n);const t=e.extractContents();if("LI"===r&&((e,t)=>e.firstChild&&"LI"===e.firstChild.nodeName)(t)){const e=Y(q(c.children,yn),O(Xt("br")));c=t.firstChild,s.insertAfter(t,n),V(e,(e=>bo(yn(c),e))),d&&c.setAttribute("style",d)}else s.insertAfter(t,n),s.insertAfter(c,n);s.remove(o)}kN(e,c)})(e,b,s,S,c);if(!(p||S!==e.getBody()&&zN(i,S)))return;const N=S.parentNode;let R;if(p)R=b(c),f.fold((()=>{vo(u,yn(R))}),(e=>{po(e,yn(R))})),e.selection.setCursorLocation(R,0);else if(ni(S))R=ci(S),i.isEmpty(S)&&AN(S),TN(e,R),kN(e,R);else if(v(!1))R=w();else if(v(!0)&&N){R=N.insertBefore(b(),S);const t=yn(d.startContainer).dom.hasChildNodes()&&d.collapsed;kN(e,UN(S,"HR")||t?R:S)}else{const t=(e=>{const t=e.cloneRange();return t.setStart(e.startContainer,jN(!0,e.startContainer,e.startOffset)),t.setEnd(e.endContainer,jN(!1,e.endContainer,e.endOffset)),t})(d).cloneRange();t.setEndAfter(S);const n=t.extractContents();(e=>{V(Fo(yn(e),Kt),(e=>{const t=e.dom;t.nodeValue=Ja(t.data)}))})(n),(e=>{let t=e;do{lr(t)&&(t.data=t.data.replace(/^[\r\n]+/,"")),t=t.firstChild}while(t)})(n),R=n.firstChild,i.insertAfter(n,S),((e,t,n)=>{var o;const r=[];if(!n)return;let s=n;for(;s=s.firstChild;){if(e.isBlock(s))return;Jo(s)&&!t[s.nodeName.toLowerCase()]&&r.push(s)}let a=r.length;for(;a--;)s=r[a],(!s.hasChildNodes()||s.firstChild===s.lastChild&&""===(null===(o=s.firstChild)||void 0===o?void 0:o.nodeValue)||FN(e,s))&&e.remove(s)})(i,l,R),((e,t)=>{t.normalize();const n=t.lastChild;(!n||Jo(n)&&/^(left|right)$/gi.test(e.getStyle(n,"float",!0)))&&e.add(t,"br")})(i,S),i.isEmpty(S)&&AN(S),R.normalize(),i.isEmpty(R)?(i.remove(R),w()):(TN(e,R),kN(e,R))}i.setAttrib(R,"id",""),e.dispatch("NewBlock",{newBlock:R})},fakeEventName:"insertParagraph"},$N=(e,t,n)=>{const o=e.dom.createRng();n?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)),e.selection.setRng(o),pg(e,o)},qN=(e,t)=>{const n=bn("br");po(yn(t),n),e.undoManager.add()},VN=(e,t)=>{WN(e.getBody(),t)||ho(yn(t),bn("br"));const n=bn("br");ho(yn(t),n),$N(e,n.dom,!1),e.undoManager.add()},WN=(e,t)=>{return n=Zi.after(t),!!gr(n.getNode())||Fu(e,Zi.after(t)).map((e=>gr(e.getNode()))).getOr(!1);var n},KN=e=>e&&"A"===e.nodeName&&"href"in e,YN=e=>e.fold(L,KN,KN,L),GN=(e,t)=>{t.fold(_,T(qN,e),T(VN,e),_)},XN={insert:(e,t)=>{const n=(e=>{const t=T(Sh,e),n=Zi.fromRangeStart(e.selection.getRng());return bx(t,e.getBody(),n).filter(YN)})(e);n.isSome()?n.each(T(GN,e)):((e,t)=>{const n=e.selection,o=e.dom,r=n.getRng();let s,a=!1;Wf(o,r).each((e=>{r.setStart(e.startContainer,e.startOffset),r.setEnd(e.endContainer,e.endOffset)}));let i=r.startOffset,l=r.startContainer;if(Jo(l)&&l.hasChildNodes()){const e=i>l.childNodes.length-1;l=l.childNodes[Math.min(i,l.childNodes.length-1)]||l,i=e&&lr(l)?l.data.length:0}let d=o.getParent(l,o.isBlock);const c=d&&d.parentNode?o.getParent(d.parentNode,o.isBlock):null,u=c?c.nodeName.toUpperCase():"",m=!(!t||!t.ctrlKey);"LI"!==u||m||(d=c),lr(l)&&i>=l.data.length&&(((e,t,n)=>{const o=new $o(t,n);let r;const s=e.getNonEmptyElements();for(;r=o.next();)if(s[r.nodeName.toLowerCase()]||lr(r)&&r.length>0)return!0;return!1})(e.schema,l,d||o.getRoot())||(s=o.create("br"),r.insertNode(s),r.setStartAfter(s),r.setEndAfter(s),a=!0)),s=o.create("br"),Ji(o,r,s),$N(e,s,a),e.undoManager.add()})(e,t)},fakeEventName:"insertLineBreak"},ZN=(e,t)=>RN(e).filter((e=>t.length>0&&En(yn(e),t))).isSome(),QN=El([{br:[]},{block:[]},{none:[]}]),JN=(e,t)=>(e=>ZN(e,Yl(e)))(e),eR=e=>(t,n)=>(e=>RN(e).filter((e=>Va(yn(e)))).isSome())(t)===e,tR=(e,t)=>(n,o)=>{const r=(e=>RN(e).fold(N(""),(e=>e.nodeName.toUpperCase())))(n)===e.toUpperCase();return r===t},nR=e=>{const t=NN(e.dom,e.selection.getStart());return y(t)},oR=e=>tR("pre",e),rR=e=>(t,n)=>$l(t)===e,sR=(e,t)=>(e=>ZN(e,Kl(e)))(e),aR=(e,t)=>t,iR=e=>{const t=ql(e),n=NN(e.dom,e.selection.getStart());return C(n)&&e.schema.isValidChild(n.nodeName,t)},lR=e=>{const t=e.selection.getRng(),n=yn(t.startContainer),o=In(n,t.startOffset).map((e=>Vt(e)&&!oo(e)));return t.collapsed&&o.getOr(!0)},dR=(e,t)=>(n,o)=>X(e,((e,t)=>e&&t(n,o)),!0)?I.some(t):I.none(),cR=(e,t,n)=>{t.selection.isCollapsed()||(e=>{e.execCommand("delete")})(t),C(n)&&wN(t,e.fakeEventName).isDefaultPrevented()||(e.insert(t,n),C(n)&&CN(t,e.fakeEventName))},uR=(e,t)=>{const n=()=>cR(XN,e,t),o=()=>cR(HN,e,t),r=((e,t)=>ix([dR([JN],QN.none()),dR([oR(!0),nR],QN.none()),dR([tR("summary",!0)],QN.br()),dR([oR(!0),rR(!1),aR],QN.br()),dR([oR(!0),rR(!1)],QN.block()),dR([oR(!0),rR(!0),aR],QN.block()),dR([oR(!0),rR(!0)],QN.br()),dR([eR(!0),aR],QN.br()),dR([eR(!0)],QN.block()),dR([sR],QN.br()),dR([aR],QN.br()),dR([iR],QN.block()),dR([lR],QN.block())],[e,!(!t||!t.shiftKey)]).getOr(QN.none()))(e,t);switch(Wl(e)){case"linebreak":r.fold(n,n,_);break;case"block":r.fold(o,o,_);break;case"invert":r.fold(o,n,_);break;default:r.fold(n,o,_)}},mR=xt(),fR=mR.os.isiOS()&&mR.browser.isSafari(),gR=(e,t)=>{var n;t.isDefaultPrevented()||(t.preventDefault(),(n=e.undoManager).typing&&(n.typing=!1,n.add()),e.undoManager.transact((()=>{uR(e,t)})))},pR=xt(),hR=e=>e.stopImmediatePropagation(),bR=e=>e.keyCode===bf.PAGE_UP||e.keyCode===bf.PAGE_DOWN,vR=(e,t,n)=>{n&&!e.get()?t.on("NodeChange",hR,!0):!n&&e.get()&&t.off("NodeChange",hR),e.set(n)},yR=(e,t)=>{const n=t.container(),o=t.offset();return lr(n)?(n.insertData(o,e),I.some(Zi(n,o+e.length))):hu(t).map((n=>{const o=vn(e);return t.isAtEnd()?ho(n,o):po(n,o),Zi(o.dom,e.length)}))},CR=T(yR,Vo),wR=T(yR," "),ER=e=>t=>{e.selection.setRng(t.toRange()),e.nodeChanged()},xR=e=>{const t=Zi.fromRangeStart(e.selection.getRng()),n=yn(e.getBody());if(e.selection.isCollapsed()){const o=T(Sh,e),r=Zi.fromRangeStart(e.selection.getRng());return bx(o,e.getBody(),r).bind((e=>t=>t.fold((t=>Uu(e.dom,Zi.before(t))),(e=>zu(e)),(e=>ju(e)),(t=>Fu(e.dom,Zi.after(t)))))(n)).map((o=>()=>((e,t,n)=>o=>rh(e,o,n)?CR(t):wR(t))(n,t,e.schema)(o).each(ER(e))))}return I.none()},_R=e=>{return It(Tt.browser.isFirefox()&&e.selection.isEditable()&&(t=e.dom,n=e.selection.getRng().startContainer,t.isEditable(t.getParent(n,"summary"))),(()=>{const t=yn(e.getBody());e.selection.isCollapsed()||e.getDoc().execCommand("Delete"),((e,t,n)=>rh(e,t,n)?CR(t):wR(t))(t,Zi.fromRangeStart(e.selection.getRng()),e.schema).each(ER(e))}));var t,n},SR=e=>xc(e)?[{keyCode:bf.TAB,action:uS(zS,e,!0)},{keyCode:bf.TAB,shiftKey:!0,action:uS(zS,e,!1)}]:[],kR=e=>{if(e.addShortcut("Meta+P","","mcePrint"),lN(e),aw(e))return ua(null);{const t=f_(e);return(e=>{e.on("beforeinput",(t=>{e.selection.isEditable()&&!$(t.getTargetRanges(),(t=>!jg(e.dom,t)))||t.preventDefault()}))})(e),(e=>{e.on("keyup compositionstart",T(J_,e))})(e),((e,t)=>{e.on("keydown",(n=>{n.isDefaultPrevented()||((e,t,n)=>{const o=Tt.os.isMacOS()||Tt.os.isiOS();mS([{keyCode:bf.RIGHT,action:uS(nS,e,!0)},{keyCode:bf.LEFT,action:uS(nS,e,!1)},{keyCode:bf.UP,action:uS(oS,e,!1)},{keyCode:bf.DOWN,action:uS(oS,e,!0)},...o?[{keyCode:bf.UP,action:uS(sS,e,!1),metaKey:!0,shiftKey:!0},{keyCode:bf.DOWN,action:uS(sS,e,!0),metaKey:!0,shiftKey:!0}]:[],{keyCode:bf.RIGHT,action:uS(LS,e,!0)},{keyCode:bf.LEFT,action:uS(LS,e,!1)},{keyCode:bf.UP,action:uS(MS,e,!1)},{keyCode:bf.DOWN,action:uS(MS,e,!0)},{keyCode:bf.UP,action:uS(MS,e,!1)},{keyCode:bf.UP,action:uS(lS,e,!1)},{keyCode:bf.DOWN,action:uS(lS,e,!0)},{keyCode:bf.RIGHT,action:uS(gS,e,!0)},{keyCode:bf.LEFT,action:uS(gS,e,!1)},{keyCode:bf.UP,action:uS(pS,e,!1)},{keyCode:bf.DOWN,action:uS(pS,e,!0)},{keyCode:bf.RIGHT,action:uS(u_,e,t,!0)},{keyCode:bf.LEFT,action:uS(u_,e,t,!1)},{keyCode:bf.RIGHT,ctrlKey:!o,altKey:o,action:uS(g_,e,t)},{keyCode:bf.LEFT,ctrlKey:!o,altKey:o,action:uS(p_,e,t)},{keyCode:bf.UP,action:uS(iS,e,!1)},{keyCode:bf.DOWN,action:uS(iS,e,!0)}],n).each((e=>{n.preventDefault()}))})(e,t,n)}))})(e,t),((e,t)=>{let n=!1;e.on("keydown",(o=>{n=o.keyCode===bf.BACKSPACE,o.isDefaultPrevented()||((e,t,n)=>{const o=n.keyCode===bf.BACKSPACE?"deleteContentBackward":"deleteContentForward",r=e.selection.isCollapsed(),s=r?"character":"selection",a=e=>r?e?"word":"line":"selection";fS([{keyCode:bf.BACKSPACE,action:uS(U_,e)},{keyCode:bf.BACKSPACE,action:uS(GE,e,!1)},{keyCode:bf.DELETE,action:uS(GE,e,!0)},{keyCode:bf.BACKSPACE,action:uS(jE,e,!1)},{keyCode:bf.DELETE,action:uS(jE,e,!0)},{keyCode:bf.BACKSPACE,action:uS(y_,e,t,!1)},{keyCode:bf.DELETE,action:uS(y_,e,t,!0)},{keyCode:bf.BACKSPACE,action:uS(cb,e,!1)},{keyCode:bf.DELETE,action:uS(cb,e,!0)},{keyCode:bf.BACKSPACE,action:uS(vN,e,!1,s)},{keyCode:bf.DELETE,action:uS(vN,e,!0,s)},..._N?[{keyCode:bf.BACKSPACE,altKey:!0,action:uS(vN,e,!1,a(!0))},{keyCode:bf.DELETE,altKey:!0,action:uS(vN,e,!0,a(!0))},{keyCode:bf.BACKSPACE,metaKey:!0,action:uS(vN,e,!1,a(!1))}]:[{keyCode:bf.BACKSPACE,ctrlKey:!0,action:uS(vN,e,!1,a(!0))},{keyCode:bf.DELETE,ctrlKey:!0,action:uS(vN,e,!0,a(!0))}],{keyCode:bf.BACKSPACE,action:uS(XE,e,!1)},{keyCode:bf.DELETE,action:uS(XE,e,!0)},{keyCode:bf.BACKSPACE,action:uS(O_,e,!1)},{keyCode:bf.DELETE,action:uS(O_,e,!0)},{keyCode:bf.BACKSPACE,action:uS(DE,e,!1)},{keyCode:bf.DELETE,action:uS(DE,e,!0)},{keyCode:bf.BACKSPACE,action:uS(OE,e,!1)},{keyCode:bf.DELETE,action:uS(OE,e,!0)},{keyCode:bf.BACKSPACE,action:uS(R_,e,!1)},{keyCode:bf.DELETE,action:uS(R_,e,!0)}],n).filter((t=>e.selection.isEditable())).each((t=>{n.preventDefault(),wN(e,o).isDefaultPrevented()||(t(),CN(e,o))}))})(e,t,o)})),e.on("keyup",(t=>{t.isDefaultPrevented()||((e,t,n)=>{mS([{keyCode:bf.BACKSPACE,action:uS(YE,e)},{keyCode:bf.DELETE,action:uS(YE,e)},..._N?[{keyCode:bf.BACKSPACE,altKey:!0,action:uS(A_,e)},{keyCode:bf.DELETE,altKey:!0,action:uS(A_,e)},...n?[{keyCode:SN?224:91,action:uS(A_,e)}]:[]]:[{keyCode:bf.BACKSPACE,ctrlKey:!0,action:uS(A_,e)},{keyCode:bf.DELETE,ctrlKey:!0,action:uS(A_,e)}]],t)})(e,t,n),n=!1}))})(e,t),(e=>{let t=I.none();e.on("keydown",(n=>{n.keyCode===bf.ENTER&&(fR&&(e=>{if(!e.collapsed)return!1;const t=e.startContainer;if(lr(t)){const n=/^[\uAC00-\uD7AF\u1100-\u11FF\u3130-\u318F\uA960-\uA97F\uD7B0-\uD7FF]$/,o=t.data.charAt(e.startOffset-1);return n.test(o)}return!1})(e.selection.getRng())?(e=>{t=I.some(e.selection.getBookmark()),e.undoManager.add()})(e):gR(e,n))})),e.on("keyup",(n=>{n.keyCode===bf.ENTER&&t.each((()=>((e,n)=>{e.undoManager.undo(),t.fold(_,(t=>e.selection.moveToBookmark(t))),gR(e,n),t=I.none()})(e,n)))}))})(e),(e=>{e.on("keydown",(t=>{t.isDefaultPrevented()||((e,t)=>{fS([{keyCode:bf.SPACEBAR,action:uS(xR,e)},{keyCode:bf.SPACEBAR,action:uS(_R,e)}],t).each((n=>{t.preventDefault(),wN(e,"insertText",{data:" "}).isDefaultPrevented()||(n(),CN(e,"insertText",{data:" "}))}))})(e,t)}))})(e),(e=>{e.on("input",(t=>{t.isComposing||(e=>{const t=yn(e.getBody());e.selection.isCollapsed()&&mh(t,Zi.fromRangeStart(e.selection.getRng()),e.schema).each((t=>{e.selection.setRng(t.toRange())}))})(e)}))})(e),(e=>{e.on("keydown",(t=>{t.isDefaultPrevented()||((e,t)=>{mS([...SR(e)],t).each((e=>{t.preventDefault()}))})(e,t)}))})(e),((e,t)=>{e.on("keydown",(n=>{n.isDefaultPrevented()||((e,t,n)=>{const o=Tt.os.isMacOS()||Tt.os.isiOS();mS([{keyCode:bf.END,action:uS(rS,e,!0)},{keyCode:bf.HOME,action:uS(rS,e,!1)},...o?[]:[{keyCode:bf.HOME,action:uS(sS,e,!1),ctrlKey:!0,shiftKey:!0},{keyCode:bf.END,action:uS(sS,e,!0),ctrlKey:!0,shiftKey:!0}],{keyCode:bf.END,action:uS(hS,e,!0)},{keyCode:bf.HOME,action:uS(hS,e,!1)},{keyCode:bf.END,action:uS(h_,e,!0,t)},{keyCode:bf.HOME,action:uS(h_,e,!1,t)}],n).each((e=>{n.preventDefault()}))})(e,t,n)}))})(e,t),((e,t)=>{if(pR.os.isMacOS())return;const n=ua(!1);e.on("keydown",(t=>{bR(t)&&vR(n,e,!0)})),e.on("keyup",(o=>{o.isDefaultPrevented()||((e,t,n)=>{mS([{keyCode:bf.PAGE_UP,action:uS(h_,e,!1,t)},{keyCode:bf.PAGE_DOWN,action:uS(h_,e,!0,t)}],n)})(e,t,o),bR(o)&&n.get()&&(vR(n,e,!1),e.nodeChanged())}))})(e,t),t}};class NR{constructor(e){let t;this.lastPath=[],this.editor=e;const n=this;"onselectionchange"in e.getDoc()||e.on("NodeChange click mouseup keyup focus",(n=>{const o=e.selection.getRng(),r={startContainer:o.startContainer,startOffset:o.startOffset,endContainer:o.endContainer,endOffset:o.endOffset};"nodechange"!==n.type&&Uf(r,t)||e.dispatch("SelectionChange"),t=r})),e.on("contextmenu",(()=>{Sg(e),e.dispatch("SelectionChange")})),e.on("SelectionChange",(()=>{const t=e.selection.getStart(!0);t&&pm(e)&&!n.isSameElementPath(t)&&e.dom.isChildOf(t,e.getBody())&&e.nodeChanged({selectionChange:!0})})),e.on("mouseup",(t=>{!t.isDefaultPrevented()&&pm(e)&&("IMG"===e.selection.getNode().nodeName?Rg.setEditorTimeout(e,(()=>{e.nodeChanged()})):e.nodeChanged())}))}nodeChanged(e={}){const t=this.editor.selection;let n;if(this.editor.initialized&&t&&!Fd(this.editor)&&!this.editor.mode.isReadOnly()){const o=this.editor.getBody();n=t.getStart(!0)||o,n.ownerDocument===this.editor.getDoc()&&this.editor.dom.isChildOf(n,o)||(n=o);const r=[];this.editor.dom.getParent(n,(e=>e===o||(r.push(e),!1))),this.editor.dispatch("NodeChange",{...e,element:n,parents:r})}}isSameElementPath(e){let t;const n=this.editor,o=oe(n.dom.getParents(e,M,n.getBody()));if(o.length===this.lastPath.length){for(t=o.length;t>=0&&o[t]===this.lastPath[t];t--);if(-1===t)return this.lastPath=o,!0}return this.lastPath=o,!1}}const RR=Pa("image"),AR=Pa("event"),TR=e=>t=>{t[AR]=e},OR=TR(0),BR=TR(2),PR=TR(1),DR=(0,e=>{const t=e;return I.from(t[AR]).exists((e=>0===e))});const LR=Pa("mode"),MR=e=>t=>{t[LR]=e},IR=(e,t)=>MR(t)(e),FR=MR(0),UR=MR(2),zR=MR(1),jR=e=>t=>{const n=t;return I.from(n[LR]).exists((t=>t===e))},HR=jR(0),$R=jR(1),qR=["none","copy","link","move"],VR=["none","copy","copyLink","copyMove","link","linkMove","move","all","uninitialized"],WR=()=>{const e=new window.DataTransfer;let t="move",n="all";const o={get dropEffect(){return t},set dropEffect(e){H(qR,e)&&(t=e)},get effectAllowed(){return n},set effectAllowed(e){DR(o)&&H(VR,e)&&(n=e)},get items(){return((e,t)=>({...t,get length(){return t.length},add:(n,o)=>{if(HR(e)){if(!m(n))return t.add(n);if(!v(o))return t.add(n,o)}return null},remove:n=>{HR(e)&&t.remove(n)},clear:()=>{HR(e)&&t.clear()}}))(o,e.items)},get files(){return $R(o)?Object.freeze({length:0,item:e=>null}):e.files},get types(){return e.types},setDragImage:(t,n,r)=>{var s;HR(o)&&(s={image:t,x:n,y:r},o[RR]=s,e.setDragImage(t,n,r))},getData:t=>$R(o)?"":e.getData(t),setData:(t,n)=>{HR(o)&&e.setData(t,n)},clearData:t=>{HR(o)&&e.clearData(t)}};return FR(o),o},KR=(e,t)=>e.setData("text/html",t),YR="x-hugerte/html",GR=N(YR),XR="\x3c!-- "+YR+" --\x3e",ZR=e=>XR+e,QR=e=>-1!==e.indexOf(XR),JR="%MCEPASTEBIN%",eA=e=>e.dom.get("mcepastebin"),tA=e=>C(e)&&"mcepastebin"===e.id,nA=e=>e===JR,oA=(e,t)=>(Pt.each(t,(t=>{e=u(t,RegExp)?e.replace(t,""):e.replace(t[0],t[1])})),e),rA=e=>oA(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,(e,t,n)=>t||n?Vo:" "],/<br class="Apple-interchange-newline">/g,/<br>$/i]),sA=(e,t)=>({content:e,cancelled:t}),aA=(e,t)=>(e.insertContent(t,{merge:lc(e),paste:!0}),!0),iA=e=>/^https?:\/\/[\w\-\/+=.,!;:&%@^~(){}?#]+$/i.test(e),lA=(e,t,n)=>!(e.selection.isCollapsed()||!iA(t))&&((e,t,n)=>(e.undoManager.extra((()=>{n(e,t)}),(()=>{e.execCommand("mceInsertLink",!1,t)})),!0))(e,t,n),dA=(e,t,n)=>!!((e,t)=>iA(t)&&$(Ec(e),(e=>qe(t.toLowerCase(),`.${e.toLowerCase()}`))))(e,t)&&((e,t,n)=>(e.undoManager.extra((()=>{n(e,t)}),(()=>{e.insertContent('<img src="'+t+'">')})),!0))(e,t,n),cA=(e=>{let t=0;return()=>"mceclip"+t++})(),uA=e=>{const t=WR();return KR(t,e),UR(t),t},mA=(e,t,n,o,r)=>{const s=((e,t,n)=>((e,t,n)=>{const o=((e,t,n)=>e.dispatch("PastePreProcess",{content:t,internal:n}))(e,t,n),r=((e,t)=>{const n=kC({sanitize:Cc(e),sandbox_iframes:Nc(e),sandbox_iframes_exclusions:Rc(e),convert_unsafe_embeds:Ac(e)},e.schema);n.addNodeFilter("meta",(e=>{Pt.each(e,(e=>{e.remove()}))}));const o=n.parse(t,{forced_root_block:!1,isRootContent:!0});return hp({validate:!0},e.schema).serialize(o)})(e,o.content);return e.hasEventListeners("PastePostProcess")&&!o.isDefaultPrevented()?((e,t,n)=>{const o=e.dom.create("div",{style:"display:none"},t),r=((e,t,n)=>e.dispatch("PastePostProcess",{node:t,internal:n}))(e,o,n);return sA(r.node.innerHTML,r.isDefaultPrevented())})(e,r,n):sA(r,o.isDefaultPrevented())})(e,t,n))(e,t,n);if(!s.cancelled){const t=s.content,n=()=>((e,t,n)=>{n||!dc(e)?aA(e,t):((e,t)=>{Pt.each([lA,dA,aA],(n=>!n(e,t,aA)))})(e,t)})(e,t,o);r?wN(e,"insertFromPaste",{dataTransfer:uA(t)}).isDefaultPrevented()||(n(),CN(e,"insertFromPaste")):n()}},fA=(e,t,n,o)=>{const r=n||QR(t);mA(e,(e=>e.replace(XR,""))(t),r,!1,o)},gA=(e,t,n)=>{const o=e.dom.encode(t).replace(/\r\n/g,"\n"),r=((e,t,n)=>{const o=e.split(/\n\n/),r=((e,t)=>{let n="<"+e;const o=we(t,((e,t)=>t+'="'+hs.encodeAllRaw(e)+'"'));return o.length&&(n+=" "+o.join(" ")),n+">"})(t,n),s="</"+t+">",a=q(o,(e=>e.split(/\n/).join("<br />")));return 1===a.length?a[0]:q(a,(e=>r+e+s)).join("")})(Xo(o,uc(e)),ql(e),Vl(e));mA(e,r,!1,!0,n)},pA=e=>{const t={};if(e&&e.types)for(let n=0;n<e.types.length;n++){const o=e.types[n];try{t[o]=e.getData(o)}catch(e){t[o]=""}}return t},hA=(e,t)=>t in e&&e[t].length>0,bA=e=>hA(e,"text/html")||hA(e,"text/plain"),vA=(e,t,n)=>{const o="paste"===t.type?t.clipboardData:t.dataTransfer;var r;if(nc(e)&&o){const s=((e,t)=>{const n=t.items?te(ce(t.items),(e=>"file"===e.kind?[e.getAsFile()]:[])):[],o=t.files?ce(t.files):[];return Y(n.length>0?n:o,(e=>{const t=Ec(e);return e=>$e(e.type,"image/")&&$(t,(t=>(e=>{const t=e.toLowerCase(),n={jpg:"jpeg",jpe:"jpeg",jfi:"jpeg",jif:"jpeg",jfif:"jpeg",pjpeg:"jpeg",pjp:"jpeg",svg:"svg+xml"};return Pt.hasOwn(n,t)?"image/"+n[t]:"image/"+t})(t)===e.type))})(e))})(e,o);if(s.length>0)return t.preventDefault(),(r=s,Promise.all(q(r,(e=>Zv(e).then((t=>({file:e,uri:t}))))))).then((t=>{n&&e.selection.setRng(n),V(t,(t=>{((e,t)=>{Gv(t.uri).each((({data:n,type:o,base64Encoded:r})=>{const s=r?n:btoa(n),a=t.file,i=e.editorUpload.blobCache,l=i.getByData(s,o),d=null!=l?l:((e,t,n,o)=>{const r=cA(),s=Ql(e)&&C(n.name),a=s?((e,t)=>{const n=t.match(/([\s\S]+?)(?:\.[a-z0-9.]+)$/i);return C(n)?e.dom.encode(n[1]):void 0})(e,n.name):r,i=s?n.name:void 0,l=t.create(r,n,o,a,i);return t.add(l),l})(e,i,a,s);fA(e,`<img src="${d.blobUri()}">`,!1,!0)}))})(e,t)}))})),!0}return!1},yA=(e,t,n,o,r)=>{let s=rA(n);const a=hA(t,GR())||QR(n),i=!a&&(e=>!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(e))(s),l=iA(s);(nA(s)||!s.length||i&&!l)&&(o=!0),(o||l)&&(s=hA(t,"text/plain")&&i?t["text/plain"]:(e=>{const t=Ps(),n=kC({},t);let o="";const r=t.getVoidElements(),s=Pt.makeMap("script noscript style textarea video audio iframe object"," "),a=t.getBlockElements(),i=e=>{const n=e.name,l=e;if("br"!==n){if("wbr"!==n)if(r[n]&&(o+=" "),s[n])o+=" ";else{if(3===e.type&&(o+=e.value),!(e.name in t.getVoidElements())){let t=e.firstChild;if(t)do{i(t)}while(t=t.next)}a[n]&&l.next&&(o+="\n","p"===n&&(o+="\n"))}}else o+="\n"};return e=oA(e,[/<!\[[^\]]+\]>/g]),i(n.parse(e)),o})(s)),nA(s)||(o?gA(e,s,r):fA(e,s,a,r))},CA=(e,t,n)=>{((e,t,n)=>{let o;e.on("keydown",(e=>{(e=>bf.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode)(e)&&!e.isDefaultPrevented()&&(o=e.shiftKey&&86===e.keyCode)})),e.on("paste",(r=>{if(r.isDefaultPrevented()||(e=>{var t,n;return Tt.os.isAndroid()&&0===(null===(n=null===(t=e.clipboardData)||void 0===t?void 0:t.items)||void 0===n?void 0:n.length)})(r))return;const s="text"===n.get()||o;o=!1;const a=pA(r.clipboardData);!bA(a)&&vA(e,r,t.getLastRng()||e.selection.getRng())||(hA(a,"text/html")?(r.preventDefault(),yA(e,a,a["text/html"],s,!0)):hA(a,"text/plain")&&hA(a,"text/uri-list")?(r.preventDefault(),yA(e,a,a["text/plain"],s,!0)):(t.create(),Rg.setEditorTimeout(e,(()=>{const n=t.getHtml();t.remove(),yA(e,a,n,s,!1)}),0)))}))})(e,t,n),(e=>{const t=e=>$e(e,"webkit-fake-url"),n=e=>$e(e,"data:");e.parser.addNodeFilter("img",((o,r,s)=>{if(!nc(e)&&(e=>{var t;return!0===(null===(t=e.data)||void 0===t?void 0:t.paste)})(s))for(const r of o){const o=r.attr("src");m(o)&&!r.attr("data-mce-object")&&o!==Tt.transparentSrc&&(t(o)||!mc(e)&&n(o))&&r.remove()}}))})(e)},wA=(e,t,n,o)=>{((e,t,n)=>{if(!e)return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(GR(),t),!0}catch(e){return!1}})(e.clipboardData,t.html,t.text)?(e.preventDefault(),o()):n(t.html,o)},EA=e=>(t,n)=>{const{dom:o,selection:r}=e,s=o.create("div",{contenteditable:"false","data-mce-bogus":"all"}),a=o.create("div",{contenteditable:"true"},t);o.setStyles(s,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),s.appendChild(a),o.add(e.getBody(),s);const i=r.getRng();a.focus();const l=o.createRng();l.selectNodeContents(a),r.setRng(l),Rg.setEditorTimeout(e,(()=>{r.setRng(i),o.remove(s),n()}),0)},xA=e=>({html:ZR(e.selection.getContent({contextual:!0})),text:e.selection.getContent({format:"text"})}),_A=e=>!e.selection.isCollapsed()||(e=>!!e.dom.getParent(e.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",e.getBody()))(e),SA=(e,t)=>{var n,o;return Gf.getCaretRangeFromPoint(null!==(n=t.clientX)&&void 0!==n?n:0,null!==(o=t.clientY)&&void 0!==o?o:0,e.getDoc())},kA=(e,t)=>{e.focus(),t&&e.selection.setRng(t)},NA=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/gi,RA=e=>Pt.trim(e).replace(NA,Hs).toLowerCase(),AA=(e,t,n)=>{const o=ac(e);if(n||"all"===o||!ic(e))return t;const r=o?o.split(/[, ]/):[];if(r&&"none"!==o){const n=e.dom,o=e.selection.getNode();t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,((e,t,s,a)=>{const i=n.parseStyle(n.decode(s)),l={};for(let e=0;e<r.length;e++){const t=i[r[e]];let s=t,a=n.getStyle(o,r[e],!0);/color/.test(r[e])&&(s=RA(s),a=RA(a)),a!==s&&(l[r[e]]=t)}const d=n.serializeStyle(l,"span");return d?t+' style="'+d+'"'+a:t+a}))}else t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return t=t.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,((e,t,n,o)=>t+' style="'+n+'"'+o)),t},TA=e=>{const t=ua(!1),n=ua(cc(e)?"text":"html"),o=(e=>{const t=ua(null);return{create:()=>((e,t)=>{const{dom:n,selection:o}=e,r=e.getBody();t.set(o.getRng());const s=n.add(e.getBody(),"div",{id:"mcepastebin",class:"mce-pastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0"},JR);Tt.browser.isFirefox()&&n.setStyle(s,"left","rtl"===n.getStyle(r,"direction",!0)?65535:-65535),n.bind(s,"beforedeactivate focusin focusout",(e=>{e.stopPropagation()})),s.focus(),o.select(s,!0)})(e,t),remove:()=>((e,t)=>{const n=e.dom;if(eA(e)){let o;const r=t.get();for(;o=eA(e);)n.remove(o),n.unbind(o);r&&e.selection.setRng(r)}t.set(null)})(e,t),getEl:()=>eA(e),getHtml:()=>(e=>{const t=e.dom,n=(e,n)=>{e.appendChild(n),t.remove(n,!0)},[o,...r]=Y(e.getBody().childNodes,tA);V(r,(e=>{n(o,e)}));const s=t.select("div[id=mcepastebin]",o);for(let e=s.length-1;e>=0;e--){const r=t.create("div");o.insertBefore(r,s[e]),n(r,s[e])}return o?o.innerHTML:""})(e),getLastRng:t.get}})(e);(e=>{(Tt.browser.isChromium()||Tt.browser.isSafari())&&((e,t)=>{e.on("PastePreProcess",(n=>{n.content=t(e,n.content,n.internal)}))})(e,AA)})(e),((e,t)=>{e.addCommand("mceTogglePlainTextPaste",(()=>{((e,t)=>{"text"===t.get()?(t.set("html"),hf(e,!1)):(t.set("text"),hf(e,!0)),e.focus()})(e,t)})),e.addCommand("mceInsertClipboardContent",((t,n)=>{n.html&&fA(e,n.html,n.internal,!1),n.text&&gA(e,n.text,!1)}))})(e,n),(e=>{const t=t=>n=>{t(e,n)},n=oc(e);w(n)&&e.on("PastePreProcess",t(n));const o=rc(e);w(o)&&e.on("PastePostProcess",t(o))})(e),e.on("PreInit",(()=>{(e=>{e.on("cut",(e=>t=>{!t.isDefaultPrevented()&&_A(e)&&e.selection.isEditable()&&wA(t,xA(e),EA(e),(()=>{if(Tt.browser.isChromium()||Tt.browser.isFirefox()){const t=e.selection.getRng();Rg.setEditorTimeout(e,(()=>{e.selection.setRng(t),e.execCommand("Delete")}),0)}else e.execCommand("Delete")}))})(e)),e.on("copy",(e=>t=>{!t.isDefaultPrevented()&&_A(e)&&wA(t,xA(e),EA(e),_)})(e))})(e),((e,t)=>{tc(e)&&e.on("dragend dragover draggesture dragdrop drop drag",(e=>{e.preventDefault(),e.stopPropagation()})),nc(e)||e.on("drop",(e=>{const t=e.dataTransfer;t&&(e=>$(e.files,(e=>/^image\//.test(e.type))))(t)&&e.preventDefault()})),e.on("drop",(n=>{if(n.isDefaultPrevented())return;const o=SA(e,n);if(y(o))return;const r=pA(n.dataTransfer),s=hA(r,GR());if((!bA(r)||(e=>{const t=e["text/plain"];return!!t&&0===t.indexOf("file://")})(r))&&vA(e,n,o))return;const a=r[GR()],i=a||r["text/html"]||r["text/plain"],l=((e,t,n,o)=>{const r=e.getParent(n,(e=>Wr(t,e)));if(!h(e.getParent(n,"summary")))return!0;if(r&&_e(o,"text/html")){const e=(new DOMParser).parseFromString(o["text/html"],"text/html").body;return!h(e.querySelector(r.nodeName.toLowerCase()))}return!1})(e.dom,e.schema,o.startContainer,r),d=t.get();d&&!l||i&&(n.preventDefault(),Rg.setEditorTimeout(e,(()=>{e.undoManager.transact((()=>{(a||d&&l)&&e.execCommand("Delete"),kA(e,o);const t=rA(i);r["text/html"]?fA(e,t,s,!0):gA(e,t,!0)}))})))})),e.on("dragstart",(e=>{t.set(!0)})),e.on("dragover dragend",(n=>{nc(e)&&!t.get()&&(n.preventDefault(),kA(e,SA(e,n))),"dragend"===n.type&&t.set(!1)})),(e=>{e.on("input",(t=>{const n=e=>h(e.querySelector("summary"));if("deleteByDrag"===t.inputType){const t=Y(e.dom.select("details"),n);V(t,(t=>{gr(t.firstChild)&&t.firstChild.remove();const n=e.dom.create("summary");n.appendChild(Ga().dom),t.prepend(n)}))}}))})(e)})(e,t),CA(e,o,n)}))},OA=gr,BA=lr,PA=e=>br(e.dom),DA=e=>t=>_n(yn(e),t),LA=(e,t)=>Qn(yn(e),PA,DA(t)),MA=(e,t,n)=>{const o=new $o(e,t),r=n?o.next.bind(o):o.prev.bind(o);let s=e;for(let t=n?e:r();t&&!OA(t);t=r())Oi(t)&&(s=t);return s},IA=e=>{const t=((e,t,n)=>{const o=Zi.fromRangeStart(e).getNode(),r=((e,t,n)=>Qn(yn(e),(e=>(e=>hr(e.dom))(e)||n.isBlock(Ht(e))),DA(t)).getOr(yn(t)).dom)(o,t,n),s=MA(o,r,!1),a=MA(o,r,!0),i=document.createRange();return LA(s,r).fold((()=>{BA(s)?i.setStart(s,0):i.setStartBefore(s)}),(e=>i.setStartBefore(e.dom))),LA(a,r).fold((()=>{BA(a)?i.setEnd(a,a.data.length):i.setEndAfter(a)}),(e=>i.setEndAfter(e.dom))),i})(e.selection.getRng(),e.getBody(),e.schema);e.selection.setRng(kb(t))};var FA;!function(e){e.Before="before",e.After="after"}(FA||(FA={}));const UA=(e,t)=>Math.abs(e.left-t),zA=(e,t)=>Math.abs(e.right-t),jA=(e,t)=>(e=>X(e,((e,t)=>e.fold((()=>I.some(t)),(e=>{const n=Math.min(t.left,e.left),o=Math.min(t.top,e.top),r=Math.max(t.right,e.right),s=Math.max(t.bottom,e.bottom);return I.some({top:o,right:r,bottom:s,left:n,width:r-n,height:s-o})}))),I.none()))(Y(e,(e=>{return(n=t)>=(o=e).top&&n<=o.bottom;var n,o}))).fold((()=>[[],e]),(t=>{const{pass:n,fail:o}=K(e,(e=>((e,t)=>{const n=((e,t)=>Math.max(0,Math.min(e.bottom,t.bottom)-Math.max(e.top,t.top)))(e,t)/Math.min(e.height,t.height);return((e,t)=>e.top<t.bottom&&e.bottom>t.top)(e,t)&&n>.5})(e,t)));return[n,o]})),HA=(e,t,n)=>t>e.left&&t<e.right?0:Math.min(Math.abs(e.left-t),Math.abs(e.right-t)),$A=(e,t,n,o)=>{const r=e=>Oi(e.node)?I.some(e):Jo(e.node)?$A(ce(e.node.childNodes),t,n,!1):I.none(),s=(e,s)=>{const a=ae(e,((e,o)=>s(e,t,n)-s(o,t,n)));return ue(a,r).map((e=>o&&!lr(e.node)&&a.length>1?((e,o,s)=>r(o).filter((o=>Math.abs(s(e,t,n)-s(o,t,n))<2&&lr(o.node))))(e,a[1],s).getOr(e):e))},[a,i]=jA(Kx(e),n),{pass:l,fail:d}=K(i,(e=>e.top<n));return s(a,HA).orThunk((()=>s(d,vi))).orThunk((()=>s(l,vi)))},qA=(e,t,n)=>((e,t,n)=>{const o=yn(e),r=Nn(o),s=Cn(r,t,n).filter((e=>Sn(o,e))).getOr(o);return((e,t,n,o)=>{const r=(t,s)=>{const a=Y(t.dom.childNodes,O((e=>Jo(e)&&e.classList.contains("mce-drag-container"))));return s.fold((()=>$A(a,n,o,!0)),(e=>{const t=Y(a,(t=>t!==e.dom));return $A(t,n,o,!0)})).orThunk((()=>(_n(t,e)?I.none():Tn(t)).bind((e=>r(e,I.some(t))))))};return r(t,I.none())})(o,s,t,n)})(e,t,n).filter((e=>Vc(e.node))).map((e=>((e,t)=>({node:e.node,position:UA(e,t)<zA(e,t)?FA.Before:FA.After}))(e,t))),VA=e=>{var t,n;const o=e.getBoundingClientRect(),r=e.ownerDocument,s=r.documentElement,a=r.defaultView;return{top:o.top+(null!==(t=null==a?void 0:a.scrollY)&&void 0!==t?t:0)-s.clientTop,left:o.left+(null!==(n=null==a?void 0:a.scrollX)&&void 0!==n?n:0)-s.clientLeft}},WA=e=>({target:e,srcElement:e}),KA=(e,t,n,o)=>{const r=((e,t)=>{const n=(e=>{const t=WR(),n=(e=>{const t=e;return I.from(t[LR])})(e);return UR(e),OR(t),t.dropEffect=e.dropEffect,t.effectAllowed=e.effectAllowed,(e=>{const t=e;return I.from(t[RR])})(e).each((e=>t.setDragImage(e.image,e.x,e.y))),V(e.types,(n=>{"Files"!==n&&t.setData(n,e.getData(n))})),V(e.files,(e=>t.items.add(e))),(e=>{const t=e;return I.from(t[AR])})(e).each((e=>{((e,t)=>{TR(t)(e)})(t,e)})),n.each((n=>{IR(e,n),IR(t,n)})),t})(e);return"dragstart"===t?(OR(n),FR(n)):"drop"===t?(BR(n),UR(n)):(PR(n),zR(n)),n})(n,e);return v(o)?((e,t,n)=>{const o=B("Function not supported on simulated event.");return{bubbles:!0,cancelBubble:!1,cancelable:!0,composed:!1,currentTarget:null,defaultPrevented:!1,eventPhase:0,isTrusted:!0,returnValue:!1,timeStamp:0,type:e,composedPath:o,initEvent:o,preventDefault:_,stopImmediatePropagation:_,stopPropagation:_,AT_TARGET:window.Event.AT_TARGET,BUBBLING_PHASE:window.Event.BUBBLING_PHASE,CAPTURING_PHASE:window.Event.CAPTURING_PHASE,NONE:window.Event.NONE,altKey:!1,button:0,buttons:0,clientX:0,clientY:0,ctrlKey:!1,metaKey:!1,movementX:0,movementY:0,offsetX:0,offsetY:0,pageX:0,pageY:0,relatedTarget:null,screenX:0,screenY:0,shiftKey:!1,x:0,y:0,detail:0,view:null,which:0,initUIEvent:o,initMouseEvent:o,getModifierState:o,dataTransfer:n,...WA(t)}})(e,t,r):((e,t,n,o)=>({...t,dataTransfer:o,type:e,...WA(n)}))(e,o,t,r)},YA=br,GA=((...e)=>t=>{for(let n=0;n<e.length;n++)if(e[n](t))return!0;return!1})(YA,hr),XA=(e,t,n,o)=>{const r=e.dom,s=t.cloneNode(!0);r.setStyles(s,{width:n,height:o}),r.setAttrib(s,"data-mce-selected",null);const a=r.create("div",{class:"mce-drag-container","data-mce-bogus":"all",unselectable:"on",contenteditable:"false"});return r.setStyles(a,{position:"absolute",opacity:.5,overflow:"hidden",border:0,padding:0,margin:0,width:n,height:o}),r.setStyles(s,{margin:0,boxSizing:"border-box"}),a.appendChild(s),a},ZA=(e,t)=>n=>()=>{const o="left"===e?n.scrollX:n.scrollY;n.scroll({[e]:o+t,behavior:"smooth"})},QA=ZA("left",-32),JA=ZA("left",32),eT=ZA("top",-32),tT=ZA("top",32),nT=e=>{e&&e.parentNode&&e.parentNode.removeChild(e)},oT=(e,t,n,o,r)=>{"dragstart"===t&&KR(o,e.dom.getOuterHTML(n));const s=KA(t,n,o,r);return e.dispatch(t,s)},rT=(e,t)=>{const n=ya(((e,n)=>((e,t,n)=>{e._selectionOverrides.hideFakeCaret(),qA(e.getBody(),t,n).fold((()=>e.selection.placeCaretAt(t,n)),(o=>{const r=e._selectionOverrides.showCaret(1,o.node,o.position===FA.Before,!1);r?e.selection.setRng(r):e.selection.placeCaretAt(t,n)}))})(t,e,n)),0);t.on("remove",n.cancel);const o=e;return r=>e.on((e=>{const s=Math.max(Math.abs(r.screenX-e.screenX),Math.abs(r.screenY-e.screenY));if(!e.dragging&&s>10){const n=oT(t,"dragstart",e.element,e.dataTransfer,r);if(C(n.dataTransfer)&&(e.dataTransfer=n.dataTransfer),n.isDefaultPrevented())return;e.dragging=!0,t.focus()}if(e.dragging){const s=r.currentTarget===t.getDoc().documentElement,l=((e,t)=>({pageX:t.pageX-e.relX,pageY:t.pageY+5}))(e,((e,t)=>{return n=(e=>e.inline?VA(e.getBody()):{left:0,top:0})(e),o=(e=>{const t=e.getBody();return e.inline?{left:t.scrollLeft,top:t.scrollTop}:{left:0,top:0}})(e),r=((e,t)=>{if(t.target.ownerDocument!==e.getDoc()){const n=VA(e.getContentAreaContainer()),o=(e=>{const t=e.getBody(),n=e.getDoc().documentElement,o={left:t.scrollLeft,top:t.scrollTop},r={left:t.scrollLeft||n.scrollLeft,top:t.scrollTop||n.scrollTop};return e.inline?o:r})(e);return{left:t.pageX-n.left+o.left,top:t.pageY-n.top+o.top}}return{left:t.pageX,top:t.pageY}})(e,t),{pageX:r.left-n.left+o.left,pageY:r.top-n.top+o.top};var n,o,r})(t,r));a=e.ghost,i=t.getBody(),a.parentNode!==i&&i.appendChild(a),((e,t,n,o,r,s,a,i,l,d,c,u)=>{let m=0,f=0;e.style.left=t.pageX+"px",e.style.top=t.pageY+"px",t.pageX+n>r&&(m=t.pageX+n-r),t.pageY+o>s&&(f=t.pageY+o-s),e.style.width=n-m+"px",e.style.height=o-f+"px";const g=l.clientHeight,p=l.clientWidth,h=a+l.getBoundingClientRect().top,b=i+l.getBoundingClientRect().left;c.on((e=>{e.intervalId.clear(),e.dragging&&u&&(a+8>=g?e.intervalId.set(tT(d)):a-8<=0?e.intervalId.set(eT(d)):i+8>=p?e.intervalId.set(JA(d)):i-8<=0?e.intervalId.set(QA(d)):h+16>=window.innerHeight?e.intervalId.set(tT(window)):h-16<=0?e.intervalId.set(eT(window)):b+16>=window.innerWidth?e.intervalId.set(JA(window)):b-16<=0&&e.intervalId.set(QA(window)))}))})(e.ghost,l,e.width,e.height,e.maxX,e.maxY,r.clientY,r.clientX,t.getContentAreaContainer(),t.getWin(),o,s),n.throttle(r.clientX,r.clientY)}var a,i}))},sT=(e,t,n)=>{e.on((e=>{e.intervalId.clear(),e.dragging&&n.fold((()=>oT(t,"dragend",e.element,e.dataTransfer)),(n=>oT(t,"dragend",e.element,e.dataTransfer,n)))})),aT(e)},aT=e=>{e.on((e=>{e.intervalId.clear(),nT(e.ghost)})),e.clear()},iT=e=>{const t=va(),n=la.DOM,o=document,r=((e,t)=>n=>{if((e=>0===e.button)(n)){const o=Q(t.dom.getParents(n.target),GA).getOr(null);if(C(o)&&((e,t,n)=>YA(n)&&n!==t&&e.isEditable(n.parentElement))(t.dom,t.getBody(),o)){const r=t.dom.getPos(o),s=t.getBody(),a=t.getDoc().documentElement;e.set({element:o,dataTransfer:WR(),dragging:!1,screenX:n.screenX,screenY:n.screenY,maxX:(t.inline?s.scrollWidth:a.offsetWidth)-2,maxY:(t.inline?s.scrollHeight:a.offsetHeight)-2,relX:n.pageX-r.x,relY:n.pageY-r.y,width:o.offsetWidth,height:o.offsetHeight,ghost:XA(t,o,o.offsetWidth,o.offsetHeight),intervalId:ba(100)})}}})(t,e),s=rT(t,e),a=((e,t)=>n=>{e.on((e=>{var o;if(e.intervalId.clear(),e.dragging){if(((e,t,n)=>!y(t)&&t!==n&&!e.dom.isChildOf(t,n)&&e.dom.isEditable(t))(t,(e=>{const t=e.getSel();if(C(t)){const e=t.getRangeAt(0).startContainer;return lr(e)?e.parentNode:e}return null})(t.selection),e.element)){const r=null!==(o=t.getDoc().elementFromPoint(n.clientX,n.clientY))&&void 0!==o?o:t.getBody();oT(t,"drop",r,e.dataTransfer,n).isDefaultPrevented()||t.undoManager.transact((()=>{((e,t)=>{const n=e.getParent(t.parentNode,e.isBlock);nT(t),n&&n!==e.getRoot()&&e.isEmpty(n)&&Xa(yn(n))})(t.dom,e.element),(e=>{const t=e.getData("text/html");return""===t?I.none():I.some(t)})(e.dataTransfer).each((e=>t.insertContent(e))),t._selectionOverrides.hideFakeCaret()}))}oT(t,"dragend",t.getBody(),e.dataTransfer,n)}})),aT(e)})(t,e),i=((e,t)=>n=>sT(e,t,I.some(n)))(t,e);e.on("mousedown",r),e.on("mousemove",s),e.on("mouseup",a),n.bind(o,"mousemove",s),n.bind(o,"mouseup",i),e.on("remove",(()=>{n.unbind(o,"mousemove",s),n.unbind(o,"mouseup",i)})),e.on("keydown",(n=>{n.keyCode===bf.ESC&&sT(t,e,I.none())}))},lT=br,dT=(e,t)=>ub(e.getBody(),t),cT=e=>{const t=e.selection,n=e.dom,o=e.getBody(),r=Hc(e,o,n.isBlock,(()=>Fg(e))),s="sel-"+n.uniqueId(),a="data-mce-selected";let i;const l=e=>e!==o&&(lT(e)||Cr(e))&&n.isChildOf(e,o)&&n.isEditable(e.parentNode),d=(n,o,s,a=!0)=>e.dispatch("ShowCaret",{target:o,direction:n,before:s}).isDefaultPrevented()?null:(a&&t.scrollIntoView(o,-1===n),r.show(s,o)),c=e=>ri(e)||li(e)||di(e),u=e=>c(e.startContainer)||c(e.endContainer),m=t=>{const o=e.schema.getVoidElements(),r=n.createRng(),s=t.startContainer,a=t.startOffset,i=t.endContainer,l=t.endOffset;return _e(o,s.nodeName.toLowerCase())?0===a?r.setStartBefore(s):r.setStartAfter(s):r.setStart(s,a),_e(o,i.nodeName.toLowerCase())?0===l?r.setEndBefore(i):r.setEndAfter(i):r.setEnd(i,l),r},f=(r,c)=>{if(!r)return null;if(r.collapsed){if(!u(r)){const e=c?1:-1,t=pu(e,o,r),s=t.getNode(!c);if(C(s)){if(Vc(s))return d(e,s,!!c&&!t.isAtEnd(),!1);if(oi(s)&&br(s.nextSibling)){const e=n.createRng();return e.setStart(s,0),e.setEnd(s,0),e}}const a=t.getNode(c);if(C(a)){if(Vc(a))return d(e,a,!c&&!t.isAtEnd(),!1);if(oi(a)&&br(a.previousSibling)){const e=n.createRng();return e.setStart(a,1),e.setEnd(a,1),e}}}return null}let m=r.startContainer,f=r.startOffset;const g=r.endOffset;if(lr(m)&&0===f&&lT(m.parentNode)&&(m=m.parentNode,f=n.nodeIndex(m),m=m.parentNode),!Jo(m))return null;if(g===f+1&&m===r.endContainer){const o=m.childNodes[f];if(l(o))return(o=>{const r=o.cloneNode(!0),l=e.dispatch("ObjectSelected",{target:o,targetClone:r});if(l.isDefaultPrevented())return null;const d=((o,r)=>{const a=yn(e.getBody()),i=e.getDoc(),l=to(a,"#"+s).getOrThunk((()=>{const e=hn('<div data-mce-bogus="all" class="mce-offscreen-selection"></div>',i);return Qt(e,"id",s),vo(a,e),e})),d=n.createRng();wo(l),Co(l,[vn(Vo,i),yn(r),vn(Vo,i)]),d.setStart(l.dom.firstChild,1),d.setEnd(l.dom.lastChild,0),lo(l,{top:n.getPos(o,e.getBody()).y+"px"}),hg(l);const c=t.getSel();return c&&(c.removeAllRanges(),c.addRange(d)),d})(o,l.targetClone),c=yn(o);return V(Uo(yn(e.getBody()),`*[${a}]`),(e=>{_n(c,e)||on(e,a)})),n.getAttrib(o,a)||o.setAttribute(a,"1"),i=o,p(),d})(o)}return null},g=()=>{i&&i.removeAttribute(a),to(yn(e.getBody()),"#"+s).each(Eo),i=null},p=()=>{r.hide()};return aw(e)||(e.on("click",(t=>{n.isEditable(t.target)||(t.preventDefault(),e.focus())})),e.on("blur NewBlock",g),e.on("ResizeWindow FullscreenStateChanged",r.reposition),e.on("tap",(t=>{const n=t.target,o=dT(e,n);lT(o)?(t.preventDefault(),ME(e,o).each(f)):l(n)&&ME(e,n).each(f)}),!0),e.on("mousedown",(r=>{const s=r.target;if(s!==o&&"HTML"!==s.nodeName&&!n.isChildOf(s,o))return;if(!((e,t,n)=>{const o=yn(e.getBody()),r=e.inline?o:yn(Nn(o).dom.documentElement),s=((e,t,n,o)=>{const r=(e=>e.dom.getBoundingClientRect())(t);return{x:n-(e?r.left+t.dom.clientLeft+Uw(t):0),y:o-(e?r.top+t.dom.clientTop+Fw(t):0)}})(e.inline,r,t,n);return((e,t,n)=>{const o=Mw(e),r=Iw(e);return t>=0&&n>=0&&t<=o&&n<=r})(r,s.x,s.y)})(e,r.clientX,r.clientY))return;g(),p();const a=dT(e,s);lT(a)?(r.preventDefault(),ME(e,a).each(f)):qA(o,r.clientX,r.clientY).each((n=>{var o;r.preventDefault(),(o=d(1,n.node,n.position===FA.Before,!1))&&t.setRng(o),er(a)?a.focus():e.getBody().focus()}))})),e.on("keypress",(e=>{bf.modifierPressed(e)||lT(t.getNode())&&e.preventDefault()})),e.on("GetSelectionRange",(e=>{let t=e.range;if(i){if(!i.parentNode)return void(i=null);t=t.cloneRange(),t.selectNode(i),e.range=t}})),e.on("SetSelectionRange",(e=>{e.range=m(e.range);const t=f(e.range,e.forward);t&&(e.range=t)})),e.on("AfterSetSelectionRange",(e=>{const t=e.range,o=t.startContainer.parentElement;var r;u(t)||Jo(r=o)&&"mcepastebin"===r.id||p(),(e=>C(e)&&n.hasClass(e,"mce-offscreen-selection"))(o)||g()})),(e=>{iT(e),qd(e)&&(e=>{const t=t=>{if(!t.isDefaultPrevented()){const n=t.dataTransfer;n&&(H(n.types,"Files")||n.files.length>0)&&(t.preventDefault(),"drop"===t.type&&Vw(e,"Dropped file type is not supported"))}},n=n=>{Bg(e,n.target)&&t(n)},o=()=>{const o=la.DOM,r=e.dom,s=document,a=e.inline?e.getBody():e.getDoc(),i=["drop","dragover"];V(i,(e=>{o.bind(s,e,n),r.bind(a,e,t)})),e.on("remove",(()=>{V(i,(e=>{o.unbind(s,e,n),r.unbind(a,e,t)}))}))};e.on("init",(()=>{Rg.setEditorTimeout(e,o,0)}))})(e)})(e),(e=>{const t=ya((()=>{if(!e.removed&&e.getBody().contains(document.activeElement)){const t=e.selection.getRng();if(t.collapsed){const n=IE(e,t,!1);e.selection.setRng(n)}}}),0);e.on("focus",(()=>{t.throttle()})),e.on("blur",(()=>{t.cancel()}))})(e),(e=>{e.on("init",(()=>{e.on("focusin",(t=>{const n=t.target;if(Cr(n)){const t=ub(e.getBody(),n),o=br(t)?t:n;e.selection.getNode()!==o&&ME(e,o).each((t=>e.selection.setRng(t)))}}))}))})(e)),{showCaret:d,showBlockCaretContainer:e=>{e.hasAttribute("data-mce-caret")&&(ci(e),t.scrollIntoView(e))},hideFakeCaret:p,destroy:()=>{r.destroy(),i=null}}},uT=(e,t)=>{let n=t;for(let t=e.previousSibling;lr(t);t=t.previousSibling)n+=t.data.length;return n},mT=(e,t,n,o,r)=>{if(lr(n)&&(o<0||o>n.data.length))return[];const s=r&&lr(n)?[uT(n,o)]:[o];let a=n;for(;a!==t&&a.parentNode;)s.push(e.nodeIndex(a,r)),a=a.parentNode;return a===t?s.reverse():[]},fT=(e,t,n,o,r,s,a=!1)=>({start:mT(e,t,n,o,a),end:mT(e,t,r,s,a)}),gT=(e,t)=>{const n=t.slice(),o=n.pop();return E(o)?X(n,((e,t)=>e.bind((e=>I.from(e.childNodes[t])))),I.some(e)).bind((e=>lr(e)&&(o<0||o>e.data.length)?I.none():I.some({node:e,offset:o}))):I.none()},pT=(e,t)=>gT(e,t.start).bind((({node:n,offset:o})=>gT(e,t.end).map((({node:e,offset:t})=>{const r=document.createRange();return r.setStart(n,o),r.setEnd(e,t),r})))),hT=(e,t,n)=>{if(t&&e.isEmpty(t)&&!n(t)){const o=t.parentNode;e.remove(t,lr(t.firstChild)&&Yo(t.firstChild.data)),hT(e,o,n)}},bT=(e,t,n,o=!0)=>{const r=t.startContainer.parentNode,s=t.endContainer.parentNode;t.deleteContents(),o&&!n(t.startContainer)&&(lr(t.startContainer)&&0===t.startContainer.data.length&&e.remove(t.startContainer),lr(t.endContainer)&&0===t.endContainer.data.length&&e.remove(t.endContainer),hT(e,r,n),r!==s&&hT(e,s,n))},vT=(e,t)=>I.from(e.dom.getParent(t.startContainer,e.dom.isBlock)),yT=(e,t,n)=>{const o=e.dynamicPatternsLookup({text:n,block:t});return{...e,blockPatterns:kl(o).concat(e.blockPatterns),inlinePatterns:Nl(o).concat(e.inlinePatterns)}},CT=(e,t,n,o)=>{const r=e.createRng();return r.setStart(t,0),r.setEnd(n,o),r.toString()},wT=(e,t)=>e.create("span",{"data-mce-type":"bookmark",id:t}),ET=(e,t)=>{const n=e.createRng();return n.setStartAfter(t.start),n.setEndBefore(t.end),n},xT=(e,t,n)=>{const o=pT(e.getRoot(),n).getOrDie("Unable to resolve path range"),r=o.startContainer,s=o.endContainer,a=0===o.endOffset?s:s.splitText(o.endOffset),i=0===o.startOffset?r:r.splitText(o.startOffset),l=i.parentNode;return{prefix:t,end:a.parentNode.insertBefore(wT(e,t+"-end"),a),start:l.insertBefore(wT(e,t+"-start"),i)}},_T=(e,t,n)=>{hT(e,e.get(t.prefix+"-end"),n),hT(e,e.get(t.prefix+"-start"),n)},ST=e=>0===e.start.length,kT=(e,t,n,o)=>{const r=t.start;var s;return KS(e,o.container,o.offset,(s=r,(e,t)=>{const n=e.data.substring(0,t),o=n.lastIndexOf(s.charAt(s.length-1)),r=n.lastIndexOf(s);return-1!==r?r+s.length:-1!==o?o+1:-1}),n).bind((o=>{var s,a;const i=null!==(a=null===(s=n.textContent)||void 0===s?void 0:s.indexOf(r))&&void 0!==a?a:-1;if(-1!==i&&o.offset>=i+r.length){const t=e.createRng();return t.setStart(o.container,o.offset-r.length),t.setEnd(o.container,o.offset),I.some(t)}{const s=o.offset-r.length;return VS(o.container,s,n).map((t=>{const n=e.createRng();return n.setStart(t.container,t.offset),n.setEnd(o.container,o.offset),n})).filter((e=>e.toString()===r)).orThunk((()=>kT(e,t,n,jS(o.container,0))))}}))},NT=(e,t,n,o)=>{const r=e.dom,s=r.getRoot(),a=n.pattern,i=n.position.container,l=n.position.offset;return VS(i,l-n.pattern.end.length,t).bind((d=>{const c=fT(r,s,d.container,d.offset,i,l,o);if(ST(a))return I.some({matches:[{pattern:a,startRng:c,endRng:c}],position:d});{const i=RT(e,n.remainingPatterns,d.container,d.offset,t,o),l=i.getOr({matches:[],position:d}),u=l.position,m=((e,t,n,o,r,s=!1)=>{if(0===t.start.length&&!s){const t=e.createRng();return t.setStart(n,o),t.setEnd(n,o),I.some(t)}return qS(n,o,r).bind((n=>kT(e,t,r,n).bind((e=>{var t;if(s){if(e.endContainer===n.container&&e.endOffset===n.offset)return I.none();if(0===n.offset&&(null===(t=e.endContainer.textContent)||void 0===t?void 0:t.length)===e.endOffset)return I.none()}return I.some(e)}))))})(r,a,u.container,u.offset,t,i.isNone());return m.map((e=>{const t=((e,t,n,o=!1)=>fT(e,t,n.startContainer,n.startOffset,n.endContainer,n.endOffset,o))(r,s,e,o);return{matches:l.matches.concat([{pattern:a,startRng:t,endRng:c}]),position:jS(e.startContainer,e.startOffset)}}))}}))},RT=(e,t,n,o,r,s)=>{const a=e.dom;return qS(n,o,a.getRoot()).bind((i=>{const l=CT(a,r,n,o);for(let a=0;a<t.length;a++){const d=t[a];if(!qe(l,d.end))continue;const c=t.slice();c.splice(a,1);const u=NT(e,r,{pattern:d,remainingPatterns:c,position:i},s);if(u.isNone()&&o>0)return RT(e,t,n,o-1,r,s);if(u.isSome())return u}return I.none()}))},AT=(e,t,n)=>{e.selection.setRng(n),"inline-format"===t.type?V(t.format,(t=>{e.formatter.apply(t)})):e.execCommand(t.cmd,!1,t.value)},TT=(e,t,n,o,r,s)=>{var a;return((e,t)=>{const n=ne(e,(e=>$(t,(t=>e.pattern.start===t.pattern.start&&e.pattern.end===t.pattern.end))));return e.length===t.length?n?e:t:e.length>t.length?e:t})(RT(e,r.inlinePatterns,n,o,t,s).fold((()=>[]),(e=>e.matches)),RT(e,(a=r.inlinePatterns,ae(a,((e,t)=>t.end.length-e.end.length))),n,o,t,s).fold((()=>[]),(e=>e.matches)))},OT=(e,t)=>{if(0===t.length)return;const n=e.dom,o=e.selection.getBookmark(),r=((e,t)=>{const n=Pa("mce_textpattern"),o=G(t,((t,o)=>{const r=xT(e,n+`_end${t.length}`,o.endRng);return t.concat([{...o,endMarker:r}])}),[]);return G(o,((t,r)=>{const s=o.length-t.length-1,a=ST(r.pattern)?r.endMarker:xT(e,n+`_start${s}`,r.startRng);return t.concat([{...r,startMarker:a}])}),[])})(n,t);V(r,(t=>{const o=n.getParent(t.startMarker.start,n.isBlock),r=e=>e===o;ST(t.pattern)?((e,t,n,o)=>{const r=ET(e.dom,n);bT(e.dom,r,o),AT(e,t,r)})(e,t.pattern,t.endMarker,r):((e,t,n,o,r)=>{const s=e.dom,a=ET(s,o),i=ET(s,n);bT(s,i,r),bT(s,a,r);const l={prefix:n.prefix,start:n.end,end:o.start},d=ET(s,l);AT(e,t,d)})(e,t.pattern,t.startMarker,t.endMarker,r),_T(n,t.endMarker,r),_T(n,t.startMarker,r)})),e.selection.moveToBookmark(o)},BT=(e,t,n)=>((e,t,n)=>{if(lr(e)&&0>=e.length)return I.some(jS(e,0));{const t=Fa(HS);return I.from(t.forwards(e,0,$S(e),n)).map((e=>jS(e.container,0)))}})(t,0,t).map((o=>{const r=o.container;return WS(r,n.start.length,t).each((n=>{const o=e.createRng();o.setStart(r,0),o.setEnd(n.container,n.offset),bT(e,o,(e=>e===t))})),r})),PT=e=>(t,n)=>{const o=t.dom,r=n.pattern,s=pT(o.getRoot(),n.range).getOrDie("Unable to resolve path range");return vT(t,s).each((n=>{"block-format"===r.type?((e,t)=>{const n=t.get(e);return p(n)&&le(n).exists((e=>_e(e,"block")))})(r.format,t.formatter)&&t.undoManager.transact((()=>{e(t.dom,n,r),t.formatter.apply(r.format)})):"block-command"===r.type&&t.undoManager.transact((()=>{e(t.dom,n,r),t.execCommand(r.cmd,!1,r.value)}))})),!0},DT=e=>(t,n)=>{const o=(e=>ae(e,((e,t)=>t.start.length-e.start.length)))(t),r=n.replace(Vo," ");return Q(o,(t=>e(t,n,r)))},LT=(e,t)=>(n,o,r,s,a)=>{var i;void 0===a&&(a=null!==(i=o.textContent)&&void 0!==i?i:"");const l=n.dom,d=ql(n);return l.is(o,d)?e(r.blockPatterns,a).map((e=>t&&Pt.trim(a).length===e.start.length?[]:[{pattern:e,range:fT(l,l.getRoot(),o,0,o,0,s)}])).getOr([]):[]},MT=PT(((e,t,n)=>{BT(e,t,n).each((e=>{const t=yn(e),n=za(t);/^\s[^\s]/.test(n)&&((e,t)=>{Ua.set(e,t)})(t,n.slice(1))}))})),IT=DT(((e,t,n)=>0===t.indexOf(e.start)||0===n.indexOf(e.start))),FT=LT(IT,!0),UT=PT(BT),zT=DT(((e,t,n)=>t===e.start||n===e.start)),jT=LT(zT,!1),HT=(e,t,n)=>{for(let o=0;o<e.length;o++)if(n(e[o],t))return!0;return!1},$T=e=>{const t=[",",".",";",":","!","?"],n=[32],o=()=>{return t=fc(e),n=gc(e),{inlinePatterns:Nl(t),blockPatterns:kl(t),dynamicPatternsLookup:n};var t,n},r=()=>(e=>e.options.isSet("text_patterns_lookup"))(e);e.on("keydown",(t=>{if(13===t.keyCode&&!bf.modifierPressed(t)&&e.selection.isCollapsed()){const n=Rl(o(),"enter");(n.inlinePatterns.length>0||n.blockPatterns.length>0||r())&&((e,t)=>((e,t)=>{const n=e.selection.getRng();return vT(e,n).map((o=>{var r;const s=Math.max(0,n.startOffset),a=yT(t,o,null!==(r=o.textContent)&&void 0!==r?r:"");return{inlineMatches:TT(e,o,n.startContainer,s,a,!0),blockMatches:FT(e,o,a,!0)}})).filter((({inlineMatches:e,blockMatches:t})=>t.length>0||e.length>0))})(e,t).fold(L,(({inlineMatches:t,blockMatches:n})=>(e.undoManager.add(),e.undoManager.extra((()=>{e.execCommand("mceInsertNewLine")}),(()=>{(e=>{e.insertContent(Za,{preserve_zwsp:!0})})(e),OT(e,t),((e,t)=>{if(0===t.length)return;const n=e.selection.getBookmark();V(t,(t=>MT(e,t))),e.selection.moveToBookmark(n)})(e,n);const o=e.selection.getRng(),r=qS(o.startContainer,o.startOffset,e.dom.getRoot());e.execCommand("mceInsertNewLine"),r.each((t=>{const n=t.container;n.data.charAt(t.offset-1)===qo&&(n.deleteData(t.offset-1,1),hT(e.dom,n.parentNode,(t=>t===e.dom.getRoot())))}))})),!0))))(e,n)&&t.preventDefault()}}),!0),e.on("keydown",(t=>{if(32===t.keyCode&&e.selection.isCollapsed()){const n=Rl(o(),"space");(n.blockPatterns.length>0||r())&&((e,t)=>((e,t)=>{const n=e.selection.getRng();return vT(e,n).map((o=>{const r=Math.max(0,n.startOffset),s=CT(e.dom,o,n.startContainer,r),a=yT(t,o,s);return jT(e,o,a,!1,s)})).filter((e=>e.length>0))})(e,t).fold(L,(t=>(e.undoManager.transact((()=>{((e,t)=>{V(t,(t=>UT(e,t)))})(e,t)})),!0))))(e,n)&&t.preventDefault()}}),!0);const s=()=>{if(e.selection.isCollapsed()){const t=Rl(o(),"space");(t.inlinePatterns.length>0||r())&&((e,t)=>{const n=e.selection.getRng();vT(e,n).map((o=>{const r=Math.max(0,n.startOffset-1),s=CT(e.dom,o,n.startContainer,r),a=yT(t,o,s),i=TT(e,o,n.startContainer,r,a,!1);i.length>0&&e.undoManager.transact((()=>{OT(e,i)}))}))})(e,t)}};e.on("keyup",(e=>{HT(n,e,((e,t)=>e===t.keyCode&&!bf.modifierPressed(t)))&&s()})),e.on("keypress",(n=>{HT(t,n,((e,t)=>e.charCodeAt(0)===t.charCode))&&Rg.setEditorTimeout(e,s)}))},qT=e=>{const t=Pt.each,n=bf.BACKSPACE,o=bf.DELETE,r=e.dom,s=e.selection,a=e.parser,i=Tt.browser,l=i.isFirefox(),d=i.isChromium()||i.isSafari(),c=Tt.deviceType.isiPhone()||Tt.deviceType.isiPad(),u=Tt.os.isMacOS()||Tt.os.isiOS(),m=(t,n)=>{try{e.getDoc().execCommand(t,!1,String(n))}catch(e){}},f=e=>e.isDefaultPrevented(),g=()=>{e.shortcuts.add("meta+a",null,"SelectAll")},p=()=>{e.inline||r.bind(e.getDoc(),"mousedown mouseup",(t=>{let n;if(t.target===e.getDoc().documentElement)if(n=s.getRng(),e.getBody().focus(),"mousedown"===t.type){if(ri(n.startContainer))return;s.placeCaretAt(t.clientX,t.clientY)}else s.setRng(n)}))},h=()=>{Range.prototype.getClientRects||e.on("mousedown",(t=>{if(!f(t)&&"HTML"===t.target.nodeName){const t=e.getBody();t.blur(),Rg.setEditorTimeout(e,(()=>{t.focus()}))}}))},b=()=>{const t=Kd(e);e.on("click",(n=>{const o=n.target;/^(IMG|HR)$/.test(o.nodeName)&&r.isEditable(o)&&(n.preventDefault(),e.selection.select(o),e.nodeChanged()),"A"===o.nodeName&&r.hasClass(o,t)&&0===o.childNodes.length&&r.isEditable(o.parentNode)&&(n.preventDefault(),s.select(o))}))},v=()=>{e.on("keydown",(e=>{if(!f(e)&&e.keyCode===n&&s.isCollapsed()&&0===s.getRng().startOffset){const t=s.getNode().previousSibling;if(t&&t.nodeName&&"table"===t.nodeName.toLowerCase())return e.preventDefault(),!1}return!0}))},y=()=>{Ud(e)||e.on("BeforeExecCommand mousedown",(()=>{m("StyleWithCSS",!1),m("enableInlineTableEditing",!1),bd(e)||m("enableObjectResizing",!1)}))},C=()=>{e.contentStyles.push("img:-moz-broken {-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}")},w=()=>{e.inline||e.on("keydown",(()=>{document.activeElement===document.body&&e.getWin().focus()}))},E=()=>{e.inline||(e.contentStyles.push("body {min-height: 150px}"),e.on("click",(t=>{let n;"HTML"===t.target.nodeName&&(n=e.selection.getRng(),e.getBody().focus(),e.selection.setRng(n),e.selection.normalize(),e.nodeChanged())})))},x=()=>{u&&e.on("keydown",(t=>{!bf.metaKeyPressed(t)||t.shiftKey||37!==t.keyCode&&39!==t.keyCode||(t.preventDefault(),e.selection.getSel().modify("move",37===t.keyCode?"backward":"forward","lineboundary"))}))},S=()=>{e.on("click",(e=>{let t=e.target;do{if("A"===t.tagName)return void e.preventDefault()}while(t=t.parentNode)})),e.contentStyles.push(".mce-content-body {-webkit-touch-callout: none}")},k=()=>{e.on("init",(()=>{e.dom.bind(e.getBody(),"submit",(e=>{e.preventDefault()}))}))},N=_;return aw(e)?(d&&(p(),b(),k(),g(),c&&(w(),E(),S())),l&&(h(),y(),C(),x())):(e.on("keydown",(t=>{if(f(t)||t.keyCode!==bf.BACKSPACE)return;let n=s.getRng();const o=n.startContainer,a=n.startOffset,i=r.getRoot();let l=o;if(n.collapsed&&0===a){for(;l.parentNode&&l.parentNode.firstChild===l&&l.parentNode!==i;)l=l.parentNode;"BLOCKQUOTE"===l.nodeName&&(e.formatter.toggle("blockquote",void 0,l),n=r.createRng(),n.setStart(o,0),n.setEnd(o,0),s.setRng(n))}})),(()=>{const t=e=>{const t=r.create("body"),n=e.cloneContents();return t.appendChild(n),s.serializer.serialize(t,{format:"html"})};e.on("keydown",(s=>{const a=s.keyCode;if(!f(s)&&(a===o||a===n)&&e.selection.isEditable()){const n=e.selection.isCollapsed(),o=e.getBody();if(n&&!Rr(e.schema,o))return;if(!n&&!(n=>{const o=t(n),s=r.createRng();return s.selectNode(e.getBody()),o===t(s)})(e.selection.getRng()))return;s.preventDefault(),e.setContent(""),o.firstChild&&r.isBlock(o.firstChild)?e.selection.setCursorLocation(o.firstChild,0):e.selection.setCursorLocation(o,0),e.nodeChanged()}}))})(),Tt.windowsPhone||e.on("keyup focusin mouseup",(t=>{bf.modifierPressed(t)||(e=>{const t=e.getBody(),n=e.selection.getRng();return n.startContainer===n.endContainer&&n.startContainer===t&&0===n.startOffset&&n.endOffset===t.childNodes.length})(e)||s.normalize()}),!0),d&&(p(),b(),e.on("init",(()=>{m("DefaultParagraphSeparator",ql(e))})),k(),v(),a.addNodeFilter("br",(e=>{let t=e.length;for(;t--;)"Apple-interchange-newline"===e[t].attr("class")&&e[t].remove()})),c?(w(),E(),S()):g()),l&&(e.on("keydown",(t=>{if(!f(t)&&t.keyCode===n){if(!e.getBody().getElementsByTagName("hr").length)return;if(s.isCollapsed()&&0===s.getRng().startOffset){const e=s.getNode(),n=e.previousSibling;if("HR"===e.nodeName)return r.remove(e),void t.preventDefault();n&&n.nodeName&&"hr"===n.nodeName.toLowerCase()&&(r.remove(n),t.preventDefault())}}})),h(),(()=>{const n=()=>{const n=r.getAttribs(s.getStart().cloneNode(!1));return()=>{const o=s.getStart();o!==e.getBody()&&(r.setAttrib(o,"style",null),t(n,(e=>{o.setAttributeNode(e.cloneNode(!0))})))}},o=()=>!s.isCollapsed()&&r.getParent(s.getStart(),r.isBlock)!==r.getParent(s.getEnd(),r.isBlock);e.on("keypress",(t=>{let r;return!(!(f(t)||8!==t.keyCode&&46!==t.keyCode)&&o()&&(r=n(),e.getDoc().execCommand("delete",!1),r(),t.preventDefault(),1))})),r.bind(e.getDoc(),"cut",(t=>{if(!f(t)&&o()){const t=n();Rg.setEditorTimeout(e,(()=>{t()}))}}))})(),y(),e.on("SetContent ExecCommand",(e=>{"setcontent"!==e.type&&"mceInsertLink"!==e.command||t(r.select("a:not([data-mce-block])"),(e=>{var t;let n=e.parentNode;const o=r.getRoot();if((null==n?void 0:n.lastChild)===e){for(;n&&!r.isBlock(n);){if((null===(t=n.parentNode)||void 0===t?void 0:t.lastChild)!==n||n===o)return;n=n.parentNode}r.add(n,"br",{"data-mce-bogus":1})}}))})),C(),x(),v())),{refreshContentEditable:N,isHidden:()=>{if(!l||e.removed)return!1;const t=e.selection.getSel();return!t||!t.rangeCount||0===t.rangeCount}}},VT=la.DOM,WT=e=>e.inline?e.getElement().nodeName.toLowerCase():void 0,KT=e=>Ce(e,(e=>!1===v(e))),YT=e=>{const t=e.options.get,n=e.editorUpload.blobCache;return KT({allow_conditional_comments:t("allow_conditional_comments"),allow_html_data_urls:t("allow_html_data_urls"),allow_svg_data_urls:t("allow_svg_data_urls"),allow_html_in_named_anchor:t("allow_html_in_named_anchor"),allow_script_urls:t("allow_script_urls"),allow_unsafe_link_target:t("allow_unsafe_link_target"),convert_unsafe_embeds:t("convert_unsafe_embeds"),convert_fonts_to_spans:t("convert_fonts_to_spans"),fix_list_elements:t("fix_list_elements"),font_size_legacy_values:t("font_size_legacy_values"),forced_root_block:t("forced_root_block"),forced_root_block_attrs:t("forced_root_block_attrs"),preserve_cdata:t("preserve_cdata"),inline_styles:t("inline_styles"),root_name:WT(e),sandbox_iframes:t("sandbox_iframes"),sandbox_iframes_exclusions:Rc(e),sanitize:t("xss_sanitization"),validate:!0,blob_cache:n,document:e.getDoc()})},GT=e=>{const t=e.options.get;return KT({custom_elements:t("custom_elements"),extended_valid_elements:t("extended_valid_elements"),invalid_elements:t("invalid_elements"),invalid_styles:t("invalid_styles"),schema:t("schema"),valid_children:t("valid_children"),valid_classes:t("valid_classes"),valid_elements:t("valid_elements"),valid_styles:t("valid_styles"),verify_html:t("verify_html"),padd_empty_block_inline_children:t("format_empty_lines")})},XT=e=>e.inline?e.ui.styleSheetLoader:e.dom.styleSheetLoader,ZT=e=>{const t=XT(e),n=gd(e),o=e.contentCSS,r=()=>{t.unloadAll(o),e.inline||e.ui.styleSheetLoader.unloadAll(n)},s=()=>{e.removed?r():e.on("remove",r)};if(e.contentStyles.length>0){let t="";Pt.each(e.contentStyles,(e=>{t+=e+"\r\n"})),e.dom.addStyle(t)}const a=Promise.all(((e,t,n)=>{const{pass:o,fail:r}=K(t,(e=>hugerte.Resource.has(e))),s=o.map((t=>{const n=hugerte.Resource.get(t);return m(n)?Promise.resolve(XT(e).loadRawCss(t,n)):Promise.resolve()})),a=[...s,XT(e).loadAll(r)];return e.inline?a:a.concat([e.ui.styleSheetLoader.loadAll(n)])})(e,o,n)).then(s).catch(s),i=fd(e);return i&&((e,t)=>{const n=yn(e.getBody()),o=Vn(qn(n)),r=bn("style");Qt(r,"type","text/css"),vo(r,vn(t)),vo(o,r),e.on("remove",(()=>{Eo(r)}))})(e,i),a},QT=e=>{!0!==e.removed&&((e=>{aw(e)||e.load({initial:!0,format:"html"}),e.startContent=e.getContent({format:"raw"})})(e),(e=>{e.bindPendingEventDelegates(),e.initialized=!0,(e=>{e.dispatch("Init")})(e),e.focus(!0),(e=>{const t=e.dom.getRoot();e.inline||pm(e)&&e.selection.getStart(!0)!==t||zu(t).each((t=>{const n=t.getNode(),o=sr(n)?zu(n).getOr(t):t;e.selection.setRng(o.toRange())}))})(e),e.nodeChanged({initial:!0});const t=Xd(e);w(t)&&t.call(e,e),(e=>{const t=Qd(e);t&&Rg.setEditorTimeout(e,(()=>{let n;n=!0===t?e:e.editorManager.get(t),n&&!n.destroyed&&(n.focus(),n.selection.scrollIntoView())}),100)})(e)})(e))},JT=e=>{const t=e.getElement();let n=e.getDoc();e.inline&&(VT.addClass(t,"mce-content-body"),e.contentDocument=n=document,e.contentWindow=window,e.bodyElement=t,e.contentAreaContainer=t);const o=e.getBody();o.disabled=!0,e.readonly=Ud(e),e._editableRoot=zd(e),!e.readonly&&e.hasEditableRoot()&&(e.inline&&"static"===VT.getStyle(o,"position",!0)&&(o.style.position="relative"),o.contentEditable="true"),o.disabled=!1,e.editorUpload=oE(e),e.schema=Ps(GT(e)),e.dom=la(n,{keep_values:!0,url_converter:e.convertURL,url_converter_scope:e,update_styles:!0,root_element:e.inline?e.getBody():null,collect:e.inline,schema:e.schema,contentCssCors:ad(e),referrerPolicy:id(e),onSetAttrib:t=>{e.dispatch("SetAttrib",t)},force_hex_color:kc(e)}),e.parser=(e=>{const t=kC(YT(e),e.schema);return t.addAttributeFilter("src,href,style,tabindex",((t,n)=>{const o=e.dom,r="data-mce-"+n;let s=t.length;for(;s--;){const a=t[s];let i=a.attr(n);if(i&&!a.attr(r)){if(0===i.indexOf("data:")||0===i.indexOf("blob:"))continue;"style"===n?(i=o.serializeStyle(o.parseStyle(i),a.name),i.length||(i=null),a.attr(r,i),a.attr(n,i)):"tabindex"===n?(a.attr(r,i),a.attr(n,null)):a.attr(r,e.convertURL(i,n,a.name))}}})),t.addNodeFilter("script",(e=>{let t=e.length;for(;t--;){const n=e[t],o=n.attr("type")||"no/type";0!==o.indexOf("mce-")&&n.attr("type","mce-"+o)}})),vc(e)&&t.addNodeFilter("#cdata",(t=>{var n;let o=t.length;for(;o--;){const r=t[o];r.type=8,r.name="#comment",r.value="[CDATA["+e.dom.encode(null!==(n=r.value)&&void 0!==n?n:"")+"]]"}})),t.addNodeFilter("p,h1,h2,h3,h4,h5,h6,div",(t=>{let n=t.length;const o=e.schema.getNonEmptyElements();for(;n--;){const e=t[n];e.isEmpty(o)&&0===e.getAll("br").length&&e.append(new Zg("br",1))}})),t})(e),e.serializer=bw((e=>{const t=e.options.get;return{...YT(e),...GT(e),...KT({remove_trailing_brs:t("remove_trailing_brs"),pad_empty_with_br:t("pad_empty_with_br"),url_converter:t("url_converter"),url_converter_scope:t("url_converter_scope"),element_format:t("element_format"),entities:t("entities"),entity_encoding:t("entity_encoding"),indent:t("indent"),indent_after:t("indent_after"),indent_before:t("indent_before")})}})(e),e),e.selection=gw(e.dom,e.getWin(),e.serializer,e),e.annotator=af(e),e.formatter=gE(e),e.undoManager=hE(e),e._nodeChangeDispatcher=new NR(e),e._selectionOverrides=cT(e),(e=>{const t=va(),n=ua(!1),o=Ca((t=>{e.dispatch("longpress",{...t,type:"longpress"}),n.set(!0)}),400);e.on("touchstart",(e=>{H_(e).each((r=>{o.cancel();const s={x:r.clientX,y:r.clientY,target:e.target};o.throttle(e),n.set(!1),t.set(s)}))}),!0),e.on("touchmove",(r=>{o.cancel(),H_(r).each((o=>{t.on((r=>{((e,t)=>{const n=Math.abs(e.clientX-t.x),o=Math.abs(e.clientY-t.y);return n>5||o>5})(o,r)&&(t.clear(),n.set(!1),e.dispatch("longpresscancel"))}))}))}),!0),e.on("touchend touchcancel",(r=>{o.cancel(),"touchcancel"!==r.type&&t.get().filter((e=>e.target.isEqualNode(r.target))).each((()=>{n.get()?r.preventDefault():e.dispatch("tap",{...r,type:"tap"})}))}),!0)})(e),(e=>{(e=>{e.on("click",(t=>{e.dom.getParent(t.target,"details")&&t.preventDefault()}))})(e),(e=>{e.parser.addNodeFilter("details",(t=>{const n=_c(e);V(t,(e=>{"expanded"===n?e.attr("open","open"):"collapsed"===n&&e.attr("open",null)}))})),e.serializer.addNodeFilter("details",(t=>{const n=Sc(e);V(t,(e=>{"expanded"===n?e.attr("open","open"):"collapsed"===n&&e.attr("open",null)}))}))})(e)})(e),(e=>{const t="contenteditable",n=" "+Pt.trim(hc(e))+" ",o=" "+Pt.trim(pc(e))+" ",r=G_(n),s=G_(o),a=bc(e);a.length>0&&e.on("BeforeSetContent",(t=>{((e,t,n)=>{let o=t.length,r=n.content;if("raw"!==n.format){for(;o--;)r=r.replace(t[o],X_(e,r,pc(e)));n.content=r}})(e,a,t)})),e.parser.addAttributeFilter("class",(e=>{let n=e.length;for(;n--;){const o=e[n];r(o)?o.attr(t,"true"):s(o)&&o.attr(t,"false")}})),e.serializer.addAttributeFilter(t,(e=>{let n=e.length;for(;n--;){const o=e[n];if(!r(o)&&!s(o))continue;const i=o.attr("data-mce-content");a.length>0&&i?Z_(a,i)?(o.name="#text",o.type=3,o.raw=!0,o.value=i):o.remove():o.attr(t,null)}}))})(e),aw(e)||((e=>{e.on("mousedown",(t=>{t.detail>=3&&(t.preventDefault(),IA(e))}))})(e),(e=>{$T(e)})(e));const r=kR(e);j_(e,r),(e=>{e.on("NodeChange",T(K_,e))})(e),(e=>{var t;const n=e.dom,o=ql(e),r=null!==(t=yd(e))&&void 0!==t?t:"",s=(t,a)=>{if((e=>{if(yE(e)){const t=e.keyCode;return!CE(e)&&(bf.metaKeyPressed(e)||e.altKey||t>=112&&t<=123||H(bE,t))}return!1})(t))return;const i=e.getBody(),l=!(e=>yE(e)&&!(CE(e)||"keyup"===e.type&&229===e.keyCode))(t)&&((e,t,n)=>{if(e.isEmpty(t,void 0,{skipBogus:!1,includeZwsp:!0})){const o=t.firstElementChild;return!o||!e.getStyle(t.firstElementChild,"padding-left")&&!e.getStyle(t.firstElementChild,"padding-right")&&n===o.nodeName.toLowerCase()}return!1})(n,i,o);(""!==n.getAttrib(i,vE)!==l||a)&&(n.setAttrib(i,vE,l?r:null),((e,t)=>{e.dispatch("PlaceholderToggle",{state:t})})(e,l),e.on(l?"keydown":"keyup",s),e.off(l?"keyup":"keydown",s))};Ge(r)&&e.on("init",(t=>{s(t,!0),e.on("change SetContent ExecCommand",s),e.on("paste",(t=>Rg.setEditorTimeout(e,(()=>s(t)))))}))})(e),TA(e);const s=(e=>{const t=e;return(e=>xe(e.plugins,"rtc").bind((e=>I.from(e.setup))))(e).fold((()=>(t.rtcInstance=sw(e),I.none())),(e=>(t.rtcInstance=(()=>{const e=N(null),t=N("");return{init:{bindEvents:_},undoManager:{beforeChange:_,add:e,undo:e,redo:e,clear:_,reset:_,hasUndo:L,hasRedo:L,transact:e,ignore:_,extra:_},formatter:{match:L,matchAll:N([]),matchNode:N(void 0),canApply:L,closest:t,apply:_,remove:_,toggle:_,formatChanged:N({unbind:_})},editor:{getContent:t,setContent:N({content:"",html:""}),insertContent:N(""),addVisual:_},selection:{getContent:t},autocompleter:{addDecoration:_,removeDecoration:_},raw:{getModel:N(I.none())}}})(),I.some((()=>e().then((e=>(t.rtcInstance=(e=>{const t=e=>f(e)?e:{},{init:n,undoManager:o,formatter:r,editor:s,selection:a,autocompleter:i,raw:l}=e;return{init:{bindEvents:n.bindEvents},undoManager:{beforeChange:o.beforeChange,add:o.add,undo:o.undo,redo:o.redo,clear:o.clear,reset:o.reset,hasUndo:o.hasUndo,hasRedo:o.hasRedo,transact:(e,t,n)=>o.transact(n),ignore:(e,t)=>o.ignore(t),extra:(e,t,n,r)=>o.extra(n,r)},formatter:{match:(e,n,o,s)=>r.match(e,t(n),s),matchAll:r.matchAll,matchNode:r.matchNode,canApply:e=>r.canApply(e),closest:e=>r.closest(e),apply:(e,n,o)=>r.apply(e,t(n)),remove:(e,n,o,s)=>r.remove(e,t(n)),toggle:(e,n,o)=>r.toggle(e,t(n)),formatChanged:(e,t,n,o,s)=>r.formatChanged(t,n,o,s)},editor:{getContent:e=>s.getContent(e),setContent:(e,t)=>({content:s.setContent(e,t),html:""}),insertContent:(e,t)=>(s.insertContent(e),""),addVisual:s.addVisual},selection:{getContent:(e,t)=>a.getContent(t)},autocompleter:{addDecoration:i.addDecoration,removeDecoration:i.removeDecoration},raw:{getModel:()=>I.some(l.getRawModel())}}})(e),e.rtc.isRemote))))))))})(e);(e=>{const t=e.getDoc(),n=e.getBody();(e=>{e.dispatch("PreInit")})(e),Jd(e)||(t.body.spellcheck=!1,VT.setAttrib(n,"spellcheck","false")),e.quirks=qT(e),(e=>{e.dispatch("PostRender")})(e);const o=pd(e);void 0!==o&&(n.dir=o);const r=ec(e);r&&e.on("BeforeSetContent",(e=>{Pt.each(r,(t=>{e.content=e.content.replace(t,(e=>"\x3c!--mce:protected "+escape(e)+"--\x3e"))}))})),e.on("SetContent",(()=>{e.addVisual(e.getBody())})),e.on("compositionstart compositionend",(t=>{e.composing="compositionstart"===t.type}))})(e),s.fold((()=>{const t=(e=>{let t=!1;const n=setTimeout((()=>{t||e.setProgressState(!0)}),500);return()=>{clearTimeout(n),t=!0,e.setProgressState(!1)}})(e);ZT(e).then((()=>{QT(e),t()}))}),(t=>{e.setProgressState(!0),ZT(e).then((()=>{t().then((t=>{e.setProgressState(!1),QT(e),dw(e)}),(t=>{e.notificationManager.open({type:"error",text:String(t)}),QT(e),dw(e)}))}))}))},eO=M,tO=la.DOM,nO=la.DOM,oO=(e,t)=>({editorContainer:e,iframeContainer:t,api:{}}),rO=e=>{const t=e.getElement();return e.inline?oO(null):(e=>{const t=nO.create("div");return nO.insertAfter(t,e),oO(t,t)})(t)},sO=async e=>{e.dispatch("ScriptsLoaded"),(e=>{const t=Pt.trim(ed(e)),n=e.ui.registry.getAll().icons,o={...Bw.get("default").icons,...Bw.get(t).icons};pe(o,((t,o)=>{_e(n,o)||e.ui.registry.addIcon(o,t)}))})(e),(e=>{const t=Ed(e);if(m(t)){const n=Hw.get(t);e.theme=n(e,Hw.urls[t])||{},w(e.theme.init)&&e.theme.init(e,Hw.urls[t]||e.documentBaseUrl.replace(/\/$/,""))}else e.theme={}})(e),(e=>{const t=_d(e),n=Pw.get(t);e.model=n(e,Pw.urls[t])})(e),(e=>{const t=[];V(Hd(e),(n=>{((e,t,n)=>{const o=jw.get(n),r=jw.urls[n]||e.documentBaseUrl.replace(/\/$/,"");if(n=Pt.trim(n),o&&-1===Pt.inArray(t,n)){if(e.plugins[n])return;try{const s=o(e,r)||{};e.plugins[n]=s,w(s.init)&&(s.init(e,r),t.push(n))}catch(t){((e,t,n)=>{const o=pa.translate(["Failed to initialize plugin: {0}",t]);cf(e,"PluginLoadError",{message:o}),Yw(o,n),Vw(e,o)})(e,n,t)}}})(e,t,(e=>e.replace(/^\-/,""))(n))}))})(e);const t=await(e=>{const t=e.getElement();return e.orgDisplay=t.style.display,m(Ed(e))?(e=>{const t=e.theme.renderUI;return t?t():rO(e)})(e):w(Ed(e))?(e=>{const t=e.getElement(),n=Ed(e)(e,t);return n.editorContainer.nodeType&&(n.editorContainer.id=n.editorContainer.id||e.id+"_parent"),n.iframeContainer&&n.iframeContainer.nodeType&&(n.iframeContainer.id=n.iframeContainer.id||e.id+"_iframecontainer"),n.height=n.iframeHeight?n.iframeHeight:t.offsetHeight,n})(e):rO(e)})(e);((e,t)=>{const n={show:I.from(t.show).getOr(_),hide:I.from(t.hide).getOr(_),isEnabled:I.from(t.isEnabled).getOr(M),setEnabled:n=>{e.mode.isReadOnly()||I.from(t.setEnabled).each((e=>e(n)))}};e.ui={...e.ui,...n}})(e,I.from(t.api).getOr({})),e.editorContainer=t.editorContainer,(e=>{e.contentCSS=e.contentCSS.concat((e=>Xw(e,md(e)))(e),(e=>Xw(e,gd(e)))(e))})(e),e.inline?JT(e):((e,t)=>{((e,t)=>{const n=e.translate("Rich Text Area"),o=tn(yn(e.getElement()),"tabindex").bind(Ze),r=((e,t,n,o)=>{const r=bn("iframe");return o.each((e=>Qt(r,"tabindex",e))),Jt(r,n),Jt(r,{id:e+"_ifr",frameBorder:"0",allowTransparency:"true",title:t}),un(r,"tox-edit-area__iframe"),r})(e.id,n,Il(e),o).dom;r.onload=()=>{r.onload=null,e.dispatch("load")},e.contentAreaContainer=t.iframeContainer,e.iframeElement=r,e.iframeHTML=(e=>{let t=Fl(e)+"<html><head>";Ul(e)!==e.documentBaseUrl&&(t+='<base href="'+e.documentBaseURI.getURI()+'" />'),t+='<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />';const n=zl(e),o=jl(e),r=e.translate(Yd(e));return Hl(e)&&(t+='<meta http-equiv="Content-Security-Policy" content="'+Hl(e)+'" />'),t+=`</head><body id="${n}" class="mce-content-body ${o}" data-id="${e.id}" aria-label="${r}"><br></body></html>`,t})(e),tO.add(t.iframeContainer,r)})(e,t),t.editorContainer&&(t.editorContainer.style.display=e.orgDisplay,e.hidden=tO.isHidden(t.editorContainer)),e.getElement().style.display="none",tO.setAttrib(e.id,"aria-hidden","true"),e.getElement().style.visibility=e.orgVisibility,(e=>{const t=e.iframeElement,n=()=>{e.contentDocument=t.contentDocument,JT(e)};if(wc(e)||Tt.browser.isFirefox()){const t=e.getDoc();t.open(),t.write(e.iframeHTML),t.close(),n()}else{const r=(o=yn(t),No(o,"load",eO,(()=>{r.unbind(),n()})));t.srcdoc=e.iframeHTML}var o})(e)})(e,{editorContainer:t.editorContainer,iframeContainer:t.iframeContainer})},aO=la.DOM,iO=e=>"-"===e.charAt(0),lO=(e,t,n)=>I.from(t).filter((e=>Ge(e)&&!Bw.has(e))).map((t=>({url:`${e.editorManager.baseURL}/icons/${t}/icons${n}.js`,name:I.some(t)}))),dO=(e,t)=>{const n=ca.ScriptLoader,o=()=>{!e.removed&&(e=>{const t=Ed(e);return!m(t)||C(Hw.get(t))})(e)&&(e=>{const t=_d(e);return C(Pw.get(t))})(e)&&sO(e)};((e,t)=>{const n=Ed(e);if(m(n)&&!iO(n)&&!_e(Hw.urls,n)){const o=xd(e),r=o?e.documentBaseURI.toAbsolute(o):`themes/${n}/theme${t}.js`;Hw.load(n,r).catch((()=>{((e,t,n)=>{Ww(e,"ThemeLoadError",Kw("theme",t,n))})(e,r,n)}))}})(e,t),((e,t)=>{const n=_d(e);if("plugin"!==n&&!_e(Pw.urls,n)){const o=Sd(e),r=m(o)?e.documentBaseURI.toAbsolute(o):`models/${n}/model${t}.js`;Pw.load(n,r).catch((()=>{((e,t,n)=>{Ww(e,"ModelLoadError",Kw("model",t,n))})(e,r,n)}))}})(e,t),((e,t)=>{const n=ld(t),o=dd(t);if(!pa.hasCode(n)&&"en"!==n){const r=Ge(o)?o:`${t.editorManager.baseURL}/langs/${n}.js`;e.add(r).catch((()=>{((e,t,n)=>{Ww(e,"LanguageLoadError",Kw("language",t,n))})(t,r,n)}))}})(n,e),((e,t,n)=>{const o=lO(t,"default",n),r=(e=>I.from(td(e)).filter(Ge).map((e=>({url:e,name:I.none()}))))(t).orThunk((()=>lO(t,ed(t),"")));V((e=>{const t=[],n=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(n);return t})([o,r]),(n=>{e.add(n.url).catch((()=>{((e,t,n)=>{Ww(e,"IconsLoadError",Kw("icons",t,n))})(t,n.url,n.name.getOrUndefined())}))}))})(n,e,t),((e,t)=>{const n=(t,n)=>{jw.load(t,n).catch((()=>{((e,t,n)=>{Ww(e,"PluginLoadError",Kw("plugin",t,n))})(e,n,t)}))};pe($d(e),((t,o)=>{n(o,t),e.options.set("plugins",Hd(e).concat(o))})),V(Hd(e),(e=>{!(e=Pt.trim(e))||jw.urls[e]||iO(e)||n(e,`plugins/${e}/plugin${t}.js`)}))})(e,t),n.loadQueue().then(o,o)},cO=xt().deviceType,uO=cO.isPhone(),mO=cO.isTablet(),fO=e=>{if(y(e))return[];{const t=p(e)?e:e.split(/[ ,]/),n=q(t,We);return Y(n,Ge)}},gO=(e,t)=>{const n=((t,n)=>{const o={},r={};return ye(t,((t,n)=>H(e,n)),ve(o),ve(r)),{t:o,f:r}})(t);return o=n.t,r=n.f,{sections:N(o),options:N(r)};var o,r},pO=(e,t)=>_e(e.sections(),t),hO=(e,t)=>({table_grid:!1,object_resizing:!1,resize:!1,toolbar_mode:xe(e,"toolbar_mode").getOr("scrolling"),toolbar_sticky:!1,...t?{menubar:!1}:{}}),bO=(e,t)=>{var n;const o=null!==(n=t.external_plugins)&&void 0!==n?n:{};return e&&e.external_plugins?Pt.extend({},e.external_plugins,o):o},vO=(e,t,n,o,r)=>{var s;const a=e?{mobile:hO(null!==(s=r.mobile)&&void 0!==s?s:{},t)}:{},i=gO(["mobile"],dk(a,r)),l=Pt.extend(n,o,i.options(),((e,t)=>e&&pO(t,"mobile"))(e,i)?((e,t,n={})=>{const o=e.sections(),r=xe(o,t).getOr({});return Pt.extend({},n,r)})(i,"mobile"):{},{external_plugins:bO(o,i.options())});return((e,t,n,o)=>{const r=fO(n.forced_plugins),s=fO(o.plugins),a=((e,t)=>pO(e,t)?e.sections()[t]:{})(t,"mobile"),i=((e,t,n,o)=>e&&pO(t,"mobile")?o:n)(e,t,s,a.plugins?fO(a.plugins):s),l=((e,t)=>[...fO(e),...fO(t)])(r,i);return Pt.extend(o,{forced_plugins:r,plugins:l})})(e,i,o,l)},yO=e=>{(e=>{const t=t=>()=>{V("left,center,right,justify".split(","),(n=>{t!==n&&e.formatter.remove("align"+n)})),"none"!==t&&((t,n)=>{e.formatter.toggle(t,void 0),e.nodeChanged()})("align"+t)};e.editorCommands.addCommands({JustifyLeft:t("left"),JustifyCenter:t("center"),JustifyRight:t("right"),JustifyFull:t("justify"),JustifyNone:t("none")})})(e),(e=>{const t=t=>()=>{const n=e.selection,o=n.isCollapsed()?[e.dom.getParent(n.getNode(),e.dom.isBlock)]:n.getSelectedBlocks();return $(o,(n=>C(e.formatter.matchNode(n,t))))};e.editorCommands.addCommands({JustifyLeft:t("alignleft"),JustifyCenter:t("aligncenter"),JustifyRight:t("alignright"),JustifyFull:t("alignjustify")},"state")})(e)},CO=(e,t)=>{const n=e.selection,o=e.dom;return/^ | $/.test(t)?((e,t,n,o)=>{const r=yn(e.getRoot());return n=sh(r,Zi.fromRangeStart(t),o)?n.replace(/^ /,"&nbsp;"):n.replace(/^&nbsp;/," "),ah(r,Zi.fromRangeEnd(t),o)?n.replace(/(&nbsp;| )(<br( \/)>)?$/,"&nbsp;"):n.replace(/&nbsp;(<br( \/)?>)?$/," ")})(o,n.getRng(),t,e.schema):t},wO=(e,t)=>{if(e.selection.isEditable()){const{content:n,details:o}=(e=>{if("string"!=typeof e){const t=Pt.extend({paste:e.paste,data:{paste:e.paste}},e);return{content:e.content,details:t}}return{content:e,details:{}}})(t);OC(e,{...o,content:CO(e,n),format:"html",set:!1,selection:!0}).each((t=>{const n=((e,t,n)=>iw(e).editor.insertContent(t,n))(e,t.content,o);BC(e,n,t),e.addVisual()}))}},EO={"font-size":"size","font-family":"face"},xO=Xt("font"),_O=e=>(t,n)=>I.from(n).map(yn).filter(Wt).bind((n=>((e,t,n)=>Fb(yn(n),(t=>(t=>mo(t,e).orThunk((()=>xO(t)?xe(EO,e).bind((e=>tn(t,e))):I.none())))(t)),(e=>_n(yn(t),e))))(e,t,n.dom).or(((e,t)=>I.from(la.DOM.getStyle(t,e,!0)))(e,n.dom)))).getOr(""),SO=_O("font-size"),kO=S((e=>e.replace(/[\'\"\\]/g,"").replace(/,\s+/g,",")),_O("font-family")),NO=e=>zu(e.getBody()).bind((e=>{const t=e.container();return I.from(lr(t)?t.parentNode:t)})),RO=(e,t)=>((e,t)=>(e=>I.from(e.selection.getRng()).bind((t=>{const n=e.getBody();return t.startContainer===n&&0===t.startOffset?I.none():I.from(e.selection.getStart(!0))})))(e).orThunk(T(NO,e)).map(yn).filter(Wt).bind(t))(e,k(I.some,t)),AO=(e,t)=>{if(/^[0-9.]+$/.test(t)){const n=parseInt(t,10);if(n>=1&&n<=7){const o=(e=>Pt.explode(e.options.get("font_size_style_values")))(e),r=(e=>Pt.explode(e.options.get("font_size_classes")))(e);return r.length>0?r[n-1]||t:o[n-1]||t}return t}return t},TO=e=>{const t=e.split(/\s*,\s*/);return q(t,(e=>-1===e.indexOf(" ")||$e(e,'"')||$e(e,"'")?e:`'${e}'`)).join(",")},OO=(e,t)=>{const n=e.dom,o=e.selection.getRng(),r=t?e.selection.getStart():e.selection.getEnd(),s=t?o.startContainer:o.endContainer,a=NN(n,s);if(!a||!a.isContentEditable)return;const i=t?po:ho,l=ql(e);((e,t,n,o)=>{const r=e.dom,s=e=>r.isBlock(e)&&e.parentElement===n,a=s(t)?t:r.getParent(o,s,n);return I.from(a).map(yn)})(e,r,a,s).each((t=>{const n=ON(e,s,t.dom,a,!1,l);i(t,yn(n)),e.selection.setCursorLocation(n,0),e.dispatch("NewBlock",{newBlock:n}),CN(e,"insertParagraph")}))},BO=e=>{yO(e),(e=>{e.editorCommands.addCommands({"Cut,Copy,Paste":t=>{const n=e.getDoc();let o;try{n.execCommand(t)}catch(e){o=!0}if("paste"!==t||n.queryCommandEnabled(t)||(o=!0),o||!n.queryCommandSupported(t)){let t=e.translate("Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.");(Tt.os.isMacOS()||Tt.os.isiOS())&&(t=t.replace(/Ctrl\+/g,"\u2318+")),e.notificationManager.open({text:t,type:"error"})}}})})(e),(e=>{e.editorCommands.addCommands({mceAddUndoLevel:()=>{e.undoManager.add()},mceEndUndoLevel:()=>{e.undoManager.add()},Undo:()=>{e.undoManager.undo()},Redo:()=>{e.undoManager.redo()}})})(e),(e=>{e.editorCommands.addCommands({mceSelectNodeDepth:(t,n,o)=>{let r=0;e.dom.getParent(e.selection.getNode(),(t=>!Jo(t)||r++!==o||(e.selection.select(t),!1)),e.getBody())},mceSelectNode:(t,n,o)=>{e.selection.select(o)},selectAll:()=>{const t=e.dom.getParent(e.selection.getStart(),hr);if(t){const n=e.dom.createRng();n.selectNodeContents(t),e.selection.setRng(n)}}})})(e),(e=>{e.editorCommands.addCommands({mceCleanup:()=>{const t=e.selection.getBookmark();e.setContent(e.getContent()),e.selection.moveToBookmark(t)},insertImage:(t,n,o)=>{wO(e,e.dom.createHTML("img",{src:o}))},insertHorizontalRule:()=>{e.execCommand("mceInsertContent",!1,"<hr>")},insertText:(t,n,o)=>{wO(e,e.dom.encode(o))},insertHTML:(t,n,o)=>{wO(e,o)},mceInsertContent:(t,n,o)=>{wO(e,o)},mceSetContent:(t,n,o)=>{e.setContent(o)},mceReplaceContent:(t,n,o)=>{e.execCommand("mceInsertContent",!1,o.replace(/\{\$selection\}/g,e.selection.getContent({format:"text"})))},mceNewDocument:()=>{e.setContent(sc(e))}})})(e),(e=>{const t=(t,n,o)=>{const r=m(o)?{href:o}:o,s=e.dom.getParent(e.selection.getNode(),"a");f(r)&&m(r.href)&&(r.href=r.href.replace(/ /g,"%20"),s&&r.href||e.formatter.remove("link"),r.href&&e.formatter.apply("link",r,s))};e.editorCommands.addCommands({unlink:()=>{if(e.selection.isEditable()){if(e.selection.isCollapsed()){const t=e.dom.getParent(e.selection.getStart(),"a");return void(t&&e.dom.remove(t,!0))}e.formatter.remove("link")}},mceInsertLink:t,createLink:t})})(e),(e=>{e.editorCommands.addCommands({Indent:()=>{(e=>{I_(e,"indent")})(e)},Outdent:()=>{F_(e)}}),e.editorCommands.addCommands({Outdent:()=>D_(e)},"state")})(e),(e=>{e.editorCommands.addCommands({InsertNewBlockBefore:()=>{(e=>{OO(e,!0)})(e)},InsertNewBlockAfter:()=>{(e=>{OO(e,!1)})(e)}})})(e),(e=>{e.editorCommands.addCommands({insertParagraph:()=>{cR(HN,e)},mceInsertNewLine:(t,n,o)=>{uR(e,o)},InsertLineBreak:(t,n,o)=>{cR(XN,e)}})})(e),(e=>{(e=>{const t=(t,n)=>{e.formatter.toggle(t,n),e.nodeChanged()};e.editorCommands.addCommands({"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":e=>{t(e)},"ForeColor,HiliteColor":(e,n,o)=>{t(e,{value:o})},BackColor:(e,n,o)=>{t("hilitecolor",{value:o})},FontName:(t,n,o)=>{((e,t)=>{const n=AO(e,t);e.formatter.toggle("fontname",{value:TO(n)}),e.nodeChanged()})(e,o)},FontSize:(t,n,o)=>{((e,t)=>{e.formatter.toggle("fontsize",{value:AO(e,t)}),e.nodeChanged()})(e,o)},LineHeight:(t,n,o)=>{((e,t)=>{e.formatter.toggle("lineheight",{value:String(t)}),e.nodeChanged()})(e,o)},Lang:(e,n,o)=>{var r;t(e,{value:o.code,customValue:null!==(r=o.customCode)&&void 0!==r?r:null})},RemoveFormat:t=>{e.formatter.remove(t)},mceBlockQuote:()=>{t("blockquote")},FormatBlock:(e,n,o)=>{t(m(o)?o:"p")},mceToggleFormat:(e,n,o)=>{t(o)}})})(e),(e=>{const t=t=>e.formatter.match(t);e.editorCommands.addCommands({"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":e=>t(e),mceBlockQuote:()=>t("blockquote")},"state"),e.editorCommands.addQueryValueHandler("FontName",(()=>(e=>RO(e,(t=>kO(e.getBody(),t.dom))).getOr(""))(e))),e.editorCommands.addQueryValueHandler("FontSize",(()=>(e=>RO(e,(t=>SO(e.getBody(),t.dom))).getOr(""))(e))),e.editorCommands.addQueryValueHandler("LineHeight",(()=>(e=>RO(e,(t=>{const n=yn(e.getBody()),o=Fb(t,(e=>mo(e,"line-height")),T(_n,n));return o.getOrThunk((()=>{const e=parseFloat(co(t,"line-height")),n=parseFloat(co(t,"font-size"));return String(e/n)}))})).getOr(""))(e)))})(e)})(e),(e=>{e.editorCommands.addCommands({mceRemoveNode:(t,n,o)=>{const r=null!=o?o:e.selection.getNode();if(r!==e.getBody()){const t=e.selection.getBookmark();e.dom.remove(r,!0),e.selection.moveToBookmark(t)}},mcePrint:()=>{e.getWin().print()},mceFocus:(t,n,o)=>{((e,t)=>{e.removed||(t?zg(e):(e=>{const t=e.selection,n=e.getBody();let o=t.getRng();e.quirks.refreshContentEditable(),C(e.bookmark)&&!Fg(e)&&kg(e).each((t=>{e.selection.setRng(t),o=t}));const r=((e,t)=>e.dom.getParent(t,(t=>"true"===e.dom.getContentEditable(t))))(e,t.getNode());if(r&&e.dom.isChildOf(r,n))return Ig(r),Mg(e,o),void zg(e);e.inline||(Tt.browser.isOpera()||Ig(n),e.getWin().focus()),(Tt.browser.isFirefox()||e.inline)&&(Ig(n),Mg(e,o)),zg(e)})(e))})(e,!0===o)},mceToggleVisualAid:()=>{e.hasVisual=!e.hasVisual,e.addVisual()}})})(e)},PO=["toggleview"],DO=e=>H(PO,e.toLowerCase());class LO{constructor(e){this.commands={state:{},exec:{},value:{}},this.editor=e}execCommand(e,t=!1,n,o){const r=this.editor,s=e.toLowerCase(),a=null==o?void 0:o.skip_focus;if(r.removed)return!1;if("mcefocus"!==s&&(/^(mceAddUndoLevel|mceEndUndoLevel)$/i.test(s)||a?(e=>{kg(e).each((t=>e.selection.setRng(t)))})(r):r.focus()),r.dispatch("BeforeExecCommand",{command:e,ui:t,value:n}).isDefaultPrevented())return!1;const i=this.commands.exec[s];return!!w(i)&&(i(s,t,n),r.dispatch("ExecCommand",{command:e,ui:t,value:n}),!0)}queryCommandState(e){if(!DO(e)&&this.editor.quirks.isHidden()||this.editor.removed)return!1;const t=e.toLowerCase(),n=this.commands.state[t];return!!w(n)&&n(t)}queryCommandValue(e){if(!DO(e)&&this.editor.quirks.isHidden()||this.editor.removed)return"";const t=e.toLowerCase(),n=this.commands.value[t];return w(n)?n(t):""}addCommands(e,t="exec"){const n=this.commands;pe(e,((e,o)=>{V(o.toLowerCase().split(","),(o=>{n[t][o]=e}))}))}addCommand(e,t,n){const o=e.toLowerCase();this.commands.exec[o]=(e,o,r)=>t.call(null!=n?n:this.editor,o,r)}queryCommandSupported(e){const t=e.toLowerCase();return!!this.commands.exec[t]}addQueryStateHandler(e,t,n){this.commands.state[e.toLowerCase()]=()=>t.call(null!=n?n:this.editor)}addQueryValueHandler(e,t,n){this.commands.value[e.toLowerCase()]=()=>t.call(null!=n?n:this.editor)}}const MO="data-mce-contenteditable",IO=(e,t,n)=>{try{e.getDoc().execCommand(t,!1,String(n))}catch(e){}},FO=(e,t)=>{e.dom.contentEditable=t?"true":"false"},UO=e=>e.readonly,zO=e=>{e.parser.addAttributeFilter("contenteditable",(t=>{UO(e)&&V(t,(e=>{e.attr(MO,e.attr("contenteditable")),e.attr("contenteditable","false")}))})),e.serializer.addAttributeFilter(MO,(t=>{UO(e)&&V(t,(e=>{e.attr("contenteditable",e.attr(MO))}))})),e.serializer.addTempAttr(MO)},jO=["copy"],HO=Pt.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input beforeinput contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchmove touchend touchcancel"," ");class $O{static isNative(e){return!!HO[e.toLowerCase()]}constructor(e){this.bindings={},this.settings=e||{},this.scope=this.settings.scope||this,this.toggleEvent=this.settings.toggleEvent||L}fire(e,t){return this.dispatch(e,t)}dispatch(e,t){const n=e.toLowerCase(),o=Ws(n,null!=t?t:{},this.scope);this.settings.beforeFire&&this.settings.beforeFire(o);const r=this.bindings[n];if(r)for(let e=0,t=r.length;e<t;e++){const t=r[e];if(!t.removed){if(t.once&&this.off(n,t.func),o.isImmediatePropagationStopped())return o;if(!1===t.func.call(this.scope,o))return o.preventDefault(),o}}return o}on(e,t,n,o){if(!1===t&&(t=L),t){const r={func:t,removed:!1};o&&Pt.extend(r,o);const s=e.toLowerCase().split(" ");let a=s.length;for(;a--;){const e=s[a];let t=this.bindings[e];t||(t=[],this.toggleEvent(e,!0)),t=n?[r,...t]:[...t,r],this.bindings[e]=t}}return this}off(e,t){if(e){const n=e.toLowerCase().split(" ");let o=n.length;for(;o--;){const r=n[o];let s=this.bindings[r];if(!r)return pe(this.bindings,((e,t)=>{this.toggleEvent(t,!1),delete this.bindings[t]})),this;if(s){if(t){const e=K(s,(e=>e.func===t));s=e.fail,this.bindings[r]=s,V(e.pass,(e=>{e.removed=!0}))}else s.length=0;s.length||(this.toggleEvent(e,!1),delete this.bindings[r])}}}else pe(this.bindings,((e,t)=>{this.toggleEvent(t,!1)})),this.bindings={};return this}once(e,t,n){return this.on(e,t,n,{once:!0})}has(e){e=e.toLowerCase();const t=this.bindings[e];return!(!t||0===t.length)}}const qO=e=>(e._eventDispatcher||(e._eventDispatcher=new $O({scope:e,toggleEvent:(t,n)=>{$O.isNative(t)&&e.toggleNativeEvent&&e.toggleNativeEvent(t,n)}})),e._eventDispatcher),VO={fire(e,t,n){return this.dispatch(e,t,n)},dispatch(e,t,n){const o=this;if(o.removed&&"remove"!==e&&"detach"!==e)return Ws(e.toLowerCase(),null!=t?t:{},o);const r=qO(o).dispatch(e,t);if(!1!==n&&o.parent){let t=o.parent();for(;t&&!r.isPropagationStopped();)t.dispatch(e,r,!1),t=t.parent?t.parent():void 0}return r},on(e,t,n){return qO(this).on(e,t,n)},off(e,t){return qO(this).off(e,t)},once(e,t){return qO(this).once(e,t)},hasEventListeners(e){return qO(this).has(e)}},WO=la.DOM;let KO;const YO=(e,t)=>{if("selectionchange"===t)return e.getDoc();if(!e.inline&&/^(?:mouse|touch|click|contextmenu|drop|dragover|dragend)/.test(t))return e.getDoc().documentElement;const n=Cd(e);return n?(e.eventRoot||(e.eventRoot=WO.select(n)[0]),e.eventRoot):e.getBody()},GO=(e,t,n)=>{(e=>!e.hidden&&!UO(e))(e)?e.dispatch(t,n):UO(e)&&((e,t)=>{if((e=>"click"===e.type)(t)&&!bf.metaKeyPressed(t)){const n=yn(t.target);((e,t)=>no(t,"a",(t=>_n(t,yn(e.getBody())))).bind((e=>tn(e,"href"))))(e,n).each((n=>{if(t.preventDefault(),/^#/.test(n)){const t=e.dom.select(`${n},[name="${je(n,"#")}"]`);t.length&&e.selection.scrollIntoView(t[0],!0)}else window.open(n,"_blank","rel=noopener noreferrer,menubar=yes,toolbar=yes,location=yes,status=yes,resizable=yes,scrollbars=yes")}))}else(e=>H(jO,e.type))(t)&&e.dispatch(t.type,t)})(e,n)},XO=(e,t)=>{if(e.delegates||(e.delegates={}),e.delegates[t]||e.removed)return;const n=YO(e,t);if(Cd(e)){if(KO||(KO={},e.editorManager.on("removeEditor",(()=>{e.editorManager.activeEditor||KO&&(pe(KO,((t,n)=>{e.dom.unbind(YO(e,n))})),KO=null)}))),KO[t])return;const o=n=>{const o=n.target,r=e.editorManager.get();let s=r.length;for(;s--;){const e=r[s].getBody();(e===o||WO.isChildOf(o,e))&&GO(r[s],t,n)}};KO[t]=o,WO.bind(n,t,o)}else{const o=n=>{GO(e,t,n)};WO.bind(n,t,o),e.delegates[t]=o}},ZO={...VO,bindPendingEventDelegates(){const e=this;Pt.each(e._pendingNativeEvents,(t=>{XO(e,t)}))},toggleNativeEvent(e,t){const n=this;"focus"!==e&&"blur"!==e&&(n.removed||(t?n.initialized?XO(n,e):n._pendingNativeEvents?n._pendingNativeEvents.push(e):n._pendingNativeEvents=[e]:n.initialized&&n.delegates&&(n.dom.unbind(YO(n,e),e,n.delegates[e]),delete n.delegates[e])))},unbindAllNativeEvents(){const e=this,t=e.getBody(),n=e.dom;e.delegates&&(pe(e.delegates,((t,n)=>{e.dom.unbind(YO(e,n),n,t)})),delete e.delegates),!e.inline&&t&&n&&(t.onload=null,n.unbind(e.getWin()),n.unbind(e.getDoc())),n&&(n.unbind(t),n.unbind(e.getContainer()))}},QO=e=>m(e)?{value:e.split(/[ ,]/),valid:!0}:x(e,m)?{value:e,valid:!0}:{valid:!1,message:"The value must be a string[] or a comma/space separated string."},JO=(e,t)=>e+(Xe(t.message)?"":`. ${t.message}`),eB=e=>e.valid,tB=(e,t,n="")=>{const o=t(e);return b(o)?o?{value:e,valid:!0}:{valid:!1,message:n}:o},nB=["design","readonly"],oB=(e,t,n,o)=>{const r=n[t.get()],s=n[o];try{s.activate()}catch(e){return void console.error(`problem while activating editor mode ${o}:`,e)}r.deactivate(),r.editorReadOnly!==s.editorReadOnly&&((e,t)=>{const n=yn(e.getBody());((e,t,n)=>{gn(e,t)&&!n?fn(e,t):n&&un(e,t)})(n,"mce-content-readonly",t),t?(e.selection.controlSelection.hideResizeRect(),e._selectionOverrides.hideFakeCaret(),(e=>{I.from(e.selection.getNode()).each((e=>{e.removeAttribute("data-mce-selected")}))})(e),e.readonly=!0,FO(n,!1),V(Uo(n,'*[contenteditable="true"]'),(e=>{Qt(e,MO,"true"),FO(e,!1)}))):(e.readonly=!1,e.hasEditableRoot()&&FO(n,!0),V(Uo(n,`*[${MO}="true"]`),(e=>{on(e,MO),FO(e,!0)})),IO(e,"StyleWithCSS",!1),IO(e,"enableInlineTableEditing",!1),IO(e,"enableObjectResizing",!1),Ug(e)&&e.focus(),(e=>{e.selection.setRng(e.selection.getRng())})(e),e.nodeChanged())})(e,s.editorReadOnly),t.set(o),((e,t)=>{e.dispatch("SwitchMode",{mode:t})})(e,o)},rB=Pt.each,sB=Pt.explode,aB={f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123},iB=Pt.makeMap("alt,ctrl,shift,meta,access"),lB=e=>{const t={},n=Tt.os.isMacOS()||Tt.os.isiOS();rB(sB(e.toLowerCase(),"+"),(e=>{(e=>e in iB)(e)?t[e]=!0:/^[0-9]{2,}$/.test(e)?t.keyCode=parseInt(e,10):(t.charCode=e.charCodeAt(0),t.keyCode=aB[e]||e.toUpperCase().charCodeAt(0))}));const o=[t.keyCode];let r;for(r in iB)t[r]?o.push(r):t[r]=!1;return t.id=o.join(","),t.access&&(t.alt=!0,n?t.ctrl=!0:t.shift=!0),t.meta&&(n?t.meta=!0:(t.ctrl=!0,t.meta=!1)),t};class dB{constructor(e){this.shortcuts={},this.pendingPatterns=[],this.editor=e;const t=this;e.on("keyup keypress keydown",(e=>{!t.hasModifier(e)&&!t.isFunctionKey(e)||e.isDefaultPrevented()||(rB(t.shortcuts,(n=>{t.matchShortcut(e,n)&&(t.pendingPatterns=n.subpatterns.slice(0),"keydown"===e.type&&t.executeShortcutAction(n))})),t.matchShortcut(e,t.pendingPatterns[0])&&(1===t.pendingPatterns.length&&"keydown"===e.type&&t.executeShortcutAction(t.pendingPatterns[0]),t.pendingPatterns.shift()))}))}add(e,t,n,o){const r=this,s=r.normalizeCommandFunc(n);return rB(sB(Pt.trim(e)),(e=>{const n=r.createShortcut(e,t,s,o);r.shortcuts[n.id]=n})),!0}remove(e){const t=this.createShortcut(e);return!!this.shortcuts[t.id]&&(delete this.shortcuts[t.id],!0)}normalizeCommandFunc(e){const t=this,n=e;return"string"==typeof n?()=>{t.editor.execCommand(n,!1,null)}:Pt.isArray(n)?()=>{t.editor.execCommand(n[0],n[1],n[2])}:n}createShortcut(e,t,n,o){const r=Pt.map(sB(e,">"),lB);return r[r.length-1]=Pt.extend(r[r.length-1],{func:n,scope:o||this.editor}),Pt.extend(r[0],{desc:this.editor.translate(t),subpatterns:r.slice(1)})}hasModifier(e){return e.altKey||e.ctrlKey||e.metaKey}isFunctionKey(e){return"keydown"===e.type&&e.keyCode>=112&&e.keyCode<=123}matchShortcut(e,t){return!!t&&t.ctrl===e.ctrlKey&&t.meta===e.metaKey&&t.alt===e.altKey&&t.shift===e.shiftKey&&!!(e.keyCode===t.keyCode||e.charCode&&e.charCode===t.charCode)&&(e.preventDefault(),!0)}executeShortcutAction(e){return e.func?e.func.call(e.scope):null}}const cB=()=>{const e=(()=>{const e={},t={},n={},o={},r={},s={},a={},i={},l=(e,t)=>(n,o)=>{e[n.toLowerCase()]={...o,type:t}};return{addButton:l(e,"button"),addGroupToolbarButton:l(e,"grouptoolbarbutton"),addToggleButton:l(e,"togglebutton"),addMenuButton:l(e,"menubutton"),addSplitButton:l(e,"splitbutton"),addMenuItem:l(t,"menuitem"),addNestedMenuItem:l(t,"nestedmenuitem"),addToggleMenuItem:l(t,"togglemenuitem"),addAutocompleter:l(n,"autocompleter"),addContextMenu:l(r,"contextmenu"),addContextToolbar:l(s,"contexttoolbar"),addContextForm:l(s,"contextform"),addSidebar:l(a,"sidebar"),addView:l(i,"views"),addIcon:(e,t)=>o[e.toLowerCase()]=t,getAll:()=>({buttons:e,menuItems:t,icons:o,popups:n,contextMenus:r,contextToolbars:s,sidebars:a,views:i})}})();return{addAutocompleter:e.addAutocompleter,addButton:e.addButton,addContextForm:e.addContextForm,addContextMenu:e.addContextMenu,addContextToolbar:e.addContextToolbar,addIcon:e.addIcon,addMenuButton:e.addMenuButton,addMenuItem:e.addMenuItem,addNestedMenuItem:e.addNestedMenuItem,addSidebar:e.addSidebar,addSplitButton:e.addSplitButton,addToggleButton:e.addToggleButton,addGroupToolbarButton:e.addGroupToolbarButton,addToggleMenuItem:e.addToggleMenuItem,addView:e.addView,getAll:e.getAll}},uB=la.DOM,mB=Pt.extend,fB=Pt.each;class gB{constructor(e,t,n){this.plugins={},this.contentCSS=[],this.contentStyles=[],this.loadedCSS={},this.isNotDirty=!1,this.composing=!1,this.destroyed=!1,this.hasHiddenInput=!1,this.iframeElement=null,this.initialized=!1,this.readonly=!1,this.removed=!1,this.startContent="",this._pendingNativeEvents=[],this._skinLoaded=!1,this._editableRoot=!0,this.editorManager=n,this.documentBaseUrl=n.documentBaseURL,mB(this,ZO);const o=this;this.id=e,this.hidden=!1;const r=((e,t)=>vO(uO||mO,uO,t,e,t))(n.defaultOptions,t);this.options=((e,t)=>{const n={},o={},r=(e,t,n)=>{const r=tB(t,n);return eB(r)?(o[e]=r.value,!0):(console.warn(JO(`Invalid value passed for the ${e} option`,r)),!1)},s=e=>_e(n,e);return{register:(e,s)=>{const a=(e=>m(e.processor))(s)?(e=>{const t=(()=>{switch(e){case"array":return p;case"boolean":return b;case"function":return w;case"number":return E;case"object":return f;case"string":return m;case"string[]":return QO;case"object[]":return e=>x(e,f);case"regexp":return e=>u(e,RegExp);default:return M}})();return n=>tB(n,t,`The value must be a ${e}.`)})(s.processor):s.processor,i=((e,t,n)=>{if(!v(t)){const o=tB(t,n);if(eB(o))return o.value;console.error(JO(`Invalid default value passed for the "${e}" option`,o))}})(e,s.default,a);n[e]={...s,default:i,processor:a},xe(o,e).orThunk((()=>xe(t,e))).each((t=>r(e,t,a)))},isRegistered:s,get:e=>xe(o,e).orThunk((()=>xe(n,e).map((e=>e.default)))).getOrUndefined(),set:(e,t)=>{if(s(e)){const o=n[e];return o.immutable?(console.error(`"${e}" is an immutable option and cannot be updated`),!1):r(e,t,o.processor)}return console.warn(`"${e}" is not a registered option. Ensure the option has been registered before setting a value.`),!1},unset:e=>{const t=s(e);return t&&delete o[e],t},isSet:e=>_e(o,e)}})(0,r),(e=>{const t=e.options.register;t("id",{processor:"string",default:e.id}),t("selector",{processor:"string"}),t("target",{processor:"object"}),t("suffix",{processor:"string"}),t("cache_suffix",{processor:"string"}),t("base_url",{processor:"string"}),t("referrer_policy",{processor:"string",default:""}),t("language_load",{processor:"boolean",default:!0}),t("inline",{processor:"boolean",default:!1}),t("iframe_attrs",{processor:"object",default:{}}),t("doctype",{processor:"string",default:"<!DOCTYPE html>"}),t("document_base_url",{processor:"string",default:e.documentBaseUrl}),t("body_id",{processor:Ml(e,"hugerte"),default:"hugerte"}),t("body_class",{processor:Ml(e),default:""}),t("content_security_policy",{processor:"string",default:""}),t("br_in_pre",{processor:"boolean",default:!0}),t("forced_root_block",{processor:e=>{const t=m(e)&&Ge(e);return t?{value:e,valid:t}:{valid:!1,message:"Must be a non-empty string."}},default:"p"}),t("forced_root_block_attrs",{processor:"object",default:{}}),t("newline_behavior",{processor:e=>{const t=H(["block","linebreak","invert","default"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: block, linebreak, invert or default."}},default:"default"}),t("br_newline_selector",{processor:"string",default:".mce-toc h2,figcaption,caption"}),t("no_newline_selector",{processor:"string",default:""}),t("keep_styles",{processor:"boolean",default:!0}),t("end_container_on_empty_block",{processor:e=>b(e)||m(e)?{valid:!0,value:e}:{valid:!1,message:"Must be boolean or a string"},default:"blockquote"}),t("font_size_style_values",{processor:"string",default:"xx-small,x-small,small,medium,large,x-large,xx-large"}),t("font_size_legacy_values",{processor:"string",default:"xx-small,small,medium,large,x-large,xx-large,300%"}),t("font_size_classes",{processor:"string",default:""}),t("automatic_uploads",{processor:"boolean",default:!0}),t("images_reuse_filename",{processor:"boolean",default:!1}),t("images_replace_blob_uris",{processor:"boolean",default:!0}),t("icons",{processor:"string",default:""}),t("icons_url",{processor:"string",default:""}),t("images_upload_url",{processor:"string",default:""}),t("images_upload_base_path",{processor:"string",default:""}),t("images_upload_credentials",{processor:"boolean",default:!1}),t("images_upload_handler",{processor:"function"}),t("language",{processor:"string",default:"en"}),t("language_url",{processor:"string",default:""}),t("entity_encoding",{processor:"string",default:"named"}),t("indent",{processor:"boolean",default:!0}),t("indent_before",{processor:"string",default:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,details,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist"}),t("indent_after",{processor:"string",default:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,details,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist"}),t("indent_use_margin",{processor:"boolean",default:!1}),t("indentation",{processor:"string",default:"40px"}),t("content_css",{processor:e=>{const t=!1===e||m(e)||x(e,m);return t?m(e)?{value:q(e.split(","),We),valid:t}:p(e)?{value:e,valid:t}:!1===e?{value:[],valid:t}:{value:e,valid:t}:{valid:!1,message:"Must be false, a string or an array of strings."}},default:Bd(e)?[]:["default"]}),t("content_style",{processor:"string"}),t("content_css_cors",{processor:"boolean",default:!1}),t("font_css",{processor:e=>{const t=m(e)||x(e,m);return t?{value:p(e)?e:q(e.split(","),We),valid:t}:{valid:!1,message:"Must be a string or an array of strings."}},default:[]}),t("inline_boundaries",{processor:"boolean",default:!0}),t("inline_boundaries_selector",{processor:"string",default:"a[href],code,span.mce-annotation"}),t("object_resizing",{processor:e=>{const t=b(e)||m(e);return t?!1===e||Tl.isiPhone()||Tl.isiPad()?{value:"",valid:t}:{value:!0===e?"table,img,figure.image,div,video,iframe":e,valid:t}:{valid:!1,message:"Must be boolean or a string"}},default:!Ol}),t("resize_img_proportional",{processor:"boolean",default:!0}),t("event_root",{processor:"string"}),t("service_message",{processor:"string"}),t("theme",{processor:e=>!1===e||m(e)||w(e),default:"silver"}),t("theme_url",{processor:"string"}),t("formats",{processor:"object"}),t("format_empty_lines",{processor:"boolean",default:!1}),t("format_noneditable_selector",{processor:"string",default:""}),t("preview_styles",{processor:e=>{const t=!1===e||m(e);return t?{value:!1===e?"":e,valid:t}:{valid:!1,message:"Must be false or a string"}},default:"font-family font-size font-weight font-style text-decoration text-transform color background-color border border-radius outline text-shadow"}),t("custom_ui_selector",{processor:"string",default:""}),t("hidden_input",{processor:"boolean",default:!0}),t("submit_patch",{processor:"boolean",default:!0}),t("encoding",{processor:"string"}),t("add_form_submit_trigger",{processor:"boolean",default:!0}),t("add_unload_trigger",{processor:"boolean",default:!0}),t("custom_undo_redo_levels",{processor:"number",default:0}),t("disable_nodechange",{processor:"boolean",default:!1}),t("readonly",{processor:"boolean",default:!1}),t("editable_root",{processor:"boolean",default:!0}),t("plugins",{processor:"string[]",default:[]}),t("external_plugins",{processor:"object"}),t("forced_plugins",{processor:"string[]"}),t("model",{processor:"string",default:e.hasPlugin("rtc")?"plugin":"dom"}),t("model_url",{processor:"string"}),t("block_unsupported_drop",{processor:"boolean",default:!0}),t("visual",{processor:"boolean",default:!0}),t("visual_table_class",{processor:"string",default:"mce-item-table"}),t("visual_anchor_class",{processor:"string",default:"mce-item-anchor"}),t("iframe_aria_text",{processor:"string",default:"Rich Text Area. Press ALT-0 for help."}),t("setup",{processor:"function"}),t("init_instance_callback",{processor:"function"}),t("url_converter",{processor:"function",default:e.convertURL}),t("url_converter_scope",{processor:"object",default:e}),t("urlconverter_callback",{processor:"function"}),t("allow_conditional_comments",{processor:"boolean",default:!1}),t("allow_html_data_urls",{processor:"boolean",default:!1}),t("allow_svg_data_urls",{processor:"boolean"}),t("allow_html_in_named_anchor",{processor:"boolean",default:!1}),t("allow_script_urls",{processor:"boolean",default:!1}),t("allow_unsafe_link_target",{processor:"boolean",default:!1}),t("convert_fonts_to_spans",{processor:"boolean",default:!0,deprecated:!0}),t("fix_list_elements",{processor:"boolean",default:!1}),t("preserve_cdata",{processor:"boolean",default:!1}),t("remove_trailing_brs",{processor:"boolean",default:!0}),t("pad_empty_with_br",{processor:"boolean",default:!1}),t("inline_styles",{processor:"boolean",default:!0,deprecated:!0}),t("element_format",{processor:"string",default:"html"}),t("entities",{processor:"string"}),t("schema",{processor:"string",default:"html5"}),t("convert_urls",{processor:"boolean",default:!0}),t("relative_urls",{processor:"boolean",default:!0}),t("remove_script_host",{processor:"boolean",default:!0}),t("custom_elements",{processor:Ll}),t("extended_valid_elements",{processor:"string"}),t("invalid_elements",{processor:"string"}),t("invalid_styles",{processor:Ll}),t("valid_children",{processor:"string"}),t("valid_classes",{processor:Ll}),t("valid_elements",{processor:"string"}),t("valid_styles",{processor:Ll}),t("verify_html",{processor:"boolean",default:!0}),t("auto_focus",{processor:e=>m(e)||!0===e}),t("browser_spellcheck",{processor:"boolean",default:!1}),t("protect",{processor:"array"}),t("images_file_types",{processor:"string",default:"jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp"}),t("deprecation_warnings",{processor:"boolean",default:!0}),t("a11y_advanced_options",{processor:"boolean",default:!1}),t("api_key",{processor:"string"}),t("paste_block_drop",{processor:"boolean",default:!1}),t("paste_data_images",{processor:"boolean",default:!0}),t("paste_preprocess",{processor:"function"}),t("paste_postprocess",{processor:"function"}),t("paste_webkit_styles",{processor:"string",default:"none"}),t("paste_remove_styles_if_webkit",{processor:"boolean",default:!0}),t("paste_merge_formats",{processor:"boolean",default:!0}),t("smart_paste",{processor:"boolean",default:!0}),t("paste_as_text",{processor:"boolean",default:!1}),t("paste_tab_spaces",{processor:"number",default:4}),t("text_patterns",{processor:e=>x(e,f)||!1===e?{value:Al(!1===e?[]:e),valid:!0}:{valid:!1,message:"Must be an array of objects or false."},default:[{start:"*",end:"*",format:"italic"},{start:"**",end:"**",format:"bold"},{start:"#",format:"h1",trigger:"space"},{start:"##",format:"h2",trigger:"space"},{start:"###",format:"h3",trigger:"space"},{start:"####",format:"h4",trigger:"space"},{start:"#####",format:"h5",trigger:"space"},{start:"######",format:"h6",trigger:"space"},{start:"1.",cmd:"InsertOrderedList",trigger:"space"},{start:"*",cmd:"InsertUnorderedList",trigger:"space"},{start:"-",cmd:"InsertUnorderedList",trigger:"space"},{start:">",cmd:"mceBlockQuote",trigger:"space"},{start:"---",cmd:"InsertHorizontalRule",trigger:"space"}]}),t("text_patterns_lookup",{processor:e=>{return w(e)?{value:(t=e,e=>{const n=t(e);return Al(n)}),valid:!0}:{valid:!1,message:"Must be a single function"};var t},default:e=>[]}),t("noneditable_class",{processor:"string",default:"mceNonEditable"}),t("editable_class",{processor:"string",default:"mceEditable"}),t("noneditable_regexp",{processor:e=>x(e,Pl)?{value:e,valid:!0}:Pl(e)?{value:[e],valid:!0}:{valid:!1,message:"Must be a RegExp or an array of RegExp."},default:[]}),t("table_tab_navigation",{processor:"boolean",default:!0}),t("highlight_on_focus",{processor:"boolean",default:!0}),t("xss_sanitization",{processor:"boolean",default:!0}),t("details_initial_state",{processor:e=>{const t=H(["inherited","collapsed","expanded"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: inherited, collapsed, or expanded."}},default:"inherited"}),t("details_serialized_state",{processor:e=>{const t=H(["inherited","collapsed","expanded"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: inherited, collapsed, or expanded."}},default:"inherited"}),t("init_content_sync",{processor:"boolean",default:!1}),t("newdocument_content",{processor:"string",default:""}),t("force_hex_color",{processor:e=>{const t=["always","rgb_only","off"],n=H(t,e);return n?{value:e,valid:n}:{valid:!1,message:`Must be one of: ${t.join(", ")}.`}},default:"off"}),t("sandbox_iframes",{processor:"boolean",default:!0}),t("sandbox_iframes_exclusions",{processor:"string[]",default:["youtube.com","youtu.be","vimeo.com","dailymotion.com","dai.ly","codepen.io"]}),t("convert_unsafe_embeds",{processor:"boolean",default:!0}),e.on("ScriptsLoaded",(()=>{t("directionality",{processor:"string",default:pa.isRtl()?"rtl":void 0}),t("placeholder",{processor:"string",default:Bl.getAttrib(e.getElement(),"placeholder")})}))})(o);const s=this.options.get;s("deprecation_warnings")&&((e,t)=>{((e,t)=>{const n=_w(e),o=Nw(t),r=o.length>0,s=n.length>0,a="mobile"===t.theme;if(r||s||a){const e="\n- ",t=a?`\n\nThemes:${e}mobile`:"",i=r?`\n\nPlugins:${e}${o.join(e)}`:"",l=s?`\n\nOptions:${e}${n.join(e)}`:"";console.warn("The following deprecated features are currently enabled but have been removed in TinyMCE 6.0 and therefore, they are not present in HugeRTE either. These features will no longer work and should be removed from the HugeRTE configuration. See https://www.hugerte.org/docs/hugerte/1/migration-from-5x/ for more information."+t+i+l)}})(e,t),((e,t)=>{const n=Sw(e),o=Rw(t),r=o.length>0,s=n.length>0;if(r||s){const e="\n- ",t=r?`\n\nPlugins:${e}${o.map(Aw).join(e)}`:"",a=s?`\n\nOptions:${e}${n.join(e)}`:"";console.warn("The following deprecated features are currently enabled but will be removed soon."+t+a)}})(e,t)})(t,r);const a=s("suffix");a&&(n.suffix=a),this.suffix=n.suffix;const i=s("base_url");i&&n._setBaseUrl(i),this.baseUri=n.baseURI;const l=id(o);l&&(ca.ScriptLoader._setReferrerPolicy(l),la.DOM.styleSheetLoader._setReferrerPolicy(l));const d=jd(o);C(d)&&la.DOM.styleSheetLoader._setContentCssCors(d),ha.languageLoad=s("language_load"),ha.baseURL=n.baseURL,this.setDirty(!1),this.documentBaseURI=new mC(Ul(o),{base_uri:this.baseUri}),this.baseURI=this.baseUri,this.inline=Bd(o),this.hasVisual=Vd(o),this.shortcuts=new dB(this),this.editorCommands=new LO(this),BO(this);const c=s("cache_suffix");c&&(Tt.cacheSuffix=c.replace(/^[\?\&]+/,"")),this.ui={registry:cB(),styleSheetLoader:void 0,show:_,hide:_,setEnabled:_,isEnabled:M},this.mode=(e=>{const t=ua("design"),n=ua({design:{activate:_,deactivate:_,editorReadOnly:!1},readonly:{activate:_,deactivate:_,editorReadOnly:!0}});return(e=>{e.serializer?zO(e):e.on("PreInit",(()=>{zO(e)}))})(e),(e=>{e.on("ShowCaret",(t=>{UO(e)&&t.preventDefault()})),e.on("ObjectSelected",(t=>{UO(e)&&t.preventDefault()}))})(e),{isReadOnly:()=>UO(e),set:o=>((e,t,n,o)=>{if(o!==n.get()){if(!_e(t,o))throw new Error(`Editor mode '${o}' is invalid`);e.initialized?oB(e,n,t,o):e.on("init",(()=>oB(e,n,t,o)))}})(e,n.get(),t,o),get:()=>t.get(),register:(e,t)=>{n.set(((e,t,n)=>{if(H(nB,t))throw new Error(`Cannot override default mode ${t}`);return{...e,[t]:{...n,deactivate:()=>{try{n.deactivate()}catch(e){console.error(`problem while deactivating editor mode ${t}:`,e)}}}}})(n.get(),e,t))}}})(o),n.dispatch("SetupEditor",{editor:this});const g=Gd(o);w(g)&&g.call(o,o)}render(){(e=>{const t=e.id;pa.setCode(ld(e));const n=()=>{aO.unbind(window,"ready",n),e.render()};if(!Qs.Event.domLoaded)return void aO.bind(window,"ready",n);if(!e.getElement())return;const o=yn(e.getElement()),r=rn(o);e.on("remove",(()=>{W(o.dom.attributes,(e=>on(o,e.name))),Jt(o,r)})),e.ui.styleSheetLoader=((e,t)=>Jr.forElement(e,{contentCssCors:jd(t),referrerPolicy:id(t)}))(o,e),Bd(e)?e.inline=!0:(e.orgVisibility=e.getElement().style.visibility,e.getElement().style.visibility="hidden");const s=e.getElement().form||aO.getParent(t,"form");s&&(e.formElement=s,Pd(e)&&!ir(e.getElement())&&(aO.insertAfter(aO.create("input",{type:"hidden",name:t}),t),e.hasHiddenInput=!0),e.formEventDelegate=t=>{e.dispatch(t.type,t)},aO.bind(s,"submit reset",e.formEventDelegate),e.on("reset",(()=>{e.resetContent()})),!Dd(e)||s.submit.nodeType||s.submit.length||s._mceOldSubmit||(s._mceOldSubmit=s.submit,s.submit=()=>(e.editorManager.triggerSave(),e.setDirty(!1),s._mceOldSubmit(s)))),e.windowManager=$w(e),e.notificationManager=zw(e),(e=>"xml"===e.options.get("encoding"))(e)&&e.on("GetContent",(e=>{e.save&&(e.content=aO.encode(e.content))})),Ld(e)&&e.on("submit",(()=>{e.initialized&&e.save()})),Md(e)&&(e._beforeUnload=()=>{!e.initialized||e.destroyed||e.isHidden()||e.save({format:"raw",no_events:!0,set_dirty:!1})},e.editorManager.on("BeforeUnload",e._beforeUnload)),e.editorManager.add(e),dO(e,e.suffix)})(this)}focus(e){this.execCommand("mceFocus",!1,e)}hasFocus(){return Fg(this)}translate(e){return pa.translate(e)}getParam(e,t,n){const o=this.options;return o.isRegistered(e)||(C(n)?o.register(e,{processor:n,default:t}):o.register(e,{processor:M,default:t})),o.isSet(e)||v(t)?o.get(e):t}hasPlugin(e,t){return!(!H(Hd(this),e)||t&&void 0===jw.get(e))}nodeChanged(e){this._nodeChangeDispatcher.nodeChanged(e)}addCommand(e,t,n){this.editorCommands.addCommand(e,t,n)}addQueryStateHandler(e,t,n){this.editorCommands.addQueryStateHandler(e,t,n)}addQueryValueHandler(e,t,n){this.editorCommands.addQueryValueHandler(e,t,n)}addShortcut(e,t,n,o){this.shortcuts.add(e,t,n,o)}execCommand(e,t,n,o){return this.editorCommands.execCommand(e,t,n,o)}queryCommandState(e){return this.editorCommands.queryCommandState(e)}queryCommandValue(e){return this.editorCommands.queryCommandValue(e)}queryCommandSupported(e){return this.editorCommands.queryCommandSupported(e)}show(){const e=this;e.hidden&&(e.hidden=!1,e.inline?e.getBody().contentEditable="true":(uB.show(e.getContainer()),uB.hide(e.id)),e.load(),e.dispatch("show"))}hide(){const e=this;e.hidden||(e.save(),e.inline?(e.getBody().contentEditable="false",e===e.editorManager.focusedEditor&&(e.editorManager.focusedEditor=null)):(uB.hide(e.getContainer()),uB.setStyle(e.id,"display",e.orgDisplay)),e.hidden=!0,e.dispatch("hide"))}isHidden(){return this.hidden}setProgressState(e,t){this.dispatch("ProgressState",{state:e,time:t})}load(e={}){const t=this,n=t.getElement();if(t.removed)return"";if(n){const o={...e,load:!0},r=ir(n)?n.value:n.innerHTML,s=t.setContent(r,o);return o.no_events||t.dispatch("LoadContent",{...o,element:n}),s}return""}save(e={}){const t=this;let n=t.getElement();if(!n||!t.initialized||t.removed)return"";const o={...e,save:!0,element:n};let r=t.getContent(o);const s={...o,content:r};if(s.no_events||t.dispatch("SaveContent",s),"raw"===s.format&&t.dispatch("RawSaveContent",s),r=s.content,ir(n))n.value=r;else{!e.is_removing&&t.inline||(n.innerHTML=r);const o=uB.getParent(t.id,"form");o&&fB(o.elements,(e=>e.name!==t.id||(e.value=r,!1)))}return s.element=o.element=n=null,!1!==s.set_dirty&&t.setDirty(!1),r}setContent(e,t){return vw(this,e,t)}getContent(e){return((e,t={})=>{const n=((e,t)=>({...e,format:t,get:!0,getInner:!0}))(t,t.format?t.format:"html");return AC(e,n).fold(R,(t=>{const n=((e,t)=>iw(e).editor.getContent(t))(e,t);return TC(e,n,t)}))})(this,e)}insertContent(e,t){t&&(e=mB({content:e},t)),this.execCommand("mceInsertContent",!1,e)}resetContent(e){void 0===e?vw(this,this.startContent,{format:"raw"}):vw(this,e),this.undoManager.reset(),this.setDirty(!1),this.nodeChanged()}isDirty(){return!this.isNotDirty}setDirty(e){const t=!this.isNotDirty;this.isNotDirty=!e,e&&e!==t&&this.dispatch("dirty")}getContainer(){const e=this;return e.container||(e.container=e.editorContainer||uB.get(e.id+"_parent")),e.container}getContentAreaContainer(){return this.contentAreaContainer}getElement(){return this.targetElm||(this.targetElm=uB.get(this.id)),this.targetElm}getWin(){const e=this;if(!e.contentWindow){const t=e.iframeElement;t&&(e.contentWindow=t.contentWindow)}return e.contentWindow}getDoc(){const e=this;if(!e.contentDocument){const t=e.getWin();t&&(e.contentDocument=t.document)}return e.contentDocument}getBody(){var e,t;const n=this.getDoc();return null!==(t=null!==(e=this.bodyElement)&&void 0!==e?e:null==n?void 0:n.body)&&void 0!==t?t:null}convertURL(e,t,n){const o=this,r=o.options.get,s=Zd(o);if(w(s))return s.call(o,e,n,!0,t);if(!r("convert_urls")||"link"===n||f(n)&&"LINK"===n.nodeName||0===e.indexOf("file:")||0===e.length)return e;const a=new mC(e);return"http"!==a.protocol&&"https"!==a.protocol&&""!==a.protocol?e:r("relative_urls")?o.documentBaseURI.toRelative(e):e=o.documentBaseURI.toAbsolute(e,r("remove_script_host"))}addVisual(e){((e,t)=>{((e,t)=>{lw(e).editor.addVisual(t)})(e,t)})(this,e)}setEditableRoot(e){((e,t)=>{e._editableRoot!==t&&(e._editableRoot=t,e.readonly||(e.getBody().contentEditable=String(e.hasEditableRoot()),e.nodeChanged()),((e,t)=>{e.dispatch("EditableRootStateChange",{state:t})})(e,t))})(this,e)}hasEditableRoot(){return this._editableRoot}remove(){(e=>{if(!e.removed){const{_selectionOverrides:t,editorUpload:n}=e,o=e.getBody(),r=e.getElement();o&&e.save({is_removing:!0}),e.removed=!0,e.unbindAllNativeEvents(),e.hasHiddenInput&&C(null==r?void 0:r.nextSibling)&&Tw.remove(r.nextSibling),(e=>{e.dispatch("remove")})(e),e.editorManager.remove(e),!e.inline&&o&&(e=>{Tw.setStyle(e.id,"display",e.orgDisplay)})(e),(e=>{e.dispatch("detach")})(e),Tw.remove(e.getContainer()),Ow(t),Ow(n),e.destroy()}})(this)}destroy(e){((e,t)=>{const{selection:n,dom:o}=e;e.destroyed||(t||e.removed?(t||(e.editorManager.off("beforeunload",e._beforeUnload),e.theme&&e.theme.destroy&&e.theme.destroy(),Ow(n),Ow(o)),(e=>{const t=e.formElement;t&&(t._mceOldSubmit&&(t.submit=t._mceOldSubmit,delete t._mceOldSubmit),Tw.unbind(t,"submit reset",e.formEventDelegate))})(e),(e=>{const t=e;t.contentAreaContainer=t.formElement=t.container=t.editorContainer=null,t.bodyElement=t.contentDocument=t.contentWindow=null,t.iframeElement=t.targetElm=null;const n=e.selection;if(n){const e=n.dom;t.selection=n.win=n.dom=e.doc=null}})(e),e.destroyed=!0):e.remove())})(this,e)}uploadImages(){return this.editorUpload.uploadImages()}_scanForImages(){return this.editorUpload.scanForImages()}}const pB=la.DOM,hB=Pt.each;let bB,vB=!1,yB=[];const CB=e=>{const t=e.type;hB(_B.get(),(n=>{switch(t){case"scroll":n.dispatch("ScrollWindow",e);break;case"resize":n.dispatch("ResizeWindow",e)}}))},wB=e=>{if(e!==vB){const t=la.DOM;e?(t.bind(window,"resize",CB),t.bind(window,"scroll",CB)):(t.unbind(window,"resize",CB),t.unbind(window,"scroll",CB)),vB=e}},EB=e=>{const t=yB;return yB=Y(yB,(t=>e!==t)),_B.activeEditor===e&&(_B.activeEditor=yB.length>0?yB[0]:null),_B.focusedEditor===e&&(_B.focusedEditor=null),t.length!==yB.length},xB="CSS1Compat"!==document.compatMode,_B={...VO,baseURI:null,baseURL:null,defaultOptions:{},documentBaseURL:null,suffix:null,majorVersion:"1",minorVersion:"0.9",releaseDate:"2025-03-15",i18n:pa,activeEditor:null,focusedEditor:null,setup(){const e=this;let t="",n="",o=mC.getDocumentBaseUrl(document.location);/^[^:]+:\/\/\/?[^\/]+\//.test(o)&&(o=o.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(o)||(o+="/"));const r=window.hugerte||window.hugeRTEPreInit;if(r)t=r.base||r.baseURL,n=r.suffix;else{const e=document.getElementsByTagName("script");for(let o=0;o<e.length;o++){const r=e[o].src||"";if(""===r)continue;const s=r.substring(r.lastIndexOf("/"));if(/hugerte(\.full|\.jquery|)(\.min|\.dev|)\.js/.test(r)){-1!==s.indexOf(".min")&&(n=".min"),t=r.substring(0,r.lastIndexOf("/"));break}}if(!t&&document.currentScript){const e=document.currentScript.src;-1!==e.indexOf(".min")&&(n=".min"),t=e.substring(0,e.lastIndexOf("/"))}}var s;e.baseURL=new mC(o).toAbsolute(t),e.documentBaseURL=o,e.baseURI=new mC(e.baseURL),e.suffix=n,(s=e).on("AddEditor",T(Dg,s)),s.on("RemoveEditor",T(Lg,s))},overrideDefaults(e){const t=e.base_url;t&&this._setBaseUrl(t);const n=e.suffix;n&&(this.suffix=n),this.defaultOptions=e;const o=e.plugin_base_urls;void 0!==o&&pe(o,((e,t)=>{ha.PluginManager.urls[t]=e}))},init(e){const t=this;let n;const o=Pt.makeMap("area base basefont br col frame hr img input isindex link meta param embed source wbr track colgroup option table tbody tfoot thead tr th td script noscript style textarea video audio iframe object menu"," ");let r=e=>{n=e};const s=()=>{let n=0;const a=[];let i;pB.unbind(window,"ready",s),(n=>{const o=e.onpageload;o&&o.apply(t,[])})(),i=me((e=>Tt.browser.isIE()||Tt.browser.isEdge()?(Yw("HugeRTE does not support the browser you are using. For a list of supported browsers please see: https://www.hugerte.org/docs/hugerte/1/support/#supportedwebbrowsers"),[]):xB?(Yw("Failed to initialize the editor as the document is not in standards mode. HugeRTE requires standards mode."),[]):m(e.selector)?pB.select(e.selector):C(e.target)?[e.target]:[])(e)),Pt.each(i,(e=>{var n;(n=t.get(e.id))&&n.initialized&&!(n.getContainer()||n.getBody()).parentNode&&(EB(n),n.unbindAllNativeEvents(),n.destroy(!0),n.removed=!0)})),i=Pt.grep(i,(e=>!t.get(e.id))),0===i.length?r([]):hB(i,(s=>{((e,t)=>e.inline&&t.tagName.toLowerCase()in o)(e,s)?Yw("Could not initialize inline editor on invalid inline target element",s):((e,o,s)=>{const l=new gB(e,o,t);a.push(l),l.on("init",(()=>{++n===i.length&&r(a)})),l.targetElm=l.targetElm||s,l.render()})((e=>{let t=e.id;return t||(t=xe(e,"name").filter((e=>!pB.get(e))).getOrThunk(pB.uniqueId),e.setAttribute("id",t)),t})(s),e,s)}))};return pB.bind(window,"ready",s),new Promise((e=>{n?e(n):r=t=>{e(t)}}))},get(e){return 0===arguments.length?yB.slice(0):m(e)?Q(yB,(t=>t.id===e)).getOr(null):E(e)&&yB[e]?yB[e]:null},add(e){const t=this,n=t.get(e.id);return n===e||(null===n&&yB.push(e),wB(!0),t.activeEditor=e,t.dispatch("AddEditor",{editor:e}),bB||(bB=e=>{const n=t.dispatch("BeforeUnload");if(n.returnValue)return e.preventDefault(),e.returnValue=n.returnValue,n.returnValue},window.addEventListener("beforeunload",bB))),e},createEditor(e,t){return this.add(new gB(e,t,this))},remove(e){const t=this;let n;if(e){if(!m(e))return n=e,h(t.get(n.id))?null:(EB(n)&&t.dispatch("RemoveEditor",{editor:n}),0===yB.length&&window.removeEventListener("beforeunload",bB),n.remove(),wB(yB.length>0),n);hB(pB.select(e),(e=>{n=t.get(e.id),n&&t.remove(n)}))}else for(let e=yB.length-1;e>=0;e--)t.remove(yB[e])},execCommand(e,t,n){var o;const r=this,s=f(n)?null!==(o=n.id)&&void 0!==o?o:n.index:n;switch(e){case"mceAddEditor":if(!r.get(s)){const e=n.options;new gB(s,e,r).render()}return!0;case"mceRemoveEditor":{const e=r.get(s);return e&&e.remove(),!0}case"mceToggleEditor":{const e=r.get(s);return e?(e.isHidden()?e.show():e.hide(),!0):(r.execCommand("mceAddEditor",!1,n),!0)}}return!!r.activeEditor&&r.activeEditor.execCommand(e,t,n)},triggerSave:()=>{hB(yB,(e=>{e.save()}))},addI18n:(e,t)=>{pa.add(e,t)},translate:e=>pa.translate(e),setActive(e){const t=this.activeEditor;this.activeEditor!==e&&(t&&t.dispatch("deactivate",{relatedTarget:e}),e.dispatch("activate",{relatedTarget:t})),this.activeEditor=e},_setBaseUrl(e){this.baseURL=new mC(this.documentBaseURL).toAbsolute(e.replace(/\/+$/,"")),this.baseURI=new mC(this.baseURL)}};_B.setup();const SB=(()=>{const e=va();return{FakeClipboardItem:e=>({items:e,types:fe(e),getType:t=>xe(e,t).getOrUndefined()}),write:t=>{e.set(t)},read:()=>e.get().getOrUndefined(),clear:e.clear}})(),kB=Math.min,NB=Math.max,RB=Math.round,AB=(e,t,n)=>{let o=t.x,r=t.y;const s=e.w,a=e.h,i=t.w,l=t.h,d=(n||"").split("");return"b"===d[0]&&(r+=l),"r"===d[1]&&(o+=i),"c"===d[0]&&(r+=RB(l/2)),"c"===d[1]&&(o+=RB(i/2)),"b"===d[3]&&(r-=a),"r"===d[4]&&(o-=s),"c"===d[3]&&(r-=RB(a/2)),"c"===d[4]&&(o-=RB(s/2)),TB(o,r,s,a)},TB=(e,t,n,o)=>({x:e,y:t,w:n,h:o}),OB={inflate:(e,t,n)=>TB(e.x-t,e.y-n,e.w+2*t,e.h+2*n),relativePosition:AB,findBestRelativePosition:(e,t,n,o)=>{for(let r=0;r<o.length;r++){const s=AB(e,t,o[r]);if(s.x>=n.x&&s.x+s.w<=n.w+n.x&&s.y>=n.y&&s.y+s.h<=n.h+n.y)return o[r]}return null},intersect:(e,t)=>{const n=NB(e.x,t.x),o=NB(e.y,t.y),r=kB(e.x+e.w,t.x+t.w),s=kB(e.y+e.h,t.y+t.h);return r-n<0||s-o<0?null:TB(n,o,r-n,s-o)},clamp:(e,t,n)=>{let o=e.x,r=e.y,s=e.x+e.w,a=e.y+e.h;const i=t.x+t.w,l=t.y+t.h,d=NB(0,t.x-o),c=NB(0,t.y-r),u=NB(0,s-i),m=NB(0,a-l);return o+=d,r+=c,n&&(s+=d,a+=c,o-=u,r-=m),s-=u,a-=m,TB(o,r,s-o,a-r)},create:TB,fromClientRect:e=>TB(e.left,e.top,e.width,e.height)},BB=(()=>{const e={},t={},n={};return{load:(n,o)=>{const r=`Script at URL "${o}" failed to load`,s=`Script at URL "${o}" did not call \`hugerte.Resource.add('${n}', data)\` within 1 second`;if(void 0!==e[n])return e[n];{const a=new Promise(((e,a)=>{const i=((e,t,n=1e3)=>{let o=!1,r=null;const s=e=>(...t)=>{o||(o=!0,null!==r&&(clearTimeout(r),r=null),e.apply(null,t))},a=s(e),i=s(t);return{start:(...e)=>{o||null!==r||(r=setTimeout((()=>i.apply(null,e)),n))},resolve:a,reject:i}})(e,a);t[n]=i.resolve,ca.ScriptLoader.loadScript(o).then((()=>i.start(s)),(()=>i.reject(r)))}));return e[n]=a,a}},add:(o,r)=>{void 0!==t[o]&&(t[o](r),delete t[o]),e[o]=Promise.resolve(r),n[o]=r},has:e=>e in n,get:e=>n[e],unload:t=>{delete e[t],delete n[t]}}})();let PB;try{const e="__storage_test__";PB=window.localStorage,PB.setItem(e,e),PB.removeItem(e)}catch(e){PB=(()=>{let e={},t=[];const n={getItem:t=>e[t]||null,setItem:(n,o)=>{t.push(n),e[n]=String(o)},key:e=>t[e],removeItem:n=>{t=t.filter((e=>e===n)),delete e[n]},clear:()=>{t=[],e={}},length:0};return Object.defineProperty(n,"length",{get:()=>t.length,configurable:!1,enumerable:!1}),n})()}const DB={geom:{Rect:OB},util:{Delay:Rg,Tools:Pt,VK:bf,URI:mC,EventDispatcher:$O,Observable:VO,I18n:pa,LocalStorage:PB,ImageUploader:e=>{const t=Qw(),n=nE(e,t);return{upload:(t,o=!0)=>n.upload(t,o?tE(e):void 0)}}},dom:{EventUtils:Qs,TreeWalker:$o,TextSeeker:Fa,DOMUtils:la,ScriptLoader:ca,RangeUtils:Gf,Serializer:bw,StyleSheetLoader:Qr,ControlSelection:Ef,BookmarkManager:lf,Selection:gw,Event:Qs.Event},html:{Styles:$s,Entities:hs,Node:Zg,Schema:Ps,DomParser:kC,Writer:pp,Serializer:hp},Env:Tt,AddOnManager:ha,Annotator:af,Formatter:gE,UndoManager:hE,EditorCommands:LO,WindowManager:$w,NotificationManager:zw,EditorObservable:ZO,Shortcuts:dB,Editor:gB,FocusManager:Ng,EditorManager:_B,DOM:la.DOM,ScriptLoader:ca.ScriptLoader,PluginManager:jw,ThemeManager:Hw,ModelManager:Pw,IconManager:Bw,Resource:BB,FakeClipboard:SB,trim:Pt.trim,isArray:Pt.isArray,is:Pt.is,toArray:Pt.toArray,makeMap:Pt.makeMap,each:Pt.each,map:Pt.map,grep:Pt.grep,inArray:Pt.inArray,extend:Pt.extend,walk:Pt.walk,resolve:Pt.resolve,explode:Pt.explode,_addCacheSuffix:Pt._addCacheSuffix},LB=Pt.extend(_B,DB);(e=>{window.hugerte=e,window.hugeRTE=e})(LB),(e=>{if("object"==typeof module)try{module.exports=e}catch(e){}})(LB)}();