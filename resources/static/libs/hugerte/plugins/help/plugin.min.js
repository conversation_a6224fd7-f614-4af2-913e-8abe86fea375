/**
 * HugeRTE version 1.0.9 (2025-03-15)
 * Copyright (c) 2022 Ephox Corporation DBA Tiny Technologies, Inc.
 * Copyright (c) 2024 HugeRTE contributors
 * Licensed under the MIT license (https://github.com/hugerte/hugerte/blob/main/LICENSE.TXT)
 */
!function(){"use strict";var e=hugerte.util.Tools.resolve("hugerte.PluginManager");let t=0;const n=e=>{const n=(new Date).getTime(),a=Math.floor(1e9*Math.random());return t++,e+"_"+a+t+String(n)},a=e=>t=>t.options.get(e),r=a("help_tabs"),o=a("forced_plugins"),s=("string",e=>"string"===(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(n=a=e,(r=String).prototype.isPrototypeOf(n)||(null===(o=a.constructor)||void 0===o?void 0:o.name)===r.name)?"string":t;var n,a,r,o})(e));const i=(void 0,e=>undefined===e);const l=e=>"function"==typeof e,c=(!1,()=>false);class u{constructor(e,t){this.tag=e,this.value=t}static some(e){return new u(!0,e)}static none(){return u.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?u.some(e(this.value)):u.none()}bind(e){return this.tag?e(this.value):u.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:u.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return null==e?u.none():u.some(e)}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}u.singletonNone=new u(!1);const h=Array.prototype.slice,m=Array.prototype.indexOf,g=(e,t)=>{const n=e.length,a=new Array(n);for(let r=0;r<n;r++){const n=e[r];a[r]=t(n,r)}return a},p=Object.keys,d=Object.hasOwnProperty,y=(e,t)=>d.call(e,t);var v=hugerte.util.Tools.resolve("hugerte.Resource"),k=hugerte.util.Tools.resolve("hugerte.util.I18n");const f=(e,t)=>v.load(`hugerte.html-i18n.help-keynav.${t}`,`${e}/js/i18n/keynav/${t}.js`),b=e=>f(e,k.getCode()).catch((()=>f(e,"en")));var A=hugerte.util.Tools.resolve("hugerte.Env");const S=e=>{const t=A.os.isMacOS()||A.os.isiOS(),n=t?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl ",access:"Shift + Alt "},a=e.split("+"),r=g(a,(e=>{const t=e.toLowerCase().trim();return y(n,t)?n[t]:e}));return t?r.join("").replace(/\s/,""):r.join("+")},M=[{shortcuts:["Meta + B"],action:"Bold"},{shortcuts:["Meta + I"],action:"Italic"},{shortcuts:["Meta + U"],action:"Underline"},{shortcuts:["Meta + A"],action:"Select all"},{shortcuts:["Meta + Y","Meta + Shift + Z"],action:"Redo"},{shortcuts:["Meta + Z"],action:"Undo"},{shortcuts:["Access + 1"],action:"Heading 1"},{shortcuts:["Access + 2"],action:"Heading 2"},{shortcuts:["Access + 3"],action:"Heading 3"},{shortcuts:["Access + 4"],action:"Heading 4"},{shortcuts:["Access + 5"],action:"Heading 5"},{shortcuts:["Access + 6"],action:"Heading 6"},{shortcuts:["Access + 7"],action:"Paragraph"},{shortcuts:["Access + 8"],action:"Div"},{shortcuts:["Access + 9"],action:"Address"},{shortcuts:["Alt + 0"],action:"Open help dialog"},{shortcuts:["Alt + F9"],action:"Focus to menubar"},{shortcuts:["Alt + F10"],action:"Focus to toolbar"},{shortcuts:["Alt + F11"],action:"Focus to element path"},{shortcuts:["Ctrl + F9"],action:"Focus to contextual toolbar"},{shortcuts:["Shift + Enter"],action:"Open popup menu for split buttons"},{shortcuts:["Meta + K"],action:"Insert link (if link plugin activated)"},{shortcuts:["Meta + S"],action:"Save (if save plugin activated)"},{shortcuts:["Meta + F"],action:"Find (if searchreplace plugin activated)"},{shortcuts:["Meta + Shift + F"],action:"Switch to or from fullscreen mode"}],w=()=>({name:"shortcuts",title:"Handy Shortcuts",items:[{type:"table",header:["Action","Shortcut"],cells:g(M,(e=>{const t=g(e.shortcuts,S).join(" or ");return[e.action,t]}))}]}),O=g([{key:"accordion",name:"Accordion"},{key:"advlist",name:"Advanced List"},{key:"anchor",name:"Anchor"},{key:"autolink",name:"Autolink"},{key:"autoresize",name:"Autoresize"},{key:"autosave",name:"Autosave"},{key:"charmap",name:"Character Map"},{key:"code",name:"Code"},{key:"codesample",name:"Code Sample"},{key:"colorpicker",name:"Color Picker"},{key:"directionality",name:"Directionality"},{key:"emoticons",name:"Emoticons"},{key:"fullscreen",name:"Full Screen"},{key:"help",name:"Help"},{key:"image",name:"Image"},{key:"importcss",name:"Import CSS"},{key:"insertdatetime",name:"Insert Date/Time"},{key:"link",name:"Link"},{key:"lists",name:"Lists"},{key:"media",name:"Media"},{key:"nonbreaking",name:"Nonbreaking"},{key:"pagebreak",name:"Page Break"},{key:"preview",name:"Preview"},{key:"quickbars",name:"Quick Toolbars"},{key:"save",name:"Save"},{key:"searchreplace",name:"Search and Replace"},{key:"table",name:"Table"},{key:"template",name:"Template"},{key:"textcolor",name:"Text Color"},{key:"visualblocks",name:"Visual Blocks"},{key:"visualchars",name:"Visual Characters"},{key:"wordcount",name:"Word Count"}],(e=>({...e,type:e.type||"opensource",slug:e.slug||e.key}))),x=e=>{const t=e=>`<a data-alloy-tabstop="true" tabindex="-1" href="${e.url}" target="_blank" rel="noopener">${e.name}</a>`,n=(e,n)=>{return(a=O,r=e=>e.key===n,((e,t,n)=>{for(let a=0,r=e.length;a<r;a++){const r=e[a];if(t(r,a))return u.some(r);if(n(r,a))break}return u.none()})(a,r,c)).fold((()=>((e,n)=>{const a=e.plugins[n].getMetadata;if(l(a)){const e=a();return{name:e.name,html:t(e)}}return{name:n,html:n}})(e,n)),(e=>{const n=t({name:e.name,url:`https://www.hugerte.org/docs/hugerte/1/${e.slug}/`});return{name:e.name,html:n}}));var a,r},a=e=>{const t=(e=>{const t=p(e.plugins),n=o(e);return i(n)?t:((e,t)=>{const a=[];for(let t=0,r=e.length;t<r;t++){const r=e[t];!((e,t)=>((e,t)=>m.call(e,t))(e,t)>-1)(n,r)&&a.push(r)}return a})(t)})(e),a=((e,t)=>{const n=h.call(e,0);return n.sort(((e,t)=>e.name.localeCompare(t.name))),n})(g(t,(t=>n(e,t)))),r=g(a,(e=>"<li>"+e.html+"</li>")),s=r.length,l=r.join("");return"<p><b>"+k.translate(["Plugins installed ({0}):",s])+"</b></p><ul>"+l+"</ul>"},r={type:"htmlpanel",presets:"document",html:[(e=>null==e?"":"<div>"+a(e)+"</div>")(e)].join("")};return{name:"plugins",title:"Plugins",items:[r]}};var C=hugerte.util.Tools.resolve("hugerte.EditorManager");const T=(e,t,a)=>()=>{(async(e,t,a)=>{const o=w(),i=await(async e=>({name:"keyboardnav",title:"Keyboard Navigation",items:[{type:"htmlpanel",presets:"document",html:await b(e)}]}))(a),l=x(e),c=(()=>{var e,t;const n='<a data-alloy-tabstop="true" tabindex="-1" href="https://hugerte.org/docs/hugerte/1/changelog/?utm_campaign=help_dialog_version_tab&utm_source=editor&utm_medium=referral" rel="noopener" target="_blank">HugeRTE '+(e=C.majorVersion,t=C.minorVersion,(0===e.indexOf("@")?"X.X.X":e+"."+t)+"</a>");return{name:"versions",title:"Version",items:[{type:"htmlpanel",html:"<p>"+k.translate(["You are using {0}",n])+"</p>",presets:"document"}]}})(),h={[o.name]:o,[i.name]:i,[l.name]:l,[c.name]:c,...t.get()};return u.from(r(e)).fold((()=>(e=>{const t=p(e),n=t.indexOf("versions");return-1!==n&&(t.splice(n,1),t.push("versions")),{tabs:e,names:t}})(h)),(e=>((e,t)=>{const a={},r=g(e,(e=>{var r;if(s(e))return y(t,e)&&(a[e]=t[e]),e;{const t=null!==(r=e.name)&&void 0!==r?r:n("tab-name");return a[t]=e,t}}));return{tabs:a,names:r}})(e,h)))})(e,t,a).then((({tabs:t,names:n})=>{const a={type:"tabpanel",tabs:(e=>{const t=[],n=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(n);return t})(g(n,(e=>{return y(n=t,a=e)?u.from(n[a]):u.none();var n,a})))};e.windowManager.open({title:"Help",size:"medium",body:a,buttons:[{type:"cancel",name:"close",text:"Close",primary:!0}],initialData:{}})}))};e.add("help",((e,t)=>{const a=(e=>{let t={};return{get:()=>t,set:e=>{t=e}}})(),r=(e=>({addTab:t=>{var a;const r=null!==(a=t.name)&&void 0!==a?a:n("tab-name"),o=e.get();o[r]=t,e.set(o)}}))(a);(e=>{(0,e.options.register)("help_tabs",{processor:"array"})})(e);const o=T(e,a,t);return((e,t)=>{e.ui.registry.addButton("help",{icon:"help",tooltip:"Help",onAction:t}),e.ui.registry.addMenuItem("help",{text:"Help",icon:"help",shortcut:"Alt+0",onAction:t})})(e,o),((e,t)=>{e.addCommand("mceHelp",t)})(e,o),e.shortcuts.add("Alt+0","Open help dialog","mceHelp"),((e,t)=>{e.on("init",(()=>{b(t)}))})(e,t),r}))}();