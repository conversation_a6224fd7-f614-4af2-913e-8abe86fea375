/**
 * HugeRTE version 1.0.9 (2025-03-15)
 * Copyright (c) 2022 Ephox Corporation DBA Tiny Technologies, Inc.
 * Copyright (c) 2024 HugeRTE contributors
 * Licensed under the MIT license (https://github.com/hugerte/hugerte/blob/main/LICENSE.TXT)
 */
!function(){"use strict";var t=hugerte.util.Tools.resolve("hugerte.PluginManager");const s=(t,s,e)=>{t.dom.toggleClass(t.getBody(),"mce-visualblocks"),e.set(!e.get()),((t,s)=>{t.dispatch("VisualBlocks",{state:s})})(t,e.get())},e=("visualblocks_default_state",t=>t.options.get("visualblocks_default_state"));const o=(t,s)=>e=>{e.setActive(s.get());const o=t=>e.setActive(t.state);return t.on("VisualBlocks",o),()=>t.off("VisualBlocks",o)};t.add("visualblocks",((t,l)=>{(t=>{(0,t.options.register)("visualblocks_default_state",{processor:"boolean",default:!1})})(t);const a=(t=>{let s=!1;return{get:()=>s,set:t=>{s=t}}})();((t,e,o)=>{t.addCommand("mceVisualBlocks",(()=>{s(t,0,o)}))})(t,0,a),((t,s)=>{const e=()=>t.execCommand("mceVisualBlocks");t.ui.registry.addToggleButton("visualblocks",{icon:"visualblocks",tooltip:"Show blocks",onAction:e,onSetup:o(t,s)}),t.ui.registry.addToggleMenuItem("visualblocks",{text:"Show blocks",icon:"visualblocks",onAction:e,onSetup:o(t,s)})})(t,a),((t,o,l)=>{t.on("PreviewFormats AfterPreviewFormats",(s=>{l.get()&&t.dom.toggleClass(t.getBody(),"mce-visualblocks","afterpreviewformats"===s.type)})),t.on("init",(()=>{e(t)&&s(t,0,l)}))})(t,0,a)}))}();