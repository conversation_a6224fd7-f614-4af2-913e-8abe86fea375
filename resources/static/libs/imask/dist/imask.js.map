{"version": 3, "file": "imask.js", "sources": ["../src/core/utils.ts", "../src/core/action-details.ts", "../src/core/holder.ts", "../src/masked/factory.ts", "../src/controls/mask-element.ts", "../src/controls/html-mask-element.ts", "../src/controls/html-input-mask-element.ts", "../src/controls/html-contenteditable-mask-element.ts", "../src/controls/input-history.ts", "../src/controls/input.ts", "../src/core/change-details.ts", "../src/core/continuous-tail-details.ts", "../src/masked/base.ts", "../src/masked/pattern/chunk-tail-details.ts", "../src/masked/pattern/cursor.ts", "../src/masked/pattern/fixed-definition.ts", "../src/masked/pattern/input-definition.ts", "../src/masked/regexp.ts", "../src/masked/pattern.ts", "../src/masked/range.ts", "../src/masked/date.ts", "../src/masked/dynamic.ts", "../src/masked/enum.ts", "../src/masked/function.ts", "../src/masked/number.ts", "../src/masked/pipe.ts", "../src/masked/repeat.ts", "../src/index.ts"], "sourcesContent": ["/** Checks if value is string */\nexport\nfunction isString (str: unknown): str is string {\n  return typeof str === 'string' || str instanceof String;\n}\n\n/** Checks if value is object */\nexport\nfunction isObject (obj: unknown): obj is object {\n  return typeof obj === 'object' && obj != null && obj?.constructor?.name === 'Object';\n}\n\nexport\nfunction pick<T extends Record<string, any>, K extends keyof T, V extends T[keyof T]> (\n  obj: T,\n  keys: K[] | ((v: V, k: K) => boolean),\n): Pick<T, K> {\n  if (Array.isArray(keys)) return pick(obj, (_, k) => keys.includes(k));\n  return (Object.entries(obj) as unknown as Array<[K, V]>)\n    .reduce((acc, [k, v]) => {\n      if (keys(v, k)) acc[k] = v;\n      return acc;\n    }, {} as any);\n}\n\n/** Direction */\nexport\nconst DIRECTION = {\n  NONE: 'NONE',\n  LEFT: 'LEFT',\n  FORCE_LEFT: 'FORCE_LEFT',\n  RIGHT: 'RIGHT',\n  FORCE_RIGHT: 'FORCE_RIGHT',\n} as const;\n\n/** Direction */\nexport\ntype Direction = typeof DIRECTION[keyof typeof DIRECTION];\n\nexport\nfunction forceDirection (direction: Direction): Direction {\n  switch (direction) {\n    case DIRECTION.LEFT:\n      return DIRECTION.FORCE_LEFT;\n    case DIRECTION.RIGHT:\n      return DIRECTION.FORCE_RIGHT;\n    default:\n      return direction;\n  }\n}\n\n/** Escapes regular expression control chars */\nexport\nfunction escapeRegExp (str: string): string {\n  return str.replace(/([.*+?^=!:${}()|[\\]/\\\\])/g, '\\\\$1');\n}\n\n// cloned from https://github.com/epoberezkin/fast-deep-equal with small changes\nexport\nfunction objectIncludes (b: any, a: any): boolean {\n  if (a === b) return true;\n\n  const arrA = Array.isArray(a), arrB = Array.isArray(b);\n  let i;\n\n  if (arrA && arrB) {\n    if (a.length != b.length) return false;\n    for (i = 0; i < a.length; i++)\n      if (!objectIncludes(a[i], b[i])) return false;\n    return true;\n  }\n\n  if (arrA != arrB) return false;\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    const dateA = a instanceof Date, dateB = b instanceof Date;\n    if (dateA && dateB) return a.getTime() == b.getTime();\n    if (dateA != dateB) return false;\n\n    const regexpA = a instanceof RegExp, regexpB = b instanceof RegExp;\n    if (regexpA && regexpB) return a.toString() == b.toString();\n    if (regexpA != regexpB) return false;\n\n    const keys = Object.keys(a);\n    // if (keys.length !== Object.keys(b).length) return false;\n\n    for (i = 0; i < keys.length; i++)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = 0; i < keys.length; i++)\n      if(!objectIncludes(b[keys[i]], a[keys[i]])) return false;\n\n    return true;\n  } else if (a && b && typeof a === 'function' && typeof b === 'function') {\n      return a.toString() === b.toString()\n  }\n\n  return false;\n}\n\n/** Selection range */\nexport\ntype Selection = {\n  start: number;\n  end: number;\n};\n", "import { type Direction, type Selection, DIRECTION } from './utils';\n\nexport\ntype ActionDetailsOptions = Pick<ActionDetails,\n  | 'value'\n  | 'cursorPos'\n  | 'oldValue'\n  | 'oldSelection'\n>;\n\n\n/** Provides details of changing input */\nexport default\nclass ActionDetails {\n  /** Current input value */\n  declare value: string;\n  /** Current cursor position */\n  declare cursorPos: number;\n  /** Old input value */\n  declare oldValue: string;\n  /** Old selection */\n  declare oldSelection: Selection;\n\n  constructor (opts: ActionDetailsOptions) {\n    Object.assign(this, opts);\n\n    // double check if left part was changed (autofilling, other non-standard input triggers)\n    while (this.value.slice(0, this.startChangePos) !== this.oldValue.slice(0, this.startChangePos)) {\n      --this.oldSelection.start;\n    }\n\n    if (this.insertedCount) {\n      // double check right part\n      while (this.value.slice(this.cursorPos) !== this.oldValue.slice(this.oldSelection.end)) {\n        if (this.value.length - this.cursorPos < this.oldValue.length - this.oldSelection.end) ++this.oldSelection.end;\n        else ++this.cursorPos;\n      }\n    }\n  }\n\n  /** Start changing position */\n  get startChangePos (): number {\n    return Math.min(this.cursorPos, this.oldSelection.start);\n  }\n\n  /** Inserted symbols count */\n  get insertedCount (): number {\n    return this.cursorPos - this.startChangePos;\n  }\n\n  /** Inserted symbols */\n  get inserted (): string {\n    return this.value.substr(this.startChangePos, this.insertedCount);\n  }\n\n  /** Removed symbols count */\n  get removedCount (): number {\n    // Math.max for opposite operation\n    return Math.max((this.oldSelection.end - this.startChangePos) ||\n      // for Delete\n      this.oldValue.length - this.value.length, 0);\n  }\n\n  /** Removed symbols */\n  get removed (): string {\n    return this.oldValue.substr(this.startChangePos, this.removedCount);\n  }\n\n  /** Unchanged head symbols */\n  get head (): string {\n    return this.value.substring(0, this.startChangePos);\n  }\n\n  /** Unchanged tail symbols */\n  get tail (): string {\n    return this.value.substring(this.startChangePos + this.insertedCount);\n  }\n\n  /** Remove direction */\n  get removeDirection (): Direction {\n    if (!this.removedCount || this.insertedCount) return DIRECTION.NONE;\n\n    // align right if delete at right\n    return (\n      (this.oldSelection.end === this.cursorPos || this.oldSelection.start === this.cursorPos) &&\n      // if not range removed (event with backspace)\n      this.oldSelection.end === this.oldSelection.start\n    ) ?\n      DIRECTION.RIGHT :\n      DIRECTION.LEFT;\n  }\n}\n", "import type { default as _InputMask, InputMaskElement as _InputMaskElement } from '../controls/input';\nimport type { default as _Masked } from '../masked/base';\nimport type { default as _MaskedPattern } from '../masked/pattern';\nimport type { default as _RepeatBlock } from '../masked/repeat';\nimport type { default as _MaskedDate } from '../masked/date';\nimport type { default as _MaskedDynamic } from '../masked/dynamic';\nimport type { default as _MaskedEnum } from '../masked/enum';\nimport type { default as _MaskedRange } from '../masked/range';\nimport type { default as _MaskedNumber } from '../masked/number';\nimport type { default as _MaskedFunction } from '../masked/function';\nimport type { default as _MaskedRegExp } from '../masked/regexp';\nimport type {\n  default as _createMask,\n  FactoryArg,\n} from '../masked/factory';\nimport type { default as _ChangeDetails } from './change-details';\n\nimport type { default as _MaskElement } from '../controls/mask-element';\nimport type { default as _HTMLMaskElement } from '../controls/html-mask-element';\nimport type { default as _HTMLContenteditableMaskElement } from '../controls/html-contenteditable-mask-element';\nimport type {\n  createPipe as _createPipe,\n  pipe as _pipe,\n  PIPE_TYPE as _PIPE_TYPE\n} from '../masked/pipe';\n\n\n/** Applies mask on element */\nfunction IMask<Opts extends FactoryArg> (el: _InputMaskElement, opts: Opts): _InputMask<Opts> {\n  // currently available only for input-like elements\n  return new IMask.InputMask(el, opts);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\ndeclare namespace IMask {\n  export let InputMask: typeof _InputMask;\n  export let createMask: typeof _createMask;\n  export let Masked: typeof _Masked;\n  export let MaskedPattern: typeof _MaskedPattern;\n  export let RepeatBlock: typeof _RepeatBlock;\n  export let MaskedDate: typeof _MaskedDate;\n  export let MaskedDynamic: typeof _MaskedDynamic;\n  export let MaskedEnum: typeof _MaskedEnum;\n  export let MaskedRange: typeof _MaskedRange;\n  export let MaskedNumber: typeof _MaskedNumber;\n  export let MaskedFunction: typeof _MaskedFunction;\n  export let MaskedRegExp: typeof _MaskedRegExp;\n  export let ChangeDetails: typeof _ChangeDetails;\n  export let MaskElement: typeof _MaskElement;\n  export let HTMLMaskElement: typeof _HTMLMaskElement;\n  export let HTMLContenteditableMaskElement: typeof _HTMLContenteditableMaskElement;\n  export let createPipe: typeof _createPipe;\n  export let pipe: typeof _pipe;\n  export let PIPE_TYPE: typeof _PIPE_TYPE;\n}\n\nexport default IMask;\n", "import { isString, pick, isObject } from '../core/utils';\nimport type Masked from './base';\nimport { type MaskedOptions } from './base';\nimport IMask from '../core/holder';\n\nimport type MaskedRegExp from './regexp';\nimport type MaskedPattern from './pattern';\nimport type MaskedFunction from './function';\nimport type MaskedDate from './date';\nimport type MaskedNumber from './number';\nimport type MaskedDynamic from './dynamic';\nimport type MaskedRange from './range';\nimport type MaskedEnum from './enum';\n\nimport { type MaskedEnumOptions } from './enum';\nimport { type MaskedRangeOptions } from './range';\nimport { type MaskedDynamicOptions } from './dynamic';\nimport { type MaskedPatternOptions } from './pattern';\nimport { type MaskedNumberOptions } from './number';\nimport { type MaskedRegExpOptions } from './regexp';\nimport { type MaskedFunctionOptions } from './function';\nimport { type MaskedDateOptions } from './date';\n\ntype MaskedDateFactoryOptions = Omit<MaskedDateOptions, 'mask'> & { mask: DateConstructor };\n\nexport\ntype FactoryStaticOpts =\n  | MaskedDateFactoryOptions\n  | MaskedNumberOptions\n  | MaskedPatternOptions\n  | MaskedDynamicOptions\n  | MaskedRegExpOptions\n  | MaskedFunctionOptions\n;\n\nexport\ntype AllFactoryStaticOpts =\n  & MaskedDateFactoryOptions\n  & MaskedNumberOptions\n  & MaskedPatternOptions\n  & MaskedDynamicOptions\n  & MaskedRegExpOptions\n  & MaskedFunctionOptions\n  & MaskedEnumOptions\n  & MaskedRangeOptions\n;\n\nexport\ntype FactoryStaticReturnMasked<Opts extends FactoryStaticOpts> =\n  Opts extends MaskedDateFactoryOptions ? MaskedDate :\n  Opts extends MaskedNumberOptions ? MaskedNumber :\n  Opts extends MaskedPatternOptions ? MaskedPattern :\n  Opts extends MaskedDynamicOptions ? MaskedDynamic :\n  Opts extends MaskedRegExpOptions ? MaskedRegExp :\n  Opts extends MaskedFunctionOptions ? MaskedFunction :\n  never\n;\n\nexport\ntype FactoryStaticMaskReturnMasked<Mask extends FactoryStaticOpts['mask']> =\n  Mask extends MaskedDateFactoryOptions['mask'] ? MaskedDate :\n  Mask extends MaskedNumberOptions['mask'] ? MaskedNumber :\n  Mask extends MaskedPatternOptions['mask'] ? MaskedPattern :\n  Mask extends MaskedDynamicOptions['mask'] ? MaskedDynamic :\n  Mask extends MaskedRegExpOptions['mask'] ? MaskedRegExp :\n  Mask extends MaskedFunctionOptions['mask'] ? MaskedFunction :\n  never\n;\n\n\nexport\ntype FactoryInstanceOpts =\n  | { mask: MaskedDate } & Omit<MaskedDateFactoryOptions, 'mask'>\n  | { mask: MaskedNumber } & Omit<MaskedNumberOptions, 'mask'>\n  | { mask: MaskedEnum } & Omit<MaskedEnumOptions, 'mask'>\n  | { mask: MaskedRange } & Omit<MaskedRangeOptions, 'mask'>\n  | { mask: MaskedRegExp } & Omit<MaskedRegExpOptions, 'mask'>\n  | { mask: MaskedFunction } & Omit<MaskedFunctionOptions, 'mask'>\n  | { mask: MaskedPattern } & Omit<MaskedPatternOptions, 'mask'>\n  | { mask: MaskedDynamic } & Omit<MaskedDynamicOptions, 'mask'>\n  | { mask: Masked } & Omit<MaskedOptions, 'mask'>\n;\n\nexport\ntype FactoryInstanceReturnMasked<Opts extends FactoryInstanceOpts> = Opts extends { mask: infer M } ? M : never;\n\nexport\ntype FactoryConstructorOpts =\n  | { mask: typeof MaskedDate } & Omit<MaskedDateFactoryOptions, 'mask'>\n  | { mask: typeof MaskedNumber } & Omit<MaskedNumberOptions, 'mask'>\n  | { mask: typeof MaskedEnum } & Omit<MaskedEnumOptions, 'mask'>\n  | { mask: typeof MaskedRange } & Omit<MaskedRangeOptions, 'mask'>\n  | { mask: typeof MaskedRegExp } & Omit<MaskedRegExpOptions, 'mask'>\n  | { mask: typeof MaskedFunction } & Omit<MaskedFunctionOptions, 'mask'>\n  | { mask: typeof MaskedPattern } & Omit<MaskedPatternOptions, 'mask'>\n  | { mask: typeof MaskedDynamic } & Omit<MaskedDynamicOptions, 'mask'>\n  | { mask: typeof Masked } & Omit<MaskedOptions, 'mask'>\n;\n \nexport\ntype FactoryConstructorReturnMasked<Opts extends FactoryConstructorOpts> =\n  Opts extends { mask: typeof MaskedDate } ? MaskedDate :\n  Opts extends { mask: typeof MaskedNumber } ? MaskedNumber :\n  Opts extends { mask: typeof MaskedEnum } ? MaskedEnum :\n  Opts extends { mask: typeof MaskedRange } ? MaskedRange :\n  Opts extends { mask: typeof MaskedRegExp } ? MaskedRegExp :\n  Opts extends { mask: typeof MaskedFunction } ? MaskedFunction :\n  Opts extends { mask: typeof MaskedPattern } ? MaskedPattern :\n  Opts extends { mask: typeof MaskedDynamic } ? MaskedDynamic :\n  Masked\n;\n\nexport\ntype FactoryOpts = FactoryConstructorOpts | FactoryInstanceOpts | FactoryStaticOpts;\n\nexport\ntype FactoryArg = Masked | FactoryOpts | FactoryStaticOpts['mask'];\n\nexport\ntype ExtendFactoryArgOptions<Opts extends { [key: string]: any }> =\n  Masked | FactoryOpts & Opts | FactoryStaticOpts['mask']\n;\n\nexport\ntype UpdateStaticOpts<Opts extends FactoryStaticOpts> =\n  Opts extends MaskedEnumOptions ? MaskedEnumOptions :\n  Opts extends MaskedRangeOptions ? MaskedRangeOptions :\n  Opts extends MaskedDynamicOptions ? MaskedDynamicOptions :\n  Opts extends MaskedPatternOptions ? MaskedPatternOptions :\n  Opts extends MaskedDateOptions ? MaskedDateOptions :\n  Opts extends MaskedNumberOptions ? MaskedNumberOptions :\n  Opts extends MaskedRegExpOptions ? MaskedRegExpOptions :\n  Opts extends MaskedFunctionOptions ? MaskedFunctionOptions :\n  never\n;\n\ntype AnyOpts = Record<string, any>;\n\nexport\ntype UpdateInstanceOpts<M extends Masked> =\n  M extends MaskedRegExp ? MaskedRegExpOptions :\n  M extends MaskedFunction ? MaskedFunctionOptions :\n  M extends MaskedDate ? MaskedDateOptions :\n  M extends MaskedNumber ? MaskedNumberOptions :\n  M extends MaskedDynamic ? MaskedDynamicOptions :\n  M extends MaskedRange ? MaskedRangeOptions :\n  M extends MaskedEnum ? MaskedEnumOptions :\n  M extends MaskedPattern ? MaskedPatternOptions :\n  AnyOpts\n;\n\nexport\ntype UpdateConstructorOpts<M extends FactoryConstructorOpts> =\n  M extends { mask: typeof MaskedDate } ? MaskedDateOptions :\n  M extends { mask: typeof MaskedNumber } ? MaskedNumberOptions :\n  M extends { mask: typeof MaskedEnum } ? MaskedEnumOptions :\n  M extends { mask: typeof MaskedRange } ? MaskedRangeOptions :\n  M extends { mask: typeof MaskedRegExp } ? MaskedRegExpOptions :\n  M extends { mask: typeof MaskedFunction } ? MaskedFunctionOptions :\n  M extends { mask: typeof MaskedPattern } ? MaskedPatternOptions :\n  M extends { mask: typeof MaskedDynamic } ? MaskedDynamicOptions :\n  AnyOpts\n;\n\nexport\ntype UpdateStaticMaskOpts<M extends FactoryStaticOpts['mask']> =\n  M extends MaskedDateFactoryOptions['mask'] ? MaskedDateOptions :\n  M extends MaskedNumberOptions['mask'] ? MaskedNumberOptions :\n  M extends MaskedPatternOptions['mask'] ? MaskedPatternOptions :\n  M extends MaskedDynamicOptions['mask'] ? MaskedDynamicOptions :\n  M extends MaskedRegExpOptions['mask'] ? MaskedRegExpOptions :\n  M extends MaskedFunctionOptions['mask'] ? MaskedFunctionOptions :\n  never\n;\n\nexport\ntype UpdateOpts<Opts extends FactoryArg> = Partial<\n  Opts extends Masked ? UpdateInstanceOpts<Opts> :\n  Opts extends FactoryStaticOpts['mask'] ? UpdateStaticMaskOpts<Opts> :\n  Opts extends FactoryStaticOpts ? UpdateStaticOpts<Opts> :\n  Opts extends FactoryInstanceOpts ? UpdateInstanceOpts<Opts['mask']> :\n  Opts extends FactoryConstructorOpts ? UpdateConstructorOpts<Opts> :\n  AnyOpts\n>;\n\nexport\ntype FactoryReturnMasked<Opts extends FactoryArg> =\n  Opts extends Masked ? Opts :\n  Opts extends FactoryStaticOpts['mask'] ? FactoryStaticMaskReturnMasked<Opts> :\n  Opts extends FactoryConstructorOpts ? FactoryConstructorReturnMasked<Opts> :\n  Opts extends FactoryInstanceOpts ? FactoryInstanceReturnMasked<Opts> :\n  Opts extends FactoryStaticOpts ? FactoryStaticReturnMasked<Opts> :\n  never\n;\n\n\n// TODO can't use overloads here because of https://github.com/microsoft/TypeScript/issues/50754\n// export function maskedClass(mask: string): typeof MaskedPattern;\n// export function maskedClass(mask: DateConstructor): typeof MaskedDate;\n// export function maskedClass(mask: NumberConstructor): typeof MaskedNumber;\n// export function maskedClass(mask: Array<any> | ArrayConstructor): typeof MaskedDynamic;\n// export function maskedClass(mask: MaskedDate): typeof MaskedDate;\n// export function maskedClass(mask: MaskedNumber): typeof MaskedNumber;\n// export function maskedClass(mask: MaskedEnum): typeof MaskedEnum;\n// export function maskedClass(mask: MaskedRange): typeof MaskedRange;\n// export function maskedClass(mask: MaskedRegExp): typeof MaskedRegExp;\n// export function maskedClass(mask: MaskedFunction): typeof MaskedFunction;\n// export function maskedClass(mask: MaskedPattern): typeof MaskedPattern;\n// export function maskedClass(mask: MaskedDynamic): typeof MaskedDynamic;\n// export function maskedClass(mask: Masked): typeof Masked;\n// export function maskedClass(mask: typeof Masked): typeof Masked;\n// export function maskedClass(mask: typeof MaskedDate): typeof MaskedDate;\n// export function maskedClass(mask: typeof MaskedNumber): typeof MaskedNumber;\n// export function maskedClass(mask: typeof MaskedEnum): typeof MaskedEnum;\n// export function maskedClass(mask: typeof MaskedRange): typeof MaskedRange;\n// export function maskedClass(mask: typeof MaskedRegExp): typeof MaskedRegExp;\n// export function maskedClass(mask: typeof MaskedFunction): typeof MaskedFunction;\n// export function maskedClass(mask: typeof MaskedPattern): typeof MaskedPattern;\n// export function maskedClass(mask: typeof MaskedDynamic): typeof MaskedDynamic;\n// export function maskedClass<Mask extends typeof Masked> (mask: Mask): Mask;\n// export function maskedClass(mask: RegExp): typeof MaskedRegExp;\n// export function maskedClass(mask: (value: string, ...args: any[]) => boolean): typeof MaskedFunction;\n\n/** Get Masked class by mask type */\nexport function maskedClass (mask: Masked | FactoryOpts['mask']): any /* TODO */ {\n  if (mask == null) throw new Error('mask property should be defined');\n\n  if (mask instanceof RegExp) return IMask.MaskedRegExp;\n  if (isString(mask)) return IMask.MaskedPattern;\n  if (mask === Date) return IMask.MaskedDate;\n  if (mask === Number) return IMask.MaskedNumber;\n  if (Array.isArray(mask) || mask === Array) return IMask.MaskedDynamic;\n  if (IMask.Masked && (mask as any).prototype instanceof IMask.Masked) return mask;\n  if (IMask.Masked && mask instanceof IMask.Masked) return mask.constructor;\n  if (mask instanceof Function) return IMask.MaskedFunction;\n\n  console.warn('Mask not found for mask', mask);  // eslint-disable-line no-console\n  return IMask.Masked;\n}\n\ntype MaskedClassOf<M extends Masked> =\n  M extends MaskedDate ? typeof MaskedDate :\n  M extends MaskedNumber ? typeof MaskedNumber :\n  M extends MaskedEnum ? typeof MaskedEnum :\n  M extends MaskedRange ? typeof MaskedRange :\n  M extends MaskedRegExp ? typeof MaskedRegExp :\n  M extends MaskedFunction ? typeof MaskedFunction :\n  M extends MaskedPattern ? typeof MaskedPattern :\n  M extends MaskedDynamic ? typeof MaskedDynamic :\n  any\n;\n\n\ntype NormalizedMaskedOpts<Opts extends Masked> = Omit<Opts, 'mask'> & {\n  _mask: Opts,\n  mask: MaskedClassOf<Opts>,\n};\n\ntype NormalizedInstanceOpts<Opts extends FactoryInstanceOpts> =\n  Omit<Opts['mask'], `_${string}` | 'mask'> &\n  NormalizedMaskedOpts<Opts['mask']>\n;\n\nexport\ntype NormalizedOpts<Opts extends FactoryArg> =\n  Opts extends FactoryStaticOpts['mask'] ? { mask: Opts } :\n  Opts extends Masked ? NormalizedMaskedOpts<Opts> :\n  Opts extends FactoryInstanceOpts ? NormalizedInstanceOpts<Opts> :\n  Opts extends FactoryStaticOpts | FactoryConstructorOpts ? Opts :\n  { mask: Opts }\n;\n\nexport\nfunction normalizeOpts<Opts extends FactoryArg> (opts: Opts): NormalizedOpts<Opts> {\n  if (!opts) throw new Error('Options in not defined');\n\n  if (IMask.Masked) {\n    if ((opts as any).prototype instanceof IMask.Masked) return { mask: opts } as NormalizedOpts<Opts>;\n\n    /*\n      handle cases like:\n      1) opts = Masked\n      2) opts = { mask: Masked, ...instanceOpts }\n    */\n    const { mask=undefined, ...instanceOpts } =\n      opts instanceof IMask.Masked ? { mask: opts } :\n      isObject(opts) && (opts as FactoryInstanceOpts).mask instanceof IMask.Masked ? (opts as FactoryInstanceOpts) : {};\n\n    if (mask) {\n      const _mask = (mask as Masked).mask;\n\n      return {\n        ...pick(mask, (_, k: string) => !k.startsWith('_')),\n        mask: mask.constructor,\n        _mask,\n        ...instanceOpts,\n      } as NormalizedOpts<Opts>;\n    }\n  }\n\n  if (!isObject(opts)) return { mask: opts } as unknown as NormalizedOpts<Opts>;\n\n  return { ...opts } as unknown as NormalizedOpts<Opts>;\n}\n\n// TODO can't use overloads here because of https://github.com/microsoft/TypeScript/issues/50754\n\n// From masked\n// export default function createMask<Opts extends Masked, ReturnMasked=Opts> (opts: Opts): ReturnMasked;\n// // From masked class\n// export default function createMask<Opts extends MaskedOptions<typeof Masked>, ReturnMasked extends Masked=InstanceType<Opts['mask']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedOptions<typeof MaskedDate>, ReturnMasked extends MaskedDate=MaskedDate<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedOptions<typeof MaskedNumber>, ReturnMasked extends MaskedNumber=MaskedNumber<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedOptions<typeof MaskedEnum>, ReturnMasked extends MaskedEnum=MaskedEnum<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedOptions<typeof MaskedRange>, ReturnMasked extends MaskedRange=MaskedRange<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedOptions<typeof MaskedRegExp>, ReturnMasked extends MaskedRegExp=MaskedRegExp<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedOptions<typeof MaskedFunction>, ReturnMasked extends MaskedFunction=MaskedFunction<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedOptions<typeof MaskedPattern>, ReturnMasked extends MaskedPattern=MaskedPattern<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedOptions<typeof MaskedDynamic>, ReturnMasked extends MaskedDynamic=MaskedDynamic<Opts['parent']>> (opts: Opts): ReturnMasked;\n// // From mask opts\n// export default function createMask<Opts extends MaskedOptions<Masked>, ReturnMasked=Opts extends MaskedOptions<infer M> ? M : never> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedNumberOptions, ReturnMasked extends MaskedNumber=MaskedNumber<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedDateFactoryOptions, ReturnMasked extends MaskedDate=MaskedDate<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedEnumOptions, ReturnMasked extends MaskedEnum=MaskedEnum<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedRangeOptions, ReturnMasked extends MaskedRange=MaskedRange<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedPatternOptions, ReturnMasked extends MaskedPattern=MaskedPattern<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedDynamicOptions, ReturnMasked extends MaskedDynamic=MaskedDynamic<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedOptions<RegExp>, ReturnMasked extends MaskedRegExp=MaskedRegExp<Opts['parent']>> (opts: Opts): ReturnMasked;\n// export default function createMask<Opts extends MaskedOptions<Function>, ReturnMasked extends MaskedFunction=MaskedFunction<Opts['parent']>> (opts: Opts): ReturnMasked;\n\n/** Creates new {@link Masked} depending on mask type */\nexport default\nfunction createMask<Opts extends FactoryArg> (opts: Opts): FactoryReturnMasked<Opts> {\n  if (IMask.Masked && (opts instanceof IMask.Masked)) return opts as FactoryReturnMasked<Opts>;\n  const nOpts = normalizeOpts(opts);\n\n  const MaskedClass = maskedClass(nOpts.mask);\n  if (!MaskedClass) throw new Error(`Masked class is not found for provided mask ${nOpts.mask}, appropriate module needs to be imported manually before creating mask.`);\n\n  if (nOpts.mask === MaskedClass) delete nOpts.mask;\n  if ((nOpts as any)._mask) { nOpts.mask = (nOpts as any)._mask; delete (nOpts as any)._mask; }\n  return new MaskedClass(nOpts);\n}\n\n\nIMask.createMask = createMask;\n", "import IMask from '../core/holder';\n\n\nexport\ntype ElementEvent =\n  | 'selectionChange'\n  | 'input'\n  | 'drop'\n  | 'click'\n  | 'focus'\n  | 'commit'\n;\n\nexport\ntype EventHandlers = { [key in ElementEvent]: (...args: any[]) => void } & {\n  undo?: (...args: any[]) => void;\n  redo?: (...args: any[]) => void;\n}\n\n/**  Generic element API to use with mask */\nexport default\nabstract class MaskElement {\n  /** */\n  abstract _unsafeSelectionStart: number | null;\n  /** */\n  abstract _unsafeSelectionEnd: number | null;\n  /** */\n  abstract value: string;\n\n  /** Safely returns selection start */\n  get selectionStart (): number {\n    let start;\n    try {\n      start = this._unsafeSelectionStart;\n    } catch {}\n\n    return start != null ?\n      start :\n      this.value.length;\n  }\n\n  /** Safely returns selection end */\n  get selectionEnd (): number {\n    let end;\n    try {\n      end = this._unsafeSelectionEnd;\n    } catch {}\n\n    return end != null ?\n      end :\n      this.value.length;\n  }\n\n  /** Safely sets element selection */\n  select (start: number, end: number) {\n    if (start == null || end == null ||\n      start === this.selectionStart && end === this.selectionEnd) return;\n\n    try {\n      this._unsafeSelect(start, end);\n    } catch {}\n  }\n\n  /** */\n  get isActive (): boolean { return false; }\n  /** */\n  abstract _unsafeSelect (start: number, end: number): void;\n  /** */\n  abstract bindEvents (handlers: EventHandlers): void;\n  /** */\n  abstract unbindEvents (): void\n}\n\n\nIMask.MaskElement = MaskElement;\n", "import MaskElement, { EventHandlers } from './mask-element';\nimport IMask from '../core/holder';\n\n\nconst KEY_Z = 90;\nconst KEY_Y = 89;\n\n/** Bridge between HTMLElement and {@link Masked} */\nexport default\nabstract class HTMLMaskElement extends MaskElement {\n  /** HTMLElement to use mask on */\n  declare input: HTMLElement;\n  declare _handlers: EventHandlers;\n  abstract value: string;\n\n  constructor (input: HTMLElement) {\n    super();\n    this.input = input;\n    this._onKeydown = this._onKeydown.bind(this);\n    this._onInput = this._onInput.bind(this);\n    this._onBeforeinput = this._onBeforeinput.bind(this);\n    this._onCompositionEnd = this._onCompositionEnd.bind(this);\n  }\n\n  get rootElement (): HTMLDocument {\n    return (this.input.getRootNode?.() ?? document) as HTMLDocument;\n  }\n\n  /** Is element in focus */\n  get isActive (): boolean {\n    return this.input === this.rootElement.activeElement;\n  }\n\n  /** Binds HTMLElement events to mask internal events */\n  override bindEvents (handlers: EventHandlers) {\n    this.input.addEventListener('keydown', this._onKeydown as EventListener);\n    this.input.addEventListener('input', this._onInput as EventListener);\n    this.input.addEventListener('beforeinput', this._onBeforeinput as EventListener);\n    this.input.addEventListener('compositionend', this._onCompositionEnd as EventListener);\n    this.input.addEventListener('drop', handlers.drop);\n    this.input.addEventListener('click', handlers.click);\n    this.input.addEventListener('focus', handlers.focus);\n    this.input.addEventListener('blur', handlers.commit);\n    this._handlers = handlers;\n  }\n\n  _onKeydown (e: KeyboardEvent) {\n    if (this._handlers.redo && (\n      (e.keyCode === KEY_Z && e.shiftKey && (e.metaKey || e.ctrlKey)) ||\n      (e.keyCode === KEY_Y && e.ctrlKey)\n    )) {\n      e.preventDefault();\n      return this._handlers.redo(e);\n    }\n\n    if (this._handlers.undo && e.keyCode === KEY_Z && (e.metaKey || e.ctrlKey)) {\n      e.preventDefault();\n      return this._handlers.undo(e);\n    }\n\n    if (!e.isComposing) this._handlers.selectionChange(e);\n  }\n\n  _onBeforeinput (e: InputEvent) {\n    if (e.inputType === 'historyUndo' && this._handlers.undo) {\n      e.preventDefault();\n      return this._handlers.undo(e);\n    }\n\n    if (e.inputType === 'historyRedo' && this._handlers.redo) {\n      e.preventDefault();\n      return this._handlers.redo(e);\n    }\n  }\n\n  _onCompositionEnd (e: CompositionEvent) {\n    this._handlers.input(e);\n  }\n\n  _onInput (e: InputEvent) {\n    if (!e.isComposing) this._handlers.input(e);\n  }\n\n  /** Unbinds HTMLElement events to mask internal events */\n  override unbindEvents () {\n    this.input.removeEventListener('keydown', this._onKeydown as EventListener);\n    this.input.removeEventListener('input', this._onInput as EventListener);\n    this.input.removeEventListener('beforeinput', this._onBeforeinput as EventListener);\n    this.input.removeEventListener('compositionend', this._onCompositionEnd as EventListener);\n    this.input.removeEventListener('drop', this._handlers.drop);\n    this.input.removeEventListener('click', this._handlers.click);\n    this.input.removeEventListener('focus', this._handlers.focus);\n    this.input.removeEventListener('blur', this._handlers.commit);\n    this._handlers = {} as EventHandlers;\n  }\n}\n\n\nIMask.HTMLMaskElement = HTMLMaskElement;\n", "import HTMLMaskElement from './html-mask-element';\nimport IMask from '../core/holder';\n\nexport\ntype InputElement = HTMLTextAreaElement | HTMLInputElement;\n\n/** Bridge between InputElement and {@link Masked} */\nexport default\nclass HTMLInputMaskElement extends HTMLMaskElement {\n  /** InputElement to use mask on */\n  declare input: InputElement;\n\n  constructor (input: InputElement) {\n    super(input);\n    this.input = input;\n  }\n\n  /** Returns InputElement selection start */\n  override get _unsafeSelectionStart (): number | null {\n    return this.input.selectionStart != null ? this.input.selectionStart : this.value.length;\n  }\n\n  /** Returns InputElement selection end */\n  override get _unsafeSelectionEnd (): number | null {\n    return this.input.selectionEnd;\n  }\n\n  /** Sets InputElement selection */\n  _unsafeSelect (start: number, end: number) {\n    this.input.setSelectionRange(start, end);\n  }\n\n  override get value (): string {\n    return this.input.value;\n  }\n  override set value (value: string) {\n    this.input.value = value;\n  }\n}\n\n\nIMask.HTMLMaskElement = HTMLMaskElement;\n", "import HTMLMaskElement from './html-mask-element';\nimport IMask from '../core/holder';\n\n\nexport default\nclass HTMLContenteditableMaskElement extends HTMLMaskElement {\n  declare input: HTMLElement;\n  /** Returns HTMLElement selection start */\n  override get _unsafeSelectionStart (): number | null {\n    const root = this.rootElement;\n    const selection = root.getSelection && root.getSelection();\n    const anchorOffset = selection && selection.anchorOffset;\n    const focusOffset = selection && selection.focusOffset;\n    if (focusOffset == null || anchorOffset == null || anchorOffset < focusOffset) {\n      return anchorOffset;\n    }\n    return focusOffset;\n  }\n\n  /** Returns HTMLElement selection end */\n  override get _unsafeSelectionEnd (): number | null {\n    const root = this.rootElement;\n    const selection = root.getSelection && root.getSelection();\n    const anchorOffset = selection && selection.anchorOffset;\n    const focusOffset = selection && selection.focusOffset;\n    if (focusOffset == null || anchorOffset == null || anchorOffset > focusOffset) {\n      return anchorOffset;\n    }\n    return focusOffset;\n  }\n\n  /** Sets HTMLElement selection */\n  override _unsafeSelect (start: number, end: number) {\n    if (!this.rootElement.createRange) return;\n\n    const range = this.rootElement.createRange();\n    range.setStart(this.input.firstChild || this.input, start);\n    range.setEnd(this.input.lastChild || this.input, end);\n    const root = this.rootElement;\n    const selection = root.getSelection && root.getSelection();\n    if (selection) {\n      selection.removeAllRanges();\n      selection.addRange(range);\n    }\n  }\n\n  /** HTMLElement value */\n  override get value (): string {\n    return this.input.textContent || '';\n  }\n  override set value (value: string) {\n    this.input.textContent = value;\n  }\n}\n\n\nIMask.HTMLContenteditableMaskElement = HTMLContenteditableMaskElement;\n", "import { type Selection } from '../core/utils';\n\n\nexport\ntype InputHistoryState = {\n  unmaskedValue: string,\n  selection: Selection,\n};\n\n\nexport default\nclass InputHistory {\n  static MAX_LENGTH = 100;\n  states: InputHistoryState[] = [];\n  currentIndex = 0;\n\n  get currentState (): InputHistoryState | undefined {\n    return this.states[this.currentIndex];\n  }\n\n  get isEmpty (): boolean {\n    return this.states.length === 0;\n  }\n\n  push (state: InputHistoryState) {\n    // if current index points before the last element then remove the future\n    if (this.currentIndex < this.states.length - 1) this.states.length = this.currentIndex + 1;\n    this.states.push(state);\n    if (this.states.length > InputHistory.MAX_LENGTH) this.states.shift();\n    this.currentIndex = this.states.length - 1;\n  }\n\n  go (steps: number): InputHistoryState | undefined {\n    this.currentIndex = Math.min(Math.max(this.currentIndex + steps, 0), this.states.length - 1);\n    return this.currentState;\n  }\n\n  undo () {\n    return this.go(-1);\n  }\n\n  redo () {\n    return this.go(+1);\n  }\n\n  clear () {\n    this.states.length = 0;\n    this.currentIndex = 0;\n  }\n}\n", "import { DIRECTION, type Selection } from '../core/utils';\nimport ActionDetails from '../core/action-details';\nimport createMask, { type UpdateOpts, maskedClass, type FactoryArg, type FactoryReturnMasked } from '../masked/factory';\nimport Masked from '../masked/base';\nimport MaskElement from './mask-element';\nimport HTMLInputMaskElement, { type InputElement } from './html-input-mask-element';\nimport HTMLContenteditableMaskElement from './html-contenteditable-mask-element';\nimport IMask from '../core/holder';\nimport InputHistory, { type InputHistoryState } from './input-history';\n\n\nexport\ntype InputMaskElement = MaskElement | InputElement | HTMLElement;\n\nexport\ntype InputMaskEventListener = (e?: InputEvent) => void;\n\n/** Listens to element events and controls changes between element and {@link Masked} */\nexport default\nclass InputMask<Opts extends FactoryArg=Record<string, unknown>> {\n  /**\n    View element\n  */\n  declare el: MaskElement;\n\n  /** Internal {@link Masked} model */\n  declare masked: FactoryReturnMasked<Opts>;\n\n  declare _listeners: Record<string, Array<InputMaskEventListener>>;\n  declare _value: string;\n  declare _changingCursorPos: number;\n  declare _unmaskedValue: string;\n  declare _rawInputValue: string;\n  declare _selection: Selection;\n  declare _cursorChanging?: ReturnType<typeof setTimeout>;\n  declare _historyChanging?: boolean;\n  declare _inputEvent?: InputEvent;\n  declare history: InputHistory;\n\n  constructor (el: InputMaskElement, opts: Opts) {\n    this.el =\n      (el instanceof MaskElement) ? el :\n      (el.isContentEditable && el.tagName !== 'INPUT' && el.tagName !== 'TEXTAREA') ? new HTMLContenteditableMaskElement(el) :\n      new HTMLInputMaskElement(el as InputElement);\n\n    this.masked = createMask(opts);\n\n    this._listeners = {};\n    this._value = '';\n    this._unmaskedValue = '';\n    this._rawInputValue = '';\n    this.history = new InputHistory();\n\n    this._saveSelection = this._saveSelection.bind(this);\n    this._onInput = this._onInput.bind(this);\n    this._onChange = this._onChange.bind(this);\n    this._onDrop = this._onDrop.bind(this);\n    this._onFocus = this._onFocus.bind(this);\n    this._onClick = this._onClick.bind(this);\n    this._onUndo = this._onUndo.bind(this);\n    this._onRedo = this._onRedo.bind(this);\n    this.alignCursor = this.alignCursor.bind(this);\n    this.alignCursorFriendly = this.alignCursorFriendly.bind(this);\n\n    this._bindEvents();\n\n    // refresh\n    this.updateValue();\n    this._onChange();\n  }\n\n  maskEquals (mask: any): boolean {\n    return mask == null || this.masked?.maskEquals(mask);\n  }\n\n  /** Masked */\n  get mask (): FactoryReturnMasked<Opts>['mask'] {\n    return this.masked.mask;\n  }\n  set mask (mask: any) {\n    if (this.maskEquals(mask)) return;\n\n    if (!((mask as Masked) instanceof IMask.Masked) && this.masked.constructor === maskedClass(mask as Masked)) {\n      // TODO \"any\" no idea\n      this.masked.updateOptions({ mask } as any);\n      return;\n    }\n\n    const masked = (mask instanceof IMask.Masked ? mask : createMask({ mask } as Opts)) as FactoryReturnMasked<Opts>;\n    masked.unmaskedValue = this.masked.unmaskedValue;\n    this.masked = masked;\n  }\n\n  /** Raw value */\n  get value (): string {\n    return this._value;\n  }\n\n  set value (str: string) {\n    if (this.value === str) return;\n\n    this.masked.value = str;\n    this.updateControl('auto');\n  }\n\n  /** Unmasked value */\n  get unmaskedValue (): string {\n    return this._unmaskedValue;\n  }\n\n  set unmaskedValue (str: string) {\n    if (this.unmaskedValue === str) return;\n\n    this.masked.unmaskedValue = str;\n    this.updateControl('auto');\n  }\n\n    /** Raw input value */\n  get rawInputValue (): string {\n    return this._rawInputValue;\n  }\n\n  set rawInputValue (str: string) {\n    if (this.rawInputValue === str) return;\n\n    this.masked.rawInputValue = str;\n    this.updateControl();\n    this.alignCursor();\n  }\n\n  /** Typed unmasked value */\n  get typedValue (): FactoryReturnMasked<Opts>['typedValue'] {\n    return this.masked.typedValue;\n  }\n\n  set typedValue (val: FactoryReturnMasked<Opts>['typedValue']) {\n    if (this.masked.typedValueEquals(val)) return;\n\n    this.masked.typedValue = val;\n    this.updateControl('auto');\n  }\n\n  /** Display value */\n  get displayValue (): string {\n    return this.masked.displayValue;\n  }\n\n  /** Starts listening to element events */\n  _bindEvents () {\n    this.el.bindEvents({\n      selectionChange: this._saveSelection,\n      input: this._onInput,\n      drop: this._onDrop,\n      click: this._onClick,\n      focus: this._onFocus,\n      commit: this._onChange,\n      undo: this._onUndo,\n      redo: this._onRedo,\n    });\n  }\n\n  /** Stops listening to element events */\n  _unbindEvents () {\n    if (this.el) this.el.unbindEvents();\n  }\n\n  /** Fires custom event */\n  _fireEvent (ev: string, e?: InputEvent) {\n    const listeners = this._listeners[ev];\n    if (!listeners) return;\n\n    listeners.forEach(l => l(e));\n  }\n\n  /** Current selection start */\n  get selectionStart (): number {\n    return this._cursorChanging ?\n      this._changingCursorPos :\n\n      this.el.selectionStart;\n  }\n\n  /** Current cursor position */\n  get cursorPos (): number {\n    return this._cursorChanging ?\n      this._changingCursorPos :\n\n      this.el.selectionEnd;\n  }\n  set cursorPos (pos: number) {\n    if (!this.el || !this.el.isActive) return;\n\n    this.el.select(pos, pos);\n    this._saveSelection();\n  }\n\n  /** Stores current selection */\n  _saveSelection (/* ev */) {\n    if (this.displayValue !== this.el.value) {\n      console.warn('Element value was changed outside of mask. Syncronize mask using `mask.updateValue()` to work properly.'); // eslint-disable-line no-console\n    }\n    this._selection = {\n      start: this.selectionStart,\n      end: this.cursorPos,\n    };\n  }\n\n  /** Syncronizes model value from view */\n  updateValue () {\n    this.masked.value = this.el.value;\n    this._value = this.masked.value;\n    this._unmaskedValue = this.masked.unmaskedValue;\n    this._rawInputValue = this.masked.rawInputValue;\n  }\n\n  /** Syncronizes view from model value, fires change events */\n  updateControl (cursorPos?: number | 'auto') {\n    const newUnmaskedValue = this.masked.unmaskedValue;\n    const newValue = this.masked.value;\n    const newRawInputValue = this.masked.rawInputValue;\n    const newDisplayValue = this.displayValue;\n\n    const isChanged =\n      this.unmaskedValue !== newUnmaskedValue ||\n      this.value !== newValue ||\n      this._rawInputValue !== newRawInputValue\n    ;\n\n    this._unmaskedValue = newUnmaskedValue;\n    this._value = newValue;\n    this._rawInputValue = newRawInputValue;\n\n    if (this.el.value !== newDisplayValue) this.el.value = newDisplayValue;\n\n    if (cursorPos === 'auto') this.alignCursor();\n    else if (cursorPos != null) this.cursorPos = cursorPos;\n\n    if (isChanged) this._fireChangeEvents();\n    if (!this._historyChanging && (isChanged || this.history.isEmpty)) this.history.push({\n      unmaskedValue: newUnmaskedValue,\n      selection: { start: this.selectionStart, end: this.cursorPos },\n    });\n  }\n\n  /** Updates options with deep equal check, recreates {@link Masked} model if mask type changes */\n  updateOptions(opts: UpdateOpts<Opts>) {\n    const { mask, ...restOpts } = opts as any; // TODO types, yes, mask is optional\n\n    const updateMask = !this.maskEquals(mask);\n    const updateOpts = this.masked.optionsIsChanged(restOpts);\n\n    if (updateMask) this.mask = mask;\n    if (updateOpts) this.masked.updateOptions(restOpts);  // TODO\n\n    if (updateMask || updateOpts) this.updateControl();\n  }\n\n  /** Updates cursor */\n  updateCursor (cursorPos: number) {\n    if (cursorPos == null) return;\n    this.cursorPos = cursorPos;\n\n    // also queue change cursor for mobile browsers\n    this._delayUpdateCursor(cursorPos);\n  }\n\n  /** Delays cursor update to support mobile browsers */\n  _delayUpdateCursor (cursorPos: number) {\n    this._abortUpdateCursor();\n    this._changingCursorPos = cursorPos;\n    this._cursorChanging = setTimeout(() => {\n      if (!this.el) return; // if was destroyed\n      this.cursorPos = this._changingCursorPos;\n      this._abortUpdateCursor();\n    }, 10);\n  }\n\n  /** Fires custom events */\n  _fireChangeEvents () {\n    this._fireEvent('accept', this._inputEvent);\n    if (this.masked.isComplete) this._fireEvent('complete', this._inputEvent);\n  }\n\n  /** Aborts delayed cursor update */\n  _abortUpdateCursor () {\n    if (this._cursorChanging) {\n      clearTimeout(this._cursorChanging);\n      delete this._cursorChanging;\n    }\n  }\n\n  /** Aligns cursor to nearest available position */\n  alignCursor () {\n    this.cursorPos = this.masked.nearestInputPos(this.masked.nearestInputPos(this.cursorPos, DIRECTION.LEFT));\n  }\n\n  /** Aligns cursor only if selection is empty */\n  alignCursorFriendly () {\n    if (this.selectionStart !== this.cursorPos) return;  // skip if range is selected\n    this.alignCursor();\n  }\n\n  /** Adds listener on custom event */\n  on (ev: string, handler: InputMaskEventListener): this {\n    if (!this._listeners[ev]) this._listeners[ev] = [];\n    this._listeners[ev].push(handler);\n    return this;\n  }\n\n  /** Removes custom event listener */\n  off (ev: string, handler: InputMaskEventListener): this {\n    if (!this._listeners[ev]) return this;\n    if (!handler) {\n      delete this._listeners[ev];\n      return this;\n    }\n    const hIndex = this._listeners[ev].indexOf(handler);\n    if (hIndex >= 0) this._listeners[ev].splice(hIndex, 1);\n    return this;\n  }\n\n  /** Handles view input event */\n  _onInput (e: InputEvent): void {\n    this._inputEvent = e;\n    this._abortUpdateCursor();\n\n    const details = new ActionDetails({\n      // new state\n      value: this.el.value,\n      cursorPos: this.cursorPos,\n\n      // old state\n      oldValue: this.displayValue,\n      oldSelection: this._selection,\n    });\n\n    const oldRawValue = this.masked.rawInputValue;\n\n    const offset = this.masked.splice(\n      details.startChangePos,\n      details.removed.length,\n      details.inserted,\n      details.removeDirection,\n      { input: true, raw: true },\n    ).offset;\n\n    // force align in remove direction only if no input chars were removed\n    // otherwise we still need to align with NONE (to get out from fixed symbols for instance)\n    const removeDirection = oldRawValue === this.masked.rawInputValue ?\n      details.removeDirection :\n      DIRECTION.NONE;\n\n    let cursorPos = this.masked.nearestInputPos(\n      details.startChangePos + offset,\n      removeDirection,\n    );\n    if (removeDirection !== DIRECTION.NONE) cursorPos = this.masked.nearestInputPos(cursorPos, DIRECTION.NONE);\n\n    this.updateControl(cursorPos);\n    delete this._inputEvent;\n  }\n\n  /** Handles view change event and commits model value */\n  _onChange () {\n    if (this.displayValue !== this.el.value) this.updateValue();\n    this.masked.doCommit();\n    this.updateControl();\n    this._saveSelection();\n  }\n\n  /** Handles view drop event, prevents by default */\n  _onDrop (ev: Event) {\n    ev.preventDefault();\n    ev.stopPropagation();\n  }\n\n  /** Restore last selection on focus */\n  _onFocus (ev: Event) {\n    this.alignCursorFriendly();\n  }\n\n  /** Restore last selection on focus */\n  _onClick (ev: Event) {\n    this.alignCursorFriendly();\n  }\n\n  _onUndo () {\n    this._applyHistoryState(this.history.undo());\n  }\n\n  _onRedo () {\n    this._applyHistoryState(this.history.redo());\n  }\n\n  _applyHistoryState (state: InputHistoryState | undefined) {\n    if (!state) return;\n\n    this._historyChanging = true;\n    this.unmaskedValue = state.unmaskedValue;\n    this.el.select(state.selection.start, state.selection.end);\n    this._saveSelection();\n    this._historyChanging = false;\n  }\n\n  /** Unbind view events and removes element reference */\n  destroy () {\n    this._unbindEvents();\n    (this._listeners as any).length = 0;\n    delete (this as any).el;\n  }\n}\n\n\nIMask.InputMask = InputMask;\n", "import IMask from \"./holder\";\n\n\nexport\ntype ChangeDetailsOptions = Pick<ChangeDetails,\n  | 'inserted'\n  | 'tailShift'\n  | 'rawInserted'\n  | 'skip'\n>;\n\n/** Provides details of changing model value */\nexport default\nclass ChangeDetails {\n  /** Inserted symbols */\n  declare inserted: string;\n  /** Additional offset if any changes occurred before tail */\n  declare tailShift: number;\n  /** Raw inserted is used by dynamic mask */\n  declare rawInserted: string;\n  /** Can skip chars */\n  declare skip: boolean;\n\n\n  static normalize (prep: string | [string, ChangeDetails]): [string, ChangeDetails] {\n    return Array.isArray(prep) ? prep : [\n      prep,\n      new ChangeDetails(),\n    ];\n  }\n\n  constructor (details?: Partial<ChangeDetailsOptions>) {\n    Object.assign(this, {\n      inserted: '',\n      rawInserted: '',\n      tailShift: 0,\n      skip: false,\n    }, details);\n  }\n\n  /** Aggregate changes */\n  aggregate (details: ChangeDetails): this {\n    this.inserted += details.inserted;\n    this.rawInserted += details.rawInserted;\n    this.tailShift += details.tailShift;\n    this.skip = this.skip || details.skip;\n\n    return this;\n  }\n\n  /** Total offset considering all changes */\n  get offset (): number {\n    return this.tailShift + this.inserted.length;\n  }\n\n  get consumed (): boolean {\n    return Boolean(this.rawInserted) || this.skip;\n  }\n\n  equals (details: ChangeDetails): boolean {\n    return this.inserted === details.inserted &&\n      this.tailShift === details.tailShift &&\n      this.rawInserted === details.rawInserted &&\n      this.skip === details.skip\n    ;\n  }\n}\n\n\nIMask.ChangeDetails = ChangeDetails;\n", "import type { TailDetails, AppendTail } from './tail-details';\nimport type ChangeDetails from './change-details';\n\n\ntype ContinuousTailState = Pick<ContinuousTailDetails,\n  | 'value'\n  | 'from'\n  | 'stop'\n>;\n\n/** Provides details of continuous extracted tail */\nexport default\nclass ContinuousTailDetails implements TailDetails {\n  /** Tail value as string */\n  declare value: string;\n  /** Tail start position */\n  declare from: number;\n  /** Start position */\n  declare stop?: number;\n\n  constructor (value: string='', from: number=0, stop?: number) {\n    this.value = value;\n    this.from = from;\n    this.stop = stop;\n  }\n\n  toString (): string { return this.value; }\n\n  extend (tail: string | TailDetails): void {\n    this.value += String(tail);\n  }\n\n  appendTo (masked: AppendTail): ChangeDetails {\n    return masked.append(this.toString(), { tail: true })\n      .aggregate(masked._appendPlaceholder());\n  }\n\n  get state (): ContinuousTailState {\n    return {\n      value: this.value,\n      from: this.from,\n      stop: this.stop,\n    };\n  }\n\n  set state (state: ContinuousTailState) {\n    Object.assign(this, state);\n  }\n\n  unshift (beforePos?: number): string {\n    if (!this.value.length || (beforePos != null && this.from >= beforePos)) return '';\n\n    const shiftChar = this.value[0];\n    this.value = this.value.slice(1);\n    return shiftChar;\n  }\n\n  shift (): string {\n    if (!this.value.length) return '';\n\n    const shiftChar = this.value[this.value.length-1];\n    this.value = this.value.slice(0, -1);\n    return shiftChar;\n  }\n}\n", "import ChangeDetails from '../core/change-details';\nimport ContinuousTailDetails from '../core/continuous-tail-details';\nimport { type Direction, DIRECTION, isString, forceDirection, objectIncludes } from '../core/utils';\nimport { type TailDetails } from '../core/tail-details';\nimport IMask from '../core/holder';\n\n\nexport\ntype MaskedState = {\n  _value: string,\n  _rawInputValue: string,\n};\n\n/** Append flags */\nexport\ntype AppendFlags<State=MaskedState> = {\n  input?: boolean,\n  tail?: boolean,\n  raw?: boolean,\n  _beforeTailState?: State,\n};\n\n/** Extract flags */\nexport\ntype ExtractFlags = {\n  raw?: boolean\n};\n\n// see https://github.com/microsoft/TypeScript/issues/6223\n\nexport\ntype MaskedOptions<M extends Masked=Masked, Props extends keyof M=never> = Partial<Pick<M,\n  | 'mask'\n  | 'parent'\n  | 'prepare'\n  | 'prepareChar'\n  | 'validate'\n  | 'commit'\n  | 'format'\n  | 'parse'\n  | 'overwrite'\n  | 'eager'\n  | 'skipInvalid'\n  | 'autofix'\n  | Props\n>>;\n\n\n/** Provides common masking stuff */\nexport default\nabstract class Masked<Value=any> {\n  static DEFAULTS: Pick<MaskedOptions, 'skipInvalid'> = {\n    skipInvalid: true,\n  };\n  static EMPTY_VALUES: Array<any> = [undefined, null, ''];\n\n  /** */\n  declare mask: unknown;\n  /** */\n  declare parent?: Masked;\n  /** Transforms value before mask processing */\n  declare prepare?: (chars: string, masked: Masked, flags: AppendFlags) => string | [string, ChangeDetails];\n  /** Transforms each char before mask processing */\n  declare prepareChar?: (chars: string, masked: Masked, flags: AppendFlags) => string | [string, ChangeDetails];\n  /** Validates if value is acceptable */\n  declare validate?: (value: string, masked: Masked, flags: AppendFlags) => boolean;\n  /** Does additional processing at the end of editing */\n  declare commit?: (value: string, masked: Masked) => void;\n  /** Format typed value to string */\n  declare format?: (value: Value, masked: Masked) => string;\n  /** Parse string to get typed value */\n  declare parse?: (str: string, masked: Masked) => Value;\n  /** Enable characters overwriting */\n  abstract overwrite?: boolean | 'shift' | undefined;\n  /** */\n  abstract eager?: boolean | 'remove' | 'append' | undefined;\n  /** */\n  abstract skipInvalid?: boolean | undefined;\n  /** */\n  abstract autofix?: boolean | 'pad' | undefined;\n\n  /** */\n  declare _initialized: boolean;\n\n  declare _value: string;\n  declare _refreshing?: boolean;\n  declare _isolated?: boolean;\n\n  constructor (opts: MaskedOptions) {\n    this._value = '';\n    this._update({\n      ...Masked.DEFAULTS,\n      ...opts,\n    });\n    this._initialized = true;\n  }\n\n  /** Sets and applies new options */\n  updateOptions (opts: Partial<MaskedOptions>) {\n    if (!this.optionsIsChanged(opts)) return;\n\n    this.withValueRefresh(this._update.bind(this, opts));\n  }\n\n  /** Sets new options */\n  _update (opts: Partial<MaskedOptions>) {\n    Object.assign(this, opts);\n  }\n\n  /** Mask state */\n  get state (): MaskedState {\n    return {\n      _value: this.value,\n      _rawInputValue: this.rawInputValue,\n    };\n  }\n\n  set state (state: MaskedState) {\n    this._value = state._value;\n  }\n\n  /** Resets value */\n  reset () {\n    this._value = '';\n  }\n\n  get value (): string {\n    return this._value;\n  }\n\n  set value (value: string) {\n    this.resolve(value, { input: true });\n  }\n\n  /** Resolve new value */\n  resolve (value: string, flags: AppendFlags={ input: true }): void {\n    this.reset();\n    this.append(value, flags, '');\n    this.doCommit();\n  }\n\n  get unmaskedValue (): string {\n    return this.value;\n  }\n\n  set unmaskedValue (value: string) {\n    this.resolve(value, {});\n  }\n\n  get typedValue (): Value {\n    return this.parse ? this.parse(this.value, this) : this.unmaskedValue as Value;\n  }\n\n  set typedValue (value: Value) {\n    if (this.format) {\n      this.value = this.format(value, this);\n    } else {\n      this.unmaskedValue = String(value);\n    }\n  }\n\n  /** Value that includes raw user input */\n  get rawInputValue (): string {\n    return this.extractInput(0, this.displayValue.length, {raw: true});\n  }\n\n  set rawInputValue (value: string) {\n    this.resolve(value, { raw: true });\n  }\n\n  get displayValue (): string {\n    return this.value;\n  }\n\n  get isComplete (): boolean {\n    return true;\n  }\n\n  get isFilled (): boolean {\n    return this.isComplete;\n  }\n\n  /** Finds nearest input position in direction */\n  nearestInputPos (cursorPos: number, direction?: Direction): number {\n    return cursorPos;\n  }\n\n  totalInputPositions (fromPos: number=0, toPos: number=this.displayValue.length): number {\n    return Math.min(this.displayValue.length, toPos - fromPos);\n  }\n\n  /** Extracts value in range considering flags */\n  extractInput (fromPos: number=0, toPos: number=this.displayValue.length, flags?: ExtractFlags): string {\n    return this.displayValue.slice(fromPos, toPos);\n  }\n\n  /** Extracts tail in range */\n  extractTail (fromPos: number=0, toPos: number=this.displayValue.length): TailDetails {\n    return new ContinuousTailDetails(this.extractInput(fromPos, toPos), fromPos);\n  }\n\n  /** Appends tail */\n  appendTail (tail: string | String | TailDetails): ChangeDetails {\n    if (isString(tail)) tail = new ContinuousTailDetails(String(tail));\n\n    return (tail as TailDetails).appendTo(this);\n  }\n\n  /** Appends char */\n  _appendCharRaw (ch: string, flags: AppendFlags={}): ChangeDetails {\n    if (!ch) return new ChangeDetails();\n\n    this._value += ch;\n    return new ChangeDetails({\n      inserted: ch,\n      rawInserted: ch,\n    });\n  }\n\n  /** Appends char */\n  _appendChar (ch: string, flags: AppendFlags={}, checkTail?: TailDetails): ChangeDetails {\n    const consistentState = this.state;\n    let details: ChangeDetails;\n    [ch, details] = this.doPrepareChar(ch, flags);\n\n    if (ch) {\n      details = details.aggregate(this._appendCharRaw(ch, flags));\n\n      // TODO handle `skip`?\n\n      // try `autofix` lookahead\n      if (!details.rawInserted && this.autofix === 'pad') {\n        const noFixState = this.state;\n        this.state = consistentState;\n\n        let fixDetails = this.pad(flags);\n        const chDetails = this._appendCharRaw(ch, flags);\n        fixDetails = fixDetails.aggregate(chDetails);\n\n        // if fix was applied or\n        // if details are equal use skip restoring state optimization\n        if (chDetails.rawInserted || fixDetails.equals(details)) {\n          details = fixDetails;\n        } else {\n          this.state = noFixState;\n        }\n      }\n    }\n\n    if (details.inserted) {\n      let consistentTail;\n      let appended = this.doValidate(flags) !== false;\n\n      if (appended && checkTail != null) {\n        // validation ok, check tail\n        const beforeTailState = this.state;\n        if (this.overwrite === true) {\n          consistentTail = checkTail.state;\n          for (let i=0; i < details.rawInserted.length; ++i) {\n            checkTail.unshift(this.displayValue.length - details.tailShift);\n          }\n        }\n\n        let tailDetails = this.appendTail(checkTail);\n        appended = tailDetails.rawInserted.length === checkTail.toString().length;\n\n        // not ok, try shift\n        if (!(appended && tailDetails.inserted) && this.overwrite === 'shift') {\n          this.state = beforeTailState;\n          consistentTail = checkTail.state;\n          for (let i=0; i < details.rawInserted.length; ++i) {\n            checkTail.shift();\n          }\n\n          tailDetails = this.appendTail(checkTail);\n          appended = tailDetails.rawInserted.length === checkTail.toString().length;\n        }\n\n        // if ok, rollback state after tail\n        if (appended && tailDetails.inserted) this.state = beforeTailState;\n      }\n\n      // revert all if something went wrong\n      if (!appended) {\n        details = new ChangeDetails();\n        this.state = consistentState;\n        if (checkTail && consistentTail) checkTail.state = consistentTail;\n      }\n    }\n    return details;\n  }\n\n  /** Appends optional placeholder at the end */\n  _appendPlaceholder (): ChangeDetails {\n    return new ChangeDetails();\n  }\n\n  /** Appends optional eager placeholder at the end */\n  _appendEager (): ChangeDetails {\n    return new ChangeDetails();\n  }\n\n  /** Appends symbols considering flags */\n  append (str: string, flags?: AppendFlags, tail?: string | String | TailDetails): ChangeDetails {\n    if (!isString(str)) throw new Error('value should be string');\n    const checkTail = isString(tail) ? new ContinuousTailDetails(String(tail)) : tail as TailDetails;\n    if (flags?.tail) flags._beforeTailState = this.state;\n\n    let details;\n    [str, details] = this.doPrepare(str, flags);\n\n    for (let ci=0; ci<str.length; ++ci) {\n      const d = this._appendChar(str[ci], flags, checkTail);\n      if (!d.rawInserted && !this.doSkipInvalid(str[ci], flags, checkTail)) break;\n      details.aggregate(d);\n    }\n\n    if ((this.eager === true || this.eager === 'append') && flags?.input && str) {\n      details.aggregate(this._appendEager());\n    }\n\n    // append tail but aggregate only tailShift\n    if (checkTail != null) {\n      details.tailShift += this.appendTail(checkTail).tailShift;\n      // TODO it's a good idea to clear state after appending ends\n      // but it causes bugs when one append calls another (when dynamic dispatch set rawInputValue)\n      // this._resetBeforeTailState();\n    }\n\n    return details;\n  }\n\n  remove (fromPos: number=0, toPos: number=this.displayValue.length): ChangeDetails {\n    this._value = this.displayValue.slice(0, fromPos) + this.displayValue.slice(toPos);\n    return new ChangeDetails();\n  }\n\n  /** Calls function and reapplies current value */\n  withValueRefresh<T>(fn: () => T): T {\n    if (this._refreshing || !this._initialized) return fn();\n    this._refreshing = true;\n\n    const rawInput = this.rawInputValue;\n    const value = this.value;\n\n    const ret = fn();\n\n    this.rawInputValue = rawInput;\n    // append lost trailing chars at the end\n    if (this.value && this.value !== value && value.indexOf(this.value) === 0) {\n      this.append(value.slice(this.displayValue.length), {}, '');\n      this.doCommit();\n    }\n\n    delete this._refreshing;\n    return ret;\n  }\n\n  runIsolated<T>(fn: (masked: this) => T): T {\n    if (this._isolated || !this._initialized) return fn(this);\n    this._isolated = true;\n    const state = this.state;\n\n    const ret = fn(this);\n\n    this.state = state;\n    delete this._isolated;\n\n    return ret;\n  }\n\n  doSkipInvalid (ch: string, flags: AppendFlags={}, checkTail?: TailDetails): boolean {\n    return Boolean(this.skipInvalid);\n  }\n\n  /** Prepares string before mask processing */\n  doPrepare (str: string, flags: AppendFlags={}): [string, ChangeDetails] {\n    return ChangeDetails.normalize(this.prepare ?\n      this.prepare(str, this, flags) :\n      str);\n  }\n\n  /** Prepares each char before mask processing */\n  doPrepareChar (str: string, flags: AppendFlags={}): [string, ChangeDetails] {\n    return ChangeDetails.normalize(this.prepareChar ?\n      this.prepareChar(str, this, flags) :\n      str);\n  }\n\n  /** Validates if value is acceptable */\n  doValidate (flags: AppendFlags): boolean {\n    return (!this.validate || this.validate(this.value, this, flags)) &&\n      (!this.parent || this.parent.doValidate(flags));\n  }\n\n  /** Does additional processing at the end of editing */\n  doCommit () {\n    if (this.commit) this.commit(this.value, this);\n  }\n\n  splice (start: number, deleteCount: number, inserted='', removeDirection: Direction = DIRECTION.NONE, flags: AppendFlags = { input: true }): ChangeDetails {\n    const tailPos: number = start + deleteCount;\n    const tail: TailDetails = this.extractTail(tailPos);\n\n    const eagerRemove = this.eager === true || this.eager === 'remove';\n\n    let oldRawValue;\n    if (eagerRemove)  {\n      removeDirection = forceDirection(removeDirection);\n      oldRawValue = this.extractInput(0, tailPos, {raw: true});\n    }\n\n    let startChangePos: number = start;\n    const details: ChangeDetails = new ChangeDetails();\n\n    // if it is just deletion without insertion\n    if (removeDirection !== DIRECTION.NONE) {\n      startChangePos = this.nearestInputPos(start,\n        deleteCount > 1 && start !== 0 && !eagerRemove ?\n        DIRECTION.NONE :\n        removeDirection\n      );\n\n      // adjust tailShift if start was aligned\n      details.tailShift = startChangePos - start;\n    }\n\n    details.aggregate(this.remove(startChangePos));\n\n    if (eagerRemove && removeDirection !== DIRECTION.NONE && oldRawValue === this.rawInputValue) {\n      if (removeDirection === DIRECTION.FORCE_LEFT) {\n        let valLength;\n        while (oldRawValue === this.rawInputValue && (valLength = this.displayValue.length)) {\n          details\n            .aggregate(new ChangeDetails({ tailShift: -1 }))\n            .aggregate(this.remove(valLength-1));\n        }\n      } else if (removeDirection === DIRECTION.FORCE_RIGHT) {\n        tail.unshift();\n      }\n    }\n\n    return details.aggregate(this.append(inserted, flags, tail));\n  }\n\n  maskEquals (mask: any): boolean {\n    return this.mask === mask;\n  }\n\n  optionsIsChanged (opts: Partial<MaskedOptions>): boolean {\n    return !objectIncludes(this, opts);\n  }\n\n  typedValueEquals (value: any): boolean {\n    const tval = this.typedValue;\n\n    return value === tval ||\n      Masked.EMPTY_VALUES.includes(value) && Masked.EMPTY_VALUES.includes(tval) ||\n      (this.format ? this.format(value, this) === this.format(this.typedValue, this) : false);\n  }\n\n  pad (flags?: AppendFlags): ChangeDetails {\n    return new ChangeDetails();\n  }\n}\n\n\nIMask.Masked = Masked;\n", "import type { TailDetails, AppendTail } from '../../core/tail-details';\nimport ChangeDetails from '../../core/change-details';\nimport { isString } from '../../core/utils';\nimport ContinuousTailDetails from '../../core/continuous-tail-details';\nimport IMask from '../../core/holder';\nimport type MaskedPattern from '../pattern';\n\n\nexport\ntype ChunksTailState = Pick<ChunksTailDetails,\n  | 'from'\n  | 'stop'\n  | 'blockIndex'\n> & { chunks: Array<TailDetails['state']> };\n\nexport default\nclass ChunksTailDetails implements TailDetails {\n  declare chunks: Array<TailDetails>;\n  declare from: number;\n  declare stop?: number;\n  /** */\n  declare blockIndex?: number;\n\n  constructor (chunks: Array<TailDetails>=[], from: number=0) {\n    this.chunks = chunks;\n    this.from = from;\n  }\n\n  toString (): string {\n    return this.chunks.map(String).join('');\n  }\n\n  extend (tailChunk: string | String | TailDetails): void {\n    if (!String(tailChunk)) return;\n    tailChunk = (isString(tailChunk) ? new ContinuousTailDetails(String(tailChunk)) : tailChunk) as TailDetails;\n\n    const lastChunk = this.chunks[this.chunks.length-1];\n    const extendLast = lastChunk &&\n      // if stops are same or tail has no stop\n      (lastChunk.stop === tailChunk.stop || tailChunk.stop == null) &&\n      // if tail chunk goes just after last chunk\n      tailChunk.from === (lastChunk.from + lastChunk.toString().length);\n\n    if (tailChunk instanceof ContinuousTailDetails) {\n      // check the ability to extend previous chunk\n      if (extendLast) {\n        // extend previous chunk\n        lastChunk.extend(tailChunk.toString());\n      } else {\n        // append new chunk\n        this.chunks.push(tailChunk);\n      }\n    } else if (tailChunk instanceof ChunksTailDetails) {\n      if (tailChunk.stop == null) {\n        // unwrap floating chunks to parent, keeping `from` pos\n        let firstTailChunk;\n        while (tailChunk.chunks.length && tailChunk.chunks[0].stop == null) {\n          firstTailChunk = tailChunk.chunks.shift() as TailDetails;  // not possible to be `undefined` because length was checked above\n          firstTailChunk.from += tailChunk.from;\n          this.extend(firstTailChunk);\n        }\n      }\n\n      // if tail chunk still has value\n      if (tailChunk.toString()) {\n        // if chunks contains stops, then popup stop to container\n        tailChunk.stop = tailChunk.blockIndex;\n        this.chunks.push(tailChunk);\n      }\n    }\n  }\n\n  appendTo (masked: AppendTail | MaskedPattern): ChangeDetails {\n    if (!(masked instanceof IMask.MaskedPattern)) {\n      const tail = new ContinuousTailDetails(this.toString());\n      return tail.appendTo(masked);\n    }\n\n    const details = new ChangeDetails();\n\n    for (let ci=0; ci < this.chunks.length; ++ci) {\n      const chunk = this.chunks[ci];\n\n      const lastBlockIter = masked._mapPosToBlock(masked.displayValue.length);\n      const stop = chunk.stop;\n      let chunkBlock;\n      if (stop != null &&\n        // if block not found or stop is behind lastBlock\n        (!lastBlockIter || lastBlockIter.index <= stop)\n      ) {\n        if (\n          chunk instanceof ChunksTailDetails ||\n          // for continuous block also check if stop is exist\n          masked._stops.indexOf(stop) >= 0\n        ) {\n          details.aggregate(masked._appendPlaceholder(stop));\n        }\n        chunkBlock = chunk instanceof ChunksTailDetails && masked._blocks[stop];\n      }\n\n      if (chunkBlock) {\n        const tailDetails = chunkBlock.appendTail(chunk);\n        details.aggregate(tailDetails);\n\n        // get not inserted chars\n        const remainChars = chunk.toString().slice(tailDetails.rawInserted.length);\n        if (remainChars) details.aggregate(masked.append(remainChars, { tail: true }));\n      } else {\n        details.aggregate(masked.append(chunk.toString(), { tail: true }));\n      }\n    }\n\n    return details;\n  }\n\n  get state (): ChunksTailState {\n    return {\n      chunks: this.chunks.map(c => c.state),\n      from: this.from,\n      stop: this.stop,\n      blockIndex: this.blockIndex,\n    };\n  }\n\n  set state (state: ChunksTailState) {\n    const { chunks, ...props } = state;\n    Object.assign(this, props);\n    this.chunks = chunks.map(cstate => {\n      const chunk = \"chunks\" in cstate ?\n        new ChunksTailDetails() :\n        new ContinuousTailDetails();\n      chunk.state = cstate;\n      return chunk;\n    });\n  }\n\n  unshift (beforePos?: number): string {\n    if (!this.chunks.length || (beforePos != null && this.from >= beforePos)) return '';\n\n    const chunkShiftPos = beforePos != null ? beforePos - this.from : beforePos;\n    let ci=0;\n    while (ci < this.chunks.length) {\n      const chunk = this.chunks[ci];\n      const shiftChar = chunk.unshift(chunkShiftPos);\n\n      if (chunk.toString()) {\n        // chunk still contains value\n        // but not shifted - means no more available chars to shift\n        if (!shiftChar) break;\n        ++ci;\n      } else {\n        // clean if chunk has no value\n        this.chunks.splice(ci, 1);\n      }\n\n      if (shiftChar) return shiftChar;\n    }\n\n    return '';\n  }\n\n  shift (): string {\n    if (!this.chunks.length) return '';\n\n    let ci=this.chunks.length-1;\n    while (0 <= ci) {\n      const chunk = this.chunks[ci];\n      const shiftChar = chunk.shift();\n\n      if (chunk.toString()) {\n        // chunk still contains value\n        // but not shifted - means no more available chars to shift\n        if (!shiftChar) break;\n        --ci;\n      } else {\n        // clean if chunk has no value\n        this.chunks.splice(ci, 1);\n      }\n\n      if (shiftChar) return shiftChar;\n    }\n\n    return '';\n  }\n}\n", "import { DIRECTION } from '../../core/utils';\nimport type MaskedPattern from '../pattern';\nimport type Pat<PERSON><PERSON><PERSON> from './block';\n\n\ntype PatternCursorState = { offset: number, index: number, ok: boolean };\n\nexport default\nclass PatternCursor<Value> {\n  declare masked: MaskedPattern<Value>;\n  declare offset: number;\n  declare index: number;\n  declare ok: boolean;\n  declare _log: PatternCursorState[];\n\n  constructor (masked: MaskedPattern<Value>, pos: number) {\n    this.masked = masked;\n    this._log = [];\n\n    const { offset, index } = masked._mapPosToBlock(pos) || (\n      pos < 0 ?\n        // first\n        { index: 0, offset: 0 } :\n        // last\n        { index: this.masked._blocks.length, offset: 0 }\n    );\n    this.offset = offset;\n    this.index = index;\n    this.ok = false;\n  }\n\n  get block (): PatternBlock {\n    return this.masked._blocks[this.index];\n  }\n\n  get pos (): number {\n    return this.masked._blockStartPos(this.index) + this.offset;\n  }\n\n  get state (): PatternCursorState {\n    return { index: this.index, offset: this.offset, ok: this.ok };\n  }\n\n  set state (s: PatternCursorState) {\n    Object.assign(this, s);\n  }\n\n  pushState () {\n    this._log.push(this.state);\n  }\n\n  popState (): PatternCursorState | undefined {\n    const s = this._log.pop();\n    if (s) this.state = s;\n    return s;\n  }\n\n  bindBlock () {\n    if (this.block) return;\n    if (this.index < 0) {\n      this.index = 0;\n      this.offset = 0;\n    }\n    if (this.index >= this.masked._blocks.length) {\n      this.index = this.masked._blocks.length - 1;\n      this.offset = (this.block as unknown as PatternBlock).displayValue.length; // TODO this is stupid type error, `block` depends on index that was changed above\n    }\n  }\n\n  _pushLeft(fn: () => boolean | undefined): boolean {\n    this.pushState();\n    for (this.bindBlock(); 0<=this.index; --this.index, this.offset=this.block?.displayValue.length || 0) {\n      if (fn()) return this.ok = true;\n    }\n\n    return this.ok = false;\n  }\n\n  _pushRight (fn: () => boolean | undefined): boolean {\n    this.pushState();\n    for (this.bindBlock(); this.index<this.masked._blocks.length; ++this.index, this.offset=0) {\n      if (fn()) return this.ok = true;\n    }\n\n    return this.ok = false;\n  }\n\n  pushLeftBeforeFilled (): boolean {\n    return this._pushLeft(() => {\n      if (this.block.isFixed || !this.block.value) return;\n\n      this.offset = this.block.nearestInputPos(this.offset, DIRECTION.FORCE_LEFT);\n      if (this.offset !== 0) return true;\n    });\n  }\n\n  pushLeftBeforeInput (): boolean {\n    // cases:\n    // filled input: 00|\n    // optional empty input: 00[]|\n    // nested block: XX<[]>|\n    return this._pushLeft(() => {\n      if (this.block.isFixed) return;\n\n      this.offset = this.block.nearestInputPos(this.offset, DIRECTION.LEFT);\n      return true;\n    });\n  }\n\n  pushLeftBeforeRequired (): boolean {\n    return this._pushLeft(() => {\n      if (this.block.isFixed || this.block.isOptional && !this.block.value) return;\n\n      this.offset = this.block.nearestInputPos(this.offset, DIRECTION.LEFT);\n      return true;\n    });\n  }\n\n  pushRightBeforeFilled (): boolean {\n    return this._pushRight(() => {\n      if (this.block.isFixed || !this.block.value) return;\n\n      this.offset = this.block.nearestInputPos(this.offset, DIRECTION.FORCE_RIGHT);\n      if (this.offset !== this.block.value.length) return true;\n    });\n  }\n\n  pushRightBeforeInput (): boolean {\n    return this._pushRight(() => {\n      if (this.block.isFixed) return;\n\n      // const o = this.offset;\n      this.offset = this.block.nearestInputPos(this.offset, DIRECTION.NONE);\n      // HACK cases like (STILL DOES NOT WORK FOR NESTED)\n      // aa|X\n      // aa<X|[]>X_    - this will not work\n      // if (o && o === this.offset && this.block instanceof PatternInputDefinition) continue;\n      return true;\n    });\n  }\n\n  pushRightBeforeRequired (): boolean {\n    return this._pushRight(() => {\n      if (this.block.isFixed || this.block.isOptional && !this.block.value) return;\n\n      // TODO check |[*]XX_\n      this.offset = this.block.nearestInputPos(this.offset, DIRECTION.NONE);\n      return true;\n    });\n  }\n}\n", "import ChangeDetails from '../../core/change-details';\nimport { DIRECTION, type Direction, isString } from '../../core/utils';\nimport { type TailDetails } from '../../core/tail-details';\nimport ContinuousTailDetails from '../../core/continuous-tail-details';\nimport { type ExtractFlags, type AppendFlags, type MaskedState } from '../base';\nimport type PatternBlock from './block';\n\n\nexport\ntype PatternFixedDefinitionOptions = Pick<PatternFixedDefinition, 'char' | 'isUnmasking' | 'eager'>;\n\n\nexport default\nclass PatternFixedDefinition implements PatternBlock {\n  /** */\n  declare _value: string;\n  /** */\n  declare char: string;\n  /** */\n  declare isUnmasking?: boolean;\n  /** */\n  declare eager: boolean | 'remove' | 'append' | undefined;\n  /** */\n  declare _isRawInput?: boolean;\n  /** */\n  declare isFixed: boolean;\n\n  constructor(opts: PatternFixedDefinitionOptions) {\n    Object.assign(this, opts);\n    this._value = '';\n    this.isFixed = true;\n  }\n\n  get value (): string {\n    return this._value;\n  }\n\n  get unmaskedValue (): string {\n    return this.isUnmasking ? this.value : '';\n  }\n\n  get rawInputValue (): string {\n    return this._isRawInput ? this.value : '';\n  }\n\n  get displayValue (): string {\n    return this.value;\n  }\n\n  reset () {\n    this._isRawInput = false;\n    this._value = '';\n  }\n\n  remove (fromPos: number=0, toPos: number=this._value.length): ChangeDetails {\n    this._value = this._value.slice(0, fromPos) + this._value.slice(toPos);\n    if (!this._value) this._isRawInput = false;\n\n    return new ChangeDetails();\n  }\n\n  nearestInputPos (cursorPos: number, direction: Direction=DIRECTION.NONE): number {\n    const minPos = 0;\n    const maxPos = this._value.length;\n\n    switch (direction) {\n      case DIRECTION.LEFT:\n      case DIRECTION.FORCE_LEFT:\n        return minPos;\n      case DIRECTION.NONE:\n      case DIRECTION.RIGHT:\n      case DIRECTION.FORCE_RIGHT:\n      default:\n        return maxPos;\n    }\n  }\n\n  totalInputPositions (fromPos: number=0, toPos: number=this._value.length): number {\n    return this._isRawInput ? (toPos - fromPos) : 0;\n  }\n\n  extractInput (fromPos: number=0, toPos: number=this._value.length, flags: ExtractFlags={}): string {\n    return flags.raw && this._isRawInput && this._value.slice(fromPos, toPos) || '';\n  }\n\n  get isComplete (): boolean {\n    return true;\n  }\n\n  get isFilled (): boolean {\n    return Boolean(this._value);\n  }\n\n  _appendChar (ch: string, flags: AppendFlags={}): ChangeDetails {\n    if (this.isFilled) return new ChangeDetails();\n    const appendEager = this.eager === true || this.eager === 'append';\n\n    const appended = this.char === ch;\n    const isResolved = appended && (this.isUnmasking || flags.input || flags.raw) && (!flags.raw || !appendEager) && !flags.tail;\n    const details = new ChangeDetails({\n      inserted: this.char,\n      rawInserted: isResolved ? this.char: '',\n    })\n    this._value = this.char;\n    this._isRawInput = isResolved && (flags.raw || flags.input);\n\n    return details;\n  }\n\n  _appendEager (): ChangeDetails {\n    return this._appendChar(this.char, { tail: true });\n  }\n\n  _appendPlaceholder (): ChangeDetails {\n    const details = new ChangeDetails();\n    if (this.isFilled) return details;\n\n    this._value = details.inserted = this.char;\n    return details;\n  }\n\n  extractTail (): TailDetails {\n    return new ContinuousTailDetails('');\n  }\n\n  appendTail (tail: string | String | TailDetails): ChangeDetails {\n    if (isString(tail)) tail = new ContinuousTailDetails(String(tail));\n\n    return (tail as TailDetails).appendTo(this);\n  }\n\n  append (str: string, flags?: AppendFlags, tail?: TailDetails): ChangeDetails {\n    const details = this._appendChar(str[0], flags);\n\n    if (tail != null) {\n      details.tailShift += this.appendTail(tail).tailShift;\n    }\n\n    return details;\n  }\n\n  doCommit () {}\n\n  get state (): MaskedState {\n    return {\n      _value: this._value,\n      _rawInputValue: this.rawInputValue,\n    };\n  }\n\n  set state (state: MaskedState) {\n    this._value = state._value;\n    this._isRawInput = Boolean(state._rawInputValue);\n  }\n\n  pad (flags?: AppendFlags): ChangeDetails {\n    return this._appendPlaceholder();\n  }\n}\n", "import createMask, { type FactoryArg, type FactoryOpts, type FactoryReturnMasked } from '../factory';\nimport type Masked from '../base';\nimport type MaskedPattern from '../pattern';\nimport { type TailDetails } from '../../core/tail-details';\nimport { type ExtractFlags, type AppendFlags, type MaskedState } from '../base';\nimport ChangeDetails from '../../core/change-details';\nimport { DIRECTION, type Direction } from '../../core/utils';\nimport type PatternBlock from './block';\n\n\nexport\ntype PatternInputDefinitionOptions<Opts extends FactoryOpts> =\n  Omit<Opts,\n  | 'parent'\n  | 'isOptional'\n  | 'lazy'\n  | 'eager'\n  | 'placeholderChar'\n  | 'displayChar'> &\n  Partial<Pick<PatternInputDefinition,\n  | 'parent'\n  | 'isOptional'\n  | 'lazy'\n  | 'eager'\n  | 'placeholderChar'\n  | 'displayChar'\n>>;\n\nexport\ntype PatternInputDefinitionState<Opts extends FactoryArg> = MaskedState & {\n  masked: FactoryReturnMasked<Opts>['state'],\n  isFilled: boolean,\n};\n\n\nexport default\nclass PatternInputDefinition<Opts extends FactoryOpts=any> implements PatternBlock<PatternInputDefinitionState<Opts>> {\n  static DEFAULT_DEFINITIONS: { [k: string]: RegExp } = {\n    '0': /\\d/,\n    'a': /[\\u0041-\\u005A\\u0061-\\u007A\\u00AA\\u00B5\\u00BA\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u0527\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u08A0\\u08A2-\\u08AC\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0977\\u0979-\\u097F\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C33\\u0C35-\\u0C39\\u0C3D\\u0C58\\u0C59\\u0C60\\u0C61\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D60\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191C\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19C1-\\u19C7\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2183\\u2184\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005\\u3006\\u3031-\\u3035\\u303B\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA697\\uA6A0-\\uA6E5\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA793\\uA7A0-\\uA7AA\\uA7F8-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA80-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uABC0-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]/,  // http://stackoverflow.com/a/22075070\n    '*': /./\n  }\n\n  /** */\n  declare readonly masked: FactoryReturnMasked<Opts>;\n  /** */\n  declare parent: Masked;\n  /** */\n  declare isOptional: boolean;\n  /** */\n  declare isFilled: boolean;\n  /** */\n  declare lazy: MaskedPattern['lazy'];\n  /** */\n  declare eager: MaskedPattern['eager'];\n  /** */\n  declare placeholderChar: MaskedPattern['placeholderChar'];\n  /** */\n  declare displayChar: MaskedPattern['displayChar'];\n\n\n  constructor(opts: PatternInputDefinitionOptions<Opts>) {\n    const { parent, isOptional, placeholderChar, displayChar, lazy, eager, ...maskOpts } = opts;\n\n    this.masked = createMask(maskOpts as Opts);\n    Object.assign(this, { parent, isOptional, placeholderChar, displayChar, lazy, eager });\n  }\n\n  reset () {\n    this.isFilled = false;\n    this.masked.reset();\n  }\n\n  remove (fromPos: number=0, toPos: number=this.value.length): ChangeDetails {\n    if (fromPos === 0 && toPos >= 1) {\n      this.isFilled = false;\n      return this.masked.remove(fromPos, toPos);\n    }\n\n    return new ChangeDetails();\n  }\n\n  get value (): string {\n    return this.masked.value ||\n      (this.isFilled && !this.isOptional ?\n        this.placeholderChar :\n        '');\n  }\n\n  get unmaskedValue (): string {\n    return this.masked.unmaskedValue;\n  }\n\n  get rawInputValue (): string {\n    return this.masked.rawInputValue;\n  }\n\n  get displayValue (): string {\n    return this.masked.value && this.displayChar || this.value;\n  }\n\n  get isComplete (): boolean {\n    return Boolean(this.masked.value) || this.isOptional;\n  }\n\n  _appendChar (ch: string, flags: AppendFlags<PatternInputDefinitionState<Opts>>={}): ChangeDetails {\n    if (this.isFilled) return new ChangeDetails();\n\n    const state = this.masked.state;\n    // simulate input\n    let details = this.masked._appendChar(ch, this.currentMaskFlags(flags));\n\n    if (details.inserted && this.doValidate(flags) === false) {\n      details = new ChangeDetails();\n      this.masked.state = state;\n    }\n\n    if (!details.inserted && !this.isOptional && !this.lazy && !flags.input) {\n      details.inserted = this.placeholderChar;\n    }\n    details.skip = !details.inserted && !this.isOptional;\n    this.isFilled = Boolean(details.inserted);\n\n    return details;\n  }\n\n  append (str: string, flags?: AppendFlags<PatternInputDefinitionState<Opts>>, tail?: TailDetails): ChangeDetails {\n    // TODO probably should be done via _appendChar\n    return this.masked.append(str, this.currentMaskFlags(flags), tail);\n  }\n\n  _appendPlaceholder (): ChangeDetails {\n    if (this.isFilled || this.isOptional) return new ChangeDetails();\n\n    this.isFilled = true;\n    return new ChangeDetails({ inserted: this.placeholderChar });\n  }\n\n  _appendEager (): ChangeDetails {\n    return new ChangeDetails();\n  }\n\n  extractTail (fromPos?: number, toPos?: number): TailDetails {\n    return this.masked.extractTail(fromPos, toPos);\n  }\n\n  appendTail (tail: string | TailDetails): ChangeDetails {\n    return this.masked.appendTail(tail);\n  }\n\n  extractInput (fromPos: number=0, toPos: number=this.value.length, flags?: ExtractFlags): string {\n    return this.masked.extractInput(fromPos, toPos, flags);\n  }\n\n  nearestInputPos (cursorPos: number, direction: Direction=DIRECTION.NONE): number {\n    const minPos = 0;\n    const maxPos = this.value.length;\n    const boundPos = Math.min(Math.max(cursorPos, minPos), maxPos);\n\n    switch (direction) {\n      case DIRECTION.LEFT:\n      case DIRECTION.FORCE_LEFT:\n        return this.isComplete ? boundPos : minPos;\n      case DIRECTION.RIGHT:\n      case DIRECTION.FORCE_RIGHT:\n        return this.isComplete ? boundPos : maxPos;\n      case DIRECTION.NONE:\n      default: return boundPos;\n    }\n  }\n\n  totalInputPositions (fromPos: number=0, toPos: number=this.value.length): number {\n    return this.value.slice(fromPos, toPos).length;\n  }\n\n  doValidate (flags: AppendFlags<PatternInputDefinitionState<Opts>>): boolean {\n    return this.masked.doValidate(this.currentMaskFlags(flags)) && (\n      !this.parent || this.parent.doValidate(this.currentMaskFlags(flags)));\n  }\n\n  doCommit () {\n    this.masked.doCommit();\n  }\n\n  get state (): PatternInputDefinitionState<Opts> {\n    return {\n      _value: this.value,\n      _rawInputValue: this.rawInputValue,\n      masked: this.masked.state,\n      isFilled: this.isFilled,\n    };\n  }\n\n  set state (state: PatternInputDefinitionState<Opts>) {\n    this.masked.state = state.masked;\n    this.isFilled = state.isFilled;\n  }\n\n  currentMaskFlags (flags?: AppendFlags<PatternInputDefinitionState<Opts>>): AppendFlags {\n    return {\n      ...flags,\n      _beforeTailState: flags?._beforeTailState?.masked || flags?._beforeTailState as unknown as MaskedState,\n    };\n  }\n\n  pad (flags?: AppendFlags): ChangeDetails {\n    return new ChangeDetails();\n  }\n}\n", "import Masked, { type MaskedOptions } from './base';\nimport IMask from '../core/holder';\n\n\nexport\ntype MaskedRegExpOptions = MaskedOptions<MaskedRegExp>;\n\n/** Masking by RegExp */\nexport default\nclass MaskedRegExp extends Masked<string> {\n  /** */\n  declare mask: RegExp;\n  /** Enable characters overwriting */\n  declare overwrite?: boolean | 'shift' | undefined;\n  /** */\n  declare eager?: boolean | 'remove' | 'append' | undefined;\n  /** */\n  declare skipInvalid?: boolean | undefined;\n  /** */\n  declare autofix?: boolean | 'pad' | undefined;\n\n  override updateOptions (opts: Partial<MaskedRegExpOptions>) {\n    super.updateOptions(opts);\n  }\n\n  override _update (opts: Partial<MaskedRegExpOptions>) {\n    const mask = opts.mask;\n    if (mask) opts.validate = (value) => value.search(mask) >= 0;\n    super._update(opts);\n  }\n}\n\n\nIMask.MaskedRegExp = MaskedRegExp;\n", "import ChangeDetails from '../core/change-details';\nimport IMask from '../core/holder';\nimport { type TailDetails } from '../core/tail-details';\nimport { DIRECTION, type Direction } from '../core/utils';\nimport Masked, { type AppendFlags, type ExtractFlags, type MaskedOptions, type MaskedState } from './base';\nimport createMask, { type FactoryArg, normalizeOpts, type ExtendFactoryArgOptions, NormalizedOpts } from './factory';\nimport type PatternBlock from './pattern/block';\nimport ChunksTailDetails from './pattern/chunk-tail-details';\nimport PatternCursor from './pattern/cursor';\nimport PatternFixedDefinition from './pattern/fixed-definition';\nimport PatternInputDefinition from './pattern/input-definition';\nimport './regexp'; // support for default definitions which are regexp's\n\n\nexport\ntype MaskedPatternOptions<\n  Value=string,\n  M extends MaskedPattern<Value>=MaskedPattern<Value>,\n  Props extends keyof M=never,\n> = MaskedOptions<M,\n  | 'definitions'\n  | 'blocks'\n  | 'placeholderChar'\n  | 'displayChar'\n  | 'lazy'\n  | Props\n>;\n\nexport\ntype Definitions = {\n  [k: string]: FactoryArg,\n};\n\nexport\ntype MaskedPatternState = MaskedState & {\n  _blocks: Array<MaskedState>,\n};\n\nexport\ntype BlockPosData = {\n  index: number,\n  offset: number,\n};\n\nexport\ntype BlockExtraOptions = {\n  expose?: boolean,\n  repeat?: number | [number, number],\n};\n\n\n/** Pattern mask */\nexport default\nclass MaskedPattern<Value=string> extends Masked<Value> {\n  static DEFAULTS = {\n    ...Masked.DEFAULTS,\n    lazy: true,\n    placeholderChar: '_'\n  };\n  static STOP_CHAR = '`';\n  static ESCAPE_CHAR = '\\\\';\n  static InputDefinition = PatternInputDefinition;\n  static FixedDefinition = PatternFixedDefinition;\n\n  declare mask: string;\n  /** */\n  declare blocks: { [key: string]: ExtendFactoryArgOptions<BlockExtraOptions> };\n  /** */\n  declare definitions: Definitions;\n  /** Single char for empty input */\n  declare placeholderChar: string;\n  /** Single char for filled input */\n  declare displayChar: string;\n  /** Show placeholder only when needed */\n  declare lazy: boolean;\n  /** Enable characters overwriting */\n  declare overwrite?: boolean | 'shift' | undefined;\n  /** */\n  declare eager?: boolean | 'remove' | 'append' | undefined;\n  /** */\n  declare skipInvalid?: boolean | undefined;\n  /** */\n  declare autofix?: boolean | 'pad' | undefined;\n\n  declare _blocks: Array<PatternBlock>;\n  declare _maskedBlocks: {[key: string]: Array<number>};\n  declare _stops: Array<number>;\n  declare exposeBlock?: Masked;\n\n  constructor (opts: MaskedPatternOptions<Value>) {\n    super({\n      ...MaskedPattern.DEFAULTS,\n      ...opts,\n      definitions: Object.assign({}, PatternInputDefinition.DEFAULT_DEFINITIONS, opts?.definitions),\n    } as MaskedOptions);\n  }\n\n  override updateOptions (opts: Partial<MaskedPatternOptions<Value>>) {\n    super.updateOptions(opts);\n  }\n\n  override _update (opts: Partial<MaskedPatternOptions<Value>>) {\n    opts.definitions = Object.assign({}, this.definitions, opts.definitions);\n    super._update(opts);\n    this._rebuildMask();\n  }\n\n  _rebuildMask () {\n    const defs = this.definitions;\n    this._blocks = []; this.exposeBlock = undefined;\n    this._stops = [];\n    this._maskedBlocks = {};\n\n    const pattern = this.mask;\n    if (!pattern || !defs) return;\n\n    let unmaskingBlock = false;\n    let optionalBlock = false;\n\n    for (let i=0; i<pattern.length; ++i) {\n      if (this.blocks) {\n        const p = pattern.slice(i);\n        const bNames = Object.keys(this.blocks).filter(bName => p.indexOf(bName) === 0);\n        // order by key length\n        bNames.sort((a, b) => b.length - a.length);\n        // use block name with max length\n        const bName = bNames[0];\n        if (bName) {\n          const { expose, repeat, ...bOpts } = normalizeOpts(this.blocks[bName]) as NormalizedOpts<FactoryArg> & BlockExtraOptions; // TODO type Opts<Arg & Extra>\n          const blockOpts = {\n            lazy: this.lazy,\n            eager: this.eager,\n            placeholderChar: this.placeholderChar,\n            displayChar: this.displayChar,\n            overwrite: this.overwrite,\n            autofix: this.autofix,\n            ...bOpts,\n            repeat,\n            parent: this,\n          };\n          const maskedBlock = repeat != null ? new IMask.RepeatBlock(blockOpts as any /* TODO */) : createMask(blockOpts);\n\n          if (maskedBlock) {\n            this._blocks.push(maskedBlock);\n            if (expose) this.exposeBlock = maskedBlock;\n\n            // store block index\n            if (!this._maskedBlocks[bName]) this._maskedBlocks[bName] = [];\n            this._maskedBlocks[bName].push(this._blocks.length - 1);\n          }\n\n          i += bName.length - 1;\n          continue;\n        }\n      }\n\n      let char = pattern[i];\n      let isInput = char in defs;\n\n      if (char === MaskedPattern.STOP_CHAR) {\n        this._stops.push(this._blocks.length);\n        continue;\n      }\n\n      if (char === '{' || char === '}') {\n        unmaskingBlock = !unmaskingBlock;\n        continue;\n      }\n\n      if (char === '[' || char === ']') {\n        optionalBlock = !optionalBlock;\n        continue;\n      }\n\n      if (char === MaskedPattern.ESCAPE_CHAR) {\n        ++i;\n        char = pattern[i];\n        if (!char) break;\n        isInput = false;\n      }\n\n      const def = isInput ?\n        new PatternInputDefinition({\n          isOptional: optionalBlock,\n          lazy: this.lazy,\n          eager: this.eager,\n          placeholderChar: this.placeholderChar,\n          displayChar: this.displayChar,\n          ...normalizeOpts(defs[char]),\n          parent: this,\n        }) as PatternBlock :\n        new PatternFixedDefinition({\n          char,\n          eager: this.eager,\n          isUnmasking: unmaskingBlock,\n        });\n\n      this._blocks.push(def);\n    }\n  }\n\n  override get state (): MaskedPatternState {\n    return {\n      ...super.state,\n      _blocks: this._blocks.map(b => b.state),\n    };\n  }\n\n  override set state (state: MaskedPatternState) {\n    if (!state) { this.reset(); return; }\n\n    const { _blocks, ...maskedState } = state;\n    this._blocks.forEach((b, bi) => b.state = _blocks[bi]);\n    super.state = maskedState;\n  }\n\n  override reset () {\n    super.reset();\n    this._blocks.forEach(b => b.reset());\n  }\n\n  override get isComplete (): boolean {\n    return this.exposeBlock ? this.exposeBlock.isComplete :\n      this._blocks.every(b => b.isComplete);\n  }\n\n  override get isFilled (): boolean {\n    return this._blocks.every(b => b.isFilled);\n  }\n\n  get isFixed (): boolean {\n    return this._blocks.every(b => b.isFixed);\n  }\n\n  get isOptional (): boolean {\n    return this._blocks.every(b => b.isOptional);\n  }\n\n  override doCommit () {\n    this._blocks.forEach(b => b.doCommit());\n    super.doCommit();\n  }\n\n  override get unmaskedValue (): string {\n    return this.exposeBlock ? this.exposeBlock.unmaskedValue :\n      this._blocks.reduce((str, b) => str += b.unmaskedValue, '');\n  }\n\n  override set unmaskedValue (unmaskedValue: string) {\n    if (this.exposeBlock) {\n      const tail = this.extractTail(this._blockStartPos(this._blocks.indexOf(this.exposeBlock)) + this.exposeBlock.displayValue.length);\n      this.exposeBlock.unmaskedValue = unmaskedValue;\n      this.appendTail(tail);\n      this.doCommit();\n    }\n    else super.unmaskedValue = unmaskedValue;\n  }\n\n  override get value (): string {\n    return this.exposeBlock ? this.exposeBlock.value :\n      // TODO return _value when not in change?\n      this._blocks.reduce((str, b) => str += b.value, '');\n  }\n\n  override set value (value: string) {\n    if (this.exposeBlock) {\n      const tail = this.extractTail(this._blockStartPos(this._blocks.indexOf(this.exposeBlock)) + this.exposeBlock.displayValue.length);\n      this.exposeBlock.value = value;\n      this.appendTail(tail);\n      this.doCommit();\n    }\n    else super.value = value;\n  }\n\n  override get typedValue (): Value {\n    return this.exposeBlock ? this.exposeBlock.typedValue :\n      super.typedValue;\n  }\n\n  override set typedValue (value: Value) {\n    if (this.exposeBlock) {\n      const tail = this.extractTail(this._blockStartPos(this._blocks.indexOf(this.exposeBlock)) + this.exposeBlock.displayValue.length);\n      this.exposeBlock.typedValue = value;\n      this.appendTail(tail);\n      this.doCommit();\n    }\n    else super.typedValue = value;\n  }\n\n  override get displayValue (): string {\n    return this._blocks.reduce((str, b) => str += b.displayValue, '');\n  }\n\n  override appendTail (tail: string | String | TailDetails): ChangeDetails {\n    return super.appendTail(tail).aggregate(this._appendPlaceholder());\n  }\n\n  override _appendEager (): ChangeDetails {\n    const details = new ChangeDetails();\n\n    let startBlockIndex = this._mapPosToBlock(this.displayValue.length)?.index;\n    if (startBlockIndex == null) return details;\n\n    // TODO test if it works for nested pattern masks\n    if (this._blocks[startBlockIndex].isFilled) ++startBlockIndex;\n\n    for (let bi=startBlockIndex; bi<this._blocks.length; ++bi) {\n      const d = this._blocks[bi]._appendEager();\n      if (!d.inserted) break;\n\n      details.aggregate(d);\n    }\n\n    return details;\n  }\n\n  override _appendCharRaw (ch: string, flags: AppendFlags<MaskedPatternState>={}): ChangeDetails {\n    const blockIter = this._mapPosToBlock(this.displayValue.length);\n    const details = new ChangeDetails();\n    if (!blockIter) return details;\n\n    for (let bi=blockIter.index, block; (block = this._blocks[bi]); ++bi) {\n      const blockDetails = block._appendChar(ch, { ...flags, _beforeTailState: flags._beforeTailState?._blocks?.[bi] });\n\n      details.aggregate(blockDetails);\n\n      if (blockDetails.consumed) break; // go next char\n    }\n\n    return details;\n  }\n\n  override extractTail (fromPos: number=0, toPos: number=this.displayValue.length): TailDetails {\n    const chunkTail = new ChunksTailDetails();\n    if (fromPos === toPos) return chunkTail;\n\n    this._forEachBlocksInRange(fromPos, toPos, (b, bi, bFromPos, bToPos) => {\n      const blockChunk = b.extractTail(bFromPos, bToPos);\n      blockChunk.stop = this._findStopBefore(bi);\n      blockChunk.from = this._blockStartPos(bi);\n      if (blockChunk instanceof ChunksTailDetails) blockChunk.blockIndex = bi;\n\n      chunkTail.extend(blockChunk);\n    });\n\n    return chunkTail;\n  }\n\n  override extractInput (fromPos: number=0, toPos: number=this.displayValue.length, flags: ExtractFlags={}): string {\n    if (fromPos === toPos) return '';\n\n    let input = '';\n\n    this._forEachBlocksInRange(fromPos, toPos, (b, _, fromPos, toPos) => {\n      input += b.extractInput(fromPos, toPos, flags);\n    });\n\n    return input;\n  }\n\n  _findStopBefore (blockIndex: number): number | undefined {\n    let stopBefore;\n    for (let si=0; si<this._stops.length; ++si) {\n      const stop = this._stops[si];\n      if (stop <= blockIndex) stopBefore = stop;\n      else break;\n    }\n    return stopBefore;\n  }\n\n  /** Appends placeholder depending on laziness */\n  override _appendPlaceholder (toBlockIndex?: number): ChangeDetails {\n    const details = new ChangeDetails();\n    if (this.lazy && toBlockIndex == null) return details;\n\n    const startBlockIter = this._mapPosToBlock(this.displayValue.length);\n    if (!startBlockIter) return details;\n\n    const startBlockIndex = startBlockIter.index;\n    const endBlockIndex = toBlockIndex != null ? toBlockIndex : this._blocks.length;\n\n    this._blocks.slice(startBlockIndex, endBlockIndex)\n      .forEach(b => {\n        if (!b.lazy || toBlockIndex != null) {\n          details.aggregate(b._appendPlaceholder((b as MaskedPattern)._blocks?.length));\n        }\n      });\n\n    return details;\n  }\n\n  /** Finds block in pos */\n  _mapPosToBlock (pos: number): BlockPosData | undefined {\n    let accVal = '';\n    for (let bi=0; bi<this._blocks.length; ++bi) {\n      const block = this._blocks[bi];\n      const blockStartPos = accVal.length;\n\n      accVal += block.displayValue;\n\n      if (pos <= accVal.length) {\n        return {\n          index: bi,\n          offset: pos - blockStartPos,\n        };\n      }\n    }\n  }\n\n  _blockStartPos (blockIndex: number): number {\n    return this._blocks\n      .slice(0, blockIndex)\n      .reduce((pos, b) => pos += b.displayValue.length, 0);\n  }\n\n  _forEachBlocksInRange (fromPos: number, toPos: number=this.displayValue.length, fn: (block: PatternBlock, blockIndex: number, fromPos: number, toPos: number) => void) {\n    const fromBlockIter = this._mapPosToBlock(fromPos);\n\n    if (fromBlockIter) {\n      const toBlockIter = this._mapPosToBlock(toPos);\n      // process first block\n      const isSameBlock = toBlockIter && fromBlockIter.index === toBlockIter.index;\n      const fromBlockStartPos = fromBlockIter.offset;\n      const fromBlockEndPos = toBlockIter && isSameBlock ?\n        toBlockIter.offset :\n        this._blocks[fromBlockIter.index].displayValue.length;\n      fn(this._blocks[fromBlockIter.index], fromBlockIter.index, fromBlockStartPos, fromBlockEndPos);\n\n      if (toBlockIter && !isSameBlock) {\n        // process intermediate blocks\n        for (let bi=fromBlockIter.index+1; bi<toBlockIter.index; ++bi) {\n          fn(this._blocks[bi], bi, 0, this._blocks[bi].displayValue.length);\n        }\n\n        // process last block\n        fn(this._blocks[toBlockIter.index], toBlockIter.index, 0, toBlockIter.offset);\n      }\n    }\n  }\n\n  override remove (fromPos: number=0, toPos: number=this.displayValue.length): ChangeDetails {\n    const removeDetails = super.remove(fromPos, toPos);\n    this._forEachBlocksInRange(fromPos, toPos, (b, _, bFromPos, bToPos) => {\n      removeDetails.aggregate(b.remove(bFromPos, bToPos));\n    });\n    return removeDetails;\n  }\n\n  override nearestInputPos (cursorPos: number, direction: Direction=DIRECTION.NONE): number {\n    if (!this._blocks.length) return 0;\n    const cursor = new PatternCursor(this, cursorPos);\n\n    if (direction === DIRECTION.NONE) {\n      // -------------------------------------------------\n      // NONE should only go out from fixed to the right!\n      // -------------------------------------------------\n      if (cursor.pushRightBeforeInput()) return cursor.pos;\n      cursor.popState();\n      if (cursor.pushLeftBeforeInput()) return cursor.pos;\n      return this.displayValue.length;\n    }\n\n    // FORCE is only about a|* otherwise is 0\n    if (direction === DIRECTION.LEFT || direction === DIRECTION.FORCE_LEFT) {\n      // try to break fast when *|a\n      if (direction === DIRECTION.LEFT) {\n        cursor.pushRightBeforeFilled();\n        if (cursor.ok && cursor.pos === cursorPos) return cursorPos;\n        cursor.popState();\n      }\n\n      // forward flow\n      cursor.pushLeftBeforeInput();\n      cursor.pushLeftBeforeRequired();\n      cursor.pushLeftBeforeFilled();\n\n      // backward flow\n      if (direction === DIRECTION.LEFT) {\n        cursor.pushRightBeforeInput();\n        cursor.pushRightBeforeRequired();\n        if (cursor.ok && cursor.pos <= cursorPos) return cursor.pos;\n        cursor.popState();\n        if (cursor.ok && cursor.pos <= cursorPos) return cursor.pos;\n        cursor.popState();\n      }\n      if (cursor.ok) return cursor.pos;\n      if (direction === DIRECTION.FORCE_LEFT) return 0;\n\n      cursor.popState();\n      if (cursor.ok) return cursor.pos;\n\n      cursor.popState();\n      if (cursor.ok) return cursor.pos;\n\n      return 0;\n    }\n\n    if (direction === DIRECTION.RIGHT || direction === DIRECTION.FORCE_RIGHT) {\n      // forward flow\n      cursor.pushRightBeforeInput();\n      cursor.pushRightBeforeRequired();\n\n      if (cursor.pushRightBeforeFilled()) return cursor.pos;\n      if (direction === DIRECTION.FORCE_RIGHT) return this.displayValue.length;\n\n      // backward flow\n      cursor.popState();\n      if (cursor.ok) return cursor.pos;\n\n      cursor.popState();\n      if (cursor.ok) return cursor.pos;\n\n      return this.nearestInputPos(cursorPos, DIRECTION.LEFT);\n    }\n\n    return cursorPos;\n  }\n\n  override totalInputPositions (fromPos: number=0, toPos: number=this.displayValue.length): number {\n    let total = 0;\n    this._forEachBlocksInRange(fromPos, toPos, (b, _, bFromPos, bToPos) => {\n      total += b.totalInputPositions(bFromPos, bToPos);\n    });\n    return total;\n  }\n\n  /** Get block by name */\n  maskedBlock (name: string): PatternBlock | undefined {\n    return this.maskedBlocks(name)[0];\n  }\n\n  /** Get all blocks by name */\n  maskedBlocks (name: string): Array<PatternBlock> {\n    const indices = this._maskedBlocks[name];\n    if (!indices) return [];\n    return indices.map(gi => this._blocks[gi]);\n  }\n\n  override pad (flags?: AppendFlags): ChangeDetails {\n    const details = new ChangeDetails();\n    this._forEachBlocksInRange(0, this.displayValue.length, b => details.aggregate(b.pad(flags)))\n    return details;\n  }\n}\n\n\nIMask.MaskedPattern = MaskedPattern;\n", "import ChangeDetails from '../core/change-details';\nimport IMask from '../core/holder';\nimport { type AppendFlags } from './base';\nimport MaskedPattern, { MaskedPatternState, type MaskedPatternOptions } from './pattern';\n\n\ntype MaskedRangePatternOptions = MaskedPatternOptions &\n  Pick<MaskedRange, 'from' | 'to'> &\n  Partial<Pick<MaskedRange, 'maxLength'>>;\n\nexport\ntype MaskedRangeOptions = Omit<MaskedRangePatternOptions, 'mask'>;\n\n\n/** Pattern which accepts ranges */\nexport default\nclass MaskedRange extends MaskedPattern {\n  /**\n    Optionally sets max length of pattern.\n    Used when pattern length is longer then `to` param length. Pads zeros at start in this case.\n  */\n  declare maxLength: number;\n  /** Min bound */\n  declare from: number;\n  /** Max bound */\n  declare to: number;\n\n  get _matchFrom (): number {\n    return this.maxLength - String(this.from).length;\n  }\n\n  constructor (opts?: MaskedRangeOptions) {\n    super(opts as MaskedPatternOptions); // mask will be created in _update\n  }\n\n  override updateOptions (opts: Partial<MaskedRangeOptions>) {\n    super.updateOptions(opts);\n  }\n\n  override _update (opts: Partial<MaskedRangeOptions>) {\n    const {\n      to=this.to || 0,\n      from=this.from || 0,\n      maxLength=this.maxLength || 0,\n      autofix=this.autofix,\n      ...patternOpts\n    }: Partial<MaskedRangePatternOptions> = opts;\n\n    this.to = to;\n    this.from = from;\n    this.maxLength = Math.max(String(to).length, maxLength);\n    this.autofix = autofix;\n\n    const fromStr = String(this.from).padStart(this.maxLength, '0');\n    const toStr = String(this.to).padStart(this.maxLength, '0');\n    let sameCharsCount = 0;\n    while (sameCharsCount < toStr.length && toStr[sameCharsCount] === fromStr[sameCharsCount]) ++sameCharsCount;\n    patternOpts.mask = toStr.slice(0, sameCharsCount).replace(/0/g, '\\\\0') + '0'.repeat(this.maxLength - sameCharsCount);\n\n    super._update(patternOpts);\n  }\n\n  override get isComplete (): boolean {\n    return super.isComplete && Boolean(this.value);\n  }\n\n  boundaries (str: string): [string, string] {\n    let minstr = '';\n    let maxstr = '';\n\n    const [, placeholder, num] = str.match(/^(\\D*)(\\d*)(\\D*)/) || [];\n    if (num) {\n      minstr = '0'.repeat(placeholder.length) + num;\n      maxstr = '9'.repeat(placeholder.length) + num;\n    }\n    minstr = minstr.padEnd(this.maxLength, '0');\n    maxstr = maxstr.padEnd(this.maxLength, '9');\n\n    return [minstr, maxstr];\n  }\n\n  override doPrepareChar (ch: string, flags: AppendFlags={}): [string, ChangeDetails] {\n    let details: ChangeDetails;\n    [ch, details] = super.doPrepareChar(ch.replace(/\\D/g, ''), flags);\n\n    if (!ch) details.skip = !this.isComplete;\n\n    return [ch, details];\n  }\n\n  override _appendCharRaw (ch: string, flags: AppendFlags<MaskedPatternState>={}): ChangeDetails {\n    if (!this.autofix || this.value.length + 1 > this.maxLength) return super._appendCharRaw(ch, flags);\n\n    const fromStr = String(this.from).padStart(this.maxLength, '0');\n    const toStr = String(this.to).padStart(this.maxLength, '0');\n\n    const [minstr, maxstr] = this.boundaries(this.value + ch);\n\n    if (Number(maxstr) < this.from) return super._appendCharRaw(fromStr[this.value.length], flags);\n\n    if (Number(minstr) > this.to) {\n      if (!flags.tail && this.autofix === 'pad' && this.value.length + 1 < this.maxLength) {\n        return super._appendCharRaw(fromStr[this.value.length], flags).aggregate(this._appendCharRaw(ch, flags));\n      }\n      return super._appendCharRaw(toStr[this.value.length], flags);\n    }\n\n    return super._appendCharRaw(ch, flags);\n  }\n\n  override doValidate (flags: AppendFlags): boolean {\n    const str = this.value;\n\n    const firstNonZero = str.search(/[^0]/);\n    if (firstNonZero === -1 && str.length <= this._matchFrom) return true;\n\n    const [minstr, maxstr] = this.boundaries(str);\n\n    return this.from <= Number(maxstr) && Number(minstr) <= this.to &&\n      super.doValidate(flags);\n  }\n\n  override pad (flags?: AppendFlags): ChangeDetails {\n    const details = new ChangeDetails();\n    if (this.value.length === this.maxLength) return details;\n\n    const value = this.value;\n    const padLength = this.maxLength - this.value.length;\n\n    if (padLength) {\n      this.reset();\n      for (let i=0; i < padLength; ++i) {\n        details.aggregate(super._appendCharRaw('0', flags as AppendFlags<MaskedPatternState>));\n      }\n\n      // append tail\n      value.split('').forEach(ch => this._appendCharRaw(ch));\n    }\n\n    return details;\n  }\n}\n\n\nIMask.MaskedRange = MaskedRange;\n", "import MaskedPattern, { type MaskedPatternOptions } from './pattern';\nimport { type MaskedRangeOptions } from './range';\nimport MaskedRange from './range';\nimport IMask from '../core/holder';\nimport type Masked from './base';\nimport { type AppendFlags } from './base';\nimport { isString } from '../core/utils';\n\n\nexport\ntype DateMaskType = DateConstructor;\n\ntype DateOptionsKeys =\n  | 'pattern'\n  | 'min'\n  | 'max'\n  | 'autofix'\n;\n\nexport\ntype DateValue = Date | null;\n\nconst DefaultPattern = 'd{.}`m{.}`Y'\n\n// Make format and parse required when pattern is provided\ntype RequiredDateOptions = ({ pattern?: never |  typeof DefaultPattern; format?: MaskedDate[\"format\"]; parse?: MaskedDate[\"parse\"] } |\n  { pattern: MaskedDate[\"pattern\"]; format: MaskedDate[\"format\"]; parse: MaskedDate[\"parse\"] })\n\nexport\ntype MaskedDateOptions =\n  Omit<MaskedPatternOptions<DateValue>, 'mask'> &\n  Partial<Pick<MaskedDate, DateOptionsKeys>> &\n  { mask?: string | DateMaskType } &\n  RequiredDateOptions;\n\n/** Date mask */\nexport default\nclass MaskedDate extends MaskedPattern<DateValue> {\n  static GET_DEFAULT_BLOCKS: () => { [k: string]: MaskedRangeOptions } = () => ({\n    d: {\n      mask: MaskedRange,\n      from: 1,\n      to: 31,\n      maxLength: 2,\n    },\n    m: {\n      mask: MaskedRange,\n      from: 1,\n      to: 12,\n      maxLength: 2,\n    },\n    Y: {\n      mask: MaskedRange,\n      from: 1900,\n      to: 9999,\n    }\n  });\n  static DEFAULTS = {\n    ...MaskedPattern.DEFAULTS,\n    mask: Date,\n    pattern: DefaultPattern,\n    format: (date: DateValue, masked: Masked): string => {\n      if (!date) return '';\n\n      const day = String(date.getDate()).padStart(2, '0');\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const year = date.getFullYear();\n\n      return [day, month, year].join('.');\n    },\n    parse: (str: string, masked: Masked): DateValue => {\n      const [day, month, year] = str.split('.').map(Number);\n      return new Date(year, month - 1, day);\n    },\n  } satisfies Partial<MaskedDateOptions>;\n\n  static extractPatternOptions (opts: Partial<MaskedDateOptions>): Partial<Omit<MaskedDateOptions, 'mask' | 'pattern'> & { mask: MaskedPatternOptions['mask'] }> {\n    const { mask, pattern, ...patternOpts } = opts;\n    return {\n      ...patternOpts,\n      mask: isString(mask) ? mask : pattern,\n    };\n  }\n\n  /** Pattern mask for date according to {@link MaskedDate#format} */\n  declare pattern: string;\n  /** Start date */\n  declare min?: Date;\n  /** End date */\n  declare max?: Date;\n  /** Format typed value to string */\n  declare format: (value: DateValue, masked: Masked) => string;\n  /** Parse string to get typed value */\n  declare parse: (str: string, masked: Masked) => DateValue;\n\n\n  constructor (opts?: MaskedDateOptions) {\n    super(MaskedDate.extractPatternOptions({\n      ...(MaskedDate.DEFAULTS as MaskedDateOptions),\n      ...opts,\n    }));\n  }\n\n  override updateOptions (opts: Partial<MaskedDateOptions> & RequiredDateOptions) {\n    super.updateOptions(opts as Partial<MaskedPatternOptions<DateValue>>);\n  }\n\n  override _update (opts: Partial<MaskedDateOptions>) {\n    const { mask, pattern, blocks, ...patternOpts } = {\n      ...MaskedDate.DEFAULTS,\n      ...opts,\n    };\n\n    const patternBlocks = Object.assign({}, MaskedDate.GET_DEFAULT_BLOCKS());\n    // adjust year block\n    if (opts.min) patternBlocks.Y.from = opts.min.getFullYear();\n    if (opts.max) patternBlocks.Y.to = opts.max.getFullYear();\n    if (opts.min && opts.max && patternBlocks.Y.from === patternBlocks.Y.to\n    ) {\n      patternBlocks.m.from = opts.min.getMonth() + 1;\n      patternBlocks.m.to = opts.max.getMonth() + 1;\n\n      if (patternBlocks.m.from === patternBlocks.m.to) {\n        patternBlocks.d.from = opts.min.getDate();\n        patternBlocks.d.to = opts.max.getDate();\n      }\n    }\n    Object.assign(patternBlocks, this.blocks, blocks);\n\n    super._update({\n      ...patternOpts,\n      mask: isString(mask) ? mask : pattern,\n      blocks: patternBlocks,\n    });\n  }\n\n  override doValidate (flags: AppendFlags): boolean {\n    const date = this.date;\n\n    return super.doValidate(flags) &&\n      (!this.isComplete ||\n        this.isDateExist(this.value) && date != null &&\n        (this.min == null || this.min <= date) &&\n        (this.max == null || date <= this.max));\n  }\n\n  /** Checks if date is exists */\n  isDateExist (str: string): boolean {\n    return this.format(this.parse(str, this), this).indexOf(str) >= 0;\n  }\n\n  /** Parsed Date */\n  get date (): DateValue {\n    return this.typedValue;\n  }\n  set date (date: DateValue) {\n    this.typedValue = date;\n  }\n\n  override get typedValue (): DateValue {\n    return this.isComplete ? super.typedValue : null;\n  }\n  override set typedValue (value: DateValue) {\n    super.typedValue = value;\n  }\n\n  override maskEquals (mask: any): boolean {\n    return mask === Date || super.maskEquals(mask);\n  }\n\n  override optionsIsChanged (opts: Partial<MaskedDateOptions>): boolean {\n    return super.optionsIsChanged(MaskedDate.extractPatternOptions(opts));\n  }\n}\n\n\nIMask.MaskedDate = MaskedDate;\n", "import { objectIncludes } from '../core/utils';\nimport ChangeDetails from '../core/change-details';\nimport createMask, { type FactoryArg, type ExtendFactoryArgOptions, type NormalizedOpts, normalizeOpts } from './factory';\nimport Masked, { type AppendFlags, type MaskedState, type MaskedOptions, type ExtractFlags } from './base';\nimport { DIRECTION, type Direction } from '../core/utils';\nimport { type TailDetails } from '../core/tail-details';\nimport IMask from '../core/holder';\n\n\ntype MaskedDynamicNoRefState = MaskedState & {\n  compiledMasks: Array<MaskedState>\n};\n\ntype MaskedDynamicRefState = MaskedDynamicNoRefState & {\n  currentMaskRef: Masked,\n  currentMask: MaskedState,\n};\n\nexport\ntype MaskedDynamicState = MaskedDynamicNoRefState | MaskedDynamicRefState;\n\nexport\ntype DynamicMaskType = Array<ExtendFactoryArgOptions<{ expose?: boolean }>> | ArrayConstructor;\n\nexport\ntype MaskedDynamicOptions = MaskedOptions<MaskedDynamic, 'dispatch'>;\n\ntype HandleState = MaskedDynamicState | MaskedState;\n\n/** Dynamic mask for choosing appropriate mask in run-time */\nexport default\nclass MaskedDynamic<Value=any> extends Masked<Value> {\n  declare mask: DynamicMaskType;\n  /** Currently chosen mask */\n  declare currentMask?: Masked;\n  /** Currently chosen mask */\n  declare exposeMask?: Masked;\n  /** Compliled {@link Masked} options */\n  declare compiledMasks: Array<Masked>;\n  /** Chooses {@link Masked} depending on input value */\n  declare dispatch: (appended: string, masked: MaskedDynamic, flags: AppendFlags<HandleState>, tail: string | String | TailDetails) => (Masked | undefined);\n\n  declare _overwrite?: this['overwrite'];\n  declare _eager?: this['eager'];\n  declare _skipInvalid?: this['skipInvalid'];\n  declare _autofix?: this['autofix'];\n\n  static DEFAULTS: typeof Masked.DEFAULTS & Pick<MaskedDynamic, 'dispatch'> = {\n    ...Masked.DEFAULTS,\n    dispatch: (appended, masked, flags, tail) => {\n      if (!masked.compiledMasks.length) return;\n\n      const inputValue = masked.rawInputValue;\n\n      // simulate input\n      const inputs = masked.compiledMasks.map((m, index) => {\n        const isCurrent = masked.currentMask === m;\n        const startInputPos = isCurrent ? m.displayValue.length : m.nearestInputPos(m.displayValue.length, DIRECTION.FORCE_LEFT);\n\n        if (m.rawInputValue !== inputValue) {\n          m.reset();\n          m.append(inputValue, { raw: true });\n        } else if (!isCurrent) {\n          m.remove(startInputPos);\n        }\n        m.append(appended, masked.currentMaskFlags(flags));\n        m.appendTail(tail);\n\n        return {\n          index,\n          weight: m.rawInputValue.length,\n          totalInputPositions: m.totalInputPositions(\n            0,\n            Math.max(startInputPos, m.nearestInputPos(m.displayValue.length, DIRECTION.FORCE_LEFT)),\n          ),\n        };\n      });\n\n      // pop masks with longer values first\n      inputs.sort((i1, i2) => i2.weight - i1.weight || i2.totalInputPositions - i1.totalInputPositions);\n\n      return masked.compiledMasks[inputs[0].index];\n    }\n  };\n\n  constructor (opts?: MaskedDynamicOptions) {\n    super({\n      ...MaskedDynamic.DEFAULTS,\n      ...opts\n    });\n\n    this.currentMask = undefined;\n  }\n\n  override updateOptions (opts: Partial<MaskedDynamicOptions>) {\n    super.updateOptions(opts);\n  }\n\n  override _update (opts: Partial<MaskedDynamicOptions>) {\n    super._update(opts);\n\n    if ('mask' in opts) {\n      this.exposeMask = undefined;\n      // mask could be totally dynamic with only `dispatch` option\n      this.compiledMasks = Array.isArray(opts.mask) ?\n        opts.mask.map(m => {\n          const { expose, ...maskOpts } = normalizeOpts(m) as NormalizedOpts<FactoryArg> & { expose?: boolean };\n\n          const masked = createMask({\n            overwrite: this._overwrite,\n            eager: this._eager,\n            skipInvalid: this._skipInvalid,\n            ...maskOpts,\n          });\n\n          if (expose) this.exposeMask = masked;\n\n          return masked;\n        }) :\n        [];\n\n      // this.currentMask = this.doDispatch(''); // probably not needed but lets see\n    }\n  }\n\n  override _appendCharRaw (ch: string, flags: AppendFlags<HandleState>={}): ChangeDetails {\n    const details = this._applyDispatch(ch, flags);\n\n    if (this.currentMask) {\n      details.aggregate(this.currentMask._appendChar(ch, this.currentMaskFlags(flags)));\n    }\n\n    return details;\n  }\n\n  _applyDispatch (appended: string='', flags: AppendFlags<HandleState>={}, tail: string | String | TailDetails = ''): ChangeDetails {\n    const prevValueBeforeTail = flags.tail && flags._beforeTailState != null ?\n      flags._beforeTailState._value :\n      this.value;\n    const inputValue = this.rawInputValue;\n    const insertValue = flags.tail && flags._beforeTailState != null ?\n      flags._beforeTailState._rawInputValue :\n      inputValue;\n    const tailValue = inputValue.slice(insertValue.length);\n    const prevMask = this.currentMask;\n    const details = new ChangeDetails();\n\n    const prevMaskState = prevMask?.state;\n\n    // clone flags to prevent overwriting `_beforeTailState`\n    this.currentMask = this.doDispatch(appended, { ...flags }, tail);\n\n    // restore state after dispatch\n    if (this.currentMask) {\n      if (this.currentMask !== prevMask) {\n        // if mask changed reapply input\n        this.currentMask.reset();\n\n        if (insertValue) {\n          this.currentMask.append(insertValue, { raw: true });\n          details.tailShift = this.currentMask.value.length - prevValueBeforeTail.length;\n        }\n\n        if (tailValue) {\n          details.tailShift += this.currentMask.append(tailValue, { raw: true, tail: true }).tailShift;\n        }\n      } else if (prevMaskState) {\n        // Dispatch can do something bad with state, so\n        // restore prev mask state\n        this.currentMask.state = prevMaskState;\n      }\n    }\n\n    return details;\n  }\n\n  override _appendPlaceholder (): ChangeDetails {\n    const details = this._applyDispatch();\n\n    if (this.currentMask) {\n      details.aggregate(this.currentMask._appendPlaceholder());\n    }\n\n    return details;\n  }\n\n  override _appendEager (): ChangeDetails {\n    const details = this._applyDispatch();\n\n    if (this.currentMask) {\n      details.aggregate(this.currentMask._appendEager());\n    }\n\n    return details;\n  }\n\n  override appendTail (tail: string | String | TailDetails): ChangeDetails {\n    const details = new ChangeDetails();\n    if (tail) details.aggregate(this._applyDispatch('', {}, tail));\n\n    return details.aggregate(this.currentMask ?\n      this.currentMask.appendTail(tail) :\n      super.appendTail(tail));\n  }\n\n  currentMaskFlags (flags: AppendFlags<HandleState>): AppendFlags {\n    return {\n      ...flags,\n      _beforeTailState:\n        (flags._beforeTailState as MaskedDynamicRefState)?.currentMaskRef === this.currentMask &&\n        (flags._beforeTailState as MaskedDynamicRefState)?.currentMask ||\n        flags._beforeTailState,\n    };\n  }\n\n  doDispatch(appended: string, flags: AppendFlags<HandleState>={}, tail: string | String | TailDetails=''): Masked | undefined {\n    return this.dispatch(appended, this, flags, tail);\n  }\n\n  override doValidate (flags: AppendFlags<HandleState>): boolean {\n    return super.doValidate(flags) && (\n      !this.currentMask || this.currentMask.doValidate(this.currentMaskFlags(flags))\n    );\n  }\n\n  override doPrepare (str: string, flags: AppendFlags<HandleState>={}): [string, ChangeDetails] {\n    let [s, details] = super.doPrepare(str, flags);\n\n    if (this.currentMask) {\n      let currentDetails;\n      ([s, currentDetails] = super.doPrepare(s, this.currentMaskFlags(flags)));\n      details = details.aggregate(currentDetails);\n    }\n\n    return [s, details];\n  }\n\n  override doPrepareChar (str: string, flags: AppendFlags<HandleState>={}): [string, ChangeDetails] {\n    let [s, details] = super.doPrepareChar(str, flags);\n\n    if (this.currentMask) {\n      let currentDetails;\n      ([s, currentDetails] = super.doPrepareChar(s, this.currentMaskFlags(flags)));\n      details = details.aggregate(currentDetails);\n    }\n\n    return [s, details];\n  }\n\n  override reset () {\n    this.currentMask?.reset();\n    this.compiledMasks.forEach(m => m.reset());\n  }\n\n  override get value (): string {\n    return this.exposeMask ? this.exposeMask.value :\n      this.currentMask ? this.currentMask.value :\n      '';\n  }\n\n  override set value (value: string) {\n    if (this.exposeMask) {\n      this.exposeMask.value = value;\n      this.currentMask = this.exposeMask;\n      this._applyDispatch();\n    }\n    else super.value = value;\n  }\n\n  override get unmaskedValue (): string {\n    return this.exposeMask ? this.exposeMask.unmaskedValue :\n      this.currentMask ? this.currentMask.unmaskedValue :\n      '';\n  }\n\n  override set unmaskedValue (unmaskedValue: string) {\n    if (this.exposeMask) {\n      this.exposeMask.unmaskedValue = unmaskedValue;\n      this.currentMask = this.exposeMask;\n      this._applyDispatch();\n    }\n    else super.unmaskedValue = unmaskedValue;\n  }\n\n  override get typedValue (): Value {\n    return this.exposeMask ? this.exposeMask.typedValue :\n      this.currentMask ? this.currentMask.typedValue :\n      '';\n  }\n\n  override set typedValue (typedValue: Value) {\n    if (this.exposeMask) {\n      this.exposeMask.typedValue = typedValue;\n      this.currentMask = this.exposeMask;\n      this._applyDispatch();\n      return;\n    }\n\n    let unmaskedValue = String(typedValue);\n\n    // double check it\n    if (this.currentMask) {\n      this.currentMask.typedValue = typedValue;\n      unmaskedValue = this.currentMask.unmaskedValue;\n    }\n    this.unmaskedValue = unmaskedValue;\n  }\n\n  override get displayValue (): string {\n    return this.currentMask ? this.currentMask.displayValue : '';\n  }\n\n  override get isComplete (): boolean {\n    return Boolean(this.currentMask?.isComplete);\n  }\n\n  override get isFilled (): boolean {\n    return Boolean(this.currentMask?.isFilled);\n  }\n\n  override remove (fromPos?: number, toPos?: number): ChangeDetails {\n    const details: ChangeDetails = new ChangeDetails();\n\n    if (this.currentMask) {\n      details.aggregate(this.currentMask.remove(fromPos, toPos))\n        // update with dispatch\n        .aggregate(this._applyDispatch());\n    }\n\n    return details;\n  }\n\n  override get state (): MaskedDynamicState {\n    return {\n      ...super.state,\n      _rawInputValue: this.rawInputValue,\n      compiledMasks: this.compiledMasks.map(m => m.state),\n      currentMaskRef: this.currentMask,\n      currentMask: this.currentMask?.state,\n    };\n  }\n\n  override set state (state: HandleState) {\n    const { compiledMasks, currentMaskRef, currentMask, ...maskedState } = state as MaskedDynamicRefState;\n    if (compiledMasks) this.compiledMasks.forEach((m, mi) => m.state = compiledMasks[mi]);\n    if (currentMaskRef != null) {\n      this.currentMask = currentMaskRef;\n      this.currentMask.state = currentMask;\n    }\n    super.state = maskedState;\n  }\n\n  override extractInput (fromPos?: number, toPos?: number, flags?: ExtractFlags): string {\n    return this.currentMask ?\n      this.currentMask.extractInput(fromPos, toPos, flags) :\n      '';\n  }\n\n  override extractTail (fromPos?: number, toPos?: number): TailDetails {\n    return this.currentMask ?\n      this.currentMask.extractTail(fromPos, toPos) :\n      super.extractTail(fromPos, toPos);\n  }\n\n  override doCommit () {\n    if (this.currentMask) this.currentMask.doCommit();\n    super.doCommit();\n  }\n\n  override nearestInputPos(cursorPos: number, direction?: Direction): number {\n    return this.currentMask ?\n      this.currentMask.nearestInputPos(cursorPos, direction) :\n      super.nearestInputPos(cursorPos, direction);\n  }\n\n  override get overwrite (): boolean | 'shift' | undefined {\n    return this.currentMask ?\n      this.currentMask.overwrite :\n      this._overwrite;\n  }\n\n  override set overwrite (overwrite: boolean | 'shift' | undefined) {\n    this._overwrite = overwrite;\n  }\n\n  override get eager (): boolean | 'remove' | 'append' | undefined {\n    return this.currentMask ?\n      this.currentMask.eager :\n      this._eager;\n  }\n\n  override set eager (eager: boolean | 'remove' | 'append' | undefined) {\n    this._eager = eager;\n  }\n\n  override get skipInvalid (): boolean | undefined {\n    return this.currentMask ?\n      this.currentMask.skipInvalid :\n      this._skipInvalid;\n  }\n\n  override set skipInvalid (skipInvalid: boolean | undefined) {\n    this._skipInvalid = skipInvalid;\n  }\n\n  override get autofix (): boolean | 'pad' | undefined {\n    return this.currentMask ?\n      this.currentMask.autofix :\n      this._autofix;\n  }\n\n  override set autofix (autofix: boolean | 'pad' | undefined) {\n    this._autofix = autofix;\n  }\n\n  override maskEquals (mask: any): boolean {\n    return Array.isArray(mask) ?\n      this.compiledMasks.every((m, mi) => {\n        if (!mask[mi]) return;\n\n        const { mask: oldMask, ...restOpts } = mask[mi];\n        return objectIncludes(m, restOpts) && m.maskEquals(oldMask);\n      }) : super.maskEquals(mask);\n  }\n\n  override typedValueEquals (value: any): boolean {\n    return Boolean(this.currentMask?.typedValueEquals(value));\n  }\n}\n\n\nIMask.MaskedDynamic = MaskedDynamic;\n", "import MaskedPattern, { MaskedPatternState, type MaskedPatternOptions } from './pattern';\nimport { AppendFlags } from './base';\nimport IMask from '../core/holder';\nimport ChangeDetails from '../core/change-details';\nimport { DIRECTION } from '../core/utils';\nimport { TailDetails } from '../core/tail-details';\nimport ContinuousTailDetails from '../core/continuous-tail-details';\n\n\nexport\ntype MaskedEnumOptions = Omit<MaskedPatternOptions, 'mask'> & Pick<MaskedEnum, 'enum'> & Partial<Pick<MaskedEnum, 'matchValue'>>;\n\nexport\ntype MaskedEnumPatternOptions = MaskedPatternOptions & Partial<Pick<MaskedEnum, 'enum' | 'matchValue'>>;\n\n\n/** Pattern which validates enum values */\nexport default\nclass MaskedEnum extends MaskedPattern {\n  declare enum: Array<string>;\n  /** Match enum value */\n  declare matchValue: (enumStr: string, inputStr: string, matchFrom: number) => boolean;\n\n  static DEFAULTS: typeof MaskedPattern.DEFAULTS & Pick<MaskedEnum, 'matchValue'> = {\n    ...MaskedPattern.DEFAULTS,\n    matchValue: (estr, istr, matchFrom) => estr.indexOf(istr, matchFrom) === matchFrom,\n  };\n\n  constructor (opts?: MaskedEnumOptions) {\n    super({\n      ...MaskedEnum.DEFAULTS,\n      ...opts,\n    } as MaskedPatternOptions); // mask will be created in _update\n  }\n\n  override updateOptions (opts: Partial<MaskedEnumOptions>) {\n    super.updateOptions(opts);\n  }\n\n  override _update (opts: Partial<MaskedEnumOptions>) {\n    const { enum: enum_, ...eopts }: MaskedEnumPatternOptions = opts;\n\n    if (enum_) {\n      const lengths = enum_.map(e => e.length);\n      const requiredLength = Math.min(...lengths);\n      const optionalLength = Math.max(...lengths) - requiredLength;\n\n      eopts.mask = '*'.repeat(requiredLength);\n      if (optionalLength) eopts.mask += '[' + '*'.repeat(optionalLength) + ']';\n\n      this.enum = enum_;\n    }\n\n    super._update(eopts);\n  }\n\n  override _appendCharRaw (ch: string, flags: AppendFlags<MaskedPatternState>={}): ChangeDetails {\n    const matchFrom = Math.min(this.nearestInputPos(0, DIRECTION.FORCE_RIGHT), this.value.length);\n\n    const matches = this.enum.filter(e => this.matchValue(e, this.unmaskedValue + ch, matchFrom));\n\n    if (matches.length) {\n      if (matches.length === 1) {\n        this._forEachBlocksInRange(0, this.value.length, (b, bi) => {\n          const mch = matches[0][bi];\n          if (bi >= this.value.length || mch === b.value) return;\n\n          b.reset();\n          b._appendChar(mch, flags);\n        });\n      }\n\n      const d = super._appendCharRaw(matches[0][this.value.length], flags);\n\n      if (matches.length === 1) {\n        matches[0].slice(this.unmaskedValue.length).split('').forEach(mch => d.aggregate(super._appendCharRaw(mch)));\n      }\n\n      return d;\n    }\n\n    return new ChangeDetails({ skip: !this.isComplete });\n  }\n\n  override extractTail (fromPos: number=0, toPos: number=this.displayValue.length): TailDetails {\n    // just drop tail\n    return new ContinuousTailDetails('', fromPos);\n  }\n\n  override remove (fromPos: number=0, toPos: number=this.displayValue.length): ChangeDetails {\n    if (fromPos === toPos) return new ChangeDetails();\n\n    const matchFrom = Math.min(super.nearestInputPos(0, DIRECTION.FORCE_RIGHT), this.value.length);\n\n    let pos: number;\n    for (pos = fromPos; pos >= 0; --pos) {\n      const matches = this.enum.filter(e => this.matchValue(e, this.value.slice(matchFrom, pos), matchFrom));\n      if (matches.length > 1) break;\n    }\n\n    const details = super.remove(pos, toPos);\n    details.tailShift += pos - fromPos;\n\n    return details;\n  }\n\n  override get isComplete (): boolean {\n    return this.enum.indexOf(this.value) >= 0;\n  }\n}\n\n\nIMask.MaskedEnum = MaskedEnum;\n", "import Masked, { type MaskedOptions } from './base';\nimport IMask from '../core/holder';\n\n\nexport\ntype MaskedFunctionOptions = MaskedOptions<MaskedFunction>;\n\n/** Masking by custom Function */\nexport default\nclass MaskedFunction<Value=any> extends Masked<Value> {\n  /** */\n  declare mask: (value: string, masked: Masked) => boolean;\n  /** Enable characters overwriting */\n  declare overwrite?: boolean | 'shift' | undefined;\n  /** */\n  declare eager?: boolean | 'remove' | 'append' | undefined;\n  /** */\n  declare skipInvalid?: boolean | undefined;\n  /** */\n  declare autofix?: boolean | 'pad' | undefined;\n\n  override updateOptions (opts: Partial<MaskedFunctionOptions>) {\n    super.updateOptions(opts);\n  }\n\n  override _update (opts: Partial<MaskedFunctionOptions>) {\n    super._update({\n      ...opts,\n      validate: opts.mask,\n    });\n  }\n}\n\n\nIMask.MaskedFunction = MaskedFunction;\n", "import { escapeRegExp, type Direction, DIRECTION } from '../core/utils';\nimport ChangeDetails from '../core/change-details';\nimport { type TailDetails } from '../core/tail-details';\n\nimport Masked, { type MaskedOptions, type ExtractFlags, type AppendFlags } from './base';\nimport IMask from '../core/holder';\n\n\nexport\ntype MaskedNumberOptions = MaskedOptions<MaskedNumber,\n  | 'radix'\n  | 'thousandsSeparator'\n  | 'mapToRadix'\n  | 'scale'\n  | 'min'\n  | 'max'\n  | 'normalizeZeros'\n  | 'padFractionalZeros'\n>;\n\n/** Number mask */\nexport default\nclass MaskedNumber extends Masked<number> {\n  static UNMASKED_RADIX = '.';\n  static EMPTY_VALUES: Array<null | undefined | string | number> = [...Masked.EMPTY_VALUES, 0];\n  static DEFAULTS = {\n    ...Masked.DEFAULTS,\n    mask: Number,\n    radix: ',',\n    thousandsSeparator: '',\n    mapToRadix: [MaskedNumber.UNMASKED_RADIX],\n    min: Number.MIN_SAFE_INTEGER,\n    max: Number.MAX_SAFE_INTEGER,\n    scale: 2,\n    normalizeZeros: true,\n    padFractionalZeros: false,\n    parse: Number,\n    format: (n: number) => n.toLocaleString('en-US', { useGrouping: false, maximumFractionDigits: 20 }),\n  };\n\n  declare mask: NumberConstructor;\n  /** Single char */\n  declare radix: string;\n  /** Single char */\n  declare thousandsSeparator: string;\n  /** Array of single chars */\n  declare mapToRadix: Array<string>;\n  /** */\n  declare min: number;\n  /** */\n  declare max: number;\n  /** Digits after point */\n  declare scale: number;\n  /** Flag to remove leading and trailing zeros in the end of editing */\n  declare normalizeZeros: boolean;\n  /** Flag to pad trailing zeros after point in the end of editing */\n  declare padFractionalZeros: boolean;\n  /** Enable characters overwriting */\n  declare overwrite?: boolean | 'shift' | undefined;\n  /** */\n  declare eager?: boolean | 'remove' | 'append' | undefined;\n  /** */\n  declare skipInvalid?: boolean | undefined;\n  /** */\n  declare autofix?: boolean | 'pad' | undefined;\n  /** Format typed value to string */\n  declare format: (value: number, masked: Masked) => string;\n  /** Parse string to get typed value */\n  declare parse: (str: string, masked: Masked) => number;\n\n  declare _numberRegExp: RegExp;\n  declare _thousandsSeparatorRegExp: RegExp;\n  declare _mapToRadixRegExp: RegExp;\n  declare _separatorsProcessed: boolean;\n\n  constructor (opts?: MaskedNumberOptions) {\n    super({\n      ...MaskedNumber.DEFAULTS,\n      ...opts,\n    });\n  }\n\n  override updateOptions (opts: Partial<MaskedNumberOptions>) {\n    super.updateOptions(opts);\n  }\n\n  override _update (opts: Partial<MaskedNumberOptions>) {\n    super._update(opts);\n    this._updateRegExps();\n  }\n\n  _updateRegExps () {\n    const start = '^' + (this.allowNegative ? '[+|\\\\-]?' : '');\n    const mid = '\\\\d*';\n    const end = (this.scale ?\n      `(${escapeRegExp(this.radix)}\\\\d{0,${this.scale}})?` :\n      '') + '$';\n\n    this._numberRegExp = new RegExp(start + mid + end);\n    this._mapToRadixRegExp = new RegExp(`[${this.mapToRadix.map(escapeRegExp).join('')}]`, 'g');\n    this._thousandsSeparatorRegExp = new RegExp(escapeRegExp(this.thousandsSeparator), 'g');\n  }\n\n  _removeThousandsSeparators (value: string): string {\n    return value.replace(this._thousandsSeparatorRegExp, '');\n  }\n\n  _insertThousandsSeparators (value: string): string {\n    // https://stackoverflow.com/questions/2901102/how-to-print-a-number-with-commas-as-thousands-separators-in-javascript\n    const parts = value.split(this.radix);\n    parts[0] = parts[0].replace(/\\B(?=(\\d{3})+(?!\\d))/g, this.thousandsSeparator);\n    return parts.join(this.radix);\n  }\n\n  override doPrepareChar (ch: string, flags: AppendFlags={}): [string, ChangeDetails] {\n    const [prepCh, details] = super.doPrepareChar(this._removeThousandsSeparators(\n      this.scale && this.mapToRadix.length && (\n        /*\n          radix should be mapped when\n          1) input is done from keyboard = flags.input && flags.raw\n          2) unmasked value is set = !flags.input && !flags.raw\n          and should not be mapped when\n          1) value is set = flags.input && !flags.raw\n          2) raw value is set = !flags.input && flags.raw\n        */\n        flags.input && flags.raw ||\n        !flags.input && !flags.raw\n      ) ? ch.replace(this._mapToRadixRegExp, this.radix) : ch\n    ), flags);\n    if (ch && !prepCh) details.skip = true;\n\n    if (prepCh && !this.allowPositive && !this.value && prepCh !== '-') details.aggregate(this._appendChar('-'));\n\n    return [prepCh, details];\n  }\n\n  _separatorsCount (to: number, extendOnSeparators: boolean=false): number {\n    let count = 0;\n\n    for (let pos = 0; pos < to; ++pos) {\n      if (this._value.indexOf(this.thousandsSeparator, pos) === pos) {\n        ++count;\n        if (extendOnSeparators) to += this.thousandsSeparator.length;\n      }\n    }\n\n    return count;\n  }\n\n  _separatorsCountFromSlice (slice: string=this._value): number {\n    return this._separatorsCount(this._removeThousandsSeparators(slice).length, true);\n  }\n\n  override extractInput (fromPos: number=0, toPos: number=this.displayValue.length, flags?: ExtractFlags): string {\n    [fromPos, toPos] = this._adjustRangeWithSeparators(fromPos, toPos);\n\n    return this._removeThousandsSeparators(super.extractInput(fromPos, toPos, flags));\n  }\n\n  \n  override _appendCharRaw (ch: string, flags: AppendFlags={}): ChangeDetails {\n    const prevBeforeTailValue = flags.tail && flags._beforeTailState ?\n      flags._beforeTailState._value :\n      this._value;\n    const prevBeforeTailSeparatorsCount = this._separatorsCountFromSlice(prevBeforeTailValue);\n    this._value = this._removeThousandsSeparators(this.value);\n\n    const oldValue = this._value;\n\n    this._value += ch;\n\n    const num = this.number;\n    let accepted = !isNaN(num);\n    let skip = false;\n\n    if (accepted) {\n      let fixedNum;\n      if (this.min != null && this.min < 0 && this.number < this.min) fixedNum = this.min;\n      if (this.max != null && this.max > 0 && this.number > this.max) fixedNum = this.max;\n\n      if (fixedNum != null) {\n        if (this.autofix) {\n          this._value = this.format(fixedNum, this).replace(MaskedNumber.UNMASKED_RADIX, this.radix);\n          skip ||= oldValue === this._value && !flags.tail; // if not changed on tail it's still ok to proceed\n        } else {\n          accepted = false;\n        }\n      }\n      accepted &&= Boolean(this._value.match(this._numberRegExp));\n    }\n\n    let appendDetails;\n    if (!accepted) {\n      this._value = oldValue;\n      appendDetails = new ChangeDetails();\n    } else {\n      appendDetails = new ChangeDetails({\n        inserted: this._value.slice(oldValue.length),\n        rawInserted: skip ? '' : ch,\n        skip,\n      });\n    }\n\n    this._value = this._insertThousandsSeparators(this._value);\n    const beforeTailValue = flags.tail && flags._beforeTailState ?\n      flags._beforeTailState._value :\n      this._value;\n    const beforeTailSeparatorsCount = this._separatorsCountFromSlice(beforeTailValue);\n\n    appendDetails.tailShift += (beforeTailSeparatorsCount - prevBeforeTailSeparatorsCount) * this.thousandsSeparator.length;\n    return appendDetails;\n  }\n\n  _findSeparatorAround (pos: number): number {\n    if (this.thousandsSeparator) {\n      const searchFrom = pos - this.thousandsSeparator.length + 1;\n      const separatorPos = this.value.indexOf(this.thousandsSeparator, searchFrom);\n      if (separatorPos <= pos) return separatorPos;\n    }\n\n    return -1;\n  }\n\n  _adjustRangeWithSeparators (from: number, to: number): [number, number] {\n    const separatorAroundFromPos = this._findSeparatorAround(from);\n    if (separatorAroundFromPos >= 0) from = separatorAroundFromPos;\n\n    const separatorAroundToPos = this._findSeparatorAround(to);\n    if (separatorAroundToPos >= 0) to = separatorAroundToPos + this.thousandsSeparator.length;\n    return [from, to];\n  }\n\n  \n  override remove (fromPos: number=0, toPos: number=this.displayValue.length): ChangeDetails {\n    [fromPos, toPos] = this._adjustRangeWithSeparators(fromPos, toPos);\n\n    const valueBeforePos = this.value.slice(0, fromPos);\n    const valueAfterPos = this.value.slice(toPos);\n\n    const prevBeforeTailSeparatorsCount = this._separatorsCount(valueBeforePos.length);\n    this._value = this._insertThousandsSeparators(this._removeThousandsSeparators(valueBeforePos + valueAfterPos));\n    const beforeTailSeparatorsCount = this._separatorsCountFromSlice(valueBeforePos);\n\n    return new ChangeDetails({\n      tailShift: (beforeTailSeparatorsCount - prevBeforeTailSeparatorsCount) * this.thousandsSeparator.length,\n    });\n  }\n\n  override nearestInputPos (cursorPos: number, direction?: Direction): number {\n    if (!this.thousandsSeparator) return cursorPos;\n\n    switch (direction) {\n      case DIRECTION.NONE:\n      case DIRECTION.LEFT:\n      case DIRECTION.FORCE_LEFT: {\n        const separatorAtLeftPos = this._findSeparatorAround(cursorPos - 1);\n        if (separatorAtLeftPos >= 0) {\n          const separatorAtLeftEndPos = separatorAtLeftPos + this.thousandsSeparator.length;\n          if (cursorPos < separatorAtLeftEndPos ||\n            this.value.length <= separatorAtLeftEndPos ||\n            direction === DIRECTION.FORCE_LEFT\n          ) {\n            return separatorAtLeftPos;\n          }\n        }\n        break;\n      }\n      case DIRECTION.RIGHT:\n      case DIRECTION.FORCE_RIGHT: {\n        const separatorAtRightPos = this._findSeparatorAround(cursorPos);\n        if (separatorAtRightPos >= 0) {\n          return separatorAtRightPos + this.thousandsSeparator.length;\n        }\n      }\n    }\n\n    return cursorPos;\n  }\n\n  override doCommit () {\n    if (this.value) {\n      const number = this.number;\n      let validnum = number;\n\n      // check bounds\n      if (this.min != null) validnum = Math.max(validnum, this.min);\n      if (this.max != null) validnum = Math.min(validnum, this.max);\n\n      if (validnum !== number) this.unmaskedValue = this.format(validnum, this);\n\n      let formatted = this.value;\n\n      if (this.normalizeZeros) formatted = this._normalizeZeros(formatted);\n      if (this.padFractionalZeros && this.scale > 0) formatted = this._padFractionalZeros(formatted);\n\n      this._value = formatted;\n    }\n\n    super.doCommit();\n  }\n\n  _normalizeZeros (value: string): string {\n    const parts = this._removeThousandsSeparators(value).split(this.radix);\n\n    // remove leading zeros\n    parts[0] = parts[0].replace(/^(\\D*)(0*)(\\d*)/, (match, sign, zeros, num) => sign + num);\n    // add leading zero\n    if (value.length && !/\\d$/.test(parts[0])) parts[0] = parts[0] + '0';\n\n    if (parts.length > 1) {\n      parts[1] = parts[1].replace(/0*$/, '');  // remove trailing zeros\n      if (!parts[1].length) parts.length = 1;  // remove fractional\n    }\n\n    return this._insertThousandsSeparators(parts.join(this.radix));\n  }\n\n  _padFractionalZeros (value: string): string {\n    if (!value) return value;\n\n    const parts = value.split(this.radix);\n    if (parts.length < 2) parts.push('');\n    parts[1] = parts[1].padEnd(this.scale, '0');\n    return parts.join(this.radix);\n  }\n\n  override doSkipInvalid (ch: string, flags: AppendFlags={}, checkTail?: TailDetails): boolean {\n    const dropFractional = this.scale === 0 && ch !== this.thousandsSeparator && (\n      ch === this.radix ||\n      ch === MaskedNumber.UNMASKED_RADIX ||\n      this.mapToRadix.includes(ch)\n    )\n    return super.doSkipInvalid(ch, flags, checkTail) && !dropFractional;\n  }\n\n  override get unmaskedValue (): string {\n    return this._removeThousandsSeparators(this._normalizeZeros(this.value))\n      .replace(this.radix, MaskedNumber.UNMASKED_RADIX);\n  }\n\n  override set unmaskedValue (unmaskedValue: string) {\n    super.unmaskedValue = unmaskedValue;\n  }\n\n  override get typedValue (): number {\n    return this.parse(this.unmaskedValue, this);\n  }\n\n  override set typedValue (n: number) {\n    this.rawInputValue = this.format(n, this).replace(MaskedNumber.UNMASKED_RADIX, this.radix);\n  }\n\n  /** Parsed Number */\n  get number (): number {\n    return this.typedValue;\n  }\n\n  set number (number: number) {\n    this.typedValue = number;\n  }\n\n  get allowNegative (): boolean {\n    return (this.min != null && this.min < 0) || (this.max != null && this.max < 0);\n  }\n\n  get allowPositive (): boolean {\n    return (this.min != null && this.min > 0) || (this.max != null && this.max > 0);\n  }\n\n  override typedValueEquals (value: any): boolean {\n    // handle  0 -> '' case (typed = 0 even if value = '')\n    // for details see https://github.com/uNmAnNeR/imaskjs/issues/134\n    return (\n      super.typedValueEquals(value) ||\n      MaskedNumber.EMPTY_VALUES.includes(value) && MaskedNumber.EMPTY_VALUES.includes(this.typedValue)\n    ) && !(value === 0 && this.value === '');\n  }\n}\n\n\nIMask.MaskedNumber = MaskedNumber;\n", "import createMask, { type FactoryArg, type FactoryReturnMasked } from './factory';\nimport IMask from '../core/holder';\n\n\n/** Mask pipe source and destination types */\nexport\nconst PIPE_TYPE = {\n  MASKED: 'value',\n  UNMASKED: 'unmaskedValue',\n  TYPED: 'typedValue',\n} as const;\n\ntype ValueOf<T> = T[keyof T];\n\ntype TypedValueOf<\n  Opts extends FactoryArg,\n  Type extends ValueOf<typeof PIPE_TYPE>\n> = Type extends (typeof PIPE_TYPE.MASKED | typeof PIPE_TYPE.UNMASKED) ?\n  string :\n  FactoryReturnMasked<Opts>['typedValue']\n;\n\n/** Creates new pipe function depending on mask type, source and destination options */\nexport\nfunction createPipe<\n  Arg extends FactoryArg,\n  From extends ValueOf<typeof PIPE_TYPE> = typeof PIPE_TYPE.MASKED,\n  To extends ValueOf<typeof PIPE_TYPE> = typeof PIPE_TYPE.MASKED,\n> (\n  arg: Arg,\n  from: From=PIPE_TYPE.MASKED as From,\n  to: To=PIPE_TYPE.MASKED as To,\n) {\n  const masked = createMask(arg);\n  return (value: TypedValueOf<Arg, From>) => masked.runIsolated(m => {\n    m[from] = value;\n    return m[to] as TypedValueOf<Arg, To>;\n  });\n}\n\n/** Pipes value through mask depending on mask type, source and destination options */\nexport\nfunction pipe<\n  Arg extends FactoryArg,\n  From extends ValueOf<typeof PIPE_TYPE> = typeof PIPE_TYPE.MASKED,\n  To extends ValueOf<typeof PIPE_TYPE> = typeof PIPE_TYPE.MASKED,\n> (\n  value: TypedValueOf<Arg, From>,\n  mask: Arg,\n  from?: From,\n  to?: To,\n) {\n  return createPipe(mask, from, to)(value);\n}\n\n\nIMask.PIPE_TYPE = PIPE_TYPE;\nIMask.createPipe = createPipe;\nIMask.pipe = pipe;\n", "import ChangeDetails from '../core/change-details';\nimport IMask from '../core/holder';\nimport { type AppendFlags } from './base';\nimport createMask, { type FactoryArg, normalizeOpts, type ExtendFactoryArgOptions, type UpdateOpts } from './factory';\nimport MaskedPattern, { type BlockExtraOptions, type MaskedPatternOptions, type MaskedPatternState } from './pattern';\nimport type PatternBlock from './pattern/block';\n\n\nexport\ntype RepeatBlockExtraOptions = Pick<BlockExtraOptions, 'repeat'>;\n\nexport\ntype RepeatBlockOptions = ExtendFactoryArgOptions<RepeatBlockExtraOptions>;\n\n\n/** Pattern mask */\nexport default\nclass RepeatBlock<M extends FactoryArg> extends MaskedPattern {\n  declare _blockOpts: M & { repeat?: number };\n  declare repeat: Required<RepeatBlockExtraOptions>['repeat'];\n\n\n  get repeatFrom (): number {\n    return (\n      Array.isArray(this.repeat) ? this.repeat[0] :\n      this.repeat === Infinity ? 0 : this.repeat\n    ) ?? 0;\n  }\n\n  get repeatTo (): number {\n    return (Array.isArray(this.repeat) ? this.repeat[1] : this.repeat) ?? Infinity;\n  }\n\n  constructor (opts: RepeatBlockOptions) {\n    super(opts as MaskedPatternOptions);\n  }\n\n  override updateOptions (opts: UpdateOpts<RepeatBlockOptions>) {\n    super.updateOptions(opts as MaskedPatternOptions);\n  }\n\n  override _update (opts: UpdateOpts<M> & RepeatBlockExtraOptions) {\n    const { repeat, ...blockOpts } = normalizeOpts(opts) as any; // TODO type\n    this._blockOpts = Object.assign({}, this._blockOpts, blockOpts);\n    const block = createMask(this._blockOpts);\n    this.repeat = repeat ?? (block as any).repeat ?? this.repeat ?? Infinity; // TODO type\n\n    super._update({\n      mask: 'm'.repeat(Math.max(this.repeatTo === Infinity && this._blocks?.length || 0, this.repeatFrom)),\n      blocks: { m: block },\n      eager: block.eager,\n      overwrite: block.overwrite,\n      skipInvalid: block.skipInvalid,\n      lazy: (block as MaskedPattern).lazy,\n      placeholderChar: (block as MaskedPattern).placeholderChar,\n      displayChar: (block as MaskedPattern).displayChar,\n    });\n  }\n\n  _allocateBlock (bi: number): PatternBlock | undefined {\n    if (bi < this._blocks.length) return this._blocks[bi];\n    if (this.repeatTo === Infinity || this._blocks.length < this.repeatTo) {\n      this._blocks.push(createMask(this._blockOpts));\n      this.mask += 'm';\n      return this._blocks[this._blocks.length - 1];\n    }\n  }\n\n  override _appendCharRaw (ch: string, flags: AppendFlags<MaskedPatternState>={}): ChangeDetails {\n    const details = new ChangeDetails();\n\n    for (\n      let bi=this._mapPosToBlock(this.displayValue.length)?.index ?? Math.max(this._blocks.length - 1, 0), block, allocated;\n      // try to get a block or\n      // try to allocate a new block if not allocated already\n      (block = this._blocks[bi] ?? (allocated = !allocated && this._allocateBlock(bi)));\n      ++bi\n    ) {\n      const blockDetails = block._appendChar(ch, { ...flags, _beforeTailState: flags._beforeTailState?._blocks?.[bi] });\n\n      if (blockDetails.skip && allocated) {\n        // remove the last allocated block and break\n        this._blocks.pop();\n        this.mask = this.mask.slice(1);\n        break;\n      }\n\n      details.aggregate(blockDetails);\n\n      if (blockDetails.consumed) break; // go next char\n    }\n\n    return details;\n  }\n\n  _trimEmptyTail (fromPos: number=0, toPos?: number): void {\n    const firstBlockIndex = Math.max(this._mapPosToBlock(fromPos)?.index || 0, this.repeatFrom, 0);\n    let lastBlockIndex;\n    if (toPos != null) lastBlockIndex = this._mapPosToBlock(toPos)?.index;\n    if (lastBlockIndex == null) lastBlockIndex = this._blocks.length - 1;\n\n    let removeCount = 0;\n    for (let blockIndex = lastBlockIndex; firstBlockIndex <= blockIndex; --blockIndex, ++removeCount) {\n      if (this._blocks[blockIndex].unmaskedValue) break;\n    }\n\n    if (removeCount) {\n      this._blocks.splice(lastBlockIndex - removeCount + 1, removeCount);\n      this.mask = this.mask.slice(removeCount);\n    }\n  }\n\n  override reset () {\n    super.reset();\n    this._trimEmptyTail();\n  }\n\n  override remove (fromPos: number=0, toPos: number=this.displayValue.length): ChangeDetails {\n    const removeDetails = super.remove(fromPos, toPos);\n    this._trimEmptyTail(fromPos, toPos);\n    return removeDetails;\n  }\n\n  override totalInputPositions (fromPos: number=0, toPos?: number): number {\n    if (toPos == null && this.repeatTo === Infinity) return Infinity;\n    return super.totalInputPositions(fromPos, toPos);\n  }\n\n  override get state (): MaskedPatternState {\n    return super.state;\n  }\n\n  override set state (state: MaskedPatternState) {\n    this._blocks.length = state._blocks.length;\n    this.mask = this.mask.slice(0, this._blocks.length);\n    super.state = state;\n  }\n}\n\n\nIMask.RepeatBlock = RepeatBlock;\n", "import IMask from './imask';\nexport { default as HTMLContenteditableMaskElement } from './controls/html-contenteditable-mask-element';\nexport { default as HTMLInputMaskElement, type InputElement } from './controls/html-input-mask-element';\nexport { default as HTMLMaskElement } from './controls/html-mask-element';\nexport { default as InputMask, type InputMaskElement } from './controls/input';\nexport { default as MaskElement } from './controls/mask-element';\nexport { default as ChangeDetails, type ChangeDetailsOptions } from './core/change-details';\nexport { type AppendTail, type TailDetails } from './core/tail-details';\nexport { DIRECTION, forceDirection, type Direction, type Selection } from './core/utils';\nexport { default as Masked, type AppendFlags, type ExtractFlags, type MaskedOptions, type MaskedState } from './masked/base';\nexport { default as MaskedDate, type DateMaskType, type MaskedDateOptions } from './masked/date';\nexport { default as MaskedDynamic, type DynamicMaskType, type MaskedDynamicOptions, type MaskedDynamicState } from './masked/dynamic';\nexport { default as MaskedEnum, type MaskedEnumOptions } from './masked/enum';\nexport {\n  default as createMask,\n  normalizeOpts,\n  type AllFactoryStaticOpts,\n  type FactoryArg,\n  type FactoryConstructorOpts,\n  type FactoryConstructorReturnMasked,\n  type FactoryInstanceOpts,\n  type FactoryInstanceReturnMasked,\n  type FactoryOpts,\n  type FactoryReturnMasked,\n  type FactoryStaticOpts,\n  type FactoryStaticReturnMasked,\n  type NormalizedOpts,\n  type UpdateOpts,\n} from './masked/factory';\nexport { default as MaskedFunction, type MaskedFunctionOptions } from './masked/function';\nexport { default as MaskedNumber, type MaskedNumberOptions } from './masked/number';\nexport { default as MaskedPattern, type BlockPosData, type Definitions, type MaskedPatternOptions, type MaskedPatternState } from './masked/pattern';\nexport { type default as PatternBlock } from './masked/pattern/block';\nexport { default as ChunksTailDetails, type ChunksTailState } from './masked/pattern/chunk-tail-details';\nexport { default as PatternFixedDefinition, type PatternFixedDefinitionOptions } from './masked/pattern/fixed-definition';\nexport { default as PatternInputDefinition, type PatternInputDefinitionOptions, type PatternInputDefinitionState } from './masked/pattern/input-definition';\nexport { createPipe, pipe, PIPE_TYPE } from './masked/pipe';\nexport { default as MaskedRange, type MaskedRangeOptions } from './masked/range';\nexport { default as MaskedRegExp, type MaskedRegExpOptions } from './masked/regexp';\nexport { default as RepeatBlock, type RepeatBlockOptions } from './masked/repeat';\n\ntry { (globalThis as any).IMask = IMask; } catch {}\nexport default IMask;\n"], "names": ["isString", "str", "String", "isObject", "obj", "_obj$constructor", "constructor", "name", "pick", "keys", "Array", "isArray", "_", "k", "includes", "Object", "entries", "reduce", "acc", "_ref", "v", "DIRECTION", "NONE", "LEFT", "FORCE_LEFT", "RIGHT", "FORCE_RIGHT", "forceDirection", "direction", "escapeRegExp", "replace", "objectIncludes", "b", "a", "arrA", "arrB", "i", "length", "dateA", "Date", "dateB", "getTime", "regexpA", "RegExp", "regexpB", "toString", "prototype", "hasOwnProperty", "call", "ActionDetails", "opts", "assign", "value", "slice", "startChangePos", "oldValue", "oldSelection", "start", "insertedCount", "cursorPos", "end", "Math", "min", "inserted", "substr", "removedCount", "max", "removed", "head", "substring", "tail", "removeDirection", "IMask", "el", "InputMask", "maskedClass", "mask", "Error", "MaskedRegExp", "MaskedPattern", "MaskedDate", "Number", "MaskedNumber", "MaskedDynamic", "Masked", "Function", "MaskedFunction", "console", "warn", "normalizeOpts", "undefined", "instanceOpts", "_mask", "startsWith", "createMask", "nOpts", "MaskedClass", "MaskElement", "selectionStart", "_unsafeSelectionStart", "selectionEnd", "_unsafeSelectionEnd", "select", "_unsafeSelect", "isActive", "KEY_Z", "KEY_Y", "HTMLMaskElement", "input", "_onKeydown", "bind", "_onInput", "_onBeforeinput", "_onCompositionEnd", "rootElement", "_this$input$getRootNo", "_this$input$getRootNo2", "_this$input", "getRootNode", "document", "activeElement", "bindEvents", "handlers", "addEventListener", "drop", "click", "focus", "commit", "_handlers", "e", "redo", "keyCode", "shift<PERSON>ey", "metaKey", "ctrl<PERSON>ey", "preventDefault", "undo", "isComposing", "selectionChange", "inputType", "unbindEvents", "removeEventListener", "HTMLInputMaskElement", "setSelectionRange", "HTMLContenteditableMaskElement", "root", "selection", "getSelection", "anchorOffset", "focusOffset", "createRange", "range", "setStart", "<PERSON><PERSON><PERSON><PERSON>", "setEnd", "<PERSON><PERSON><PERSON><PERSON>", "removeAllRanges", "addRange", "textContent", "InputHistory", "states", "currentIndex", "currentState", "isEmpty", "push", "state", "MAX_LENGTH", "shift", "go", "steps", "clear", "isContentEditable", "tagName", "masked", "_listeners", "_value", "_unmaskedValue", "_rawInputValue", "history", "_saveSelection", "_onChange", "_onDrop", "_onFocus", "_onClick", "_onUndo", "_onRedo", "alignCursor", "alignCursorFriendly", "_bindEvents", "updateValue", "maskEquals", "_this$masked", "updateOptions", "unmasked<PERSON><PERSON>ue", "updateControl", "rawInputValue", "typedValue", "val", "typedValueEquals", "displayValue", "_unbindEvents", "_fireEvent", "ev", "listeners", "for<PERSON>ach", "l", "_cursorChanging", "_changingCursorPos", "pos", "_selection", "newUnmaskedValue", "newValue", "newRawInputValue", "newDisplayValue", "isChanged", "_fireChangeEvents", "_historyChanging", "restOpts", "updateMask", "updateOpts", "optionsIsChanged", "updateCursor", "_delayUpdateCursor", "_abortUpdateCursor", "setTimeout", "_inputEvent", "isComplete", "clearTimeout", "nearestInputPos", "on", "handler", "off", "hIndex", "indexOf", "splice", "details", "oldRawValue", "offset", "raw", "doCommit", "stopPropagation", "_applyHistoryState", "destroy", "ChangeDetails", "normalize", "prep", "rawInserted", "tailShift", "skip", "aggregate", "consumed", "Boolean", "equals", "ContinuousTailDetails", "from", "stop", "extend", "appendTo", "append", "_appendPlaceholder", "unshift", "beforePos", "shiftChar", "_update", "DEFAULTS", "_initialized", "withValueRefresh", "reset", "resolve", "flags", "parse", "format", "extractInput", "isFilled", "totalInputPositions", "fromPos", "toPos", "extractTail", "appendTail", "_appendCharRaw", "ch", "_appendChar", "checkTail", "consistentState", "doPrepareChar", "autofix", "noFixState", "fixDetails", "pad", "chDetails", "consistentTail", "appended", "doValidate", "beforeTailState", "overwrite", "tailDetails", "_appendEager", "_beforeTailState", "doPrepare", "ci", "d", "doSkipInvalid", "eager", "remove", "fn", "_refreshing", "rawInput", "ret", "runIsolated", "_isolated", "skipInvalid", "prepare", "prepareChar", "validate", "parent", "deleteCount", "tailPos", "<PERSON><PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON><PERSON>", "tval", "EMPTY_VALUES", "ChunksTailDetails", "chunks", "map", "join", "tailChunk", "lastChunk", "extendLast", "firstTailChunk", "blockIndex", "chunk", "lastBlockIter", "_mapPosToBlock", "chunkBlock", "index", "_stops", "_blocks", "remainChars", "c", "props", "cstate", "chunkShiftPos", "Pat<PERSON><PERSON>urs<PERSON>", "_log", "ok", "block", "_blockStartPos", "s", "pushState", "popState", "pop", "bindBlock", "_pushLeft", "_this$block", "_pushRight", "pushLeftBeforeFilled", "isFixed", "pushLeftBeforeInput", "pushLeftBeforeRequired", "isOptional", "pushRightBeforeFilled", "pushRightBeforeInput", "pushRightBeforeRequired", "PatternFixedDefinition", "isUnmasking", "_isRawInput", "minPos", "maxPos", "appendEager", "char", "isResolved", "PatternInputDefinition", "placeholder<PERSON><PERSON>", "displayChar", "lazy", "maskOpts", "currentMaskFlags", "boundPos", "_flags$_beforeTailSta", "DEFAULT_DEFINITIONS", "search", "definitions", "_rebuildMask", "defs", "exposeBlock", "_maskedBlocks", "pattern", "unmaskingBlock", "optionalBlock", "blocks", "p", "bNames", "filter", "bName", "sort", "expose", "repeat", "bOpts", "blockOpts", "<PERSON><PERSON><PERSON>", "Repeat<PERSON>lock", "isInput", "STOP_CHAR", "ESCAPE_CHAR", "def", "maskedState", "bi", "every", "_this$_mapPosToBlock", "startBlockIndex", "blockIter", "blockDetails", "chunkTail", "_forEachBlocksInRange", "bFromPos", "bToPos", "blockChunk", "_findStopBefore", "stopBefore", "si", "toBlockIndex", "startBlockIter", "endBlockIndex", "_blocks2", "accVal", "blockStartPos", "fromBlockIter", "toBlockIter", "isSameBlock", "fromBlockStartPos", "fromBlockEndPos", "removeDetails", "cursor", "total", "maskedBlocks", "indices", "gi", "InputDefinition", "FixedDefinition", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_matchFrom", "max<PERSON><PERSON><PERSON>", "to", "patternOpts", "fromStr", "padStart", "toStr", "sameCharsCount", "boundaries", "minstr", "maxstr", "placeholder", "num", "match", "padEnd", "firstNonZero", "<PERSON><PERSON><PERSON><PERSON>", "split", "DefaultPattern", "extractPatternOptions", "patternBlocks", "GET_DEFAULT_BLOCKS", "Y", "getFullYear", "m", "getMonth", "getDate", "date", "isDateExist", "day", "month", "year", "currentMask", "exposeMask", "compiledMasks", "_overwrite", "_eager", "_skipInvalid", "_applyDispatch", "prevValueBeforeTail", "inputValue", "insertValue", "tailValue", "prevMask", "prevMaskState", "doDispatch", "_flags$_beforeTailSta2", "currentMaskRef", "dispatch", "currentDetails", "_this$currentMask", "_this$currentMask2", "_this$currentMask3", "_this$currentMask4", "mi", "_autofix", "oldMask", "_this$currentMask5", "inputs", "isCurrent", "startInputPos", "weight", "i1", "i2", "MaskedEnum", "enum", "enum_", "eopts", "lengths", "<PERSON><PERSON><PERSON><PERSON>", "optional<PERSON><PERSON>th", "matchFrom", "matches", "matchValue", "mch", "estr", "istr", "_updateRegExps", "allowNegative", "mid", "scale", "radix", "_numberRegExp", "_mapToRadixRegExp", "mapToRadix", "_thousandsSeparatorRegExp", "thousandsSeparator", "_removeThousandsSeparators", "_insertThousandsSeparators", "parts", "prepCh", "allowPositive", "_separatorsCount", "extendOnSeparators", "count", "_separatorsCountFromSlice", "_adjustRangeWithSeparators", "prevBeforeTailValue", "prevBeforeTailSeparatorsCount", "number", "accepted", "isNaN", "fixedNum", "UNMASKED_RADIX", "appendDetails", "beforeTailValue", "beforeTailSeparatorsCount", "_findSeparatorAround", "searchFrom", "separatorPos", "separatorAroundFromPos", "separatorAroundToPos", "valueBeforePos", "valueAfterPos", "separatorAtLeftPos", "separatorAtLeftEndPos", "separatorAtRightPos", "validnum", "formatted", "normalizeZeros", "_normalizeZeros", "padFractionalZeros", "_padFractionalZeros", "sign", "zeros", "test", "dropFractional", "n", "_MaskedNumber", "MIN_SAFE_INTEGER", "MAX_SAFE_INTEGER", "toLocaleString", "useGrouping", "maximumFractionDigits", "PIPE_TYPE", "MASKED", "UNMASKED", "TYPED", "createPipe", "arg", "pipe", "repeatFrom", "Infinity", "repeatTo", "_ref2", "_ref3", "_ref4", "_this$_blocks", "_blockOpts", "_allocateBlock", "_this$_mapPosToBlock$", "allocated", "_this$_blocks$bi", "_trimEmptyTail", "_this$_mapPosToBlock2", "_this$_mapPosToBlock3", "firstBlockIndex", "lastBlockIndex", "removeCount", "globalThis"], "mappings": ";;;;;;EAAA;EAEA,SAASA,QAAQA,CAAEC,GAAY,EAAiB;EAC9C,EAAA,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYC,MAAM,CAAA;EACzD,CAAA;;EAEA;EAEA,SAASC,QAAQA,CAAEC,GAAY,EAAiB;EAAA,EAAA,IAAAC,gBAAA,CAAA;IAC9C,OAAO,OAAOD,GAAG,KAAK,QAAQ,IAAIA,GAAG,IAAI,IAAI,IAAI,CAAAA,GAAG,aAAAC,gBAAA,GAAHD,GAAG,CAAEE,WAAW,qBAAhBD,gBAAA,CAAkBE,IAAI,MAAK,QAAQ,CAAA;EACtF,CAAA;EAGA,SAASC,IAAIA,CACXJ,GAAM,EACNK,IAAqC,EACzB;IACZ,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE,OAAOD,IAAI,CAACJ,GAAG,EAAE,CAACQ,CAAC,EAAEC,CAAC,KAAKJ,IAAI,CAACK,QAAQ,CAACD,CAAC,CAAC,CAAC,CAAA;EACrE,EAAA,OAAQE,MAAM,CAACC,OAAO,CAACZ,GAAG,CAAC,CACxBa,MAAM,CAAC,CAACC,GAAG,EAAAC,IAAA,KAAa;EAAA,IAAA,IAAX,CAACN,CAAC,EAAEO,CAAC,CAAC,GAAAD,IAAA,CAAA;EAClB,IAAA,IAAIV,IAAI,CAACW,CAAC,EAAEP,CAAC,CAAC,EAAEK,GAAG,CAACL,CAAC,CAAC,GAAGO,CAAC,CAAA;EAC1B,IAAA,OAAOF,GAAG,CAAA;KACX,EAAE,EAAS,CAAC,CAAA;EACjB,CAAA;;EAEA;AAEA,QAAMG,SAAS,GAAG;EAChBC,EAAAA,IAAI,EAAE,MAAM;EACZC,EAAAA,IAAI,EAAE,MAAM;EACZC,EAAAA,UAAU,EAAE,YAAY;EACxBC,EAAAA,KAAK,EAAE,OAAO;EACdC,EAAAA,WAAW,EAAE,aAAA;EACf,EAAU;;EAEV;;EAKA,SAASC,cAAcA,CAAEC,SAAoB,EAAa;EACxD,EAAA,QAAQA,SAAS;MACf,KAAKP,SAAS,CAACE,IAAI;QACjB,OAAOF,SAAS,CAACG,UAAU,CAAA;MAC7B,KAAKH,SAAS,CAACI,KAAK;QAClB,OAAOJ,SAAS,CAACK,WAAW,CAAA;EAC9B,IAAA;EACE,MAAA,OAAOE,SAAS,CAAA;EACpB,GAAA;EACF,CAAA;;EAEA;EAEA,SAASC,YAAYA,CAAE5B,GAAW,EAAU;EAC1C,EAAA,OAAOA,GAAG,CAAC6B,OAAO,CAAC,2BAA2B,EAAE,MAAM,CAAC,CAAA;EACzD,CAAA;;EAEA;EAEA,SAASC,cAAcA,CAAEC,CAAM,EAAEC,CAAM,EAAW;EAChD,EAAA,IAAIA,CAAC,KAAKD,CAAC,EAAE,OAAO,IAAI,CAAA;EAExB,EAAA,MAAME,IAAI,GAAGxB,KAAK,CAACC,OAAO,CAACsB,CAAC,CAAC;EAAEE,IAAAA,IAAI,GAAGzB,KAAK,CAACC,OAAO,CAACqB,CAAC,CAAC,CAAA;EACtD,EAAA,IAAII,CAAC,CAAA;IAEL,IAAIF,IAAI,IAAIC,IAAI,EAAE;MAChB,IAAIF,CAAC,CAACI,MAAM,IAAIL,CAAC,CAACK,MAAM,EAAE,OAAO,KAAK,CAAA;EACtC,IAAA,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,CAACI,MAAM,EAAED,CAAC,EAAE,EAC3B,IAAI,CAACL,cAAc,CAACE,CAAC,CAACG,CAAC,CAAC,EAAEJ,CAAC,CAACI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,CAAA;EAC/C,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;EAEA,EAAA,IAAIF,IAAI,IAAIC,IAAI,EAAE,OAAO,KAAK,CAAA;EAE9B,EAAA,IAAIF,CAAC,IAAID,CAAC,IAAI,OAAOC,CAAC,KAAK,QAAQ,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;EAC5D,IAAA,MAAMM,KAAK,GAAGL,CAAC,YAAYM,IAAI;QAAEC,KAAK,GAAGR,CAAC,YAAYO,IAAI,CAAA;EAC1D,IAAA,IAAID,KAAK,IAAIE,KAAK,EAAE,OAAOP,CAAC,CAACQ,OAAO,EAAE,IAAIT,CAAC,CAACS,OAAO,EAAE,CAAA;EACrD,IAAA,IAAIH,KAAK,IAAIE,KAAK,EAAE,OAAO,KAAK,CAAA;EAEhC,IAAA,MAAME,OAAO,GAAGT,CAAC,YAAYU,MAAM;QAAEC,OAAO,GAAGZ,CAAC,YAAYW,MAAM,CAAA;EAClE,IAAA,IAAID,OAAO,IAAIE,OAAO,EAAE,OAAOX,CAAC,CAACY,QAAQ,EAAE,IAAIb,CAAC,CAACa,QAAQ,EAAE,CAAA;EAC3D,IAAA,IAAIH,OAAO,IAAIE,OAAO,EAAE,OAAO,KAAK,CAAA;EAEpC,IAAA,MAAMnC,IAAI,GAAGM,MAAM,CAACN,IAAI,CAACwB,CAAC,CAAC,CAAA;EAC3B;;EAEA,IAAA,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,IAAI,CAAC4B,MAAM,EAAED,CAAC,EAAE,EAC9B,IAAI,CAACrB,MAAM,CAAC+B,SAAS,CAACC,cAAc,CAACC,IAAI,CAAChB,CAAC,EAAEvB,IAAI,CAAC2B,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,CAAA;EAErE,IAAA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,IAAI,CAAC4B,MAAM,EAAED,CAAC,EAAE,EAC9B,IAAG,CAACL,cAAc,CAACC,CAAC,CAACvB,IAAI,CAAC2B,CAAC,CAAC,CAAC,EAAEH,CAAC,CAACxB,IAAI,CAAC2B,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,CAAA;EAE1D,IAAA,OAAO,IAAI,CAAA;EACb,GAAC,MAAM,IAAIH,CAAC,IAAID,CAAC,IAAI,OAAOC,CAAC,KAAK,UAAU,IAAI,OAAOD,CAAC,KAAK,UAAU,EAAE;MACrE,OAAOC,CAAC,CAACY,QAAQ,EAAE,KAAKb,CAAC,CAACa,QAAQ,EAAE,CAAA;EACxC,GAAA;EAEA,EAAA,OAAO,KAAK,CAAA;EACd,CAAA;;EAEA;;ECzFA;EAEA,MAAMI,aAAa,CAAC;EAClB;;EAEA;;EAEA;;EAEA;;IAGA3C,WAAWA,CAAE4C,IAA0B,EAAE;EACvCnC,IAAAA,MAAM,CAACoC,MAAM,CAAC,IAAI,EAAED,IAAI,CAAC,CAAA;;EAEzB;MACA,OAAO,IAAI,CAACE,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,IAAI,CAACC,cAAc,CAAC,KAAK,IAAI,CAACC,QAAQ,CAACF,KAAK,CAAC,CAAC,EAAE,IAAI,CAACC,cAAc,CAAC,EAAE;EAC/F,MAAA,EAAE,IAAI,CAACE,YAAY,CAACC,KAAK,CAAA;EAC3B,KAAA;MAEA,IAAI,IAAI,CAACC,aAAa,EAAE;EACtB;QACA,OAAO,IAAI,CAACN,KAAK,CAACC,KAAK,CAAC,IAAI,CAACM,SAAS,CAAC,KAAK,IAAI,CAACJ,QAAQ,CAACF,KAAK,CAAC,IAAI,CAACG,YAAY,CAACI,GAAG,CAAC,EAAE;EACtF,QAAA,IAAI,IAAI,CAACR,KAAK,CAACf,MAAM,GAAG,IAAI,CAACsB,SAAS,GAAG,IAAI,CAACJ,QAAQ,CAAClB,MAAM,GAAG,IAAI,CAACmB,YAAY,CAACI,GAAG,EAAE,EAAE,IAAI,CAACJ,YAAY,CAACI,GAAG,CAAC,KAC1G,EAAE,IAAI,CAACD,SAAS,CAAA;EACvB,OAAA;EACF,KAAA;EACF,GAAA;;EAEA;IACA,IAAIL,cAAcA,GAAY;EAC5B,IAAA,OAAOO,IAAI,CAACC,GAAG,CAAC,IAAI,CAACH,SAAS,EAAE,IAAI,CAACH,YAAY,CAACC,KAAK,CAAC,CAAA;EAC1D,GAAA;;EAEA;IACA,IAAIC,aAAaA,GAAY;EAC3B,IAAA,OAAO,IAAI,CAACC,SAAS,GAAG,IAAI,CAACL,cAAc,CAAA;EAC7C,GAAA;;EAEA;IACA,IAAIS,QAAQA,GAAY;EACtB,IAAA,OAAO,IAAI,CAACX,KAAK,CAACY,MAAM,CAAC,IAAI,CAACV,cAAc,EAAE,IAAI,CAACI,aAAa,CAAC,CAAA;EACnE,GAAA;;EAEA;IACA,IAAIO,YAAYA,GAAY;EAC1B;EACA,IAAA,OAAOJ,IAAI,CAACK,GAAG,CAAE,IAAI,CAACV,YAAY,CAACI,GAAG,GAAG,IAAI,CAACN,cAAc;EAC1D;EACA,IAAA,IAAI,CAACC,QAAQ,CAAClB,MAAM,GAAG,IAAI,CAACe,KAAK,CAACf,MAAM,EAAE,CAAC,CAAC,CAAA;EAChD,GAAA;;EAEA;IACA,IAAI8B,OAAOA,GAAY;EACrB,IAAA,OAAO,IAAI,CAACZ,QAAQ,CAACS,MAAM,CAAC,IAAI,CAACV,cAAc,EAAE,IAAI,CAACW,YAAY,CAAC,CAAA;EACrE,GAAA;;EAEA;IACA,IAAIG,IAAIA,GAAY;MAClB,OAAO,IAAI,CAAChB,KAAK,CAACiB,SAAS,CAAC,CAAC,EAAE,IAAI,CAACf,cAAc,CAAC,CAAA;EACrD,GAAA;;EAEA;IACA,IAAIgB,IAAIA,GAAY;EAClB,IAAA,OAAO,IAAI,CAAClB,KAAK,CAACiB,SAAS,CAAC,IAAI,CAACf,cAAc,GAAG,IAAI,CAACI,aAAa,CAAC,CAAA;EACvE,GAAA;;EAEA;IACA,IAAIa,eAAeA,GAAe;EAChC,IAAA,IAAI,CAAC,IAAI,CAACN,YAAY,IAAI,IAAI,CAACP,aAAa,EAAE,OAAOrC,SAAS,CAACC,IAAI,CAAA;;EAEnE;EACA,IAAA,OACE,CAAC,IAAI,CAACkC,YAAY,CAACI,GAAG,KAAK,IAAI,CAACD,SAAS,IAAI,IAAI,CAACH,YAAY,CAACC,KAAK,KAAK,IAAI,CAACE,SAAS;EACvF;EACA,IAAA,IAAI,CAACH,YAAY,CAACI,GAAG,KAAK,IAAI,CAACJ,YAAY,CAACC,KAAK,GAEjDpC,SAAS,CAACI,KAAK,GACfJ,SAAS,CAACE,IAAI,CAAA;EAClB,GAAA;EACF;;EChEA;EACA,SAASiD,KAAKA,CAA2BC,EAAqB,EAAEvB,IAAU,EAAoB;EAC5F;IACA,OAAO,IAAIsB,KAAK,CAACE,SAAS,CAACD,EAAE,EAAEvB,IAAI,CAAC,CAAA;EACtC;;ECqKA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACO,SAASyB,WAAWA,CAAEC,IAAkC,YAAkB;IAC/E,IAAIA,IAAI,IAAI,IAAI,EAAE,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC,CAAA;EAEpE,EAAA,IAAID,IAAI,YAAYjC,MAAM,EAAE,OAAO6B,KAAK,CAACM,YAAY,CAAA;IACrD,IAAI9E,QAAQ,CAAC4E,IAAI,CAAC,EAAE,OAAOJ,KAAK,CAACO,aAAa,CAAA;EAC9C,EAAA,IAAIH,IAAI,KAAKrC,IAAI,EAAE,OAAOiC,KAAK,CAACQ,UAAU,CAAA;EAC1C,EAAA,IAAIJ,IAAI,KAAKK,MAAM,EAAE,OAAOT,KAAK,CAACU,YAAY,CAAA;EAC9C,EAAA,IAAIxE,KAAK,CAACC,OAAO,CAACiE,IAAI,CAAC,IAAIA,IAAI,KAAKlE,KAAK,EAAE,OAAO8D,KAAK,CAACW,aAAa,CAAA;EACrE,EAAA,IAAIX,KAAK,CAACY,MAAM,IAAKR,IAAI,CAAS9B,SAAS,YAAY0B,KAAK,CAACY,MAAM,EAAE,OAAOR,IAAI,CAAA;EAChF,EAAA,IAAIJ,KAAK,CAACY,MAAM,IAAIR,IAAI,YAAYJ,KAAK,CAACY,MAAM,EAAE,OAAOR,IAAI,CAACtE,WAAW,CAAA;EACzE,EAAA,IAAIsE,IAAI,YAAYS,QAAQ,EAAE,OAAOb,KAAK,CAACc,cAAc,CAAA;IAEzDC,OAAO,CAACC,IAAI,CAAC,yBAAyB,EAAEZ,IAAI,CAAC,CAAC;IAC9C,OAAOJ,KAAK,CAACY,MAAM,CAAA;EACrB,CAAA;EAmCA,SAASK,aAAaA,CAA2BvC,IAAU,EAAwB;IACjF,IAAI,CAACA,IAAI,EAAE,MAAM,IAAI2B,KAAK,CAAC,wBAAwB,CAAC,CAAA;IAEpD,IAAIL,KAAK,CAACY,MAAM,EAAE;MAChB,IAAKlC,IAAI,CAASJ,SAAS,YAAY0B,KAAK,CAACY,MAAM,EAAE,OAAO;EAAER,MAAAA,IAAI,EAAE1B,IAAAA;OAAM,CAAA;;EAE1E;EACJ;EACA;EACA;EACA;MACI,MAAM;EAAE0B,MAAAA,IAAI,GAACc,SAAS;QAAE,GAAGC,YAAAA;EAAa,KAAC,GACvCzC,IAAI,YAAYsB,KAAK,CAACY,MAAM,GAAG;EAAER,MAAAA,IAAI,EAAE1B,IAAAA;EAAK,KAAC,GAC7C/C,QAAQ,CAAC+C,IAAI,CAAC,IAAKA,IAAI,CAAyB0B,IAAI,YAAYJ,KAAK,CAACY,MAAM,GAAIlC,IAAI,GAA2B,EAAE,CAAA;EAEnH,IAAA,IAAI0B,IAAI,EAAE;EACR,MAAA,MAAMgB,KAAK,GAAIhB,IAAI,CAAYA,IAAI,CAAA;QAEnC,OAAO;EACL,QAAA,GAAGpE,IAAI,CAACoE,IAAI,EAAE,CAAChE,CAAC,EAAEC,CAAS,KAAK,CAACA,CAAC,CAACgF,UAAU,CAAC,GAAG,CAAC,CAAC;UACnDjB,IAAI,EAAEA,IAAI,CAACtE,WAAW;UACtBsF,KAAK;UACL,GAAGD,YAAAA;SACJ,CAAA;EACH,KAAA;EACF,GAAA;EAEA,EAAA,IAAI,CAACxF,QAAQ,CAAC+C,IAAI,CAAC,EAAE,OAAO;EAAE0B,IAAAA,IAAI,EAAE1B,IAAAA;KAAM,CAAA;IAE1C,OAAO;MAAE,GAAGA,IAAAA;KAAM,CAAA;EACpB,CAAA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EAEA,SAAS4C,UAAUA,CAA2B5C,IAAU,EAA6B;IACnF,IAAIsB,KAAK,CAACY,MAAM,IAAKlC,IAAI,YAAYsB,KAAK,CAACY,MAAO,EAAE,OAAOlC,IAAI,CAAA;EAC/D,EAAA,MAAM6C,KAAK,GAAGN,aAAa,CAACvC,IAAI,CAAC,CAAA;EAEjC,EAAA,MAAM8C,WAAW,GAAGrB,WAAW,CAACoB,KAAK,CAACnB,IAAI,CAAC,CAAA;IAC3C,IAAI,CAACoB,WAAW,EAAE,MAAM,IAAInB,KAAK,CAAA,8CAAA,GAAgDkB,KAAK,CAACnB,IAAI,GAAA,0EAA0E,CAAC,CAAA;IAEtK,IAAImB,KAAK,CAACnB,IAAI,KAAKoB,WAAW,EAAE,OAAOD,KAAK,CAACnB,IAAI,CAAA;IACjD,IAAKmB,KAAK,CAASH,KAAK,EAAE;EAAEG,IAAAA,KAAK,CAACnB,IAAI,GAAImB,KAAK,CAASH,KAAK,CAAA;MAAE,OAAQG,KAAK,CAASH,KAAK,CAAA;EAAE,GAAA;EAC5F,EAAA,OAAO,IAAII,WAAW,CAACD,KAAK,CAAC,CAAA;EAC/B,CAAA;EAGAvB,KAAK,CAACsB,UAAU,GAAGA,UAAU;;ECtU7B;EAEA,MAAeG,WAAW,CAAC;EACzB;;EAEA;;EAEA;;EAGA;IACA,IAAIC,cAAcA,GAAY;EAC5B,IAAA,IAAIzC,KAAK,CAAA;MACT,IAAI;QACFA,KAAK,GAAG,IAAI,CAAC0C,qBAAqB,CAAA;OACnC,CAAC,MAAM,EAAC;MAET,OAAO1C,KAAK,IAAI,IAAI,GAClBA,KAAK,GACL,IAAI,CAACL,KAAK,CAACf,MAAM,CAAA;EACrB,GAAA;;EAEA;IACA,IAAI+D,YAAYA,GAAY;EAC1B,IAAA,IAAIxC,GAAG,CAAA;MACP,IAAI;QACFA,GAAG,GAAG,IAAI,CAACyC,mBAAmB,CAAA;OAC/B,CAAC,MAAM,EAAC;MAET,OAAOzC,GAAG,IAAI,IAAI,GAChBA,GAAG,GACH,IAAI,CAACR,KAAK,CAACf,MAAM,CAAA;EACrB,GAAA;;EAEA;EACAiE,EAAAA,MAAMA,CAAE7C,KAAa,EAAEG,GAAW,EAAE;EAClC,IAAA,IAAIH,KAAK,IAAI,IAAI,IAAIG,GAAG,IAAI,IAAI,IAC9BH,KAAK,KAAK,IAAI,CAACyC,cAAc,IAAItC,GAAG,KAAK,IAAI,CAACwC,YAAY,EAAE,OAAA;MAE9D,IAAI;EACF,MAAA,IAAI,CAACG,aAAa,CAAC9C,KAAK,EAAEG,GAAG,CAAC,CAAA;OAC/B,CAAC,MAAM,EAAC;EACX,GAAA;;EAEA;IACA,IAAI4C,QAAQA,GAAa;EAAE,IAAA,OAAO,KAAK,CAAA;EAAE,GAAA;EACzC;;EAEA;;EAEA;EAEF,CAAA;EAGAhC,KAAK,CAACyB,WAAW,GAAGA,WAAW;;ECtE/B,MAAMQ,KAAK,GAAG,EAAE,CAAA;EAChB,MAAMC,KAAK,GAAG,EAAE,CAAA;;EAEhB;EAEA,MAAeC,eAAe,SAASV,WAAW,CAAC;EACjD;;IAKA3F,WAAWA,CAAEsG,KAAkB,EAAE;EAC/B,IAAA,KAAK,EAAE,CAAA;MACP,IAAI,CAACA,KAAK,GAAGA,KAAK,CAAA;MAClB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC,CAAA;MAC5C,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACD,IAAI,CAAC,IAAI,CAAC,CAAA;MACxC,IAAI,CAACE,cAAc,GAAG,IAAI,CAACA,cAAc,CAACF,IAAI,CAAC,IAAI,CAAC,CAAA;MACpD,IAAI,CAACG,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAACH,IAAI,CAAC,IAAI,CAAC,CAAA;EAC5D,GAAA;IAEA,IAAII,WAAWA,GAAkB;EAAA,IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,WAAA,CAAA;MAC/B,OAAAF,CAAAA,qBAAA,IAAAC,sBAAA,GAAQ,CAAAC,WAAA,GAAA,IAAI,CAACT,KAAK,EAACU,WAAW,KAAtBF,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAApE,IAAA,CAAAqE,WAAyB,CAAC,KAAA,IAAA,GAAAF,qBAAA,GAAII,QAAQ,CAAA;EAChD,GAAA;;EAEA;IACA,IAAIf,QAAQA,GAAa;MACvB,OAAO,IAAI,CAACI,KAAK,KAAK,IAAI,CAACM,WAAW,CAACM,aAAa,CAAA;EACtD,GAAA;;EAEA;IACSC,UAAUA,CAAEC,QAAuB,EAAE;MAC5C,IAAI,CAACd,KAAK,CAACe,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACd,UAA2B,CAAC,CAAA;MACxE,IAAI,CAACD,KAAK,CAACe,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACZ,QAAyB,CAAC,CAAA;MACpE,IAAI,CAACH,KAAK,CAACe,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACX,cAA+B,CAAC,CAAA;MAChF,IAAI,CAACJ,KAAK,CAACe,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAACV,iBAAkC,CAAC,CAAA;MACtF,IAAI,CAACL,KAAK,CAACe,gBAAgB,CAAC,MAAM,EAAED,QAAQ,CAACE,IAAI,CAAC,CAAA;MAClD,IAAI,CAAChB,KAAK,CAACe,gBAAgB,CAAC,OAAO,EAAED,QAAQ,CAACG,KAAK,CAAC,CAAA;MACpD,IAAI,CAACjB,KAAK,CAACe,gBAAgB,CAAC,OAAO,EAAED,QAAQ,CAACI,KAAK,CAAC,CAAA;MACpD,IAAI,CAAClB,KAAK,CAACe,gBAAgB,CAAC,MAAM,EAAED,QAAQ,CAACK,MAAM,CAAC,CAAA;MACpD,IAAI,CAACC,SAAS,GAAGN,QAAQ,CAAA;EAC3B,GAAA;IAEAb,UAAUA,CAAEoB,CAAgB,EAAE;EAC5B,IAAA,IAAI,IAAI,CAACD,SAAS,CAACE,IAAI,KACpBD,CAAC,CAACE,OAAO,KAAK1B,KAAK,IAAIwB,CAAC,CAACG,QAAQ,KAAKH,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACK,OAAO,CAAC,IAC7DL,CAAC,CAACE,OAAO,KAAKzB,KAAK,IAAIuB,CAAC,CAACK,OAAQ,CACnC,EAAE;QACDL,CAAC,CAACM,cAAc,EAAE,CAAA;EAClB,MAAA,OAAO,IAAI,CAACP,SAAS,CAACE,IAAI,CAACD,CAAC,CAAC,CAAA;EAC/B,KAAA;MAEA,IAAI,IAAI,CAACD,SAAS,CAACQ,IAAI,IAAIP,CAAC,CAACE,OAAO,KAAK1B,KAAK,KAAKwB,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACK,OAAO,CAAC,EAAE;QAC1EL,CAAC,CAACM,cAAc,EAAE,CAAA;EAClB,MAAA,OAAO,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACP,CAAC,CAAC,CAAA;EAC/B,KAAA;EAEA,IAAA,IAAI,CAACA,CAAC,CAACQ,WAAW,EAAE,IAAI,CAACT,SAAS,CAACU,eAAe,CAACT,CAAC,CAAC,CAAA;EACvD,GAAA;IAEAjB,cAAcA,CAAEiB,CAAa,EAAE;MAC7B,IAAIA,CAAC,CAACU,SAAS,KAAK,aAAa,IAAI,IAAI,CAACX,SAAS,CAACQ,IAAI,EAAE;QACxDP,CAAC,CAACM,cAAc,EAAE,CAAA;EAClB,MAAA,OAAO,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACP,CAAC,CAAC,CAAA;EAC/B,KAAA;MAEA,IAAIA,CAAC,CAACU,SAAS,KAAK,aAAa,IAAI,IAAI,CAACX,SAAS,CAACE,IAAI,EAAE;QACxDD,CAAC,CAACM,cAAc,EAAE,CAAA;EAClB,MAAA,OAAO,IAAI,CAACP,SAAS,CAACE,IAAI,CAACD,CAAC,CAAC,CAAA;EAC/B,KAAA;EACF,GAAA;IAEAhB,iBAAiBA,CAAEgB,CAAmB,EAAE;EACtC,IAAA,IAAI,CAACD,SAAS,CAACpB,KAAK,CAACqB,CAAC,CAAC,CAAA;EACzB,GAAA;IAEAlB,QAAQA,CAAEkB,CAAa,EAAE;EACvB,IAAA,IAAI,CAACA,CAAC,CAACQ,WAAW,EAAE,IAAI,CAACT,SAAS,CAACpB,KAAK,CAACqB,CAAC,CAAC,CAAA;EAC7C,GAAA;;EAEA;EACSW,EAAAA,YAAYA,GAAI;MACvB,IAAI,CAAChC,KAAK,CAACiC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAChC,UAA2B,CAAC,CAAA;MAC3E,IAAI,CAACD,KAAK,CAACiC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC9B,QAAyB,CAAC,CAAA;MACvE,IAAI,CAACH,KAAK,CAACiC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC7B,cAA+B,CAAC,CAAA;MACnF,IAAI,CAACJ,KAAK,CAACiC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC5B,iBAAkC,CAAC,CAAA;EACzF,IAAA,IAAI,CAACL,KAAK,CAACiC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACb,SAAS,CAACJ,IAAI,CAAC,CAAA;EAC3D,IAAA,IAAI,CAAChB,KAAK,CAACiC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACb,SAAS,CAACH,KAAK,CAAC,CAAA;EAC7D,IAAA,IAAI,CAACjB,KAAK,CAACiC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACb,SAAS,CAACF,KAAK,CAAC,CAAA;EAC7D,IAAA,IAAI,CAAClB,KAAK,CAACiC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACb,SAAS,CAACD,MAAM,CAAC,CAAA;EAC7D,IAAA,IAAI,CAACC,SAAS,GAAG,EAAmB,CAAA;EACtC,GAAA;EACF,CAAA;EAGAxD,KAAK,CAACmC,eAAe,GAAGA,eAAe;;EC5FvC;EAEA,MAAMmC,oBAAoB,SAASnC,eAAe,CAAC;EACjD;;IAGArG,WAAWA,CAAEsG,KAAmB,EAAE;MAChC,KAAK,CAACA,KAAK,CAAC,CAAA;MACZ,IAAI,CAACA,KAAK,GAAGA,KAAK,CAAA;EACpB,GAAA;;EAEA;IACA,IAAaT,qBAAqBA,GAAmB;EACnD,IAAA,OAAO,IAAI,CAACS,KAAK,CAACV,cAAc,IAAI,IAAI,GAAG,IAAI,CAACU,KAAK,CAACV,cAAc,GAAG,IAAI,CAAC9C,KAAK,CAACf,MAAM,CAAA;EAC1F,GAAA;;EAEA;IACA,IAAagE,mBAAmBA,GAAmB;EACjD,IAAA,OAAO,IAAI,CAACO,KAAK,CAACR,YAAY,CAAA;EAChC,GAAA;;EAEA;EACAG,EAAAA,aAAaA,CAAE9C,KAAa,EAAEG,GAAW,EAAE;MACzC,IAAI,CAACgD,KAAK,CAACmC,iBAAiB,CAACtF,KAAK,EAAEG,GAAG,CAAC,CAAA;EAC1C,GAAA;IAEA,IAAaR,KAAKA,GAAY;EAC5B,IAAA,OAAO,IAAI,CAACwD,KAAK,CAACxD,KAAK,CAAA;EACzB,GAAA;IACA,IAAaA,KAAKA,CAAEA,KAAa,EAAE;EACjC,IAAA,IAAI,CAACwD,KAAK,CAACxD,KAAK,GAAGA,KAAK,CAAA;EAC1B,GAAA;EACF,CAAA;EAGAoB,KAAK,CAACmC,eAAe,GAAGA,eAAe;;ECpCvC,MAAMqC,8BAA8B,SAASrC,eAAe,CAAC;EAE3D;IACA,IAAaR,qBAAqBA,GAAmB;EACnD,IAAA,MAAM8C,IAAI,GAAG,IAAI,CAAC/B,WAAW,CAAA;MAC7B,MAAMgC,SAAS,GAAGD,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACE,YAAY,EAAE,CAAA;EAC1D,IAAA,MAAMC,YAAY,GAAGF,SAAS,IAAIA,SAAS,CAACE,YAAY,CAAA;EACxD,IAAA,MAAMC,WAAW,GAAGH,SAAS,IAAIA,SAAS,CAACG,WAAW,CAAA;MACtD,IAAIA,WAAW,IAAI,IAAI,IAAID,YAAY,IAAI,IAAI,IAAIA,YAAY,GAAGC,WAAW,EAAE;EAC7E,MAAA,OAAOD,YAAY,CAAA;EACrB,KAAA;EACA,IAAA,OAAOC,WAAW,CAAA;EACpB,GAAA;;EAEA;IACA,IAAahD,mBAAmBA,GAAmB;EACjD,IAAA,MAAM4C,IAAI,GAAG,IAAI,CAAC/B,WAAW,CAAA;MAC7B,MAAMgC,SAAS,GAAGD,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACE,YAAY,EAAE,CAAA;EAC1D,IAAA,MAAMC,YAAY,GAAGF,SAAS,IAAIA,SAAS,CAACE,YAAY,CAAA;EACxD,IAAA,MAAMC,WAAW,GAAGH,SAAS,IAAIA,SAAS,CAACG,WAAW,CAAA;MACtD,IAAIA,WAAW,IAAI,IAAI,IAAID,YAAY,IAAI,IAAI,IAAIA,YAAY,GAAGC,WAAW,EAAE;EAC7E,MAAA,OAAOD,YAAY,CAAA;EACrB,KAAA;EACA,IAAA,OAAOC,WAAW,CAAA;EACpB,GAAA;;EAEA;EACS9C,EAAAA,aAAaA,CAAE9C,KAAa,EAAEG,GAAW,EAAE;EAClD,IAAA,IAAI,CAAC,IAAI,CAACsD,WAAW,CAACoC,WAAW,EAAE,OAAA;MAEnC,MAAMC,KAAK,GAAG,IAAI,CAACrC,WAAW,CAACoC,WAAW,EAAE,CAAA;EAC5CC,IAAAA,KAAK,CAACC,QAAQ,CAAC,IAAI,CAAC5C,KAAK,CAAC6C,UAAU,IAAI,IAAI,CAAC7C,KAAK,EAAEnD,KAAK,CAAC,CAAA;EAC1D8F,IAAAA,KAAK,CAACG,MAAM,CAAC,IAAI,CAAC9C,KAAK,CAAC+C,SAAS,IAAI,IAAI,CAAC/C,KAAK,EAAEhD,GAAG,CAAC,CAAA;EACrD,IAAA,MAAMqF,IAAI,GAAG,IAAI,CAAC/B,WAAW,CAAA;MAC7B,MAAMgC,SAAS,GAAGD,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACE,YAAY,EAAE,CAAA;EAC1D,IAAA,IAAID,SAAS,EAAE;QACbA,SAAS,CAACU,eAAe,EAAE,CAAA;EAC3BV,MAAAA,SAAS,CAACW,QAAQ,CAACN,KAAK,CAAC,CAAA;EAC3B,KAAA;EACF,GAAA;;EAEA;IACA,IAAanG,KAAKA,GAAY;EAC5B,IAAA,OAAO,IAAI,CAACwD,KAAK,CAACkD,WAAW,IAAI,EAAE,CAAA;EACrC,GAAA;IACA,IAAa1G,KAAKA,CAAEA,KAAa,EAAE;EACjC,IAAA,IAAI,CAACwD,KAAK,CAACkD,WAAW,GAAG1G,KAAK,CAAA;EAChC,GAAA;EACF,CAAA;EAGAoB,KAAK,CAACwE,8BAA8B,GAAGA,8BAA8B;;EC7CrE,MAAMe,YAAY,CAAC;IAAAzJ,WAAA,GAAA;MAAA,IAEjB0J,CAAAA,MAAM,GAAwB,EAAE,CAAA;MAAA,IAChCC,CAAAA,YAAY,GAAG,CAAC,CAAA;EAAA,GAAA;IAEhB,IAAIC,YAAYA,GAAmC;EACjD,IAAA,OAAO,IAAI,CAACF,MAAM,CAAC,IAAI,CAACC,YAAY,CAAC,CAAA;EACvC,GAAA;IAEA,IAAIE,OAAOA,GAAa;EACtB,IAAA,OAAO,IAAI,CAACH,MAAM,CAAC3H,MAAM,KAAK,CAAC,CAAA;EACjC,GAAA;IAEA+H,IAAIA,CAAEC,KAAwB,EAAE;EAC9B;MACA,IAAI,IAAI,CAACJ,YAAY,GAAG,IAAI,CAACD,MAAM,CAAC3H,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC2H,MAAM,CAAC3H,MAAM,GAAG,IAAI,CAAC4H,YAAY,GAAG,CAAC,CAAA;EAC1F,IAAA,IAAI,CAACD,MAAM,CAACI,IAAI,CAACC,KAAK,CAAC,CAAA;EACvB,IAAA,IAAI,IAAI,CAACL,MAAM,CAAC3H,MAAM,GAAG0H,YAAY,CAACO,UAAU,EAAE,IAAI,CAACN,MAAM,CAACO,KAAK,EAAE,CAAA;MACrE,IAAI,CAACN,YAAY,GAAG,IAAI,CAACD,MAAM,CAAC3H,MAAM,GAAG,CAAC,CAAA;EAC5C,GAAA;IAEAmI,EAAEA,CAAEC,KAAa,EAAiC;MAChD,IAAI,CAACR,YAAY,GAAGpG,IAAI,CAACC,GAAG,CAACD,IAAI,CAACK,GAAG,CAAC,IAAI,CAAC+F,YAAY,GAAGQ,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAACT,MAAM,CAAC3H,MAAM,GAAG,CAAC,CAAC,CAAA;MAC5F,OAAO,IAAI,CAAC6H,YAAY,CAAA;EAC1B,GAAA;EAEA1B,EAAAA,IAAIA,GAAI;EACN,IAAA,OAAO,IAAI,CAACgC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;EACpB,GAAA;EAEAtC,EAAAA,IAAIA,GAAI;EACN,IAAA,OAAO,IAAI,CAACsC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;EACpB,GAAA;EAEAE,EAAAA,KAAKA,GAAI;EACP,IAAA,IAAI,CAACV,MAAM,CAAC3H,MAAM,GAAG,CAAC,CAAA;MACtB,IAAI,CAAC4H,YAAY,GAAG,CAAC,CAAA;EACvB,GAAA;EACF,CAAA;EAtCMF,YAAY,CACTO,UAAU,GAAG,GAAG;;ECKzB;EAEA,MAAM5F,SAAS,CAAkD;EAC/D;EACF;EACA;;EAGE;;EAcApE,EAAAA,WAAWA,CAAEmE,EAAoB,EAAEvB,IAAU,EAAE;EAC7C,IAAA,IAAI,CAACuB,EAAE,GACJA,EAAE,YAAYwB,WAAW,GAAIxB,EAAE,GAC/BA,EAAE,CAACkG,iBAAiB,IAAIlG,EAAE,CAACmG,OAAO,KAAK,OAAO,IAAInG,EAAE,CAACmG,OAAO,KAAK,UAAU,GAAI,IAAI5B,8BAA8B,CAACvE,EAAE,CAAC,GACtH,IAAIqE,oBAAoB,CAACrE,EAAkB,CAAC,CAAA;EAE9C,IAAA,IAAI,CAACoG,MAAM,GAAG/E,UAAU,CAAC5C,IAAI,CAAC,CAAA;EAE9B,IAAA,IAAI,CAAC4H,UAAU,GAAG,EAAE,CAAA;MACpB,IAAI,CAACC,MAAM,GAAG,EAAE,CAAA;MAChB,IAAI,CAACC,cAAc,GAAG,EAAE,CAAA;MACxB,IAAI,CAACC,cAAc,GAAG,EAAE,CAAA;EACxB,IAAA,IAAI,CAACC,OAAO,GAAG,IAAInB,YAAY,EAAE,CAAA;MAEjC,IAAI,CAACoB,cAAc,GAAG,IAAI,CAACA,cAAc,CAACrE,IAAI,CAAC,IAAI,CAAC,CAAA;MACpD,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACD,IAAI,CAAC,IAAI,CAAC,CAAA;MACxC,IAAI,CAACsE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACtE,IAAI,CAAC,IAAI,CAAC,CAAA;MAC1C,IAAI,CAACuE,OAAO,GAAG,IAAI,CAACA,OAAO,CAACvE,IAAI,CAAC,IAAI,CAAC,CAAA;MACtC,IAAI,CAACwE,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACxE,IAAI,CAAC,IAAI,CAAC,CAAA;MACxC,IAAI,CAACyE,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACzE,IAAI,CAAC,IAAI,CAAC,CAAA;MACxC,IAAI,CAAC0E,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC1E,IAAI,CAAC,IAAI,CAAC,CAAA;MACtC,IAAI,CAAC2E,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC3E,IAAI,CAAC,IAAI,CAAC,CAAA;MACtC,IAAI,CAAC4E,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC5E,IAAI,CAAC,IAAI,CAAC,CAAA;MAC9C,IAAI,CAAC6E,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAAC7E,IAAI,CAAC,IAAI,CAAC,CAAA;MAE9D,IAAI,CAAC8E,WAAW,EAAE,CAAA;;EAElB;MACA,IAAI,CAACC,WAAW,EAAE,CAAA;MAClB,IAAI,CAACT,SAAS,EAAE,CAAA;EAClB,GAAA;IAEAU,UAAUA,CAAElH,IAAS,EAAW;EAAA,IAAA,IAAAmH,YAAA,CAAA;EAC9B,IAAA,OAAOnH,IAAI,IAAI,IAAI,KAAA,CAAAmH,YAAA,GAAI,IAAI,CAAClB,MAAM,qBAAXkB,YAAA,CAAaD,UAAU,CAAClH,IAAI,CAAC,CAAA,CAAA;EACtD,GAAA;;EAEA;IACA,IAAIA,IAAIA,GAAuC;EAC7C,IAAA,OAAO,IAAI,CAACiG,MAAM,CAACjG,IAAI,CAAA;EACzB,GAAA;IACA,IAAIA,IAAIA,CAAEA,IAAS,EAAE;EACnB,IAAA,IAAI,IAAI,CAACkH,UAAU,CAAClH,IAAI,CAAC,EAAE,OAAA;EAE3B,IAAA,IAAI,EAAGA,IAAI,YAAuBJ,KAAK,CAACY,MAAM,CAAC,IAAI,IAAI,CAACyF,MAAM,CAACvK,WAAW,KAAKqE,WAAW,CAACC,IAAc,CAAC,EAAE;EAC1G;EACA,MAAA,IAAI,CAACiG,MAAM,CAACmB,aAAa,CAAC;EAAEpH,QAAAA,IAAAA;EAAK,OAAQ,CAAC,CAAA;EAC1C,MAAA,OAAA;EACF,KAAA;MAEA,MAAMiG,MAAM,GAAIjG,IAAI,YAAYJ,KAAK,CAACY,MAAM,GAAGR,IAAI,GAAGkB,UAAU,CAAC;EAAElB,MAAAA,IAAAA;EAAK,KAAS,CAA+B,CAAA;EAChHiG,IAAAA,MAAM,CAACoB,aAAa,GAAG,IAAI,CAACpB,MAAM,CAACoB,aAAa,CAAA;MAChD,IAAI,CAACpB,MAAM,GAAGA,MAAM,CAAA;EACtB,GAAA;;EAEA;IACA,IAAIzH,KAAKA,GAAY;MACnB,OAAO,IAAI,CAAC2H,MAAM,CAAA;EACpB,GAAA;IAEA,IAAI3H,KAAKA,CAAEnD,GAAW,EAAE;EACtB,IAAA,IAAI,IAAI,CAACmD,KAAK,KAAKnD,GAAG,EAAE,OAAA;EAExB,IAAA,IAAI,CAAC4K,MAAM,CAACzH,KAAK,GAAGnD,GAAG,CAAA;EACvB,IAAA,IAAI,CAACiM,aAAa,CAAC,MAAM,CAAC,CAAA;EAC5B,GAAA;;EAEA;IACA,IAAID,aAAaA,GAAY;MAC3B,OAAO,IAAI,CAACjB,cAAc,CAAA;EAC5B,GAAA;IAEA,IAAIiB,aAAaA,CAAEhM,GAAW,EAAE;EAC9B,IAAA,IAAI,IAAI,CAACgM,aAAa,KAAKhM,GAAG,EAAE,OAAA;EAEhC,IAAA,IAAI,CAAC4K,MAAM,CAACoB,aAAa,GAAGhM,GAAG,CAAA;EAC/B,IAAA,IAAI,CAACiM,aAAa,CAAC,MAAM,CAAC,CAAA;EAC5B,GAAA;;EAEE;IACF,IAAIC,aAAaA,GAAY;MAC3B,OAAO,IAAI,CAAClB,cAAc,CAAA;EAC5B,GAAA;IAEA,IAAIkB,aAAaA,CAAElM,GAAW,EAAE;EAC9B,IAAA,IAAI,IAAI,CAACkM,aAAa,KAAKlM,GAAG,EAAE,OAAA;EAEhC,IAAA,IAAI,CAAC4K,MAAM,CAACsB,aAAa,GAAGlM,GAAG,CAAA;MAC/B,IAAI,CAACiM,aAAa,EAAE,CAAA;MACpB,IAAI,CAACR,WAAW,EAAE,CAAA;EACpB,GAAA;;EAEA;IACA,IAAIU,UAAUA,GAA6C;EACzD,IAAA,OAAO,IAAI,CAACvB,MAAM,CAACuB,UAAU,CAAA;EAC/B,GAAA;IAEA,IAAIA,UAAUA,CAAEC,GAA4C,EAAE;MAC5D,IAAI,IAAI,CAACxB,MAAM,CAACyB,gBAAgB,CAACD,GAAG,CAAC,EAAE,OAAA;EAEvC,IAAA,IAAI,CAACxB,MAAM,CAACuB,UAAU,GAAGC,GAAG,CAAA;EAC5B,IAAA,IAAI,CAACH,aAAa,CAAC,MAAM,CAAC,CAAA;EAC5B,GAAA;;EAEA;IACA,IAAIK,YAAYA,GAAY;EAC1B,IAAA,OAAO,IAAI,CAAC1B,MAAM,CAAC0B,YAAY,CAAA;EACjC,GAAA;;EAEA;EACAX,EAAAA,WAAWA,GAAI;EACb,IAAA,IAAI,CAACnH,EAAE,CAACgD,UAAU,CAAC;QACjBiB,eAAe,EAAE,IAAI,CAACyC,cAAc;QACpCvE,KAAK,EAAE,IAAI,CAACG,QAAQ;QACpBa,IAAI,EAAE,IAAI,CAACyD,OAAO;QAClBxD,KAAK,EAAE,IAAI,CAAC0D,QAAQ;QACpBzD,KAAK,EAAE,IAAI,CAACwD,QAAQ;QACpBvD,MAAM,EAAE,IAAI,CAACqD,SAAS;QACtB5C,IAAI,EAAE,IAAI,CAACgD,OAAO;QAClBtD,IAAI,EAAE,IAAI,CAACuD,OAAAA;EACb,KAAC,CAAC,CAAA;EACJ,GAAA;;EAEA;EACAe,EAAAA,aAAaA,GAAI;MACf,IAAI,IAAI,CAAC/H,EAAE,EAAE,IAAI,CAACA,EAAE,CAACmE,YAAY,EAAE,CAAA;EACrC,GAAA;;EAEA;EACA6D,EAAAA,UAAUA,CAAEC,EAAU,EAAEzE,CAAc,EAAE;EACtC,IAAA,MAAM0E,SAAS,GAAG,IAAI,CAAC7B,UAAU,CAAC4B,EAAE,CAAC,CAAA;MACrC,IAAI,CAACC,SAAS,EAAE,OAAA;MAEhBA,SAAS,CAACC,OAAO,CAACC,CAAC,IAAIA,CAAC,CAAC5E,CAAC,CAAC,CAAC,CAAA;EAC9B,GAAA;;EAEA;IACA,IAAI/B,cAAcA,GAAY;EAC5B,IAAA,OAAO,IAAI,CAAC4G,eAAe,GACzB,IAAI,CAACC,kBAAkB,GAEvB,IAAI,CAACtI,EAAE,CAACyB,cAAc,CAAA;EAC1B,GAAA;;EAEA;IACA,IAAIvC,SAASA,GAAY;EACvB,IAAA,OAAO,IAAI,CAACmJ,eAAe,GACzB,IAAI,CAACC,kBAAkB,GAEvB,IAAI,CAACtI,EAAE,CAAC2B,YAAY,CAAA;EACxB,GAAA;IACA,IAAIzC,SAASA,CAAEqJ,GAAW,EAAE;MAC1B,IAAI,CAAC,IAAI,CAACvI,EAAE,IAAI,CAAC,IAAI,CAACA,EAAE,CAAC+B,QAAQ,EAAE,OAAA;MAEnC,IAAI,CAAC/B,EAAE,CAAC6B,MAAM,CAAC0G,GAAG,EAAEA,GAAG,CAAC,CAAA;MACxB,IAAI,CAAC7B,cAAc,EAAE,CAAA;EACvB,GAAA;;EAEA;EACAA,EAAAA,cAAcA;MAAY;MACxB,IAAI,IAAI,CAACoB,YAAY,KAAK,IAAI,CAAC9H,EAAE,CAACrB,KAAK,EAAE;EACvCmC,MAAAA,OAAO,CAACC,IAAI,CAAC,yGAAyG,CAAC,CAAC;EAC1H,KAAA;MACA,IAAI,CAACyH,UAAU,GAAG;QAChBxJ,KAAK,EAAE,IAAI,CAACyC,cAAc;QAC1BtC,GAAG,EAAE,IAAI,CAACD,SAAAA;OACX,CAAA;EACH,GAAA;;EAEA;EACAkI,EAAAA,WAAWA,GAAI;MACb,IAAI,CAAChB,MAAM,CAACzH,KAAK,GAAG,IAAI,CAACqB,EAAE,CAACrB,KAAK,CAAA;EACjC,IAAA,IAAI,CAAC2H,MAAM,GAAG,IAAI,CAACF,MAAM,CAACzH,KAAK,CAAA;EAC/B,IAAA,IAAI,CAAC4H,cAAc,GAAG,IAAI,CAACH,MAAM,CAACoB,aAAa,CAAA;EAC/C,IAAA,IAAI,CAAChB,cAAc,GAAG,IAAI,CAACJ,MAAM,CAACsB,aAAa,CAAA;EACjD,GAAA;;EAEA;IACAD,aAAaA,CAAEvI,SAA2B,EAAE;EAC1C,IAAA,MAAMuJ,gBAAgB,GAAG,IAAI,CAACrC,MAAM,CAACoB,aAAa,CAAA;EAClD,IAAA,MAAMkB,QAAQ,GAAG,IAAI,CAACtC,MAAM,CAACzH,KAAK,CAAA;EAClC,IAAA,MAAMgK,gBAAgB,GAAG,IAAI,CAACvC,MAAM,CAACsB,aAAa,CAAA;EAClD,IAAA,MAAMkB,eAAe,GAAG,IAAI,CAACd,YAAY,CAAA;EAEzC,IAAA,MAAMe,SAAS,GACb,IAAI,CAACrB,aAAa,KAAKiB,gBAAgB,IACvC,IAAI,CAAC9J,KAAK,KAAK+J,QAAQ,IACvB,IAAI,CAAClC,cAAc,KAAKmC,gBAAgB,CAAA;MAG1C,IAAI,CAACpC,cAAc,GAAGkC,gBAAgB,CAAA;MACtC,IAAI,CAACnC,MAAM,GAAGoC,QAAQ,CAAA;MACtB,IAAI,CAAClC,cAAc,GAAGmC,gBAAgB,CAAA;EAEtC,IAAA,IAAI,IAAI,CAAC3I,EAAE,CAACrB,KAAK,KAAKiK,eAAe,EAAE,IAAI,CAAC5I,EAAE,CAACrB,KAAK,GAAGiK,eAAe,CAAA;MAEtE,IAAI1J,SAAS,KAAK,MAAM,EAAE,IAAI,CAAC+H,WAAW,EAAE,CAAC,KACxC,IAAI/H,SAAS,IAAI,IAAI,EAAE,IAAI,CAACA,SAAS,GAAGA,SAAS,CAAA;EAEtD,IAAA,IAAI2J,SAAS,EAAE,IAAI,CAACC,iBAAiB,EAAE,CAAA;EACvC,IAAA,IAAI,CAAC,IAAI,CAACC,gBAAgB,KAAKF,SAAS,IAAI,IAAI,CAACpC,OAAO,CAACf,OAAO,CAAC,EAAE,IAAI,CAACe,OAAO,CAACd,IAAI,CAAC;EACnF6B,MAAAA,aAAa,EAAEiB,gBAAgB;EAC/BhE,MAAAA,SAAS,EAAE;UAAEzF,KAAK,EAAE,IAAI,CAACyC,cAAc;UAAEtC,GAAG,EAAE,IAAI,CAACD,SAAAA;EAAU,OAAA;EAC/D,KAAC,CAAC,CAAA;EACJ,GAAA;;EAEA;IACAqI,aAAaA,CAAC9I,IAAsB,EAAE;MACpC,MAAM;QAAE0B,IAAI;QAAE,GAAG6I,QAAAA;OAAU,GAAGvK,IAAW,CAAC;;MAE1C,MAAMwK,UAAU,GAAG,CAAC,IAAI,CAAC5B,UAAU,CAAClH,IAAI,CAAC,CAAA;MACzC,MAAM+I,UAAU,GAAG,IAAI,CAAC9C,MAAM,CAAC+C,gBAAgB,CAACH,QAAQ,CAAC,CAAA;EAEzD,IAAA,IAAIC,UAAU,EAAE,IAAI,CAAC9I,IAAI,GAAGA,IAAI,CAAA;MAChC,IAAI+I,UAAU,EAAE,IAAI,CAAC9C,MAAM,CAACmB,aAAa,CAACyB,QAAQ,CAAC,CAAC;;MAEpD,IAAIC,UAAU,IAAIC,UAAU,EAAE,IAAI,CAACzB,aAAa,EAAE,CAAA;EACpD,GAAA;;EAEA;IACA2B,YAAYA,CAAElK,SAAiB,EAAE;MAC/B,IAAIA,SAAS,IAAI,IAAI,EAAE,OAAA;MACvB,IAAI,CAACA,SAAS,GAAGA,SAAS,CAAA;;EAE1B;EACA,IAAA,IAAI,CAACmK,kBAAkB,CAACnK,SAAS,CAAC,CAAA;EACpC,GAAA;;EAEA;IACAmK,kBAAkBA,CAAEnK,SAAiB,EAAE;MACrC,IAAI,CAACoK,kBAAkB,EAAE,CAAA;MACzB,IAAI,CAAChB,kBAAkB,GAAGpJ,SAAS,CAAA;EACnC,IAAA,IAAI,CAACmJ,eAAe,GAAGkB,UAAU,CAAC,MAAM;EACtC,MAAA,IAAI,CAAC,IAAI,CAACvJ,EAAE,EAAE,OAAO;EACrB,MAAA,IAAI,CAACd,SAAS,GAAG,IAAI,CAACoJ,kBAAkB,CAAA;QACxC,IAAI,CAACgB,kBAAkB,EAAE,CAAA;OAC1B,EAAE,EAAE,CAAC,CAAA;EACR,GAAA;;EAEA;EACAR,EAAAA,iBAAiBA,GAAI;MACnB,IAAI,CAACd,UAAU,CAAC,QAAQ,EAAE,IAAI,CAACwB,WAAW,CAAC,CAAA;EAC3C,IAAA,IAAI,IAAI,CAACpD,MAAM,CAACqD,UAAU,EAAE,IAAI,CAACzB,UAAU,CAAC,UAAU,EAAE,IAAI,CAACwB,WAAW,CAAC,CAAA;EAC3E,GAAA;;EAEA;EACAF,EAAAA,kBAAkBA,GAAI;MACpB,IAAI,IAAI,CAACjB,eAAe,EAAE;EACxBqB,MAAAA,YAAY,CAAC,IAAI,CAACrB,eAAe,CAAC,CAAA;QAClC,OAAO,IAAI,CAACA,eAAe,CAAA;EAC7B,KAAA;EACF,GAAA;;EAEA;EACApB,EAAAA,WAAWA,GAAI;MACb,IAAI,CAAC/H,SAAS,GAAG,IAAI,CAACkH,MAAM,CAACuD,eAAe,CAAC,IAAI,CAACvD,MAAM,CAACuD,eAAe,CAAC,IAAI,CAACzK,SAAS,EAAEtC,SAAS,CAACE,IAAI,CAAC,CAAC,CAAA;EAC3G,GAAA;;EAEA;EACAoK,EAAAA,mBAAmBA,GAAI;MACrB,IAAI,IAAI,CAACzF,cAAc,KAAK,IAAI,CAACvC,SAAS,EAAE,OAAO;MACnD,IAAI,CAAC+H,WAAW,EAAE,CAAA;EACpB,GAAA;;EAEA;EACA2C,EAAAA,EAAEA,CAAE3B,EAAU,EAAE4B,OAA+B,EAAQ;EACrD,IAAA,IAAI,CAAC,IAAI,CAACxD,UAAU,CAAC4B,EAAE,CAAC,EAAE,IAAI,CAAC5B,UAAU,CAAC4B,EAAE,CAAC,GAAG,EAAE,CAAA;MAClD,IAAI,CAAC5B,UAAU,CAAC4B,EAAE,CAAC,CAACtC,IAAI,CAACkE,OAAO,CAAC,CAAA;EACjC,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;EACAC,EAAAA,GAAGA,CAAE7B,EAAU,EAAE4B,OAA+B,EAAQ;MACtD,IAAI,CAAC,IAAI,CAACxD,UAAU,CAAC4B,EAAE,CAAC,EAAE,OAAO,IAAI,CAAA;MACrC,IAAI,CAAC4B,OAAO,EAAE;EACZ,MAAA,OAAO,IAAI,CAACxD,UAAU,CAAC4B,EAAE,CAAC,CAAA;EAC1B,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;EACA,IAAA,MAAM8B,MAAM,GAAG,IAAI,CAAC1D,UAAU,CAAC4B,EAAE,CAAC,CAAC+B,OAAO,CAACH,OAAO,CAAC,CAAA;EACnD,IAAA,IAAIE,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC1D,UAAU,CAAC4B,EAAE,CAAC,CAACgC,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC,CAAA;EACtD,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;IACAzH,QAAQA,CAAEkB,CAAa,EAAQ;MAC7B,IAAI,CAACgG,WAAW,GAAGhG,CAAC,CAAA;MACpB,IAAI,CAAC8F,kBAAkB,EAAE,CAAA;EAEzB,IAAA,MAAMY,OAAO,GAAG,IAAI1L,aAAa,CAAC;EAChC;EACAG,MAAAA,KAAK,EAAE,IAAI,CAACqB,EAAE,CAACrB,KAAK;QACpBO,SAAS,EAAE,IAAI,CAACA,SAAS;EAEzB;QACAJ,QAAQ,EAAE,IAAI,CAACgJ,YAAY;QAC3B/I,YAAY,EAAE,IAAI,CAACyJ,UAAAA;EACrB,KAAC,CAAC,CAAA;EAEF,IAAA,MAAM2B,WAAW,GAAG,IAAI,CAAC/D,MAAM,CAACsB,aAAa,CAAA;MAE7C,MAAM0C,MAAM,GAAG,IAAI,CAAChE,MAAM,CAAC6D,MAAM,CAC/BC,OAAO,CAACrL,cAAc,EACtBqL,OAAO,CAACxK,OAAO,CAAC9B,MAAM,EACtBsM,OAAO,CAAC5K,QAAQ,EAChB4K,OAAO,CAACpK,eAAe,EACvB;EAAEqC,MAAAA,KAAK,EAAE,IAAI;EAAEkI,MAAAA,GAAG,EAAE,IAAA;OACtB,CAAC,CAACD,MAAM,CAAA;;EAER;EACA;EACA,IAAA,MAAMtK,eAAe,GAAGqK,WAAW,KAAK,IAAI,CAAC/D,MAAM,CAACsB,aAAa,GAC/DwC,OAAO,CAACpK,eAAe,GACvBlD,SAAS,CAACC,IAAI,CAAA;EAEhB,IAAA,IAAIqC,SAAS,GAAG,IAAI,CAACkH,MAAM,CAACuD,eAAe,CACzCO,OAAO,CAACrL,cAAc,GAAGuL,MAAM,EAC/BtK,eACF,CAAC,CAAA;EACD,IAAA,IAAIA,eAAe,KAAKlD,SAAS,CAACC,IAAI,EAAEqC,SAAS,GAAG,IAAI,CAACkH,MAAM,CAACuD,eAAe,CAACzK,SAAS,EAAEtC,SAAS,CAACC,IAAI,CAAC,CAAA;EAE1G,IAAA,IAAI,CAAC4K,aAAa,CAACvI,SAAS,CAAC,CAAA;MAC7B,OAAO,IAAI,CAACsK,WAAW,CAAA;EACzB,GAAA;;EAEA;EACA7C,EAAAA,SAASA,GAAI;EACX,IAAA,IAAI,IAAI,CAACmB,YAAY,KAAK,IAAI,CAAC9H,EAAE,CAACrB,KAAK,EAAE,IAAI,CAACyI,WAAW,EAAE,CAAA;EAC3D,IAAA,IAAI,CAAChB,MAAM,CAACkE,QAAQ,EAAE,CAAA;MACtB,IAAI,CAAC7C,aAAa,EAAE,CAAA;MACpB,IAAI,CAACf,cAAc,EAAE,CAAA;EACvB,GAAA;;EAEA;IACAE,OAAOA,CAAEqB,EAAS,EAAE;MAClBA,EAAE,CAACnE,cAAc,EAAE,CAAA;MACnBmE,EAAE,CAACsC,eAAe,EAAE,CAAA;EACtB,GAAA;;EAEA;IACA1D,QAAQA,CAAEoB,EAAS,EAAE;MACnB,IAAI,CAACf,mBAAmB,EAAE,CAAA;EAC5B,GAAA;;EAEA;IACAJ,QAAQA,CAAEmB,EAAS,EAAE;MACnB,IAAI,CAACf,mBAAmB,EAAE,CAAA;EAC5B,GAAA;EAEAH,EAAAA,OAAOA,GAAI;MACT,IAAI,CAACyD,kBAAkB,CAAC,IAAI,CAAC/D,OAAO,CAAC1C,IAAI,EAAE,CAAC,CAAA;EAC9C,GAAA;EAEAiD,EAAAA,OAAOA,GAAI;MACT,IAAI,CAACwD,kBAAkB,CAAC,IAAI,CAAC/D,OAAO,CAAChD,IAAI,EAAE,CAAC,CAAA;EAC9C,GAAA;IAEA+G,kBAAkBA,CAAE5E,KAAoC,EAAE;MACxD,IAAI,CAACA,KAAK,EAAE,OAAA;MAEZ,IAAI,CAACmD,gBAAgB,GAAG,IAAI,CAAA;EAC5B,IAAA,IAAI,CAACvB,aAAa,GAAG5B,KAAK,CAAC4B,aAAa,CAAA;EACxC,IAAA,IAAI,CAACxH,EAAE,CAAC6B,MAAM,CAAC+D,KAAK,CAACnB,SAAS,CAACzF,KAAK,EAAE4G,KAAK,CAACnB,SAAS,CAACtF,GAAG,CAAC,CAAA;MAC1D,IAAI,CAACuH,cAAc,EAAE,CAAA;MACrB,IAAI,CAACqC,gBAAgB,GAAG,KAAK,CAAA;EAC/B,GAAA;;EAEA;EACA0B,EAAAA,OAAOA,GAAI;MACT,IAAI,CAAC1C,aAAa,EAAE,CAAA;EACnB,IAAA,IAAI,CAAC1B,UAAU,CAASzI,MAAM,GAAG,CAAC,CAAA;MACnC,OAAQ,IAAI,CAASoC,EAAE,CAAA;EACzB,GAAA;EACF,CAAA;EAGAD,KAAK,CAACE,SAAS,GAAGA,SAAS;;EClZ3B;EAEA,MAAMyK,aAAa,CAAC;EAClB;;EAEA;;EAEA;;EAEA;;IAIA,OAAOC,SAASA,CAAEC,IAAsC,EAA2B;EACjF,IAAA,OAAO3O,KAAK,CAACC,OAAO,CAAC0O,IAAI,CAAC,GAAGA,IAAI,GAAG,CAClCA,IAAI,EACJ,IAAIF,aAAa,EAAE,CACpB,CAAA;EACH,GAAA;IAEA7O,WAAWA,CAAEqO,OAAuC,EAAE;EACpD5N,IAAAA,MAAM,CAACoC,MAAM,CAAC,IAAI,EAAE;EAClBY,MAAAA,QAAQ,EAAE,EAAE;EACZuL,MAAAA,WAAW,EAAE,EAAE;EACfC,MAAAA,SAAS,EAAE,CAAC;EACZC,MAAAA,IAAI,EAAE,KAAA;OACP,EAAEb,OAAO,CAAC,CAAA;EACb,GAAA;;EAEA;IACAc,SAASA,CAAEd,OAAsB,EAAQ;EACvC,IAAA,IAAI,CAAC5K,QAAQ,IAAI4K,OAAO,CAAC5K,QAAQ,CAAA;EACjC,IAAA,IAAI,CAACuL,WAAW,IAAIX,OAAO,CAACW,WAAW,CAAA;EACvC,IAAA,IAAI,CAACC,SAAS,IAAIZ,OAAO,CAACY,SAAS,CAAA;MACnC,IAAI,CAACC,IAAI,GAAG,IAAI,CAACA,IAAI,IAAIb,OAAO,CAACa,IAAI,CAAA;EAErC,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;IACA,IAAIX,MAAMA,GAAY;MACpB,OAAO,IAAI,CAACU,SAAS,GAAG,IAAI,CAACxL,QAAQ,CAAC1B,MAAM,CAAA;EAC9C,GAAA;IAEA,IAAIqN,QAAQA,GAAa;MACvB,OAAOC,OAAO,CAAC,IAAI,CAACL,WAAW,CAAC,IAAI,IAAI,CAACE,IAAI,CAAA;EAC/C,GAAA;IAEAI,MAAMA,CAAEjB,OAAsB,EAAW;EACvC,IAAA,OAAO,IAAI,CAAC5K,QAAQ,KAAK4K,OAAO,CAAC5K,QAAQ,IACvC,IAAI,CAACwL,SAAS,KAAKZ,OAAO,CAACY,SAAS,IACpC,IAAI,CAACD,WAAW,KAAKX,OAAO,CAACW,WAAW,IACxC,IAAI,CAACE,IAAI,KAAKb,OAAO,CAACa,IAAI,CAAA;EAE9B,GAAA;EACF,CAAA;EAGAhL,KAAK,CAAC2K,aAAa,GAAGA,aAAa;;EC3DnC;EAEA,MAAMU,qBAAqB,CAAwB;EACjD;;EAEA;;EAEA;;EAGAvP,EAAAA,WAAWA,CAAE8C,KAAa,EAAK0M,IAAY,EAAIC,IAAa,EAAE;EAAA,IAAA,IAAjD3M,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,EAAE,CAAA;EAAA,KAAA;EAAA,IAAA,IAAE0M,IAAY,KAAA,KAAA,CAAA,EAAA;EAAZA,MAAAA,IAAY,GAAC,CAAC,CAAA;EAAA,KAAA;MAC3C,IAAI,CAAC1M,KAAK,GAAGA,KAAK,CAAA;MAClB,IAAI,CAAC0M,IAAI,GAAGA,IAAI,CAAA;MAChB,IAAI,CAACC,IAAI,GAAGA,IAAI,CAAA;EAClB,GAAA;EAEAlN,EAAAA,QAAQA,GAAY;MAAE,OAAO,IAAI,CAACO,KAAK,CAAA;EAAE,GAAA;IAEzC4M,MAAMA,CAAE1L,IAA0B,EAAQ;EACxC,IAAA,IAAI,CAAClB,KAAK,IAAIlD,MAAM,CAACoE,IAAI,CAAC,CAAA;EAC5B,GAAA;IAEA2L,QAAQA,CAAEpF,MAAkB,EAAiB;MAC3C,OAAOA,MAAM,CAACqF,MAAM,CAAC,IAAI,CAACrN,QAAQ,EAAE,EAAE;EAAEyB,MAAAA,IAAI,EAAE,IAAA;OAAM,CAAC,CAClDmL,SAAS,CAAC5E,MAAM,CAACsF,kBAAkB,EAAE,CAAC,CAAA;EAC3C,GAAA;IAEA,IAAI9F,KAAKA,GAAyB;MAChC,OAAO;QACLjH,KAAK,EAAE,IAAI,CAACA,KAAK;QACjB0M,IAAI,EAAE,IAAI,CAACA,IAAI;QACfC,IAAI,EAAE,IAAI,CAACA,IAAAA;OACZ,CAAA;EACH,GAAA;IAEA,IAAI1F,KAAKA,CAAEA,KAA0B,EAAE;EACrCtJ,IAAAA,MAAM,CAACoC,MAAM,CAAC,IAAI,EAAEkH,KAAK,CAAC,CAAA;EAC5B,GAAA;IAEA+F,OAAOA,CAAEC,SAAkB,EAAU;EACnC,IAAA,IAAI,CAAC,IAAI,CAACjN,KAAK,CAACf,MAAM,IAAKgO,SAAS,IAAI,IAAI,IAAI,IAAI,CAACP,IAAI,IAAIO,SAAU,EAAE,OAAO,EAAE,CAAA;EAElF,IAAA,MAAMC,SAAS,GAAG,IAAI,CAAClN,KAAK,CAAC,CAAC,CAAC,CAAA;MAC/B,IAAI,CAACA,KAAK,GAAG,IAAI,CAACA,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAAA;EAChC,IAAA,OAAOiN,SAAS,CAAA;EAClB,GAAA;EAEA/F,EAAAA,KAAKA,GAAY;MACf,IAAI,CAAC,IAAI,CAACnH,KAAK,CAACf,MAAM,EAAE,OAAO,EAAE,CAAA;EAEjC,IAAA,MAAMiO,SAAS,GAAG,IAAI,CAAClN,KAAK,CAAC,IAAI,CAACA,KAAK,CAACf,MAAM,GAAC,CAAC,CAAC,CAAA;EACjD,IAAA,IAAI,CAACe,KAAK,GAAG,IAAI,CAACA,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;EACpC,IAAA,OAAOiN,SAAS,CAAA;EAClB,GAAA;EACF;;ECnDA;;EASA;;EAMA;;EAoBA;EAEA,MAAelL,MAAM,CAAY;EAM/B;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAGA;;IAOA9E,WAAWA,CAAE4C,IAAmB,EAAE;MAChC,IAAI,CAAC6H,MAAM,GAAG,EAAE,CAAA;MAChB,IAAI,CAACwF,OAAO,CAAC;QACX,GAAGnL,MAAM,CAACoL,QAAQ;QAClB,GAAGtN,IAAAA;EACL,KAAC,CAAC,CAAA;MACF,IAAI,CAACuN,YAAY,GAAG,IAAI,CAAA;EAC1B,GAAA;;EAEA;IACAzE,aAAaA,CAAE9I,IAA4B,EAAE;EAC3C,IAAA,IAAI,CAAC,IAAI,CAAC0K,gBAAgB,CAAC1K,IAAI,CAAC,EAAE,OAAA;EAElC,IAAA,IAAI,CAACwN,gBAAgB,CAAC,IAAI,CAACH,OAAO,CAACzJ,IAAI,CAAC,IAAI,EAAE5D,IAAI,CAAC,CAAC,CAAA;EACtD,GAAA;;EAEA;IACAqN,OAAOA,CAAErN,IAA4B,EAAE;EACrCnC,IAAAA,MAAM,CAACoC,MAAM,CAAC,IAAI,EAAED,IAAI,CAAC,CAAA;EAC3B,GAAA;;EAEA;IACA,IAAImH,KAAKA,GAAiB;MACxB,OAAO;QACLU,MAAM,EAAE,IAAI,CAAC3H,KAAK;QAClB6H,cAAc,EAAE,IAAI,CAACkB,aAAAA;OACtB,CAAA;EACH,GAAA;IAEA,IAAI9B,KAAKA,CAAEA,KAAkB,EAAE;EAC7B,IAAA,IAAI,CAACU,MAAM,GAAGV,KAAK,CAACU,MAAM,CAAA;EAC5B,GAAA;;EAEA;EACA4F,EAAAA,KAAKA,GAAI;MACP,IAAI,CAAC5F,MAAM,GAAG,EAAE,CAAA;EAClB,GAAA;IAEA,IAAI3H,KAAKA,GAAY;MACnB,OAAO,IAAI,CAAC2H,MAAM,CAAA;EACpB,GAAA;IAEA,IAAI3H,KAAKA,CAAEA,KAAa,EAAE;EACxB,IAAA,IAAI,CAACwN,OAAO,CAACxN,KAAK,EAAE;EAAEwD,MAAAA,KAAK,EAAE,IAAA;EAAK,KAAC,CAAC,CAAA;EACtC,GAAA;;EAEA;EACAgK,EAAAA,OAAOA,CAAExN,KAAa,EAAEyN,KAAkB,EAAwB;EAAA,IAAA,IAA1CA,KAAkB,KAAA,KAAA,CAAA,EAAA;EAAlBA,MAAAA,KAAkB,GAAC;EAAEjK,QAAAA,KAAK,EAAE,IAAA;SAAM,CAAA;EAAA,KAAA;MACxD,IAAI,CAAC+J,KAAK,EAAE,CAAA;MACZ,IAAI,CAACT,MAAM,CAAC9M,KAAK,EAAEyN,KAAK,EAAE,EAAE,CAAC,CAAA;MAC7B,IAAI,CAAC9B,QAAQ,EAAE,CAAA;EACjB,GAAA;IAEA,IAAI9C,aAAaA,GAAY;MAC3B,OAAO,IAAI,CAAC7I,KAAK,CAAA;EACnB,GAAA;IAEA,IAAI6I,aAAaA,CAAE7I,KAAa,EAAE;EAChC,IAAA,IAAI,CAACwN,OAAO,CAACxN,KAAK,EAAE,EAAE,CAAC,CAAA;EACzB,GAAA;IAEA,IAAIgJ,UAAUA,GAAW;EACvB,IAAA,OAAO,IAAI,CAAC0E,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,IAAI,CAAC1N,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC6I,aAAsB,CAAA;EAChF,GAAA;IAEA,IAAIG,UAAUA,CAAEhJ,KAAY,EAAE;MAC5B,IAAI,IAAI,CAAC2N,MAAM,EAAE;QACf,IAAI,CAAC3N,KAAK,GAAG,IAAI,CAAC2N,MAAM,CAAC3N,KAAK,EAAE,IAAI,CAAC,CAAA;EACvC,KAAC,MAAM;EACL,MAAA,IAAI,CAAC6I,aAAa,GAAG/L,MAAM,CAACkD,KAAK,CAAC,CAAA;EACpC,KAAA;EACF,GAAA;;EAEA;IACA,IAAI+I,aAAaA,GAAY;MAC3B,OAAO,IAAI,CAAC6E,YAAY,CAAC,CAAC,EAAE,IAAI,CAACzE,YAAY,CAAClK,MAAM,EAAE;EAACyM,MAAAA,GAAG,EAAE,IAAA;EAAI,KAAC,CAAC,CAAA;EACpE,GAAA;IAEA,IAAI3C,aAAaA,CAAE/I,KAAa,EAAE;EAChC,IAAA,IAAI,CAACwN,OAAO,CAACxN,KAAK,EAAE;EAAE0L,MAAAA,GAAG,EAAE,IAAA;EAAK,KAAC,CAAC,CAAA;EACpC,GAAA;IAEA,IAAIvC,YAAYA,GAAY;MAC1B,OAAO,IAAI,CAACnJ,KAAK,CAAA;EACnB,GAAA;IAEA,IAAI8K,UAAUA,GAAa;EACzB,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;IAEA,IAAI+C,QAAQA,GAAa;MACvB,OAAO,IAAI,CAAC/C,UAAU,CAAA;EACxB,GAAA;;EAEA;EACAE,EAAAA,eAAeA,CAAEzK,SAAiB,EAAE/B,SAAqB,EAAU;EACjE,IAAA,OAAO+B,SAAS,CAAA;EAClB,GAAA;EAEAuN,EAAAA,mBAAmBA,CAAEC,OAAe,EAAIC,KAAa,EAAmC;EAAA,IAAA,IAAnED,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;EAC5E,IAAA,OAAOwB,IAAI,CAACC,GAAG,CAAC,IAAI,CAACyI,YAAY,CAAClK,MAAM,EAAE+O,KAAK,GAAGD,OAAO,CAAC,CAAA;EAC5D,GAAA;;EAEA;EACAH,EAAAA,YAAYA,CAAEG,OAAe,EAAIC,KAAa,EAA2BP,KAAoB,EAAU;EAAA,IAAA,IAAzFM,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;MACrE,OAAO,IAAI,CAACkK,YAAY,CAAClJ,KAAK,CAAC8N,OAAO,EAAEC,KAAK,CAAC,CAAA;EAChD,GAAA;;EAEA;EACAC,EAAAA,WAAWA,CAAEF,OAAe,EAAIC,KAAa,EAAwC;EAAA,IAAA,IAAxED,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;EACpE,IAAA,OAAO,IAAIwN,qBAAqB,CAAC,IAAI,CAACmB,YAAY,CAACG,OAAO,EAAEC,KAAK,CAAC,EAAED,OAAO,CAAC,CAAA;EAC9E,GAAA;;EAEA;IACAG,UAAUA,CAAEhN,IAAmC,EAAiB;EAC9D,IAAA,IAAItE,QAAQ,CAACsE,IAAI,CAAC,EAAEA,IAAI,GAAG,IAAIuL,qBAAqB,CAAC3P,MAAM,CAACoE,IAAI,CAAC,CAAC,CAAA;EAElE,IAAA,OAAQA,IAAI,CAAiB2L,QAAQ,CAAC,IAAI,CAAC,CAAA;EAC7C,GAAA;;EAEA;EACAsB,EAAAA,cAAcA,CAAEC,EAAU,EAAEX,KAAkB,EAAoB;EAChE,IAAA,IAAI,CAACW,EAAE,EAAE,OAAO,IAAIrC,aAAa,EAAE,CAAA;MAEnC,IAAI,CAACpE,MAAM,IAAIyG,EAAE,CAAA;MACjB,OAAO,IAAIrC,aAAa,CAAC;EACvBpL,MAAAA,QAAQ,EAAEyN,EAAE;EACZlC,MAAAA,WAAW,EAAEkC,EAAAA;EACf,KAAC,CAAC,CAAA;EACJ,GAAA;;EAEA;EACAC,EAAAA,WAAWA,CAAED,EAAU,EAAEX,KAAkB,EAAKa,SAAuB,EAAiB;EAAA,IAAA,IAA/Db,KAAkB,KAAA,KAAA,CAAA,EAAA;QAAlBA,KAAkB,GAAC,EAAE,CAAA;EAAA,KAAA;EAC5C,IAAA,MAAMc,eAAe,GAAG,IAAI,CAACtH,KAAK,CAAA;EAClC,IAAA,IAAIsE,OAAsB,CAAA;EAC1B,IAAA,CAAC6C,EAAE,EAAE7C,OAAO,CAAC,GAAG,IAAI,CAACiD,aAAa,CAACJ,EAAE,EAAEX,KAAK,CAAC,CAAA;EAE7C,IAAA,IAAIW,EAAE,EAAE;EACN7C,MAAAA,OAAO,GAAGA,OAAO,CAACc,SAAS,CAAC,IAAI,CAAC8B,cAAc,CAACC,EAAE,EAAEX,KAAK,CAAC,CAAC,CAAA;;EAE3D;;EAEA;QACA,IAAI,CAAClC,OAAO,CAACW,WAAW,IAAI,IAAI,CAACuC,OAAO,KAAK,KAAK,EAAE;EAClD,QAAA,MAAMC,UAAU,GAAG,IAAI,CAACzH,KAAK,CAAA;UAC7B,IAAI,CAACA,KAAK,GAAGsH,eAAe,CAAA;EAE5B,QAAA,IAAII,UAAU,GAAG,IAAI,CAACC,GAAG,CAACnB,KAAK,CAAC,CAAA;UAChC,MAAMoB,SAAS,GAAG,IAAI,CAACV,cAAc,CAACC,EAAE,EAAEX,KAAK,CAAC,CAAA;EAChDkB,QAAAA,UAAU,GAAGA,UAAU,CAACtC,SAAS,CAACwC,SAAS,CAAC,CAAA;;EAE5C;EACA;UACA,IAAIA,SAAS,CAAC3C,WAAW,IAAIyC,UAAU,CAACnC,MAAM,CAACjB,OAAO,CAAC,EAAE;EACvDA,UAAAA,OAAO,GAAGoD,UAAU,CAAA;EACtB,SAAC,MAAM;YACL,IAAI,CAAC1H,KAAK,GAAGyH,UAAU,CAAA;EACzB,SAAA;EACF,OAAA;EACF,KAAA;MAEA,IAAInD,OAAO,CAAC5K,QAAQ,EAAE;EACpB,MAAA,IAAImO,cAAc,CAAA;QAClB,IAAIC,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACvB,KAAK,CAAC,KAAK,KAAK,CAAA;EAE/C,MAAA,IAAIsB,QAAQ,IAAIT,SAAS,IAAI,IAAI,EAAE;EACjC;EACA,QAAA,MAAMW,eAAe,GAAG,IAAI,CAAChI,KAAK,CAAA;EAClC,QAAA,IAAI,IAAI,CAACiI,SAAS,KAAK,IAAI,EAAE;YAC3BJ,cAAc,GAAGR,SAAS,CAACrH,KAAK,CAAA;EAChC,UAAA,KAAK,IAAIjI,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAGuM,OAAO,CAACW,WAAW,CAACjN,MAAM,EAAE,EAAED,CAAC,EAAE;EACjDsP,YAAAA,SAAS,CAACtB,OAAO,CAAC,IAAI,CAAC7D,YAAY,CAAClK,MAAM,GAAGsM,OAAO,CAACY,SAAS,CAAC,CAAA;EACjE,WAAA;EACF,SAAA;EAEA,QAAA,IAAIgD,WAAW,GAAG,IAAI,CAACjB,UAAU,CAACI,SAAS,CAAC,CAAA;EAC5CS,QAAAA,QAAQ,GAAGI,WAAW,CAACjD,WAAW,CAACjN,MAAM,KAAKqP,SAAS,CAAC7O,QAAQ,EAAE,CAACR,MAAM,CAAA;;EAEzE;EACA,QAAA,IAAI,EAAE8P,QAAQ,IAAII,WAAW,CAACxO,QAAQ,CAAC,IAAI,IAAI,CAACuO,SAAS,KAAK,OAAO,EAAE;YACrE,IAAI,CAACjI,KAAK,GAAGgI,eAAe,CAAA;YAC5BH,cAAc,GAAGR,SAAS,CAACrH,KAAK,CAAA;EAChC,UAAA,KAAK,IAAIjI,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAGuM,OAAO,CAACW,WAAW,CAACjN,MAAM,EAAE,EAAED,CAAC,EAAE;cACjDsP,SAAS,CAACnH,KAAK,EAAE,CAAA;EACnB,WAAA;EAEAgI,UAAAA,WAAW,GAAG,IAAI,CAACjB,UAAU,CAACI,SAAS,CAAC,CAAA;EACxCS,UAAAA,QAAQ,GAAGI,WAAW,CAACjD,WAAW,CAACjN,MAAM,KAAKqP,SAAS,CAAC7O,QAAQ,EAAE,CAACR,MAAM,CAAA;EAC3E,SAAA;;EAEA;UACA,IAAI8P,QAAQ,IAAII,WAAW,CAACxO,QAAQ,EAAE,IAAI,CAACsG,KAAK,GAAGgI,eAAe,CAAA;EACpE,OAAA;;EAEA;QACA,IAAI,CAACF,QAAQ,EAAE;EACbxD,QAAAA,OAAO,GAAG,IAAIQ,aAAa,EAAE,CAAA;UAC7B,IAAI,CAAC9E,KAAK,GAAGsH,eAAe,CAAA;UAC5B,IAAID,SAAS,IAAIQ,cAAc,EAAER,SAAS,CAACrH,KAAK,GAAG6H,cAAc,CAAA;EACnE,OAAA;EACF,KAAA;EACA,IAAA,OAAOvD,OAAO,CAAA;EAChB,GAAA;;EAEA;EACAwB,EAAAA,kBAAkBA,GAAmB;MACnC,OAAO,IAAIhB,aAAa,EAAE,CAAA;EAC5B,GAAA;;EAEA;EACAqD,EAAAA,YAAYA,GAAmB;MAC7B,OAAO,IAAIrD,aAAa,EAAE,CAAA;EAC5B,GAAA;;EAEA;EACAe,EAAAA,MAAMA,CAAEjQ,GAAW,EAAE4Q,KAAmB,EAAEvM,IAAoC,EAAiB;MAC7F,IAAI,CAACtE,QAAQ,CAACC,GAAG,CAAC,EAAE,MAAM,IAAI4E,KAAK,CAAC,wBAAwB,CAAC,CAAA;EAC7D,IAAA,MAAM6M,SAAS,GAAG1R,QAAQ,CAACsE,IAAI,CAAC,GAAG,IAAIuL,qBAAqB,CAAC3P,MAAM,CAACoE,IAAI,CAAC,CAAC,GAAGA,IAAmB,CAAA;EAChG,IAAA,IAAIuM,KAAK,IAAA,IAAA,IAALA,KAAK,CAAEvM,IAAI,EAAEuM,KAAK,CAAC4B,gBAAgB,GAAG,IAAI,CAACpI,KAAK,CAAA;EAEpD,IAAA,IAAIsE,OAAO,CAAA;EACX,IAAA,CAAC1O,GAAG,EAAE0O,OAAO,CAAC,GAAG,IAAI,CAAC+D,SAAS,CAACzS,GAAG,EAAE4Q,KAAK,CAAC,CAAA;EAE3C,IAAA,KAAK,IAAI8B,EAAE,GAAC,CAAC,EAAEA,EAAE,GAAC1S,GAAG,CAACoC,MAAM,EAAE,EAAEsQ,EAAE,EAAE;EAClC,MAAA,MAAMC,CAAC,GAAG,IAAI,CAACnB,WAAW,CAACxR,GAAG,CAAC0S,EAAE,CAAC,EAAE9B,KAAK,EAAEa,SAAS,CAAC,CAAA;EACrD,MAAA,IAAI,CAACkB,CAAC,CAACtD,WAAW,IAAI,CAAC,IAAI,CAACuD,aAAa,CAAC5S,GAAG,CAAC0S,EAAE,CAAC,EAAE9B,KAAK,EAAEa,SAAS,CAAC,EAAE,MAAA;EACtE/C,MAAAA,OAAO,CAACc,SAAS,CAACmD,CAAC,CAAC,CAAA;EACtB,KAAA;MAEA,IAAI,CAAC,IAAI,CAACE,KAAK,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,KAAK,QAAQ,KAAKjC,KAAK,IAALA,IAAAA,IAAAA,KAAK,CAAEjK,KAAK,IAAI3G,GAAG,EAAE;QAC3E0O,OAAO,CAACc,SAAS,CAAC,IAAI,CAAC+C,YAAY,EAAE,CAAC,CAAA;EACxC,KAAA;;EAEA;MACA,IAAId,SAAS,IAAI,IAAI,EAAE;QACrB/C,OAAO,CAACY,SAAS,IAAI,IAAI,CAAC+B,UAAU,CAACI,SAAS,CAAC,CAACnC,SAAS,CAAA;EACzD;EACA;EACA;EACF,KAAA;EAEA,IAAA,OAAOZ,OAAO,CAAA;EAChB,GAAA;EAEAoE,EAAAA,MAAMA,CAAE5B,OAAe,EAAIC,KAAa,EAA0C;EAAA,IAAA,IAA1ED,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;MAC/D,IAAI,CAAC0I,MAAM,GAAG,IAAI,CAACwB,YAAY,CAAClJ,KAAK,CAAC,CAAC,EAAE8N,OAAO,CAAC,GAAG,IAAI,CAAC5E,YAAY,CAAClJ,KAAK,CAAC+N,KAAK,CAAC,CAAA;MAClF,OAAO,IAAIjC,aAAa,EAAE,CAAA;EAC5B,GAAA;;EAEA;IACAuB,gBAAgBA,CAAIsC,EAAW,EAAK;EAClC,IAAA,IAAI,IAAI,CAACC,WAAW,IAAI,CAAC,IAAI,CAACxC,YAAY,EAAE,OAAOuC,EAAE,EAAE,CAAA;MACvD,IAAI,CAACC,WAAW,GAAG,IAAI,CAAA;EAEvB,IAAA,MAAMC,QAAQ,GAAG,IAAI,CAAC/G,aAAa,CAAA;EACnC,IAAA,MAAM/I,KAAK,GAAG,IAAI,CAACA,KAAK,CAAA;EAExB,IAAA,MAAM+P,GAAG,GAAGH,EAAE,EAAE,CAAA;MAEhB,IAAI,CAAC7G,aAAa,GAAG+G,QAAQ,CAAA;EAC7B;MACA,IAAI,IAAI,CAAC9P,KAAK,IAAI,IAAI,CAACA,KAAK,KAAKA,KAAK,IAAIA,KAAK,CAACqL,OAAO,CAAC,IAAI,CAACrL,KAAK,CAAC,KAAK,CAAC,EAAE;EACzE,MAAA,IAAI,CAAC8M,MAAM,CAAC9M,KAAK,CAACC,KAAK,CAAC,IAAI,CAACkJ,YAAY,CAAClK,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;QAC1D,IAAI,CAAC0M,QAAQ,EAAE,CAAA;EACjB,KAAA;MAEA,OAAO,IAAI,CAACkE,WAAW,CAAA;EACvB,IAAA,OAAOE,GAAG,CAAA;EACZ,GAAA;IAEAC,WAAWA,CAAIJ,EAAuB,EAAK;EACzC,IAAA,IAAI,IAAI,CAACK,SAAS,IAAI,CAAC,IAAI,CAAC5C,YAAY,EAAE,OAAOuC,EAAE,CAAC,IAAI,CAAC,CAAA;MACzD,IAAI,CAACK,SAAS,GAAG,IAAI,CAAA;EACrB,IAAA,MAAMhJ,KAAK,GAAG,IAAI,CAACA,KAAK,CAAA;EAExB,IAAA,MAAM8I,GAAG,GAAGH,EAAE,CAAC,IAAI,CAAC,CAAA;MAEpB,IAAI,CAAC3I,KAAK,GAAGA,KAAK,CAAA;MAClB,OAAO,IAAI,CAACgJ,SAAS,CAAA;EAErB,IAAA,OAAOF,GAAG,CAAA;EACZ,GAAA;EAEAN,EAAAA,aAAaA,CAAErB,EAAU,EAAEX,KAAkB,EAAKa,SAAuB,EAAW;EAClF,IAAA,OAAO/B,OAAO,CAAC,IAAI,CAAC2D,WAAW,CAAC,CAAA;EAClC,GAAA;;EAEA;EACAZ,EAAAA,SAASA,CAAEzS,GAAW,EAAE4Q,KAAkB,EAA8B;EAAA,IAAA,IAAhDA,KAAkB,KAAA,KAAA,CAAA,EAAA;QAAlBA,KAAkB,GAAC,EAAE,CAAA;EAAA,KAAA;MAC3C,OAAO1B,aAAa,CAACC,SAAS,CAAC,IAAI,CAACmE,OAAO,GACzC,IAAI,CAACA,OAAO,CAACtT,GAAG,EAAE,IAAI,EAAE4Q,KAAK,CAAC,GAC9B5Q,GAAG,CAAC,CAAA;EACR,GAAA;;EAEA;EACA2R,EAAAA,aAAaA,CAAE3R,GAAW,EAAE4Q,KAAkB,EAA8B;EAAA,IAAA,IAAhDA,KAAkB,KAAA,KAAA,CAAA,EAAA;QAAlBA,KAAkB,GAAC,EAAE,CAAA;EAAA,KAAA;MAC/C,OAAO1B,aAAa,CAACC,SAAS,CAAC,IAAI,CAACoE,WAAW,GAC7C,IAAI,CAACA,WAAW,CAACvT,GAAG,EAAE,IAAI,EAAE4Q,KAAK,CAAC,GAClC5Q,GAAG,CAAC,CAAA;EACR,GAAA;;EAEA;IACAmS,UAAUA,CAAEvB,KAAkB,EAAW;EACvC,IAAA,OAAO,CAAC,CAAC,IAAI,CAAC4C,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAAC,IAAI,CAACrQ,KAAK,EAAE,IAAI,EAAEyN,KAAK,CAAC,MAC7D,CAAC,IAAI,CAAC6C,MAAM,IAAI,IAAI,CAACA,MAAM,CAACtB,UAAU,CAACvB,KAAK,CAAC,CAAC,CAAA;EACnD,GAAA;;EAEA;EACA9B,EAAAA,QAAQA,GAAI;EACV,IAAA,IAAI,IAAI,CAAChH,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,IAAI,CAAC3E,KAAK,EAAE,IAAI,CAAC,CAAA;EAChD,GAAA;IAEAsL,MAAMA,CAAEjL,KAAa,EAAEkQ,WAAmB,EAAE5P,QAAQ,EAAKQ,eAA0B,EAAmBsM,KAAkB,EAAmC;EAAA,IAAA,IAA/G9M,QAAQ,KAAA,KAAA,CAAA,EAAA;EAARA,MAAAA,QAAQ,GAAC,EAAE,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEQ,eAA0B,KAAA,KAAA,CAAA,EAAA;QAA1BA,eAA0B,GAAGlD,SAAS,CAACC,IAAI,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEuP,KAAkB,KAAA,KAAA,CAAA,EAAA;EAAlBA,MAAAA,KAAkB,GAAG;EAAEjK,QAAAA,KAAK,EAAE,IAAA;SAAM,CAAA;EAAA,KAAA;EACxI,IAAA,MAAMgN,OAAe,GAAGnQ,KAAK,GAAGkQ,WAAW,CAAA;EAC3C,IAAA,MAAMrP,IAAiB,GAAG,IAAI,CAAC+M,WAAW,CAACuC,OAAO,CAAC,CAAA;EAEnD,IAAA,MAAMC,WAAW,GAAG,IAAI,CAACf,KAAK,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,KAAK,QAAQ,CAAA;EAElE,IAAA,IAAIlE,WAAW,CAAA;EACf,IAAA,IAAIiF,WAAW,EAAG;EAChBtP,MAAAA,eAAe,GAAG5C,cAAc,CAAC4C,eAAe,CAAC,CAAA;QACjDqK,WAAW,GAAG,IAAI,CAACoC,YAAY,CAAC,CAAC,EAAE4C,OAAO,EAAE;EAAC9E,QAAAA,GAAG,EAAE,IAAA;EAAI,OAAC,CAAC,CAAA;EAC1D,KAAA;MAEA,IAAIxL,cAAsB,GAAGG,KAAK,CAAA;EAClC,IAAA,MAAMkL,OAAsB,GAAG,IAAIQ,aAAa,EAAE,CAAA;;EAElD;EACA,IAAA,IAAI5K,eAAe,KAAKlD,SAAS,CAACC,IAAI,EAAE;QACtCgC,cAAc,GAAG,IAAI,CAAC8K,eAAe,CAAC3K,KAAK,EACzCkQ,WAAW,GAAG,CAAC,IAAIlQ,KAAK,KAAK,CAAC,IAAI,CAACoQ,WAAW,GAC9CxS,SAAS,CAACC,IAAI,GACdiD,eACF,CAAC,CAAA;;EAED;EACAoK,MAAAA,OAAO,CAACY,SAAS,GAAGjM,cAAc,GAAGG,KAAK,CAAA;EAC5C,KAAA;MAEAkL,OAAO,CAACc,SAAS,CAAC,IAAI,CAACsD,MAAM,CAACzP,cAAc,CAAC,CAAC,CAAA;EAE9C,IAAA,IAAIuQ,WAAW,IAAItP,eAAe,KAAKlD,SAAS,CAACC,IAAI,IAAIsN,WAAW,KAAK,IAAI,CAACzC,aAAa,EAAE;EAC3F,MAAA,IAAI5H,eAAe,KAAKlD,SAAS,CAACG,UAAU,EAAE;EAC5C,QAAA,IAAIsS,SAAS,CAAA;EACb,QAAA,OAAOlF,WAAW,KAAK,IAAI,CAACzC,aAAa,KAAK2H,SAAS,GAAG,IAAI,CAACvH,YAAY,CAAClK,MAAM,CAAC,EAAE;EACnFsM,UAAAA,OAAO,CACJc,SAAS,CAAC,IAAIN,aAAa,CAAC;EAAEI,YAAAA,SAAS,EAAE,CAAC,CAAA;EAAE,WAAC,CAAC,CAAC,CAC/CE,SAAS,CAAC,IAAI,CAACsD,MAAM,CAACe,SAAS,GAAC,CAAC,CAAC,CAAC,CAAA;EACxC,SAAA;EACF,OAAC,MAAM,IAAIvP,eAAe,KAAKlD,SAAS,CAACK,WAAW,EAAE;UACpD4C,IAAI,CAAC8L,OAAO,EAAE,CAAA;EAChB,OAAA;EACF,KAAA;EAEA,IAAA,OAAOzB,OAAO,CAACc,SAAS,CAAC,IAAI,CAACS,MAAM,CAACnM,QAAQ,EAAE8M,KAAK,EAAEvM,IAAI,CAAC,CAAC,CAAA;EAC9D,GAAA;IAEAwH,UAAUA,CAAElH,IAAS,EAAW;EAC9B,IAAA,OAAO,IAAI,CAACA,IAAI,KAAKA,IAAI,CAAA;EAC3B,GAAA;IAEAgJ,gBAAgBA,CAAE1K,IAA4B,EAAW;EACvD,IAAA,OAAO,CAACnB,cAAc,CAAC,IAAI,EAAEmB,IAAI,CAAC,CAAA;EACpC,GAAA;IAEAoJ,gBAAgBA,CAAElJ,KAAU,EAAW;EACrC,IAAA,MAAM2Q,IAAI,GAAG,IAAI,CAAC3H,UAAU,CAAA;MAE5B,OAAOhJ,KAAK,KAAK2Q,IAAI,IACnB3O,MAAM,CAAC4O,YAAY,CAAClT,QAAQ,CAACsC,KAAK,CAAC,IAAIgC,MAAM,CAAC4O,YAAY,CAAClT,QAAQ,CAACiT,IAAI,CAAC,KACxE,IAAI,CAAChD,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC3N,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC2N,MAAM,CAAC,IAAI,CAAC3E,UAAU,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,CAAA;EAC3F,GAAA;IAEA4F,GAAGA,CAAEnB,KAAmB,EAAiB;MACvC,OAAO,IAAI1B,aAAa,EAAE,CAAA;EAC5B,GAAA;EACF,CAAA;EA9Ze/J,MAAM,CACZoL,QAAQ,GAAuC;EACpD8C,EAAAA,WAAW,EAAE,IAAA;EACf,CAAC,CAAA;EAHYlO,MAAM,CAIZ4O,YAAY,GAAe,CAACtO,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;EA6ZzDlB,KAAK,CAACY,MAAM,GAAGA,MAAM;;ECncrB,MAAM6O,iBAAiB,CAAwB;EAI7C;;EAGA3T,EAAAA,WAAWA,CAAE4T,MAA0B,EAAKpE,IAAY,EAAI;EAAA,IAAA,IAA/CoE,MAA0B,KAAA,KAAA,CAAA,EAAA;EAA1BA,MAAAA,MAA0B,GAAC,EAAE,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEpE,IAAY,KAAA,KAAA,CAAA,EAAA;EAAZA,MAAAA,IAAY,GAAC,CAAC,CAAA;EAAA,KAAA;MACxD,IAAI,CAACoE,MAAM,GAAGA,MAAM,CAAA;MACpB,IAAI,CAACpE,IAAI,GAAGA,IAAI,CAAA;EAClB,GAAA;EAEAjN,EAAAA,QAAQA,GAAY;EAClB,IAAA,OAAO,IAAI,CAACqR,MAAM,CAACC,GAAG,CAACjU,MAAM,CAAC,CAACkU,IAAI,CAAC,EAAE,CAAC,CAAA;EACzC,GAAA;IAEApE,MAAMA,CAAEqE,SAAwC,EAAQ;EACtD,IAAA,IAAI,CAACnU,MAAM,CAACmU,SAAS,CAAC,EAAE,OAAA;EACxBA,IAAAA,SAAS,GAAIrU,QAAQ,CAACqU,SAAS,CAAC,GAAG,IAAIxE,qBAAqB,CAAC3P,MAAM,CAACmU,SAAS,CAAC,CAAC,GAAGA,SAAyB,CAAA;EAE3G,IAAA,MAAMC,SAAS,GAAG,IAAI,CAACJ,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC7R,MAAM,GAAC,CAAC,CAAC,CAAA;MACnD,MAAMkS,UAAU,GAAGD,SAAS;EAC1B;EACCA,IAAAA,SAAS,CAACvE,IAAI,KAAKsE,SAAS,CAACtE,IAAI,IAAIsE,SAAS,CAACtE,IAAI,IAAI,IAAI,CAAC;EAC7D;EACAsE,IAAAA,SAAS,CAACvE,IAAI,KAAMwE,SAAS,CAACxE,IAAI,GAAGwE,SAAS,CAACzR,QAAQ,EAAE,CAACR,MAAO,CAAA;MAEnE,IAAIgS,SAAS,YAAYxE,qBAAqB,EAAE;EAC9C;EACA,MAAA,IAAI0E,UAAU,EAAE;EACd;UACAD,SAAS,CAACtE,MAAM,CAACqE,SAAS,CAACxR,QAAQ,EAAE,CAAC,CAAA;EACxC,OAAC,MAAM;EACL;EACA,QAAA,IAAI,CAACqR,MAAM,CAAC9J,IAAI,CAACiK,SAAS,CAAC,CAAA;EAC7B,OAAA;EACF,KAAC,MAAM,IAAIA,SAAS,YAAYJ,iBAAiB,EAAE;EACjD,MAAA,IAAII,SAAS,CAACtE,IAAI,IAAI,IAAI,EAAE;EAC1B;EACA,QAAA,IAAIyE,cAAc,CAAA;EAClB,QAAA,OAAOH,SAAS,CAACH,MAAM,CAAC7R,MAAM,IAAIgS,SAAS,CAACH,MAAM,CAAC,CAAC,CAAC,CAACnE,IAAI,IAAI,IAAI,EAAE;YAClEyE,cAAc,GAAGH,SAAS,CAACH,MAAM,CAAC3J,KAAK,EAAiB,CAAC;EACzDiK,UAAAA,cAAc,CAAC1E,IAAI,IAAIuE,SAAS,CAACvE,IAAI,CAAA;EACrC,UAAA,IAAI,CAACE,MAAM,CAACwE,cAAc,CAAC,CAAA;EAC7B,SAAA;EACF,OAAA;;EAEA;EACA,MAAA,IAAIH,SAAS,CAACxR,QAAQ,EAAE,EAAE;EACxB;EACAwR,QAAAA,SAAS,CAACtE,IAAI,GAAGsE,SAAS,CAACI,UAAU,CAAA;EACrC,QAAA,IAAI,CAACP,MAAM,CAAC9J,IAAI,CAACiK,SAAS,CAAC,CAAA;EAC7B,OAAA;EACF,KAAA;EACF,GAAA;IAEApE,QAAQA,CAAEpF,MAAkC,EAAiB;EAC3D,IAAA,IAAI,EAAEA,MAAM,YAAYrG,KAAK,CAACO,aAAa,CAAC,EAAE;QAC5C,MAAMT,IAAI,GAAG,IAAIuL,qBAAqB,CAAC,IAAI,CAAChN,QAAQ,EAAE,CAAC,CAAA;EACvD,MAAA,OAAOyB,IAAI,CAAC2L,QAAQ,CAACpF,MAAM,CAAC,CAAA;EAC9B,KAAA;EAEA,IAAA,MAAM8D,OAAO,GAAG,IAAIQ,aAAa,EAAE,CAAA;EAEnC,IAAA,KAAK,IAAIwD,EAAE,GAAC,CAAC,EAAEA,EAAE,GAAG,IAAI,CAACuB,MAAM,CAAC7R,MAAM,EAAE,EAAEsQ,EAAE,EAAE;EAC5C,MAAA,MAAM+B,KAAK,GAAG,IAAI,CAACR,MAAM,CAACvB,EAAE,CAAC,CAAA;QAE7B,MAAMgC,aAAa,GAAG9J,MAAM,CAAC+J,cAAc,CAAC/J,MAAM,CAAC0B,YAAY,CAAClK,MAAM,CAAC,CAAA;EACvE,MAAA,MAAM0N,IAAI,GAAG2E,KAAK,CAAC3E,IAAI,CAAA;EACvB,MAAA,IAAI8E,UAAU,CAAA;QACd,IAAI9E,IAAI,IAAI,IAAI;EACd;QACC,CAAC4E,aAAa,IAAIA,aAAa,CAACG,KAAK,IAAI/E,IAAI,CAAC,EAC/C;UACA,IACE2E,KAAK,YAAYT,iBAAiB;EAClC;UACApJ,MAAM,CAACkK,MAAM,CAACtG,OAAO,CAACsB,IAAI,CAAC,IAAI,CAAC,EAChC;YACApB,OAAO,CAACc,SAAS,CAAC5E,MAAM,CAACsF,kBAAkB,CAACJ,IAAI,CAAC,CAAC,CAAA;EACpD,SAAA;UACA8E,UAAU,GAAGH,KAAK,YAAYT,iBAAiB,IAAIpJ,MAAM,CAACmK,OAAO,CAACjF,IAAI,CAAC,CAAA;EACzE,OAAA;EAEA,MAAA,IAAI8E,UAAU,EAAE;EACd,QAAA,MAAMtC,WAAW,GAAGsC,UAAU,CAACvD,UAAU,CAACoD,KAAK,CAAC,CAAA;EAChD/F,QAAAA,OAAO,CAACc,SAAS,CAAC8C,WAAW,CAAC,CAAA;;EAE9B;EACA,QAAA,MAAM0C,WAAW,GAAGP,KAAK,CAAC7R,QAAQ,EAAE,CAACQ,KAAK,CAACkP,WAAW,CAACjD,WAAW,CAACjN,MAAM,CAAC,CAAA;UAC1E,IAAI4S,WAAW,EAAEtG,OAAO,CAACc,SAAS,CAAC5E,MAAM,CAACqF,MAAM,CAAC+E,WAAW,EAAE;EAAE3Q,UAAAA,IAAI,EAAE,IAAA;EAAK,SAAC,CAAC,CAAC,CAAA;EAChF,OAAC,MAAM;EACLqK,QAAAA,OAAO,CAACc,SAAS,CAAC5E,MAAM,CAACqF,MAAM,CAACwE,KAAK,CAAC7R,QAAQ,EAAE,EAAE;EAAEyB,UAAAA,IAAI,EAAE,IAAA;EAAK,SAAC,CAAC,CAAC,CAAA;EACpE,OAAA;EACF,KAAA;EAEA,IAAA,OAAOqK,OAAO,CAAA;EAChB,GAAA;IAEA,IAAItE,KAAKA,GAAqB;MAC5B,OAAO;EACL6J,MAAAA,MAAM,EAAE,IAAI,CAACA,MAAM,CAACC,GAAG,CAACe,CAAC,IAAIA,CAAC,CAAC7K,KAAK,CAAC;QACrCyF,IAAI,EAAE,IAAI,CAACA,IAAI;QACfC,IAAI,EAAE,IAAI,CAACA,IAAI;QACf0E,UAAU,EAAE,IAAI,CAACA,UAAAA;OAClB,CAAA;EACH,GAAA;IAEA,IAAIpK,KAAKA,CAAEA,KAAsB,EAAE;MACjC,MAAM;QAAE6J,MAAM;QAAE,GAAGiB,KAAAA;EAAM,KAAC,GAAG9K,KAAK,CAAA;EAClCtJ,IAAAA,MAAM,CAACoC,MAAM,CAAC,IAAI,EAAEgS,KAAK,CAAC,CAAA;MAC1B,IAAI,CAACjB,MAAM,GAAGA,MAAM,CAACC,GAAG,CAACiB,MAAM,IAAI;EACjC,MAAA,MAAMV,KAAK,GAAG,QAAQ,IAAIU,MAAM,GAC9B,IAAInB,iBAAiB,EAAE,GACvB,IAAIpE,qBAAqB,EAAE,CAAA;QAC7B6E,KAAK,CAACrK,KAAK,GAAG+K,MAAM,CAAA;EACpB,MAAA,OAAOV,KAAK,CAAA;EACd,KAAC,CAAC,CAAA;EACJ,GAAA;IAEAtE,OAAOA,CAAEC,SAAkB,EAAU;EACnC,IAAA,IAAI,CAAC,IAAI,CAAC6D,MAAM,CAAC7R,MAAM,IAAKgO,SAAS,IAAI,IAAI,IAAI,IAAI,CAACP,IAAI,IAAIO,SAAU,EAAE,OAAO,EAAE,CAAA;EAEnF,IAAA,MAAMgF,aAAa,GAAGhF,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG,IAAI,CAACP,IAAI,GAAGO,SAAS,CAAA;MAC3E,IAAIsC,EAAE,GAAC,CAAC,CAAA;EACR,IAAA,OAAOA,EAAE,GAAG,IAAI,CAACuB,MAAM,CAAC7R,MAAM,EAAE;EAC9B,MAAA,MAAMqS,KAAK,GAAG,IAAI,CAACR,MAAM,CAACvB,EAAE,CAAC,CAAA;EAC7B,MAAA,MAAMrC,SAAS,GAAGoE,KAAK,CAACtE,OAAO,CAACiF,aAAa,CAAC,CAAA;EAE9C,MAAA,IAAIX,KAAK,CAAC7R,QAAQ,EAAE,EAAE;EACpB;EACA;UACA,IAAI,CAACyN,SAAS,EAAE,MAAA;EAChB,QAAA,EAAEqC,EAAE,CAAA;EACN,OAAC,MAAM;EACL;UACA,IAAI,CAACuB,MAAM,CAACxF,MAAM,CAACiE,EAAE,EAAE,CAAC,CAAC,CAAA;EAC3B,OAAA;QAEA,IAAIrC,SAAS,EAAE,OAAOA,SAAS,CAAA;EACjC,KAAA;EAEA,IAAA,OAAO,EAAE,CAAA;EACX,GAAA;EAEA/F,EAAAA,KAAKA,GAAY;MACf,IAAI,CAAC,IAAI,CAAC2J,MAAM,CAAC7R,MAAM,EAAE,OAAO,EAAE,CAAA;MAElC,IAAIsQ,EAAE,GAAC,IAAI,CAACuB,MAAM,CAAC7R,MAAM,GAAC,CAAC,CAAA;MAC3B,OAAO,CAAC,IAAIsQ,EAAE,EAAE;EACd,MAAA,MAAM+B,KAAK,GAAG,IAAI,CAACR,MAAM,CAACvB,EAAE,CAAC,CAAA;EAC7B,MAAA,MAAMrC,SAAS,GAAGoE,KAAK,CAACnK,KAAK,EAAE,CAAA;EAE/B,MAAA,IAAImK,KAAK,CAAC7R,QAAQ,EAAE,EAAE;EACpB;EACA;UACA,IAAI,CAACyN,SAAS,EAAE,MAAA;EAChB,QAAA,EAAEqC,EAAE,CAAA;EACN,OAAC,MAAM;EACL;UACA,IAAI,CAACuB,MAAM,CAACxF,MAAM,CAACiE,EAAE,EAAE,CAAC,CAAC,CAAA;EAC3B,OAAA;QAEA,IAAIrC,SAAS,EAAE,OAAOA,SAAS,CAAA;EACjC,KAAA;EAEA,IAAA,OAAO,EAAE,CAAA;EACX,GAAA;EACF;;EChLA,MAAMgF,aAAa,CAAQ;EAOzBhV,EAAAA,WAAWA,CAAEuK,MAA4B,EAAEmC,GAAW,EAAE;MACtD,IAAI,CAACnC,MAAM,GAAGA,MAAM,CAAA;MACpB,IAAI,CAAC0K,IAAI,GAAG,EAAE,CAAA;MAEd,MAAM;QAAE1G,MAAM;EAAEiG,MAAAA,KAAAA;OAAO,GAAGjK,MAAM,CAAC+J,cAAc,CAAC5H,GAAG,CAAC,KAClDA,GAAG,GAAG,CAAC;EACL;EACA,IAAA;EAAE8H,MAAAA,KAAK,EAAE,CAAC;EAAEjG,MAAAA,MAAM,EAAE,CAAA;OAAG;EACvB;EACA,IAAA;EAAEiG,MAAAA,KAAK,EAAE,IAAI,CAACjK,MAAM,CAACmK,OAAO,CAAC3S,MAAM;EAAEwM,MAAAA,MAAM,EAAE,CAAA;EAAE,KAAC,CACnD,CAAA;MACD,IAAI,CAACA,MAAM,GAAGA,MAAM,CAAA;MACpB,IAAI,CAACiG,KAAK,GAAGA,KAAK,CAAA;MAClB,IAAI,CAACU,EAAE,GAAG,KAAK,CAAA;EACjB,GAAA;IAEA,IAAIC,KAAKA,GAAkB;MACzB,OAAO,IAAI,CAAC5K,MAAM,CAACmK,OAAO,CAAC,IAAI,CAACF,KAAK,CAAC,CAAA;EACxC,GAAA;IAEA,IAAI9H,GAAGA,GAAY;EACjB,IAAA,OAAO,IAAI,CAACnC,MAAM,CAAC6K,cAAc,CAAC,IAAI,CAACZ,KAAK,CAAC,GAAG,IAAI,CAACjG,MAAM,CAAA;EAC7D,GAAA;IAEA,IAAIxE,KAAKA,GAAwB;MAC/B,OAAO;QAAEyK,KAAK,EAAE,IAAI,CAACA,KAAK;QAAEjG,MAAM,EAAE,IAAI,CAACA,MAAM;QAAE2G,EAAE,EAAE,IAAI,CAACA,EAAAA;OAAI,CAAA;EAChE,GAAA;IAEA,IAAInL,KAAKA,CAAEsL,CAAqB,EAAE;EAChC5U,IAAAA,MAAM,CAACoC,MAAM,CAAC,IAAI,EAAEwS,CAAC,CAAC,CAAA;EACxB,GAAA;EAEAC,EAAAA,SAASA,GAAI;MACX,IAAI,CAACL,IAAI,CAACnL,IAAI,CAAC,IAAI,CAACC,KAAK,CAAC,CAAA;EAC5B,GAAA;EAEAwL,EAAAA,QAAQA,GAAoC;MAC1C,MAAMF,CAAC,GAAG,IAAI,CAACJ,IAAI,CAACO,GAAG,EAAE,CAAA;EACzB,IAAA,IAAIH,CAAC,EAAE,IAAI,CAACtL,KAAK,GAAGsL,CAAC,CAAA;EACrB,IAAA,OAAOA,CAAC,CAAA;EACV,GAAA;EAEAI,EAAAA,SAASA,GAAI;MACX,IAAI,IAAI,CAACN,KAAK,EAAE,OAAA;EAChB,IAAA,IAAI,IAAI,CAACX,KAAK,GAAG,CAAC,EAAE;QAClB,IAAI,CAACA,KAAK,GAAG,CAAC,CAAA;QACd,IAAI,CAACjG,MAAM,GAAG,CAAC,CAAA;EACjB,KAAA;MACA,IAAI,IAAI,CAACiG,KAAK,IAAI,IAAI,CAACjK,MAAM,CAACmK,OAAO,CAAC3S,MAAM,EAAE;QAC5C,IAAI,CAACyS,KAAK,GAAG,IAAI,CAACjK,MAAM,CAACmK,OAAO,CAAC3S,MAAM,GAAG,CAAC,CAAA;QAC3C,IAAI,CAACwM,MAAM,GAAI,IAAI,CAAC4G,KAAK,CAA6BlJ,YAAY,CAAClK,MAAM,CAAC;EAC5E,KAAA;EACF,GAAA;IAEA2T,SAASA,CAAChD,EAA6B,EAAW;MAChD,IAAI,CAAC4C,SAAS,EAAE,CAAA;EAChB,IAAA,KAAK,IAAI,CAACG,SAAS,EAAE,EAAE,CAAC,IAAE,IAAI,CAACjB,KAAK,EAAE,EAAE,IAAI,CAACA,KAAK,EAAE,IAAI,CAACjG,MAAM,GAAC,CAAAoH,CAAAA,WAAA,OAAI,CAACR,KAAK,KAAVQ,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,WAAA,CAAY1J,YAAY,CAAClK,MAAM,KAAI,CAAC,EAAE;EAAA,MAAA,IAAA4T,WAAA,CAAA;QACpG,IAAIjD,EAAE,EAAE,EAAE,OAAO,IAAI,CAACwC,EAAE,GAAG,IAAI,CAAA;EACjC,KAAA;EAEA,IAAA,OAAO,IAAI,CAACA,EAAE,GAAG,KAAK,CAAA;EACxB,GAAA;IAEAU,UAAUA,CAAElD,EAA6B,EAAW;MAClD,IAAI,CAAC4C,SAAS,EAAE,CAAA;EAChB,IAAA,KAAK,IAAI,CAACG,SAAS,EAAE,EAAE,IAAI,CAACjB,KAAK,GAAC,IAAI,CAACjK,MAAM,CAACmK,OAAO,CAAC3S,MAAM,EAAE,EAAE,IAAI,CAACyS,KAAK,EAAE,IAAI,CAACjG,MAAM,GAAC,CAAC,EAAE;QACzF,IAAImE,EAAE,EAAE,EAAE,OAAO,IAAI,CAACwC,EAAE,GAAG,IAAI,CAAA;EACjC,KAAA;EAEA,IAAA,OAAO,IAAI,CAACA,EAAE,GAAG,KAAK,CAAA;EACxB,GAAA;EAEAW,EAAAA,oBAAoBA,GAAa;EAC/B,IAAA,OAAO,IAAI,CAACH,SAAS,CAAC,MAAM;EAC1B,MAAA,IAAI,IAAI,CAACP,KAAK,CAACW,OAAO,IAAI,CAAC,IAAI,CAACX,KAAK,CAACrS,KAAK,EAAE,OAAA;EAE7C,MAAA,IAAI,CAACyL,MAAM,GAAG,IAAI,CAAC4G,KAAK,CAACrH,eAAe,CAAC,IAAI,CAACS,MAAM,EAAExN,SAAS,CAACG,UAAU,CAAC,CAAA;EAC3E,MAAA,IAAI,IAAI,CAACqN,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI,CAAA;EACpC,KAAC,CAAC,CAAA;EACJ,GAAA;EAEAwH,EAAAA,mBAAmBA,GAAa;EAC9B;EACA;EACA;EACA;EACA,IAAA,OAAO,IAAI,CAACL,SAAS,CAAC,MAAM;EAC1B,MAAA,IAAI,IAAI,CAACP,KAAK,CAACW,OAAO,EAAE,OAAA;EAExB,MAAA,IAAI,CAACvH,MAAM,GAAG,IAAI,CAAC4G,KAAK,CAACrH,eAAe,CAAC,IAAI,CAACS,MAAM,EAAExN,SAAS,CAACE,IAAI,CAAC,CAAA;EACrE,MAAA,OAAO,IAAI,CAAA;EACb,KAAC,CAAC,CAAA;EACJ,GAAA;EAEA+U,EAAAA,sBAAsBA,GAAa;EACjC,IAAA,OAAO,IAAI,CAACN,SAAS,CAAC,MAAM;EAC1B,MAAA,IAAI,IAAI,CAACP,KAAK,CAACW,OAAO,IAAI,IAAI,CAACX,KAAK,CAACc,UAAU,IAAI,CAAC,IAAI,CAACd,KAAK,CAACrS,KAAK,EAAE,OAAA;EAEtE,MAAA,IAAI,CAACyL,MAAM,GAAG,IAAI,CAAC4G,KAAK,CAACrH,eAAe,CAAC,IAAI,CAACS,MAAM,EAAExN,SAAS,CAACE,IAAI,CAAC,CAAA;EACrE,MAAA,OAAO,IAAI,CAAA;EACb,KAAC,CAAC,CAAA;EACJ,GAAA;EAEAiV,EAAAA,qBAAqBA,GAAa;EAChC,IAAA,OAAO,IAAI,CAACN,UAAU,CAAC,MAAM;EAC3B,MAAA,IAAI,IAAI,CAACT,KAAK,CAACW,OAAO,IAAI,CAAC,IAAI,CAACX,KAAK,CAACrS,KAAK,EAAE,OAAA;EAE7C,MAAA,IAAI,CAACyL,MAAM,GAAG,IAAI,CAAC4G,KAAK,CAACrH,eAAe,CAAC,IAAI,CAACS,MAAM,EAAExN,SAAS,CAACK,WAAW,CAAC,CAAA;EAC5E,MAAA,IAAI,IAAI,CAACmN,MAAM,KAAK,IAAI,CAAC4G,KAAK,CAACrS,KAAK,CAACf,MAAM,EAAE,OAAO,IAAI,CAAA;EAC1D,KAAC,CAAC,CAAA;EACJ,GAAA;EAEAoU,EAAAA,oBAAoBA,GAAa;EAC/B,IAAA,OAAO,IAAI,CAACP,UAAU,CAAC,MAAM;EAC3B,MAAA,IAAI,IAAI,CAACT,KAAK,CAACW,OAAO,EAAE,OAAA;;EAExB;EACA,MAAA,IAAI,CAACvH,MAAM,GAAG,IAAI,CAAC4G,KAAK,CAACrH,eAAe,CAAC,IAAI,CAACS,MAAM,EAAExN,SAAS,CAACC,IAAI,CAAC,CAAA;EACrE;EACA;EACA;EACA;EACA,MAAA,OAAO,IAAI,CAAA;EACb,KAAC,CAAC,CAAA;EACJ,GAAA;EAEAoV,EAAAA,uBAAuBA,GAAa;EAClC,IAAA,OAAO,IAAI,CAACR,UAAU,CAAC,MAAM;EAC3B,MAAA,IAAI,IAAI,CAACT,KAAK,CAACW,OAAO,IAAI,IAAI,CAACX,KAAK,CAACc,UAAU,IAAI,CAAC,IAAI,CAACd,KAAK,CAACrS,KAAK,EAAE,OAAA;;EAEtE;EACA,MAAA,IAAI,CAACyL,MAAM,GAAG,IAAI,CAAC4G,KAAK,CAACrH,eAAe,CAAC,IAAI,CAACS,MAAM,EAAExN,SAAS,CAACC,IAAI,CAAC,CAAA;EACrE,MAAA,OAAO,IAAI,CAAA;EACb,KAAC,CAAC,CAAA;EACJ,GAAA;EACF;;ECzIA,MAAMqV,sBAAsB,CAAyB;EACnD;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;IAGArW,WAAWA,CAAC4C,IAAmC,EAAE;EAC/CnC,IAAAA,MAAM,CAACoC,MAAM,CAAC,IAAI,EAAED,IAAI,CAAC,CAAA;MACzB,IAAI,CAAC6H,MAAM,GAAG,EAAE,CAAA;MAChB,IAAI,CAACqL,OAAO,GAAG,IAAI,CAAA;EACrB,GAAA;IAEA,IAAIhT,KAAKA,GAAY;MACnB,OAAO,IAAI,CAAC2H,MAAM,CAAA;EACpB,GAAA;IAEA,IAAIkB,aAAaA,GAAY;MAC3B,OAAO,IAAI,CAAC2K,WAAW,GAAG,IAAI,CAACxT,KAAK,GAAG,EAAE,CAAA;EAC3C,GAAA;IAEA,IAAI+I,aAAaA,GAAY;MAC3B,OAAO,IAAI,CAAC0K,WAAW,GAAG,IAAI,CAACzT,KAAK,GAAG,EAAE,CAAA;EAC3C,GAAA;IAEA,IAAImJ,YAAYA,GAAY;MAC1B,OAAO,IAAI,CAACnJ,KAAK,CAAA;EACnB,GAAA;EAEAuN,EAAAA,KAAKA,GAAI;MACP,IAAI,CAACkG,WAAW,GAAG,KAAK,CAAA;MACxB,IAAI,CAAC9L,MAAM,GAAG,EAAE,CAAA;EAClB,GAAA;EAEAgI,EAAAA,MAAMA,CAAE5B,OAAe,EAAIC,KAAa,EAAoC;EAAA,IAAA,IAApED,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAACrG,MAAM,CAAC1I,MAAM,CAAA;EAAA,KAAA;MACzD,IAAI,CAAC0I,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC1H,KAAK,CAAC,CAAC,EAAE8N,OAAO,CAAC,GAAG,IAAI,CAACpG,MAAM,CAAC1H,KAAK,CAAC+N,KAAK,CAAC,CAAA;MACtE,IAAI,CAAC,IAAI,CAACrG,MAAM,EAAE,IAAI,CAAC8L,WAAW,GAAG,KAAK,CAAA;MAE1C,OAAO,IAAI1H,aAAa,EAAE,CAAA;EAC5B,GAAA;EAEAf,EAAAA,eAAeA,CAAEzK,SAAiB,EAAE/B,SAAoB,EAAyB;EAAA,IAAA,IAA7CA,SAAoB,KAAA,KAAA,CAAA,EAAA;QAApBA,SAAoB,GAACP,SAAS,CAACC,IAAI,CAAA;EAAA,KAAA;MACrE,MAAMwV,MAAM,GAAG,CAAC,CAAA;EAChB,IAAA,MAAMC,MAAM,GAAG,IAAI,CAAChM,MAAM,CAAC1I,MAAM,CAAA;EAEjC,IAAA,QAAQT,SAAS;QACf,KAAKP,SAAS,CAACE,IAAI,CAAA;QACnB,KAAKF,SAAS,CAACG,UAAU;EACvB,QAAA,OAAOsV,MAAM,CAAA;QACf,KAAKzV,SAAS,CAACC,IAAI,CAAA;QACnB,KAAKD,SAAS,CAACI,KAAK,CAAA;QACpB,KAAKJ,SAAS,CAACK,WAAW,CAAA;EAC1B,MAAA;EACE,QAAA,OAAOqV,MAAM,CAAA;EACjB,KAAA;EACF,GAAA;EAEA7F,EAAAA,mBAAmBA,CAAEC,OAAe,EAAIC,KAAa,EAA6B;EAAA,IAAA,IAA7DD,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAACrG,MAAM,CAAC1I,MAAM,CAAA;EAAA,KAAA;MACtE,OAAO,IAAI,CAACwU,WAAW,GAAIzF,KAAK,GAAGD,OAAO,GAAI,CAAC,CAAA;EACjD,GAAA;EAEAH,EAAAA,YAAYA,CAAEG,OAAe,EAAIC,KAAa,EAAqBP,KAAmB,EAAa;EAAA,IAAA,IAArFM,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAACrG,MAAM,CAAC1I,MAAM,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEwO,KAAmB,KAAA,KAAA,CAAA,EAAA;QAAnBA,KAAmB,GAAC,EAAE,CAAA;EAAA,KAAA;EACvF,IAAA,OAAOA,KAAK,CAAC/B,GAAG,IAAI,IAAI,CAAC+H,WAAW,IAAI,IAAI,CAAC9L,MAAM,CAAC1H,KAAK,CAAC8N,OAAO,EAAEC,KAAK,CAAC,IAAI,EAAE,CAAA;EACjF,GAAA;IAEA,IAAIlD,UAAUA,GAAa;EACzB,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;IAEA,IAAI+C,QAAQA,GAAa;EACvB,IAAA,OAAOtB,OAAO,CAAC,IAAI,CAAC5E,MAAM,CAAC,CAAA;EAC7B,GAAA;EAEA0G,EAAAA,WAAWA,CAAED,EAAU,EAAEX,KAAkB,EAAoB;EAAA,IAAA,IAAtCA,KAAkB,KAAA,KAAA,CAAA,EAAA;QAAlBA,KAAkB,GAAC,EAAE,CAAA;EAAA,KAAA;MAC5C,IAAI,IAAI,CAACI,QAAQ,EAAE,OAAO,IAAI9B,aAAa,EAAE,CAAA;EAC7C,IAAA,MAAM6H,WAAW,GAAG,IAAI,CAAClE,KAAK,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,KAAK,QAAQ,CAAA;EAElE,IAAA,MAAMX,QAAQ,GAAG,IAAI,CAAC8E,IAAI,KAAKzF,EAAE,CAAA;EACjC,IAAA,MAAM0F,UAAU,GAAG/E,QAAQ,KAAK,IAAI,CAACyE,WAAW,IAAI/F,KAAK,CAACjK,KAAK,IAAIiK,KAAK,CAAC/B,GAAG,CAAC,KAAK,CAAC+B,KAAK,CAAC/B,GAAG,IAAI,CAACkI,WAAW,CAAC,IAAI,CAACnG,KAAK,CAACvM,IAAI,CAAA;EAC5H,IAAA,MAAMqK,OAAO,GAAG,IAAIQ,aAAa,CAAC;QAChCpL,QAAQ,EAAE,IAAI,CAACkT,IAAI;EACnB3H,MAAAA,WAAW,EAAE4H,UAAU,GAAG,IAAI,CAACD,IAAI,GAAE,EAAA;EACvC,KAAC,CAAC,CAAA;EACF,IAAA,IAAI,CAAClM,MAAM,GAAG,IAAI,CAACkM,IAAI,CAAA;EACvB,IAAA,IAAI,CAACJ,WAAW,GAAGK,UAAU,KAAKrG,KAAK,CAAC/B,GAAG,IAAI+B,KAAK,CAACjK,KAAK,CAAC,CAAA;EAE3D,IAAA,OAAO+H,OAAO,CAAA;EAChB,GAAA;EAEA6D,EAAAA,YAAYA,GAAmB;EAC7B,IAAA,OAAO,IAAI,CAACf,WAAW,CAAC,IAAI,CAACwF,IAAI,EAAE;EAAE3S,MAAAA,IAAI,EAAE,IAAA;EAAK,KAAC,CAAC,CAAA;EACpD,GAAA;EAEA6L,EAAAA,kBAAkBA,GAAmB;EACnC,IAAA,MAAMxB,OAAO,GAAG,IAAIQ,aAAa,EAAE,CAAA;EACnC,IAAA,IAAI,IAAI,CAAC8B,QAAQ,EAAE,OAAOtC,OAAO,CAAA;MAEjC,IAAI,CAAC5D,MAAM,GAAG4D,OAAO,CAAC5K,QAAQ,GAAG,IAAI,CAACkT,IAAI,CAAA;EAC1C,IAAA,OAAOtI,OAAO,CAAA;EAChB,GAAA;EAEA0C,EAAAA,WAAWA,GAAiB;EAC1B,IAAA,OAAO,IAAIxB,qBAAqB,CAAC,EAAE,CAAC,CAAA;EACtC,GAAA;IAEAyB,UAAUA,CAAEhN,IAAmC,EAAiB;EAC9D,IAAA,IAAItE,QAAQ,CAACsE,IAAI,CAAC,EAAEA,IAAI,GAAG,IAAIuL,qBAAqB,CAAC3P,MAAM,CAACoE,IAAI,CAAC,CAAC,CAAA;EAElE,IAAA,OAAQA,IAAI,CAAiB2L,QAAQ,CAAC,IAAI,CAAC,CAAA;EAC7C,GAAA;EAEAC,EAAAA,MAAMA,CAAEjQ,GAAW,EAAE4Q,KAAmB,EAAEvM,IAAkB,EAAiB;EAC3E,IAAA,MAAMqK,OAAO,GAAG,IAAI,CAAC8C,WAAW,CAACxR,GAAG,CAAC,CAAC,CAAC,EAAE4Q,KAAK,CAAC,CAAA;MAE/C,IAAIvM,IAAI,IAAI,IAAI,EAAE;QAChBqK,OAAO,CAACY,SAAS,IAAI,IAAI,CAAC+B,UAAU,CAAChN,IAAI,CAAC,CAACiL,SAAS,CAAA;EACtD,KAAA;EAEA,IAAA,OAAOZ,OAAO,CAAA;EAChB,GAAA;IAEAI,QAAQA,GAAI,EAAC;IAEb,IAAI1E,KAAKA,GAAiB;MACxB,OAAO;QACLU,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBE,cAAc,EAAE,IAAI,CAACkB,aAAAA;OACtB,CAAA;EACH,GAAA;IAEA,IAAI9B,KAAKA,CAAEA,KAAkB,EAAE;EAC7B,IAAA,IAAI,CAACU,MAAM,GAAGV,KAAK,CAACU,MAAM,CAAA;MAC1B,IAAI,CAAC8L,WAAW,GAAGlH,OAAO,CAACtF,KAAK,CAACY,cAAc,CAAC,CAAA;EAClD,GAAA;IAEA+G,GAAGA,CAAEnB,KAAmB,EAAiB;EACvC,IAAA,OAAO,IAAI,CAACV,kBAAkB,EAAE,CAAA;EAClC,GAAA;EACF;;EC1HA,MAAMgH,sBAAsB,CAA0F;EAOpH;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;IAIA7W,WAAWA,CAAC4C,IAAyC,EAAE;MACrD,MAAM;QAAEwQ,MAAM;QAAE6C,UAAU;QAAEa,eAAe;QAAEC,WAAW;QAAEC,IAAI;QAAExE,KAAK;QAAE,GAAGyE,QAAAA;EAAS,KAAC,GAAGrU,IAAI,CAAA;EAE3F,IAAA,IAAI,CAAC2H,MAAM,GAAG/E,UAAU,CAACyR,QAAgB,CAAC,CAAA;EAC1CxW,IAAAA,MAAM,CAACoC,MAAM,CAAC,IAAI,EAAE;QAAEuQ,MAAM;QAAE6C,UAAU;QAAEa,eAAe;QAAEC,WAAW;QAAEC,IAAI;EAAExE,MAAAA,KAAAA;EAAM,KAAC,CAAC,CAAA;EACxF,GAAA;EAEAnC,EAAAA,KAAKA,GAAI;MACP,IAAI,CAACM,QAAQ,GAAG,KAAK,CAAA;EACrB,IAAA,IAAI,CAACpG,MAAM,CAAC8F,KAAK,EAAE,CAAA;EACrB,GAAA;EAEAoC,EAAAA,MAAMA,CAAE5B,OAAe,EAAIC,KAAa,EAAmC;EAAA,IAAA,IAAnED,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAChO,KAAK,CAACf,MAAM,CAAA;EAAA,KAAA;EACxD,IAAA,IAAI8O,OAAO,KAAK,CAAC,IAAIC,KAAK,IAAI,CAAC,EAAE;QAC/B,IAAI,CAACH,QAAQ,GAAG,KAAK,CAAA;QACrB,OAAO,IAAI,CAACpG,MAAM,CAACkI,MAAM,CAAC5B,OAAO,EAAEC,KAAK,CAAC,CAAA;EAC3C,KAAA;MAEA,OAAO,IAAIjC,aAAa,EAAE,CAAA;EAC5B,GAAA;IAEA,IAAI/L,KAAKA,GAAY;MACnB,OAAO,IAAI,CAACyH,MAAM,CAACzH,KAAK,KACrB,IAAI,CAAC6N,QAAQ,IAAI,CAAC,IAAI,CAACsF,UAAU,GAChC,IAAI,CAACa,eAAe,GACpB,EAAE,CAAC,CAAA;EACT,GAAA;IAEA,IAAInL,aAAaA,GAAY;EAC3B,IAAA,OAAO,IAAI,CAACpB,MAAM,CAACoB,aAAa,CAAA;EAClC,GAAA;IAEA,IAAIE,aAAaA,GAAY;EAC3B,IAAA,OAAO,IAAI,CAACtB,MAAM,CAACsB,aAAa,CAAA;EAClC,GAAA;IAEA,IAAII,YAAYA,GAAY;EAC1B,IAAA,OAAO,IAAI,CAAC1B,MAAM,CAACzH,KAAK,IAAI,IAAI,CAACiU,WAAW,IAAI,IAAI,CAACjU,KAAK,CAAA;EAC5D,GAAA;IAEA,IAAI8K,UAAUA,GAAa;MACzB,OAAOyB,OAAO,CAAC,IAAI,CAAC9E,MAAM,CAACzH,KAAK,CAAC,IAAI,IAAI,CAACmT,UAAU,CAAA;EACtD,GAAA;EAEA9E,EAAAA,WAAWA,CAAED,EAAU,EAAEX,KAAqD,EAAoB;EAAA,IAAA,IAAzEA,KAAqD,KAAA,KAAA,CAAA,EAAA;QAArDA,KAAqD,GAAC,EAAE,CAAA;EAAA,KAAA;MAC/E,IAAI,IAAI,CAACI,QAAQ,EAAE,OAAO,IAAI9B,aAAa,EAAE,CAAA;EAE7C,IAAA,MAAM9E,KAAK,GAAG,IAAI,CAACQ,MAAM,CAACR,KAAK,CAAA;EAC/B;EACA,IAAA,IAAIsE,OAAO,GAAG,IAAI,CAAC9D,MAAM,CAAC4G,WAAW,CAACD,EAAE,EAAE,IAAI,CAACgG,gBAAgB,CAAC3G,KAAK,CAAC,CAAC,CAAA;EAEvE,IAAA,IAAIlC,OAAO,CAAC5K,QAAQ,IAAI,IAAI,CAACqO,UAAU,CAACvB,KAAK,CAAC,KAAK,KAAK,EAAE;EACxDlC,MAAAA,OAAO,GAAG,IAAIQ,aAAa,EAAE,CAAA;EAC7B,MAAA,IAAI,CAACtE,MAAM,CAACR,KAAK,GAAGA,KAAK,CAAA;EAC3B,KAAA;EAEA,IAAA,IAAI,CAACsE,OAAO,CAAC5K,QAAQ,IAAI,CAAC,IAAI,CAACwS,UAAU,IAAI,CAAC,IAAI,CAACe,IAAI,IAAI,CAACzG,KAAK,CAACjK,KAAK,EAAE;EACvE+H,MAAAA,OAAO,CAAC5K,QAAQ,GAAG,IAAI,CAACqT,eAAe,CAAA;EACzC,KAAA;MACAzI,OAAO,CAACa,IAAI,GAAG,CAACb,OAAO,CAAC5K,QAAQ,IAAI,CAAC,IAAI,CAACwS,UAAU,CAAA;MACpD,IAAI,CAACtF,QAAQ,GAAGtB,OAAO,CAAChB,OAAO,CAAC5K,QAAQ,CAAC,CAAA;EAEzC,IAAA,OAAO4K,OAAO,CAAA;EAChB,GAAA;EAEAuB,EAAAA,MAAMA,CAAEjQ,GAAW,EAAE4Q,KAAsD,EAAEvM,IAAkB,EAAiB;EAC9G;EACA,IAAA,OAAO,IAAI,CAACuG,MAAM,CAACqF,MAAM,CAACjQ,GAAG,EAAE,IAAI,CAACuX,gBAAgB,CAAC3G,KAAK,CAAC,EAAEvM,IAAI,CAAC,CAAA;EACpE,GAAA;EAEA6L,EAAAA,kBAAkBA,GAAmB;EACnC,IAAA,IAAI,IAAI,CAACc,QAAQ,IAAI,IAAI,CAACsF,UAAU,EAAE,OAAO,IAAIpH,aAAa,EAAE,CAAA;MAEhE,IAAI,CAAC8B,QAAQ,GAAG,IAAI,CAAA;MACpB,OAAO,IAAI9B,aAAa,CAAC;QAAEpL,QAAQ,EAAE,IAAI,CAACqT,eAAAA;EAAgB,KAAC,CAAC,CAAA;EAC9D,GAAA;EAEA5E,EAAAA,YAAYA,GAAmB;MAC7B,OAAO,IAAIrD,aAAa,EAAE,CAAA;EAC5B,GAAA;EAEAkC,EAAAA,WAAWA,CAAEF,OAAgB,EAAEC,KAAc,EAAe;MAC1D,OAAO,IAAI,CAACvG,MAAM,CAACwG,WAAW,CAACF,OAAO,EAAEC,KAAK,CAAC,CAAA;EAChD,GAAA;IAEAE,UAAUA,CAAEhN,IAA0B,EAAiB;EACrD,IAAA,OAAO,IAAI,CAACuG,MAAM,CAACyG,UAAU,CAAChN,IAAI,CAAC,CAAA;EACrC,GAAA;EAEA0M,EAAAA,YAAYA,CAAEG,OAAe,EAAIC,KAAa,EAAoBP,KAAoB,EAAU;EAAA,IAAA,IAAlFM,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAChO,KAAK,CAACf,MAAM,CAAA;EAAA,KAAA;MAC9D,OAAO,IAAI,CAACwI,MAAM,CAACmG,YAAY,CAACG,OAAO,EAAEC,KAAK,EAAEP,KAAK,CAAC,CAAA;EACxD,GAAA;EAEAzC,EAAAA,eAAeA,CAAEzK,SAAiB,EAAE/B,SAAoB,EAAyB;EAAA,IAAA,IAA7CA,SAAoB,KAAA,KAAA,CAAA,EAAA;QAApBA,SAAoB,GAACP,SAAS,CAACC,IAAI,CAAA;EAAA,KAAA;MACrE,MAAMwV,MAAM,GAAG,CAAC,CAAA;EAChB,IAAA,MAAMC,MAAM,GAAG,IAAI,CAAC3T,KAAK,CAACf,MAAM,CAAA;EAChC,IAAA,MAAMoV,QAAQ,GAAG5T,IAAI,CAACC,GAAG,CAACD,IAAI,CAACK,GAAG,CAACP,SAAS,EAAEmT,MAAM,CAAC,EAAEC,MAAM,CAAC,CAAA;EAE9D,IAAA,QAAQnV,SAAS;QACf,KAAKP,SAAS,CAACE,IAAI,CAAA;QACnB,KAAKF,SAAS,CAACG,UAAU;EACvB,QAAA,OAAO,IAAI,CAAC0M,UAAU,GAAGuJ,QAAQ,GAAGX,MAAM,CAAA;QAC5C,KAAKzV,SAAS,CAACI,KAAK,CAAA;QACpB,KAAKJ,SAAS,CAACK,WAAW;EACxB,QAAA,OAAO,IAAI,CAACwM,UAAU,GAAGuJ,QAAQ,GAAGV,MAAM,CAAA;QAC5C,KAAK1V,SAAS,CAACC,IAAI,CAAA;EACnB,MAAA;EAAS,QAAA,OAAOmW,QAAQ,CAAA;EAC1B,KAAA;EACF,GAAA;EAEAvG,EAAAA,mBAAmBA,CAAEC,OAAe,EAAIC,KAAa,EAA4B;EAAA,IAAA,IAA5DD,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAChO,KAAK,CAACf,MAAM,CAAA;EAAA,KAAA;MACrE,OAAO,IAAI,CAACe,KAAK,CAACC,KAAK,CAAC8N,OAAO,EAAEC,KAAK,CAAC,CAAC/O,MAAM,CAAA;EAChD,GAAA;IAEA+P,UAAUA,CAAEvB,KAAqD,EAAW;EAC1E,IAAA,OAAO,IAAI,CAAChG,MAAM,CAACuH,UAAU,CAAC,IAAI,CAACoF,gBAAgB,CAAC3G,KAAK,CAAC,CAAC,KACzD,CAAC,IAAI,CAAC6C,MAAM,IAAI,IAAI,CAACA,MAAM,CAACtB,UAAU,CAAC,IAAI,CAACoF,gBAAgB,CAAC3G,KAAK,CAAC,CAAC,CAAC,CAAA;EACzE,GAAA;EAEA9B,EAAAA,QAAQA,GAAI;EACV,IAAA,IAAI,CAAClE,MAAM,CAACkE,QAAQ,EAAE,CAAA;EACxB,GAAA;IAEA,IAAI1E,KAAKA,GAAuC;MAC9C,OAAO;QACLU,MAAM,EAAE,IAAI,CAAC3H,KAAK;QAClB6H,cAAc,EAAE,IAAI,CAACkB,aAAa;EAClCtB,MAAAA,MAAM,EAAE,IAAI,CAACA,MAAM,CAACR,KAAK;QACzB4G,QAAQ,EAAE,IAAI,CAACA,QAAAA;OAChB,CAAA;EACH,GAAA;IAEA,IAAI5G,KAAKA,CAAEA,KAAwC,EAAE;EACnD,IAAA,IAAI,CAACQ,MAAM,CAACR,KAAK,GAAGA,KAAK,CAACQ,MAAM,CAAA;EAChC,IAAA,IAAI,CAACoG,QAAQ,GAAG5G,KAAK,CAAC4G,QAAQ,CAAA;EAChC,GAAA;IAEAuG,gBAAgBA,CAAE3G,KAAsD,EAAe;EAAA,IAAA,IAAA6G,qBAAA,CAAA;MACrF,OAAO;EACL,MAAA,GAAG7G,KAAK;EACR4B,MAAAA,gBAAgB,EAAE,CAAA5B,KAAK,aAAA6G,qBAAA,GAAL7G,KAAK,CAAE4B,gBAAgB,KAAvBiF,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAyB7M,MAAM,MAAIgG,KAAK,IAALA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAAE4B,gBAAgB,CAAA;OAC7E,CAAA;EACH,GAAA;IAEAT,GAAGA,CAAEnB,KAAmB,EAAiB;MACvC,OAAO,IAAI1B,aAAa,EAAE,CAAA;EAC5B,GAAA;EACF,CAAA;EA5KMgI,sBAAsB,CACnBQ,mBAAmB,GAA4B;EACpD,EAAA,GAAG,EAAE,IAAI;EACT,EAAA,GAAG,EAAE,qnIAAqnI;EAAG;EAC7nI,EAAA,GAAG,EAAE,GAAA;EACP,CAAC;;EClCH;EAEA,MAAM7S,YAAY,SAASM,MAAM,CAAS;EACxC;;EAEA;;EAEA;;EAEA;;EAEA;;IAGS4G,aAAaA,CAAE9I,IAAkC,EAAE;EAC1D,IAAA,KAAK,CAAC8I,aAAa,CAAC9I,IAAI,CAAC,CAAA;EAC3B,GAAA;IAESqN,OAAOA,CAAErN,IAAkC,EAAE;EACpD,IAAA,MAAM0B,IAAI,GAAG1B,IAAI,CAAC0B,IAAI,CAAA;EACtB,IAAA,IAAIA,IAAI,EAAE1B,IAAI,CAACuQ,QAAQ,GAAIrQ,KAAK,IAAKA,KAAK,CAACwU,MAAM,CAAChT,IAAI,CAAC,IAAI,CAAC,CAAA;EAC5D,IAAA,KAAK,CAAC2L,OAAO,CAACrN,IAAI,CAAC,CAAA;EACrB,GAAA;EACF,CAAA;EAGAsB,KAAK,CAACM,YAAY,GAAGA,YAAY;;ECkBjC;EAEA,MAAMC,aAAa,SAAuBK,MAAM,CAAQ;EAYtD;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;IAQA9E,WAAWA,CAAE4C,IAAiC,EAAE;EAC9C,IAAA,KAAK,CAAC;QACJ,GAAG6B,aAAa,CAACyL,QAAQ;EACzB,MAAA,GAAGtN,IAAI;EACP2U,MAAAA,WAAW,EAAE9W,MAAM,CAACoC,MAAM,CAAC,EAAE,EAAEgU,sBAAsB,CAACQ,mBAAmB,EAAEzU,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAE2U,WAAW,CAAA;EAC9F,KAAkB,CAAC,CAAA;EACrB,GAAA;IAES7L,aAAaA,CAAE9I,IAA0C,EAAE;EAClE,IAAA,KAAK,CAAC8I,aAAa,CAAC9I,IAAI,CAAC,CAAA;EAC3B,GAAA;IAESqN,OAAOA,CAAErN,IAA0C,EAAE;EAC5DA,IAAAA,IAAI,CAAC2U,WAAW,GAAG9W,MAAM,CAACoC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC0U,WAAW,EAAE3U,IAAI,CAAC2U,WAAW,CAAC,CAAA;EACxE,IAAA,KAAK,CAACtH,OAAO,CAACrN,IAAI,CAAC,CAAA;MACnB,IAAI,CAAC4U,YAAY,EAAE,CAAA;EACrB,GAAA;EAEAA,EAAAA,YAAYA,GAAI;EACd,IAAA,MAAMC,IAAI,GAAG,IAAI,CAACF,WAAW,CAAA;MAC7B,IAAI,CAAC7C,OAAO,GAAG,EAAE,CAAA;MAAE,IAAI,CAACgD,WAAW,GAAGtS,SAAS,CAAA;MAC/C,IAAI,CAACqP,MAAM,GAAG,EAAE,CAAA;EAChB,IAAA,IAAI,CAACkD,aAAa,GAAG,EAAE,CAAA;EAEvB,IAAA,MAAMC,OAAO,GAAG,IAAI,CAACtT,IAAI,CAAA;EACzB,IAAA,IAAI,CAACsT,OAAO,IAAI,CAACH,IAAI,EAAE,OAAA;MAEvB,IAAII,cAAc,GAAG,KAAK,CAAA;MAC1B,IAAIC,aAAa,GAAG,KAAK,CAAA;EAEzB,IAAA,KAAK,IAAIhW,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAC8V,OAAO,CAAC7V,MAAM,EAAE,EAAED,CAAC,EAAE;QACnC,IAAI,IAAI,CAACiW,MAAM,EAAE;EACf,QAAA,MAAMC,CAAC,GAAGJ,OAAO,CAAC7U,KAAK,CAACjB,CAAC,CAAC,CAAA;UAC1B,MAAMmW,MAAM,GAAGxX,MAAM,CAACN,IAAI,CAAC,IAAI,CAAC4X,MAAM,CAAC,CAACG,MAAM,CAACC,KAAK,IAAIH,CAAC,CAAC7J,OAAO,CAACgK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;EAC/E;EACAF,QAAAA,MAAM,CAACG,IAAI,CAAC,CAACzW,CAAC,EAAED,CAAC,KAAKA,CAAC,CAACK,MAAM,GAAGJ,CAAC,CAACI,MAAM,CAAC,CAAA;EAC1C;EACA,QAAA,MAAMoW,KAAK,GAAGF,MAAM,CAAC,CAAC,CAAC,CAAA;EACvB,QAAA,IAAIE,KAAK,EAAE;YACT,MAAM;cAAEE,MAAM;cAAEC,MAAM;cAAE,GAAGC,KAAAA;aAAO,GAAGpT,aAAa,CAAC,IAAI,CAAC4S,MAAM,CAACI,KAAK,CAAC,CAAmD,CAAC;EACzH,UAAA,MAAMK,SAAS,GAAG;cAChBxB,IAAI,EAAE,IAAI,CAACA,IAAI;cACfxE,KAAK,EAAE,IAAI,CAACA,KAAK;cACjBsE,eAAe,EAAE,IAAI,CAACA,eAAe;cACrCC,WAAW,EAAE,IAAI,CAACA,WAAW;cAC7B/E,SAAS,EAAE,IAAI,CAACA,SAAS;cACzBT,OAAO,EAAE,IAAI,CAACA,OAAO;EACrB,YAAA,GAAGgH,KAAK;cACRD,MAAM;EACNlF,YAAAA,MAAM,EAAE,IAAA;aACT,CAAA;EACD,UAAA,MAAMqF,WAAW,GAAGH,MAAM,IAAI,IAAI,GAAG,IAAIpU,KAAK,CAACwU,WAAW,CAACF,SAAS,YAAmB,GAAGhT,UAAU,CAACgT,SAAS,CAAC,CAAA;EAE/G,UAAA,IAAIC,WAAW,EAAE;EACf,YAAA,IAAI,CAAC/D,OAAO,CAAC5K,IAAI,CAAC2O,WAAW,CAAC,CAAA;EAC9B,YAAA,IAAIJ,MAAM,EAAE,IAAI,CAACX,WAAW,GAAGe,WAAW,CAAA;;EAE1C;EACA,YAAA,IAAI,CAAC,IAAI,CAACd,aAAa,CAACQ,KAAK,CAAC,EAAE,IAAI,CAACR,aAAa,CAACQ,KAAK,CAAC,GAAG,EAAE,CAAA;EAC9D,YAAA,IAAI,CAACR,aAAa,CAACQ,KAAK,CAAC,CAACrO,IAAI,CAAC,IAAI,CAAC4K,OAAO,CAAC3S,MAAM,GAAG,CAAC,CAAC,CAAA;EACzD,WAAA;EAEAD,UAAAA,CAAC,IAAIqW,KAAK,CAACpW,MAAM,GAAG,CAAC,CAAA;EACrB,UAAA,SAAA;EACF,SAAA;EACF,OAAA;EAEA,MAAA,IAAI4U,IAAI,GAAGiB,OAAO,CAAC9V,CAAC,CAAC,CAAA;EACrB,MAAA,IAAI6W,OAAO,IAAGhC,IAAI,IAAIc,IAAI,CAAA,CAAA;EAE1B,MAAA,IAAId,IAAI,KAAKlS,aAAa,CAACmU,SAAS,EAAE;UACpC,IAAI,CAACnE,MAAM,CAAC3K,IAAI,CAAC,IAAI,CAAC4K,OAAO,CAAC3S,MAAM,CAAC,CAAA;EACrC,QAAA,SAAA;EACF,OAAA;EAEA,MAAA,IAAI4U,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;UAChCkB,cAAc,GAAG,CAACA,cAAc,CAAA;EAChC,QAAA,SAAA;EACF,OAAA;EAEA,MAAA,IAAIlB,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;UAChCmB,aAAa,GAAG,CAACA,aAAa,CAAA;EAC9B,QAAA,SAAA;EACF,OAAA;EAEA,MAAA,IAAInB,IAAI,KAAKlS,aAAa,CAACoU,WAAW,EAAE;EACtC,QAAA,EAAE/W,CAAC,CAAA;EACH6U,QAAAA,IAAI,GAAGiB,OAAO,CAAC9V,CAAC,CAAC,CAAA;UACjB,IAAI,CAAC6U,IAAI,EAAE,MAAA;EACXgC,QAAAA,OAAO,GAAG,KAAK,CAAA;EACjB,OAAA;EAEA,MAAA,MAAMG,GAAG,GAAGH,OAAO,GACjB,IAAI9B,sBAAsB,CAAC;EACzBZ,QAAAA,UAAU,EAAE6B,aAAa;UACzBd,IAAI,EAAE,IAAI,CAACA,IAAI;UACfxE,KAAK,EAAE,IAAI,CAACA,KAAK;UACjBsE,eAAe,EAAE,IAAI,CAACA,eAAe;UACrCC,WAAW,EAAE,IAAI,CAACA,WAAW;EAC7B,QAAA,GAAG5R,aAAa,CAACsS,IAAI,CAACd,IAAI,CAAC,CAAC;EAC5BvD,QAAAA,MAAM,EAAE,IAAA;EACV,OAAC,CAAC,GACF,IAAIiD,sBAAsB,CAAC;UACzBM,IAAI;UACJnE,KAAK,EAAE,IAAI,CAACA,KAAK;EACjB8D,QAAAA,WAAW,EAAEuB,cAAAA;EACf,OAAC,CAAC,CAAA;EAEJ,MAAA,IAAI,CAACnD,OAAO,CAAC5K,IAAI,CAACgP,GAAG,CAAC,CAAA;EACxB,KAAA;EACF,GAAA;IAEA,IAAa/O,KAAKA,GAAwB;MACxC,OAAO;QACL,GAAG,KAAK,CAACA,KAAK;QACd2K,OAAO,EAAE,IAAI,CAACA,OAAO,CAACb,GAAG,CAACnS,CAAC,IAAIA,CAAC,CAACqI,KAAK,CAAA;OACvC,CAAA;EACH,GAAA;IAEA,IAAaA,KAAKA,CAAEA,KAAyB,EAAE;MAC7C,IAAI,CAACA,KAAK,EAAE;QAAE,IAAI,CAACsG,KAAK,EAAE,CAAA;EAAE,MAAA,OAAA;EAAQ,KAAA;MAEpC,MAAM;QAAEqE,OAAO;QAAE,GAAGqE,WAAAA;EAAY,KAAC,GAAGhP,KAAK,CAAA;EACzC,IAAA,IAAI,CAAC2K,OAAO,CAACpI,OAAO,CAAC,CAAC5K,CAAC,EAAEsX,EAAE,KAAKtX,CAAC,CAACqI,KAAK,GAAG2K,OAAO,CAACsE,EAAE,CAAC,CAAC,CAAA;MACtD,KAAK,CAACjP,KAAK,GAAGgP,WAAW,CAAA;EAC3B,GAAA;EAES1I,EAAAA,KAAKA,GAAI;MAChB,KAAK,CAACA,KAAK,EAAE,CAAA;EACb,IAAA,IAAI,CAACqE,OAAO,CAACpI,OAAO,CAAC5K,CAAC,IAAIA,CAAC,CAAC2O,KAAK,EAAE,CAAC,CAAA;EACtC,GAAA;IAEA,IAAazC,UAAUA,GAAa;MAClC,OAAO,IAAI,CAAC8J,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC9J,UAAU,GACnD,IAAI,CAAC8G,OAAO,CAACuE,KAAK,CAACvX,CAAC,IAAIA,CAAC,CAACkM,UAAU,CAAC,CAAA;EACzC,GAAA;IAEA,IAAa+C,QAAQA,GAAa;MAChC,OAAO,IAAI,CAAC+D,OAAO,CAACuE,KAAK,CAACvX,CAAC,IAAIA,CAAC,CAACiP,QAAQ,CAAC,CAAA;EAC5C,GAAA;IAEA,IAAImF,OAAOA,GAAa;MACtB,OAAO,IAAI,CAACpB,OAAO,CAACuE,KAAK,CAACvX,CAAC,IAAIA,CAAC,CAACoU,OAAO,CAAC,CAAA;EAC3C,GAAA;IAEA,IAAIG,UAAUA,GAAa;MACzB,OAAO,IAAI,CAACvB,OAAO,CAACuE,KAAK,CAACvX,CAAC,IAAIA,CAAC,CAACuU,UAAU,CAAC,CAAA;EAC9C,GAAA;EAESxH,EAAAA,QAAQA,GAAI;EACnB,IAAA,IAAI,CAACiG,OAAO,CAACpI,OAAO,CAAC5K,CAAC,IAAIA,CAAC,CAAC+M,QAAQ,EAAE,CAAC,CAAA;MACvC,KAAK,CAACA,QAAQ,EAAE,CAAA;EAClB,GAAA;IAEA,IAAa9C,aAAaA,GAAY;EACpC,IAAA,OAAO,IAAI,CAAC+L,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC/L,aAAa,GACtD,IAAI,CAAC+I,OAAO,CAAC/T,MAAM,CAAC,CAAChB,GAAG,EAAE+B,CAAC,KAAK/B,GAAG,IAAI+B,CAAC,CAACiK,aAAa,EAAE,EAAE,CAAC,CAAA;EAC/D,GAAA;IAEA,IAAaA,aAAaA,CAAEA,aAAqB,EAAE;MACjD,IAAI,IAAI,CAAC+L,WAAW,EAAE;EACpB,MAAA,MAAM1T,IAAI,GAAG,IAAI,CAAC+M,WAAW,CAAC,IAAI,CAACqE,cAAc,CAAC,IAAI,CAACV,OAAO,CAACvG,OAAO,CAAC,IAAI,CAACuJ,WAAW,CAAC,CAAC,GAAG,IAAI,CAACA,WAAW,CAACzL,YAAY,CAAClK,MAAM,CAAC,CAAA;EACjI,MAAA,IAAI,CAAC2V,WAAW,CAAC/L,aAAa,GAAGA,aAAa,CAAA;EAC9C,MAAA,IAAI,CAACqF,UAAU,CAAChN,IAAI,CAAC,CAAA;QACrB,IAAI,CAACyK,QAAQ,EAAE,CAAA;EACjB,KAAC,MACI,KAAK,CAAC9C,aAAa,GAAGA,aAAa,CAAA;EAC1C,GAAA;IAEA,IAAa7I,KAAKA,GAAY;MAC5B,OAAO,IAAI,CAAC4U,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC5U,KAAK;EAC9C;EACA,IAAA,IAAI,CAAC4R,OAAO,CAAC/T,MAAM,CAAC,CAAChB,GAAG,EAAE+B,CAAC,KAAK/B,GAAG,IAAI+B,CAAC,CAACoB,KAAK,EAAE,EAAE,CAAC,CAAA;EACvD,GAAA;IAEA,IAAaA,KAAKA,CAAEA,KAAa,EAAE;MACjC,IAAI,IAAI,CAAC4U,WAAW,EAAE;EACpB,MAAA,MAAM1T,IAAI,GAAG,IAAI,CAAC+M,WAAW,CAAC,IAAI,CAACqE,cAAc,CAAC,IAAI,CAACV,OAAO,CAACvG,OAAO,CAAC,IAAI,CAACuJ,WAAW,CAAC,CAAC,GAAG,IAAI,CAACA,WAAW,CAACzL,YAAY,CAAClK,MAAM,CAAC,CAAA;EACjI,MAAA,IAAI,CAAC2V,WAAW,CAAC5U,KAAK,GAAGA,KAAK,CAAA;EAC9B,MAAA,IAAI,CAACkO,UAAU,CAAChN,IAAI,CAAC,CAAA;QACrB,IAAI,CAACyK,QAAQ,EAAE,CAAA;EACjB,KAAC,MACI,KAAK,CAAC3L,KAAK,GAAGA,KAAK,CAAA;EAC1B,GAAA;IAEA,IAAagJ,UAAUA,GAAW;EAChC,IAAA,OAAO,IAAI,CAAC4L,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC5L,UAAU,GACnD,KAAK,CAACA,UAAU,CAAA;EACpB,GAAA;IAEA,IAAaA,UAAUA,CAAEhJ,KAAY,EAAE;MACrC,IAAI,IAAI,CAAC4U,WAAW,EAAE;EACpB,MAAA,MAAM1T,IAAI,GAAG,IAAI,CAAC+M,WAAW,CAAC,IAAI,CAACqE,cAAc,CAAC,IAAI,CAACV,OAAO,CAACvG,OAAO,CAAC,IAAI,CAACuJ,WAAW,CAAC,CAAC,GAAG,IAAI,CAACA,WAAW,CAACzL,YAAY,CAAClK,MAAM,CAAC,CAAA;EACjI,MAAA,IAAI,CAAC2V,WAAW,CAAC5L,UAAU,GAAGhJ,KAAK,CAAA;EACnC,MAAA,IAAI,CAACkO,UAAU,CAAChN,IAAI,CAAC,CAAA;QACrB,IAAI,CAACyK,QAAQ,EAAE,CAAA;EACjB,KAAC,MACI,KAAK,CAAC3C,UAAU,GAAGhJ,KAAK,CAAA;EAC/B,GAAA;IAEA,IAAamJ,YAAYA,GAAY;EACnC,IAAA,OAAO,IAAI,CAACyI,OAAO,CAAC/T,MAAM,CAAC,CAAChB,GAAG,EAAE+B,CAAC,KAAK/B,GAAG,IAAI+B,CAAC,CAACuK,YAAY,EAAE,EAAE,CAAC,CAAA;EACnE,GAAA;IAES+E,UAAUA,CAAEhN,IAAmC,EAAiB;EACvE,IAAA,OAAO,KAAK,CAACgN,UAAU,CAAChN,IAAI,CAAC,CAACmL,SAAS,CAAC,IAAI,CAACU,kBAAkB,EAAE,CAAC,CAAA;EACpE,GAAA;EAESqC,EAAAA,YAAYA,GAAmB;EAAA,IAAA,IAAAgH,oBAAA,CAAA;EACtC,IAAA,MAAM7K,OAAO,GAAG,IAAIQ,aAAa,EAAE,CAAA;EAEnC,IAAA,IAAIsK,eAAe,GAAAD,CAAAA,oBAAA,GAAG,IAAI,CAAC5E,cAAc,CAAC,IAAI,CAACrI,YAAY,CAAClK,MAAM,CAAC,KAA7CmX,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,oBAAA,CAA+C1E,KAAK,CAAA;EAC1E,IAAA,IAAI2E,eAAe,IAAI,IAAI,EAAE,OAAO9K,OAAO,CAAA;;EAE3C;MACA,IAAI,IAAI,CAACqG,OAAO,CAACyE,eAAe,CAAC,CAACxI,QAAQ,EAAE,EAAEwI,eAAe,CAAA;EAE7D,IAAA,KAAK,IAAIH,EAAE,GAACG,eAAe,EAAEH,EAAE,GAAC,IAAI,CAACtE,OAAO,CAAC3S,MAAM,EAAE,EAAEiX,EAAE,EAAE;QACzD,MAAM1G,CAAC,GAAG,IAAI,CAACoC,OAAO,CAACsE,EAAE,CAAC,CAAC9G,YAAY,EAAE,CAAA;EACzC,MAAA,IAAI,CAACI,CAAC,CAAC7O,QAAQ,EAAE,MAAA;EAEjB4K,MAAAA,OAAO,CAACc,SAAS,CAACmD,CAAC,CAAC,CAAA;EACtB,KAAA;EAEA,IAAA,OAAOjE,OAAO,CAAA;EAChB,GAAA;EAES4C,EAAAA,cAAcA,CAAEC,EAAU,EAAEX,KAAsC,EAAoB;EAAA,IAAA,IAA1DA,KAAsC,KAAA,KAAA,CAAA,EAAA;QAAtCA,KAAsC,GAAC,EAAE,CAAA;EAAA,KAAA;MAC5E,MAAM6I,SAAS,GAAG,IAAI,CAAC9E,cAAc,CAAC,IAAI,CAACrI,YAAY,CAAClK,MAAM,CAAC,CAAA;EAC/D,IAAA,MAAMsM,OAAO,GAAG,IAAIQ,aAAa,EAAE,CAAA;EACnC,IAAA,IAAI,CAACuK,SAAS,EAAE,OAAO/K,OAAO,CAAA;MAE9B,KAAK,IAAI2K,EAAE,GAACI,SAAS,CAAC5E,KAAK,EAAEW,KAAK,EAAGA,KAAK,GAAG,IAAI,CAACT,OAAO,CAACsE,EAAE,CAAC,EAAG,EAAEA,EAAE,EAAE;EAAA,MAAA,IAAA5B,qBAAA,CAAA;EACpE,MAAA,MAAMiC,YAAY,GAAGlE,KAAK,CAAChE,WAAW,CAACD,EAAE,EAAE;EAAE,QAAA,GAAGX,KAAK;EAAE4B,QAAAA,gBAAgB,EAAAiF,CAAAA,qBAAA,GAAE7G,KAAK,CAAC4B,gBAAgB,KAAA,IAAA,IAAA,CAAAiF,qBAAA,GAAtBA,qBAAA,CAAwB1C,OAAO,KAA/B0C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAkC4B,EAAE,CAAA;EAAE,OAAC,CAAC,CAAA;EAEjH3K,MAAAA,OAAO,CAACc,SAAS,CAACkK,YAAY,CAAC,CAAA;EAE/B,MAAA,IAAIA,YAAY,CAACjK,QAAQ,EAAE,MAAM;EACnC,KAAA;EAEA,IAAA,OAAOf,OAAO,CAAA;EAChB,GAAA;EAES0C,EAAAA,WAAWA,CAAEF,OAAe,EAAIC,KAAa,EAAwC;EAAA,IAAA,IAAxED,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;EAC7E,IAAA,MAAMuX,SAAS,GAAG,IAAI3F,iBAAiB,EAAE,CAAA;EACzC,IAAA,IAAI9C,OAAO,KAAKC,KAAK,EAAE,OAAOwI,SAAS,CAAA;EAEvC,IAAA,IAAI,CAACC,qBAAqB,CAAC1I,OAAO,EAAEC,KAAK,EAAE,CAACpP,CAAC,EAAEsX,EAAE,EAAEQ,QAAQ,EAAEC,MAAM,KAAK;QACtE,MAAMC,UAAU,GAAGhY,CAAC,CAACqP,WAAW,CAACyI,QAAQ,EAAEC,MAAM,CAAC,CAAA;QAClDC,UAAU,CAACjK,IAAI,GAAG,IAAI,CAACkK,eAAe,CAACX,EAAE,CAAC,CAAA;QAC1CU,UAAU,CAAClK,IAAI,GAAG,IAAI,CAAC4F,cAAc,CAAC4D,EAAE,CAAC,CAAA;QACzC,IAAIU,UAAU,YAAY/F,iBAAiB,EAAE+F,UAAU,CAACvF,UAAU,GAAG6E,EAAE,CAAA;EAEvEM,MAAAA,SAAS,CAAC5J,MAAM,CAACgK,UAAU,CAAC,CAAA;EAC9B,KAAC,CAAC,CAAA;EAEF,IAAA,OAAOJ,SAAS,CAAA;EAClB,GAAA;EAES5I,EAAAA,YAAYA,CAAEG,OAAe,EAAIC,KAAa,EAA2BP,KAAmB,EAAa;EAAA,IAAA,IAA3FM,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEwO,KAAmB,KAAA,KAAA,CAAA,EAAA;QAAnBA,KAAmB,GAAC,EAAE,CAAA;EAAA,KAAA;EACtG,IAAA,IAAIM,OAAO,KAAKC,KAAK,EAAE,OAAO,EAAE,CAAA;MAEhC,IAAIxK,KAAK,GAAG,EAAE,CAAA;EAEd,IAAA,IAAI,CAACiT,qBAAqB,CAAC1I,OAAO,EAAEC,KAAK,EAAE,CAACpP,CAAC,EAAEpB,CAAC,EAAEuQ,OAAO,EAAEC,KAAK,KAAK;QACnExK,KAAK,IAAI5E,CAAC,CAACgP,YAAY,CAACG,OAAO,EAAEC,KAAK,EAAEP,KAAK,CAAC,CAAA;EAChD,KAAC,CAAC,CAAA;EAEF,IAAA,OAAOjK,KAAK,CAAA;EACd,GAAA;IAEAqT,eAAeA,CAAExF,UAAkB,EAAsB;EACvD,IAAA,IAAIyF,UAAU,CAAA;EACd,IAAA,KAAK,IAAIC,EAAE,GAAC,CAAC,EAAEA,EAAE,GAAC,IAAI,CAACpF,MAAM,CAAC1S,MAAM,EAAE,EAAE8X,EAAE,EAAE;EAC1C,MAAA,MAAMpK,IAAI,GAAG,IAAI,CAACgF,MAAM,CAACoF,EAAE,CAAC,CAAA;QAC5B,IAAIpK,IAAI,IAAI0E,UAAU,EAAEyF,UAAU,GAAGnK,IAAI,CAAC,KACrC,MAAA;EACP,KAAA;EACA,IAAA,OAAOmK,UAAU,CAAA;EACnB,GAAA;;EAEA;IACS/J,kBAAkBA,CAAEiK,YAAqB,EAAiB;EACjE,IAAA,MAAMzL,OAAO,GAAG,IAAIQ,aAAa,EAAE,CAAA;MACnC,IAAI,IAAI,CAACmI,IAAI,IAAI8C,YAAY,IAAI,IAAI,EAAE,OAAOzL,OAAO,CAAA;MAErD,MAAM0L,cAAc,GAAG,IAAI,CAACzF,cAAc,CAAC,IAAI,CAACrI,YAAY,CAAClK,MAAM,CAAC,CAAA;EACpE,IAAA,IAAI,CAACgY,cAAc,EAAE,OAAO1L,OAAO,CAAA;EAEnC,IAAA,MAAM8K,eAAe,GAAGY,cAAc,CAACvF,KAAK,CAAA;EAC5C,IAAA,MAAMwF,aAAa,GAAGF,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAG,IAAI,CAACpF,OAAO,CAAC3S,MAAM,CAAA;EAE/E,IAAA,IAAI,CAAC2S,OAAO,CAAC3R,KAAK,CAACoW,eAAe,EAAEa,aAAa,CAAC,CAC/C1N,OAAO,CAAC5K,CAAC,IAAI;QACZ,IAAI,CAACA,CAAC,CAACsV,IAAI,IAAI8C,YAAY,IAAI,IAAI,EAAE;EAAA,QAAA,IAAAG,QAAA,CAAA;EACnC5L,QAAAA,OAAO,CAACc,SAAS,CAACzN,CAAC,CAACmO,kBAAkB,CAAAoK,CAAAA,QAAA,GAAEvY,CAAC,CAAmBgT,OAAO,KAAA,IAAA,GAAA,KAAA,CAAA,GAA5BuF,QAAA,CAA8BlY,MAAM,CAAC,CAAC,CAAA;EAC/E,OAAA;EACF,KAAC,CAAC,CAAA;EAEJ,IAAA,OAAOsM,OAAO,CAAA;EAChB,GAAA;;EAEA;IACAiG,cAAcA,CAAE5H,GAAW,EAA4B;MACrD,IAAIwN,MAAM,GAAG,EAAE,CAAA;EACf,IAAA,KAAK,IAAIlB,EAAE,GAAC,CAAC,EAAEA,EAAE,GAAC,IAAI,CAACtE,OAAO,CAAC3S,MAAM,EAAE,EAAEiX,EAAE,EAAE;EAC3C,MAAA,MAAM7D,KAAK,GAAG,IAAI,CAACT,OAAO,CAACsE,EAAE,CAAC,CAAA;EAC9B,MAAA,MAAMmB,aAAa,GAAGD,MAAM,CAACnY,MAAM,CAAA;QAEnCmY,MAAM,IAAI/E,KAAK,CAAClJ,YAAY,CAAA;EAE5B,MAAA,IAAIS,GAAG,IAAIwN,MAAM,CAACnY,MAAM,EAAE;UACxB,OAAO;EACLyS,UAAAA,KAAK,EAAEwE,EAAE;YACTzK,MAAM,EAAE7B,GAAG,GAAGyN,aAAAA;WACf,CAAA;EACH,OAAA;EACF,KAAA;EACF,GAAA;IAEA/E,cAAcA,CAAEjB,UAAkB,EAAU;MAC1C,OAAO,IAAI,CAACO,OAAO,CAChB3R,KAAK,CAAC,CAAC,EAAEoR,UAAU,CAAC,CACpBxT,MAAM,CAAC,CAAC+L,GAAG,EAAEhL,CAAC,KAAKgL,GAAG,IAAIhL,CAAC,CAACuK,YAAY,CAAClK,MAAM,EAAE,CAAC,CAAC,CAAA;EACxD,GAAA;EAEAwX,EAAAA,qBAAqBA,CAAE1I,OAAe,EAAEC,KAAa,EAA2B4B,EAAqF,EAAE;EAAA,IAAA,IAA/H5B,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;EAC5E,IAAA,MAAMqY,aAAa,GAAG,IAAI,CAAC9F,cAAc,CAACzD,OAAO,CAAC,CAAA;EAElD,IAAA,IAAIuJ,aAAa,EAAE;EACjB,MAAA,MAAMC,WAAW,GAAG,IAAI,CAAC/F,cAAc,CAACxD,KAAK,CAAC,CAAA;EAC9C;QACA,MAAMwJ,WAAW,GAAGD,WAAW,IAAID,aAAa,CAAC5F,KAAK,KAAK6F,WAAW,CAAC7F,KAAK,CAAA;EAC5E,MAAA,MAAM+F,iBAAiB,GAAGH,aAAa,CAAC7L,MAAM,CAAA;QAC9C,MAAMiM,eAAe,GAAGH,WAAW,IAAIC,WAAW,GAChDD,WAAW,CAAC9L,MAAM,GAClB,IAAI,CAACmG,OAAO,CAAC0F,aAAa,CAAC5F,KAAK,CAAC,CAACvI,YAAY,CAAClK,MAAM,CAAA;EACvD2Q,MAAAA,EAAE,CAAC,IAAI,CAACgC,OAAO,CAAC0F,aAAa,CAAC5F,KAAK,CAAC,EAAE4F,aAAa,CAAC5F,KAAK,EAAE+F,iBAAiB,EAAEC,eAAe,CAAC,CAAA;EAE9F,MAAA,IAAIH,WAAW,IAAI,CAACC,WAAW,EAAE;EAC/B;EACA,QAAA,KAAK,IAAItB,EAAE,GAACoB,aAAa,CAAC5F,KAAK,GAAC,CAAC,EAAEwE,EAAE,GAACqB,WAAW,CAAC7F,KAAK,EAAE,EAAEwE,EAAE,EAAE;YAC7DtG,EAAE,CAAC,IAAI,CAACgC,OAAO,CAACsE,EAAE,CAAC,EAAEA,EAAE,EAAE,CAAC,EAAE,IAAI,CAACtE,OAAO,CAACsE,EAAE,CAAC,CAAC/M,YAAY,CAAClK,MAAM,CAAC,CAAA;EACnE,SAAA;;EAEA;EACA2Q,QAAAA,EAAE,CAAC,IAAI,CAACgC,OAAO,CAAC2F,WAAW,CAAC7F,KAAK,CAAC,EAAE6F,WAAW,CAAC7F,KAAK,EAAE,CAAC,EAAE6F,WAAW,CAAC9L,MAAM,CAAC,CAAA;EAC/E,OAAA;EACF,KAAA;EACF,GAAA;EAESkE,EAAAA,MAAMA,CAAE5B,OAAe,EAAIC,KAAa,EAA0C;EAAA,IAAA,IAA1ED,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;MACxE,MAAM0Y,aAAa,GAAG,KAAK,CAAChI,MAAM,CAAC5B,OAAO,EAAEC,KAAK,CAAC,CAAA;EAClD,IAAA,IAAI,CAACyI,qBAAqB,CAAC1I,OAAO,EAAEC,KAAK,EAAE,CAACpP,CAAC,EAAEpB,CAAC,EAAEkZ,QAAQ,EAAEC,MAAM,KAAK;QACrEgB,aAAa,CAACtL,SAAS,CAACzN,CAAC,CAAC+Q,MAAM,CAAC+G,QAAQ,EAAEC,MAAM,CAAC,CAAC,CAAA;EACrD,KAAC,CAAC,CAAA;EACF,IAAA,OAAOgB,aAAa,CAAA;EACtB,GAAA;EAES3M,EAAAA,eAAeA,CAAEzK,SAAiB,EAAE/B,SAAoB,EAAyB;EAAA,IAAA,IAA7CA,SAAoB,KAAA,KAAA,CAAA,EAAA;QAApBA,SAAoB,GAACP,SAAS,CAACC,IAAI,CAAA;EAAA,KAAA;MAC9E,IAAI,CAAC,IAAI,CAAC0T,OAAO,CAAC3S,MAAM,EAAE,OAAO,CAAC,CAAA;MAClC,MAAM2Y,MAAM,GAAG,IAAI1F,aAAa,CAAC,IAAI,EAAE3R,SAAS,CAAC,CAAA;EAEjD,IAAA,IAAI/B,SAAS,KAAKP,SAAS,CAACC,IAAI,EAAE;EAChC;EACA;EACA;QACA,IAAI0Z,MAAM,CAACvE,oBAAoB,EAAE,EAAE,OAAOuE,MAAM,CAAChO,GAAG,CAAA;QACpDgO,MAAM,CAACnF,QAAQ,EAAE,CAAA;QACjB,IAAImF,MAAM,CAAC3E,mBAAmB,EAAE,EAAE,OAAO2E,MAAM,CAAChO,GAAG,CAAA;EACnD,MAAA,OAAO,IAAI,CAACT,YAAY,CAAClK,MAAM,CAAA;EACjC,KAAA;;EAEA;MACA,IAAIT,SAAS,KAAKP,SAAS,CAACE,IAAI,IAAIK,SAAS,KAAKP,SAAS,CAACG,UAAU,EAAE;EACtE;EACA,MAAA,IAAII,SAAS,KAAKP,SAAS,CAACE,IAAI,EAAE;UAChCyZ,MAAM,CAACxE,qBAAqB,EAAE,CAAA;UAC9B,IAAIwE,MAAM,CAACxF,EAAE,IAAIwF,MAAM,CAAChO,GAAG,KAAKrJ,SAAS,EAAE,OAAOA,SAAS,CAAA;UAC3DqX,MAAM,CAACnF,QAAQ,EAAE,CAAA;EACnB,OAAA;;EAEA;QACAmF,MAAM,CAAC3E,mBAAmB,EAAE,CAAA;QAC5B2E,MAAM,CAAC1E,sBAAsB,EAAE,CAAA;QAC/B0E,MAAM,CAAC7E,oBAAoB,EAAE,CAAA;;EAE7B;EACA,MAAA,IAAIvU,SAAS,KAAKP,SAAS,CAACE,IAAI,EAAE;UAChCyZ,MAAM,CAACvE,oBAAoB,EAAE,CAAA;UAC7BuE,MAAM,CAACtE,uBAAuB,EAAE,CAAA;EAChC,QAAA,IAAIsE,MAAM,CAACxF,EAAE,IAAIwF,MAAM,CAAChO,GAAG,IAAIrJ,SAAS,EAAE,OAAOqX,MAAM,CAAChO,GAAG,CAAA;UAC3DgO,MAAM,CAACnF,QAAQ,EAAE,CAAA;EACjB,QAAA,IAAImF,MAAM,CAACxF,EAAE,IAAIwF,MAAM,CAAChO,GAAG,IAAIrJ,SAAS,EAAE,OAAOqX,MAAM,CAAChO,GAAG,CAAA;UAC3DgO,MAAM,CAACnF,QAAQ,EAAE,CAAA;EACnB,OAAA;EACA,MAAA,IAAImF,MAAM,CAACxF,EAAE,EAAE,OAAOwF,MAAM,CAAChO,GAAG,CAAA;EAChC,MAAA,IAAIpL,SAAS,KAAKP,SAAS,CAACG,UAAU,EAAE,OAAO,CAAC,CAAA;QAEhDwZ,MAAM,CAACnF,QAAQ,EAAE,CAAA;EACjB,MAAA,IAAImF,MAAM,CAACxF,EAAE,EAAE,OAAOwF,MAAM,CAAChO,GAAG,CAAA;QAEhCgO,MAAM,CAACnF,QAAQ,EAAE,CAAA;EACjB,MAAA,IAAImF,MAAM,CAACxF,EAAE,EAAE,OAAOwF,MAAM,CAAChO,GAAG,CAAA;EAEhC,MAAA,OAAO,CAAC,CAAA;EACV,KAAA;MAEA,IAAIpL,SAAS,KAAKP,SAAS,CAACI,KAAK,IAAIG,SAAS,KAAKP,SAAS,CAACK,WAAW,EAAE;EACxE;QACAsZ,MAAM,CAACvE,oBAAoB,EAAE,CAAA;QAC7BuE,MAAM,CAACtE,uBAAuB,EAAE,CAAA;QAEhC,IAAIsE,MAAM,CAACxE,qBAAqB,EAAE,EAAE,OAAOwE,MAAM,CAAChO,GAAG,CAAA;QACrD,IAAIpL,SAAS,KAAKP,SAAS,CAACK,WAAW,EAAE,OAAO,IAAI,CAAC6K,YAAY,CAAClK,MAAM,CAAA;;EAExE;QACA2Y,MAAM,CAACnF,QAAQ,EAAE,CAAA;EACjB,MAAA,IAAImF,MAAM,CAACxF,EAAE,EAAE,OAAOwF,MAAM,CAAChO,GAAG,CAAA;QAEhCgO,MAAM,CAACnF,QAAQ,EAAE,CAAA;EACjB,MAAA,IAAImF,MAAM,CAACxF,EAAE,EAAE,OAAOwF,MAAM,CAAChO,GAAG,CAAA;QAEhC,OAAO,IAAI,CAACoB,eAAe,CAACzK,SAAS,EAAEtC,SAAS,CAACE,IAAI,CAAC,CAAA;EACxD,KAAA;EAEA,IAAA,OAAOoC,SAAS,CAAA;EAClB,GAAA;EAESuN,EAAAA,mBAAmBA,CAAEC,OAAe,EAAIC,KAAa,EAAmC;EAAA,IAAA,IAAnED,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;MACrF,IAAI4Y,KAAK,GAAG,CAAC,CAAA;EACb,IAAA,IAAI,CAACpB,qBAAqB,CAAC1I,OAAO,EAAEC,KAAK,EAAE,CAACpP,CAAC,EAAEpB,CAAC,EAAEkZ,QAAQ,EAAEC,MAAM,KAAK;QACrEkB,KAAK,IAAIjZ,CAAC,CAACkP,mBAAmB,CAAC4I,QAAQ,EAAEC,MAAM,CAAC,CAAA;EAClD,KAAC,CAAC,CAAA;EACF,IAAA,OAAOkB,KAAK,CAAA;EACd,GAAA;;EAEA;IACAlC,WAAWA,CAAExY,IAAY,EAA4B;MACnD,OAAO,IAAI,CAAC2a,YAAY,CAAC3a,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;EACnC,GAAA;;EAEA;IACA2a,YAAYA,CAAE3a,IAAY,EAAuB;EAC/C,IAAA,MAAM4a,OAAO,GAAG,IAAI,CAAClD,aAAa,CAAC1X,IAAI,CAAC,CAAA;EACxC,IAAA,IAAI,CAAC4a,OAAO,EAAE,OAAO,EAAE,CAAA;EACvB,IAAA,OAAOA,OAAO,CAAChH,GAAG,CAACiH,EAAE,IAAI,IAAI,CAACpG,OAAO,CAACoG,EAAE,CAAC,CAAC,CAAA;EAC5C,GAAA;IAESpJ,GAAGA,CAAEnB,KAAmB,EAAiB;EAChD,IAAA,MAAMlC,OAAO,GAAG,IAAIQ,aAAa,EAAE,CAAA;MACnC,IAAI,CAAC0K,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAACtN,YAAY,CAAClK,MAAM,EAAEL,CAAC,IAAI2M,OAAO,CAACc,SAAS,CAACzN,CAAC,CAACgQ,GAAG,CAACnB,KAAK,CAAC,CAAC,CAAC,CAAA;EAC7F,IAAA,OAAOlC,OAAO,CAAA;EAChB,GAAA;EACF,CAAA;EA1eM5J,aAAa,CACVyL,QAAQ,GAAG;IAChB,GAAGpL,MAAM,CAACoL,QAAQ;EAClB8G,EAAAA,IAAI,EAAE,IAAI;EACVF,EAAAA,eAAe,EAAE,GAAA;EACnB,CAAC,CAAA;EALGrS,aAAa,CAMVmU,SAAS,GAAG,GAAG,CAAA;EANlBnU,aAAa,CAOVoU,WAAW,GAAG,IAAI,CAAA;EAPrBpU,aAAa,CAQVsW,eAAe,GAAGlE,sBAAsB,CAAA;EAR3CpS,aAAa,CASVuW,eAAe,GAAG3E,sBAAsB,CAAA;EAoejDnS,KAAK,CAACO,aAAa,GAAGA,aAAa;;ECphBnC;EAEA,MAAMwW,WAAW,SAASxW,aAAa,CAAC;EACtC;EACF;EACA;EACA;;EAEE;;EAEA;;IAGA,IAAIyW,UAAUA,GAAY;MACxB,OAAO,IAAI,CAACC,SAAS,GAAGvb,MAAM,CAAC,IAAI,CAAC4P,IAAI,CAAC,CAACzN,MAAM,CAAA;EAClD,GAAA;IAEA/B,WAAWA,CAAE4C,IAAyB,EAAE;EACtC,IAAA,KAAK,CAACA,IAA4B,CAAC,CAAC;EACtC,GAAA;IAES8I,aAAaA,CAAE9I,IAAiC,EAAE;EACzD,IAAA,KAAK,CAAC8I,aAAa,CAAC9I,IAAI,CAAC,CAAA;EAC3B,GAAA;IAESqN,OAAOA,CAAErN,IAAiC,EAAE;MACnD,MAAM;EACJwY,MAAAA,EAAE,GAAC,IAAI,CAACA,EAAE,IAAI,CAAC;EACf5L,MAAAA,IAAI,GAAC,IAAI,CAACA,IAAI,IAAI,CAAC;EACnB2L,MAAAA,SAAS,GAAC,IAAI,CAACA,SAAS,IAAI,CAAC;QAC7B5J,OAAO,GAAC,IAAI,CAACA,OAAO;QACpB,GAAG8J,WAAAA;EAC+B,KAAC,GAAGzY,IAAI,CAAA;MAE5C,IAAI,CAACwY,EAAE,GAAGA,EAAE,CAAA;MACZ,IAAI,CAAC5L,IAAI,GAAGA,IAAI,CAAA;EAChB,IAAA,IAAI,CAAC2L,SAAS,GAAG5X,IAAI,CAACK,GAAG,CAAChE,MAAM,CAACwb,EAAE,CAAC,CAACrZ,MAAM,EAAEoZ,SAAS,CAAC,CAAA;MACvD,IAAI,CAAC5J,OAAO,GAAGA,OAAO,CAAA;EAEtB,IAAA,MAAM+J,OAAO,GAAG1b,MAAM,CAAC,IAAI,CAAC4P,IAAI,CAAC,CAAC+L,QAAQ,CAAC,IAAI,CAACJ,SAAS,EAAE,GAAG,CAAC,CAAA;EAC/D,IAAA,MAAMK,KAAK,GAAG5b,MAAM,CAAC,IAAI,CAACwb,EAAE,CAAC,CAACG,QAAQ,CAAC,IAAI,CAACJ,SAAS,EAAE,GAAG,CAAC,CAAA;MAC3D,IAAIM,cAAc,GAAG,CAAC,CAAA;EACtB,IAAA,OAAOA,cAAc,GAAGD,KAAK,CAACzZ,MAAM,IAAIyZ,KAAK,CAACC,cAAc,CAAC,KAAKH,OAAO,CAACG,cAAc,CAAC,EAAE,EAAEA,cAAc,CAAA;EAC3GJ,IAAAA,WAAW,CAAC/W,IAAI,GAAGkX,KAAK,CAACzY,KAAK,CAAC,CAAC,EAAE0Y,cAAc,CAAC,CAACja,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,CAAC8W,MAAM,CAAC,IAAI,CAAC6C,SAAS,GAAGM,cAAc,CAAC,CAAA;EAEpH,IAAA,KAAK,CAACxL,OAAO,CAACoL,WAAW,CAAC,CAAA;EAC5B,GAAA;IAEA,IAAazN,UAAUA,GAAa;MAClC,OAAO,KAAK,CAACA,UAAU,IAAIyB,OAAO,CAAC,IAAI,CAACvM,KAAK,CAAC,CAAA;EAChD,GAAA;IAEA4Y,UAAUA,CAAE/b,GAAW,EAAoB;MACzC,IAAIgc,MAAM,GAAG,EAAE,CAAA;MACf,IAAIC,MAAM,GAAG,EAAE,CAAA;EAEf,IAAA,MAAM,GAAGC,WAAW,EAAEC,GAAG,CAAC,GAAGnc,GAAG,CAACoc,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAA;EAChE,IAAA,IAAID,GAAG,EAAE;QACPH,MAAM,GAAG,GAAG,CAACrD,MAAM,CAACuD,WAAW,CAAC9Z,MAAM,CAAC,GAAG+Z,GAAG,CAAA;QAC7CF,MAAM,GAAG,GAAG,CAACtD,MAAM,CAACuD,WAAW,CAAC9Z,MAAM,CAAC,GAAG+Z,GAAG,CAAA;EAC/C,KAAA;MACAH,MAAM,GAAGA,MAAM,CAACK,MAAM,CAAC,IAAI,CAACb,SAAS,EAAE,GAAG,CAAC,CAAA;MAC3CS,MAAM,GAAGA,MAAM,CAACI,MAAM,CAAC,IAAI,CAACb,SAAS,EAAE,GAAG,CAAC,CAAA;EAE3C,IAAA,OAAO,CAACQ,MAAM,EAAEC,MAAM,CAAC,CAAA;EACzB,GAAA;EAEStK,EAAAA,aAAaA,CAAEJ,EAAU,EAAEX,KAAkB,EAA8B;EAAA,IAAA,IAAhDA,KAAkB,KAAA,KAAA,CAAA,EAAA;QAAlBA,KAAkB,GAAC,EAAE,CAAA;EAAA,KAAA;EACvD,IAAA,IAAIlC,OAAsB,CAAA;EAC1B,IAAA,CAAC6C,EAAE,EAAE7C,OAAO,CAAC,GAAG,KAAK,CAACiD,aAAa,CAACJ,EAAE,CAAC1P,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE+O,KAAK,CAAC,CAAA;MAEjE,IAAI,CAACW,EAAE,EAAE7C,OAAO,CAACa,IAAI,GAAG,CAAC,IAAI,CAACtB,UAAU,CAAA;EAExC,IAAA,OAAO,CAACsD,EAAE,EAAE7C,OAAO,CAAC,CAAA;EACtB,GAAA;EAES4C,EAAAA,cAAcA,CAAEC,EAAU,EAAEX,KAAsC,EAAoB;EAAA,IAAA,IAA1DA,KAAsC,KAAA,KAAA,CAAA,EAAA;QAAtCA,KAAsC,GAAC,EAAE,CAAA;EAAA,KAAA;MAC5E,IAAI,CAAC,IAAI,CAACgB,OAAO,IAAI,IAAI,CAACzO,KAAK,CAACf,MAAM,GAAG,CAAC,GAAG,IAAI,CAACoZ,SAAS,EAAE,OAAO,KAAK,CAAClK,cAAc,CAACC,EAAE,EAAEX,KAAK,CAAC,CAAA;EAEnG,IAAA,MAAM+K,OAAO,GAAG1b,MAAM,CAAC,IAAI,CAAC4P,IAAI,CAAC,CAAC+L,QAAQ,CAAC,IAAI,CAACJ,SAAS,EAAE,GAAG,CAAC,CAAA;EAC/D,IAAA,MAAMK,KAAK,GAAG5b,MAAM,CAAC,IAAI,CAACwb,EAAE,CAAC,CAACG,QAAQ,CAAC,IAAI,CAACJ,SAAS,EAAE,GAAG,CAAC,CAAA;EAE3D,IAAA,MAAM,CAACQ,MAAM,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACF,UAAU,CAAC,IAAI,CAAC5Y,KAAK,GAAGoO,EAAE,CAAC,CAAA;MAEzD,IAAIvM,MAAM,CAACiX,MAAM,CAAC,GAAG,IAAI,CAACpM,IAAI,EAAE,OAAO,KAAK,CAACyB,cAAc,CAACqK,OAAO,CAAC,IAAI,CAACxY,KAAK,CAACf,MAAM,CAAC,EAAEwO,KAAK,CAAC,CAAA;MAE9F,IAAI5L,MAAM,CAACgX,MAAM,CAAC,GAAG,IAAI,CAACP,EAAE,EAAE;QAC5B,IAAI,CAAC7K,KAAK,CAACvM,IAAI,IAAI,IAAI,CAACuN,OAAO,KAAK,KAAK,IAAI,IAAI,CAACzO,KAAK,CAACf,MAAM,GAAG,CAAC,GAAG,IAAI,CAACoZ,SAAS,EAAE;UACnF,OAAO,KAAK,CAAClK,cAAc,CAACqK,OAAO,CAAC,IAAI,CAACxY,KAAK,CAACf,MAAM,CAAC,EAAEwO,KAAK,CAAC,CAACpB,SAAS,CAAC,IAAI,CAAC8B,cAAc,CAACC,EAAE,EAAEX,KAAK,CAAC,CAAC,CAAA;EAC1G,OAAA;EACA,MAAA,OAAO,KAAK,CAACU,cAAc,CAACuK,KAAK,CAAC,IAAI,CAAC1Y,KAAK,CAACf,MAAM,CAAC,EAAEwO,KAAK,CAAC,CAAA;EAC9D,KAAA;EAEA,IAAA,OAAO,KAAK,CAACU,cAAc,CAACC,EAAE,EAAEX,KAAK,CAAC,CAAA;EACxC,GAAA;IAESuB,UAAUA,CAAEvB,KAAkB,EAAW;EAChD,IAAA,MAAM5Q,GAAG,GAAG,IAAI,CAACmD,KAAK,CAAA;EAEtB,IAAA,MAAMmZ,YAAY,GAAGtc,GAAG,CAAC2X,MAAM,CAAC,MAAM,CAAC,CAAA;EACvC,IAAA,IAAI2E,YAAY,KAAK,CAAC,CAAC,IAAItc,GAAG,CAACoC,MAAM,IAAI,IAAI,CAACmZ,UAAU,EAAE,OAAO,IAAI,CAAA;MAErE,MAAM,CAACS,MAAM,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACF,UAAU,CAAC/b,GAAG,CAAC,CAAA;MAE7C,OAAO,IAAI,CAAC6P,IAAI,IAAI7K,MAAM,CAACiX,MAAM,CAAC,IAAIjX,MAAM,CAACgX,MAAM,CAAC,IAAI,IAAI,CAACP,EAAE,IAC7D,KAAK,CAACtJ,UAAU,CAACvB,KAAK,CAAC,CAAA;EAC3B,GAAA;IAESmB,GAAGA,CAAEnB,KAAmB,EAAiB;EAChD,IAAA,MAAMlC,OAAO,GAAG,IAAIQ,aAAa,EAAE,CAAA;MACnC,IAAI,IAAI,CAAC/L,KAAK,CAACf,MAAM,KAAK,IAAI,CAACoZ,SAAS,EAAE,OAAO9M,OAAO,CAAA;EAExD,IAAA,MAAMvL,KAAK,GAAG,IAAI,CAACA,KAAK,CAAA;MACxB,MAAMoZ,SAAS,GAAG,IAAI,CAACf,SAAS,GAAG,IAAI,CAACrY,KAAK,CAACf,MAAM,CAAA;EAEpD,IAAA,IAAIma,SAAS,EAAE;QACb,IAAI,CAAC7L,KAAK,EAAE,CAAA;QACZ,KAAK,IAAIvO,CAAC,GAAC,CAAC,EAAEA,CAAC,GAAGoa,SAAS,EAAE,EAAEpa,CAAC,EAAE;UAChCuM,OAAO,CAACc,SAAS,CAAC,KAAK,CAAC8B,cAAc,CAAC,GAAG,EAAEV,KAAwC,CAAC,CAAC,CAAA;EACxF,OAAA;;EAEA;EACAzN,MAAAA,KAAK,CAACqZ,KAAK,CAAC,EAAE,CAAC,CAAC7P,OAAO,CAAC4E,EAAE,IAAI,IAAI,CAACD,cAAc,CAACC,EAAE,CAAC,CAAC,CAAA;EACxD,KAAA;EAEA,IAAA,OAAO7C,OAAO,CAAA;EAChB,GAAA;EACF,CAAA;EAGAnK,KAAK,CAAC+W,WAAW,GAAGA,WAAW;;EC1H/B,MAAMmB,cAAc,GAAG,aAAa,CAAA;;EAEpC;;EAWA;EAEA,MAAM1X,UAAU,SAASD,aAAa,CAAY;IAuChD,OAAO4X,qBAAqBA,CAAEzZ,IAAgC,EAAiG;MAC7J,MAAM;QAAE0B,IAAI;QAAEsT,OAAO;QAAE,GAAGyD,WAAAA;EAAY,KAAC,GAAGzY,IAAI,CAAA;MAC9C,OAAO;EACL,MAAA,GAAGyY,WAAW;EACd/W,MAAAA,IAAI,EAAE5E,QAAQ,CAAC4E,IAAI,CAAC,GAAGA,IAAI,GAAGsT,OAAAA;OAC/B,CAAA;EACH,GAAA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;IAIA5X,WAAWA,CAAE4C,IAAwB,EAAE;EACrC,IAAA,KAAK,CAAC8B,UAAU,CAAC2X,qBAAqB,CAAC;QACrC,GAAI3X,UAAU,CAACwL,QAA8B;QAC7C,GAAGtN,IAAAA;EACL,KAAC,CAAC,CAAC,CAAA;EACL,GAAA;IAES8I,aAAaA,CAAE9I,IAAsD,EAAE;EAC9E,IAAA,KAAK,CAAC8I,aAAa,CAAC9I,IAAgD,CAAC,CAAA;EACvE,GAAA;IAESqN,OAAOA,CAAErN,IAAgC,EAAE;MAClD,MAAM;QAAE0B,IAAI;QAAEsT,OAAO;QAAEG,MAAM;QAAE,GAAGsD,WAAAA;EAAY,KAAC,GAAG;QAChD,GAAG3W,UAAU,CAACwL,QAAQ;QACtB,GAAGtN,IAAAA;OACJ,CAAA;EAED,IAAA,MAAM0Z,aAAa,GAAG7b,MAAM,CAACoC,MAAM,CAAC,EAAE,EAAE6B,UAAU,CAAC6X,kBAAkB,EAAE,CAAC,CAAA;EACxE;EACA,IAAA,IAAI3Z,IAAI,CAACY,GAAG,EAAE8Y,aAAa,CAACE,CAAC,CAAChN,IAAI,GAAG5M,IAAI,CAACY,GAAG,CAACiZ,WAAW,EAAE,CAAA;EAC3D,IAAA,IAAI7Z,IAAI,CAACgB,GAAG,EAAE0Y,aAAa,CAACE,CAAC,CAACpB,EAAE,GAAGxY,IAAI,CAACgB,GAAG,CAAC6Y,WAAW,EAAE,CAAA;EACzD,IAAA,IAAI7Z,IAAI,CAACY,GAAG,IAAIZ,IAAI,CAACgB,GAAG,IAAI0Y,aAAa,CAACE,CAAC,CAAChN,IAAI,KAAK8M,aAAa,CAACE,CAAC,CAACpB,EAAE,EACrE;EACAkB,MAAAA,aAAa,CAACI,CAAC,CAAClN,IAAI,GAAG5M,IAAI,CAACY,GAAG,CAACmZ,QAAQ,EAAE,GAAG,CAAC,CAAA;EAC9CL,MAAAA,aAAa,CAACI,CAAC,CAACtB,EAAE,GAAGxY,IAAI,CAACgB,GAAG,CAAC+Y,QAAQ,EAAE,GAAG,CAAC,CAAA;QAE5C,IAAIL,aAAa,CAACI,CAAC,CAAClN,IAAI,KAAK8M,aAAa,CAACI,CAAC,CAACtB,EAAE,EAAE;UAC/CkB,aAAa,CAAChK,CAAC,CAAC9C,IAAI,GAAG5M,IAAI,CAACY,GAAG,CAACoZ,OAAO,EAAE,CAAA;UACzCN,aAAa,CAAChK,CAAC,CAAC8I,EAAE,GAAGxY,IAAI,CAACgB,GAAG,CAACgZ,OAAO,EAAE,CAAA;EACzC,OAAA;EACF,KAAA;MACAnc,MAAM,CAACoC,MAAM,CAACyZ,aAAa,EAAE,IAAI,CAACvE,MAAM,EAAEA,MAAM,CAAC,CAAA;MAEjD,KAAK,CAAC9H,OAAO,CAAC;EACZ,MAAA,GAAGoL,WAAW;QACd/W,IAAI,EAAE5E,QAAQ,CAAC4E,IAAI,CAAC,GAAGA,IAAI,GAAGsT,OAAO;EACrCG,MAAAA,MAAM,EAAEuE,aAAAA;EACV,KAAC,CAAC,CAAA;EACJ,GAAA;IAESxK,UAAUA,CAAEvB,KAAkB,EAAW;EAChD,IAAA,MAAMsM,IAAI,GAAG,IAAI,CAACA,IAAI,CAAA;MAEtB,OAAO,KAAK,CAAC/K,UAAU,CAACvB,KAAK,CAAC,KAC3B,CAAC,IAAI,CAAC3C,UAAU,IACf,IAAI,CAACkP,WAAW,CAAC,IAAI,CAACha,KAAK,CAAC,IAAI+Z,IAAI,IAAI,IAAI,KAC3C,IAAI,CAACrZ,GAAG,IAAI,IAAI,IAAI,IAAI,CAACA,GAAG,IAAIqZ,IAAI,CAAC,KACrC,IAAI,CAACjZ,GAAG,IAAI,IAAI,IAAIiZ,IAAI,IAAI,IAAI,CAACjZ,GAAG,CAAC,CAAC,CAAA;EAC7C,GAAA;;EAEA;IACAkZ,WAAWA,CAAEnd,GAAW,EAAW;MACjC,OAAO,IAAI,CAAC8Q,MAAM,CAAC,IAAI,CAACD,KAAK,CAAC7Q,GAAG,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAACwO,OAAO,CAACxO,GAAG,CAAC,IAAI,CAAC,CAAA;EACnE,GAAA;;EAEA;IACA,IAAIkd,IAAIA,GAAe;MACrB,OAAO,IAAI,CAAC/Q,UAAU,CAAA;EACxB,GAAA;IACA,IAAI+Q,IAAIA,CAAEA,IAAe,EAAE;MACzB,IAAI,CAAC/Q,UAAU,GAAG+Q,IAAI,CAAA;EACxB,GAAA;IAEA,IAAa/Q,UAAUA,GAAe;MACpC,OAAO,IAAI,CAAC8B,UAAU,GAAG,KAAK,CAAC9B,UAAU,GAAG,IAAI,CAAA;EAClD,GAAA;IACA,IAAaA,UAAUA,CAAEhJ,KAAgB,EAAE;MACzC,KAAK,CAACgJ,UAAU,GAAGhJ,KAAK,CAAA;EAC1B,GAAA;IAES0I,UAAUA,CAAElH,IAAS,EAAW;MACvC,OAAOA,IAAI,KAAKrC,IAAI,IAAI,KAAK,CAACuJ,UAAU,CAAClH,IAAI,CAAC,CAAA;EAChD,GAAA;IAESgJ,gBAAgBA,CAAE1K,IAAgC,EAAW;MACpE,OAAO,KAAK,CAAC0K,gBAAgB,CAAC5I,UAAU,CAAC2X,qBAAqB,CAACzZ,IAAI,CAAC,CAAC,CAAA;EACvE,GAAA;EACF,CAAA;EAxIM8B,UAAU,CACP6X,kBAAkB,GAA8C,OAAO;EAC5EjK,EAAAA,CAAC,EAAE;EACDhO,IAAAA,IAAI,EAAE2W,WAAW;EACjBzL,IAAAA,IAAI,EAAE,CAAC;EACP4L,IAAAA,EAAE,EAAE,EAAE;EACND,IAAAA,SAAS,EAAE,CAAA;KACZ;EACDuB,EAAAA,CAAC,EAAE;EACDpY,IAAAA,IAAI,EAAE2W,WAAW;EACjBzL,IAAAA,IAAI,EAAE,CAAC;EACP4L,IAAAA,EAAE,EAAE,EAAE;EACND,IAAAA,SAAS,EAAE,CAAA;KACZ;EACDqB,EAAAA,CAAC,EAAE;EACDlY,IAAAA,IAAI,EAAE2W,WAAW;EACjBzL,IAAAA,IAAI,EAAE,IAAI;EACV4L,IAAAA,EAAE,EAAE,IAAA;EACN,GAAA;EACF,CAAC,CAAC,CAAA;EAnBE1W,UAAU,CAoBPwL,QAAQ,GAAG;IAChB,GAAGzL,aAAa,CAACyL,QAAQ;EACzB5L,EAAAA,IAAI,EAAErC,IAAI;EACV2V,EAAAA,OAAO,EAAEwE,cAAc;EACvB3L,EAAAA,MAAM,EAAEA,CAACoM,IAAe,EAAEtS,MAAc,KAAa;EACnD,IAAA,IAAI,CAACsS,IAAI,EAAE,OAAO,EAAE,CAAA;EAEpB,IAAA,MAAME,GAAG,GAAGnd,MAAM,CAACid,IAAI,CAACD,OAAO,EAAE,CAAC,CAACrB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;EACnD,IAAA,MAAMyB,KAAK,GAAGpd,MAAM,CAACid,IAAI,CAACF,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACpB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;EAC1D,IAAA,MAAM0B,IAAI,GAAGJ,IAAI,CAACJ,WAAW,EAAE,CAAA;MAE/B,OAAO,CAACM,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,CAACnJ,IAAI,CAAC,GAAG,CAAC,CAAA;KACpC;EACDtD,EAAAA,KAAK,EAAEA,CAAC7Q,GAAW,EAAE4K,MAAc,KAAgB;EACjD,IAAA,MAAM,CAACwS,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,GAAGtd,GAAG,CAACwc,KAAK,CAAC,GAAG,CAAC,CAACtI,GAAG,CAAClP,MAAM,CAAC,CAAA;MACrD,OAAO,IAAI1C,IAAI,CAACgb,IAAI,EAAED,KAAK,GAAG,CAAC,EAAED,GAAG,CAAC,CAAA;EACvC,GAAA;EACF,CAAC,CAAA;EAsGH7Y,KAAK,CAACQ,UAAU,GAAGA,UAAU;;ECnJ7B;EAEA,MAAMG,aAAa,SAAoBC,MAAM,CAAQ;IAsDnD9E,WAAWA,CAAE4C,IAA2B,EAAE;EACxC,IAAA,KAAK,CAAC;QACJ,GAAGiC,aAAa,CAACqL,QAAQ;QACzB,GAAGtN,IAAAA;EACL,KAAC,CAAC,CAAA;MAEF,IAAI,CAACsa,WAAW,GAAG9X,SAAS,CAAA;EAC9B,GAAA;IAESsG,aAAaA,CAAE9I,IAAmC,EAAE;EAC3D,IAAA,KAAK,CAAC8I,aAAa,CAAC9I,IAAI,CAAC,CAAA;EAC3B,GAAA;IAESqN,OAAOA,CAAErN,IAAmC,EAAE;EACrD,IAAA,KAAK,CAACqN,OAAO,CAACrN,IAAI,CAAC,CAAA;MAEnB,IAAI,MAAM,IAAIA,IAAI,EAAE;QAClB,IAAI,CAACua,UAAU,GAAG/X,SAAS,CAAA;EAC3B;EACA,MAAA,IAAI,CAACgY,aAAa,GAAGhd,KAAK,CAACC,OAAO,CAACuC,IAAI,CAAC0B,IAAI,CAAC,GAC3C1B,IAAI,CAAC0B,IAAI,CAACuP,GAAG,CAAC6I,CAAC,IAAI;UACjB,MAAM;YAAErE,MAAM;YAAE,GAAGpB,QAAAA;EAAS,SAAC,GAAG9R,aAAa,CAACuX,CAAC,CAAsD,CAAA;UAErG,MAAMnS,MAAM,GAAG/E,UAAU,CAAC;YACxBwM,SAAS,EAAE,IAAI,CAACqL,UAAU;YAC1B7K,KAAK,EAAE,IAAI,CAAC8K,MAAM;YAClBtK,WAAW,EAAE,IAAI,CAACuK,YAAY;YAC9B,GAAGtG,QAAAA;EACL,SAAC,CAAC,CAAA;EAEF,QAAA,IAAIoB,MAAM,EAAE,IAAI,CAAC8E,UAAU,GAAG5S,MAAM,CAAA;EAEpC,QAAA,OAAOA,MAAM,CAAA;SACd,CAAC,GACF,EAAE,CAAA;;EAEJ;EACF,KAAA;EACF,GAAA;EAES0G,EAAAA,cAAcA,CAAEC,EAAU,EAAEX,KAA+B,EAAoB;EAAA,IAAA,IAAnDA,KAA+B,KAAA,KAAA,CAAA,EAAA;QAA/BA,KAA+B,GAAC,EAAE,CAAA;EAAA,KAAA;MACrE,MAAMlC,OAAO,GAAG,IAAI,CAACmP,cAAc,CAACtM,EAAE,EAAEX,KAAK,CAAC,CAAA;MAE9C,IAAI,IAAI,CAAC2M,WAAW,EAAE;EACpB7O,MAAAA,OAAO,CAACc,SAAS,CAAC,IAAI,CAAC+N,WAAW,CAAC/L,WAAW,CAACD,EAAE,EAAE,IAAI,CAACgG,gBAAgB,CAAC3G,KAAK,CAAC,CAAC,CAAC,CAAA;EACnF,KAAA;EAEA,IAAA,OAAOlC,OAAO,CAAA;EAChB,GAAA;EAEAmP,EAAAA,cAAcA,CAAE3L,QAAgB,EAAKtB,KAA+B,EAAKvM,IAAmC,EAAsB;EAAA,IAAA,IAAlH6N,QAAgB,KAAA,KAAA,CAAA,EAAA;EAAhBA,MAAAA,QAAgB,GAAC,EAAE,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEtB,KAA+B,KAAA,KAAA,CAAA,EAAA;QAA/BA,KAA+B,GAAC,EAAE,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEvM,IAAmC,KAAA,KAAA,CAAA,EAAA;EAAnCA,MAAAA,IAAmC,GAAG,EAAE,CAAA;EAAA,KAAA;MAC/G,MAAMyZ,mBAAmB,GAAGlN,KAAK,CAACvM,IAAI,IAAIuM,KAAK,CAAC4B,gBAAgB,IAAI,IAAI,GACtE5B,KAAK,CAAC4B,gBAAgB,CAAC1H,MAAM,GAC7B,IAAI,CAAC3H,KAAK,CAAA;EACZ,IAAA,MAAM4a,UAAU,GAAG,IAAI,CAAC7R,aAAa,CAAA;EACrC,IAAA,MAAM8R,WAAW,GAAGpN,KAAK,CAACvM,IAAI,IAAIuM,KAAK,CAAC4B,gBAAgB,IAAI,IAAI,GAC9D5B,KAAK,CAAC4B,gBAAgB,CAACxH,cAAc,GACrC+S,UAAU,CAAA;MACZ,MAAME,SAAS,GAAGF,UAAU,CAAC3a,KAAK,CAAC4a,WAAW,CAAC5b,MAAM,CAAC,CAAA;EACtD,IAAA,MAAM8b,QAAQ,GAAG,IAAI,CAACX,WAAW,CAAA;EACjC,IAAA,MAAM7O,OAAO,GAAG,IAAIQ,aAAa,EAAE,CAAA;EAEnC,IAAA,MAAMiP,aAAa,GAAGD,QAAQ,IAARA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAQ,CAAE9T,KAAK,CAAA;;EAErC;MACA,IAAI,CAACmT,WAAW,GAAG,IAAI,CAACa,UAAU,CAAClM,QAAQ,EAAE;QAAE,GAAGtB,KAAAA;OAAO,EAAEvM,IAAI,CAAC,CAAA;;EAEhE;MACA,IAAI,IAAI,CAACkZ,WAAW,EAAE;EACpB,MAAA,IAAI,IAAI,CAACA,WAAW,KAAKW,QAAQ,EAAE;EACjC;EACA,QAAA,IAAI,CAACX,WAAW,CAAC7M,KAAK,EAAE,CAAA;EAExB,QAAA,IAAIsN,WAAW,EAAE;EACf,UAAA,IAAI,CAACT,WAAW,CAACtN,MAAM,CAAC+N,WAAW,EAAE;EAAEnP,YAAAA,GAAG,EAAE,IAAA;EAAK,WAAC,CAAC,CAAA;EACnDH,UAAAA,OAAO,CAACY,SAAS,GAAG,IAAI,CAACiO,WAAW,CAACpa,KAAK,CAACf,MAAM,GAAG0b,mBAAmB,CAAC1b,MAAM,CAAA;EAChF,SAAA;EAEA,QAAA,IAAI6b,SAAS,EAAE;YACbvP,OAAO,CAACY,SAAS,IAAI,IAAI,CAACiO,WAAW,CAACtN,MAAM,CAACgO,SAAS,EAAE;EAAEpP,YAAAA,GAAG,EAAE,IAAI;EAAExK,YAAAA,IAAI,EAAE,IAAA;aAAM,CAAC,CAACiL,SAAS,CAAA;EAC9F,SAAA;SACD,MAAM,IAAI6O,aAAa,EAAE;EACxB;EACA;EACA,QAAA,IAAI,CAACZ,WAAW,CAACnT,KAAK,GAAG+T,aAAa,CAAA;EACxC,OAAA;EACF,KAAA;EAEA,IAAA,OAAOzP,OAAO,CAAA;EAChB,GAAA;EAESwB,EAAAA,kBAAkBA,GAAmB;EAC5C,IAAA,MAAMxB,OAAO,GAAG,IAAI,CAACmP,cAAc,EAAE,CAAA;MAErC,IAAI,IAAI,CAACN,WAAW,EAAE;QACpB7O,OAAO,CAACc,SAAS,CAAC,IAAI,CAAC+N,WAAW,CAACrN,kBAAkB,EAAE,CAAC,CAAA;EAC1D,KAAA;EAEA,IAAA,OAAOxB,OAAO,CAAA;EAChB,GAAA;EAES6D,EAAAA,YAAYA,GAAmB;EACtC,IAAA,MAAM7D,OAAO,GAAG,IAAI,CAACmP,cAAc,EAAE,CAAA;MAErC,IAAI,IAAI,CAACN,WAAW,EAAE;QACpB7O,OAAO,CAACc,SAAS,CAAC,IAAI,CAAC+N,WAAW,CAAChL,YAAY,EAAE,CAAC,CAAA;EACpD,KAAA;EAEA,IAAA,OAAO7D,OAAO,CAAA;EAChB,GAAA;IAES2C,UAAUA,CAAEhN,IAAmC,EAAiB;EACvE,IAAA,MAAMqK,OAAO,GAAG,IAAIQ,aAAa,EAAE,CAAA;EACnC,IAAA,IAAI7K,IAAI,EAAEqK,OAAO,CAACc,SAAS,CAAC,IAAI,CAACqO,cAAc,CAAC,EAAE,EAAE,EAAE,EAAExZ,IAAI,CAAC,CAAC,CAAA;MAE9D,OAAOqK,OAAO,CAACc,SAAS,CAAC,IAAI,CAAC+N,WAAW,GACvC,IAAI,CAACA,WAAW,CAAClM,UAAU,CAAChN,IAAI,CAAC,GACjC,KAAK,CAACgN,UAAU,CAAChN,IAAI,CAAC,CAAC,CAAA;EAC3B,GAAA;IAEAkT,gBAAgBA,CAAE3G,KAA+B,EAAe;MAAA,IAAA6G,qBAAA,EAAA4G,sBAAA,CAAA;MAC9D,OAAO;EACL,MAAA,GAAGzN,KAAK;QACR4B,gBAAgB,EACd,CAAAiF,CAAAA,qBAAA,GAAC7G,KAAK,CAAC4B,gBAAgB,KAAvBiF,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAmD6G,cAAc,MAAK,IAAI,CAACf,WAAW,KAAAc,CAAAA,sBAAA,GACrFzN,KAAK,CAAC4B,gBAAgB,KAAvB6L,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAmDd,WAAW,CAC9D3M,IAAAA,KAAK,CAAC4B,gBAAAA;OACT,CAAA;EACH,GAAA;EAEA4L,EAAAA,UAAUA,CAAClM,QAAgB,EAAEtB,KAA+B,EAAKvM,IAAmC,EAAyB;EAAA,IAAA,IAAhGuM,KAA+B,KAAA,KAAA,CAAA,EAAA;QAA/BA,KAA+B,GAAC,EAAE,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEvM,IAAmC,KAAA,KAAA,CAAA,EAAA;EAAnCA,MAAAA,IAAmC,GAAC,EAAE,CAAA;EAAA,KAAA;MACrG,OAAO,IAAI,CAACka,QAAQ,CAACrM,QAAQ,EAAE,IAAI,EAAEtB,KAAK,EAAEvM,IAAI,CAAC,CAAA;EACnD,GAAA;IAES8N,UAAUA,CAAEvB,KAA+B,EAAW;MAC7D,OAAO,KAAK,CAACuB,UAAU,CAACvB,KAAK,CAAC,KAC5B,CAAC,IAAI,CAAC2M,WAAW,IAAI,IAAI,CAACA,WAAW,CAACpL,UAAU,CAAC,IAAI,CAACoF,gBAAgB,CAAC3G,KAAK,CAAC,CAAC,CAC/E,CAAA;EACH,GAAA;EAES6B,EAAAA,SAASA,CAAEzS,GAAW,EAAE4Q,KAA+B,EAA8B;EAAA,IAAA,IAA7DA,KAA+B,KAAA,KAAA,CAAA,EAAA;QAA/BA,KAA+B,GAAC,EAAE,CAAA;EAAA,KAAA;EACjE,IAAA,IAAI,CAAC8E,CAAC,EAAEhH,OAAO,CAAC,GAAG,KAAK,CAAC+D,SAAS,CAACzS,GAAG,EAAE4Q,KAAK,CAAC,CAAA;MAE9C,IAAI,IAAI,CAAC2M,WAAW,EAAE;EACpB,MAAA,IAAIiB,cAAc,CAAA;EACjB,MAAA,CAAC9I,CAAC,EAAE8I,cAAc,CAAC,GAAG,KAAK,CAAC/L,SAAS,CAACiD,CAAC,EAAE,IAAI,CAAC6B,gBAAgB,CAAC3G,KAAK,CAAC,CAAC,CAAA;EACvElC,MAAAA,OAAO,GAAGA,OAAO,CAACc,SAAS,CAACgP,cAAc,CAAC,CAAA;EAC7C,KAAA;EAEA,IAAA,OAAO,CAAC9I,CAAC,EAAEhH,OAAO,CAAC,CAAA;EACrB,GAAA;EAESiD,EAAAA,aAAaA,CAAE3R,GAAW,EAAE4Q,KAA+B,EAA8B;EAAA,IAAA,IAA7DA,KAA+B,KAAA,KAAA,CAAA,EAAA;QAA/BA,KAA+B,GAAC,EAAE,CAAA;EAAA,KAAA;EACrE,IAAA,IAAI,CAAC8E,CAAC,EAAEhH,OAAO,CAAC,GAAG,KAAK,CAACiD,aAAa,CAAC3R,GAAG,EAAE4Q,KAAK,CAAC,CAAA;MAElD,IAAI,IAAI,CAAC2M,WAAW,EAAE;EACpB,MAAA,IAAIiB,cAAc,CAAA;EACjB,MAAA,CAAC9I,CAAC,EAAE8I,cAAc,CAAC,GAAG,KAAK,CAAC7M,aAAa,CAAC+D,CAAC,EAAE,IAAI,CAAC6B,gBAAgB,CAAC3G,KAAK,CAAC,CAAC,CAAA;EAC3ElC,MAAAA,OAAO,GAAGA,OAAO,CAACc,SAAS,CAACgP,cAAc,CAAC,CAAA;EAC7C,KAAA;EAEA,IAAA,OAAO,CAAC9I,CAAC,EAAEhH,OAAO,CAAC,CAAA;EACrB,GAAA;EAESgC,EAAAA,KAAKA,GAAI;EAAA,IAAA,IAAA+N,iBAAA,CAAA;MAChB,CAAAA,iBAAA,OAAI,CAAClB,WAAW,aAAhBkB,iBAAA,CAAkB/N,KAAK,EAAE,CAAA;EACzB,IAAA,IAAI,CAAC+M,aAAa,CAAC9Q,OAAO,CAACoQ,CAAC,IAAIA,CAAC,CAACrM,KAAK,EAAE,CAAC,CAAA;EAC5C,GAAA;IAEA,IAAavN,KAAKA,GAAY;MAC5B,OAAO,IAAI,CAACqa,UAAU,GAAG,IAAI,CAACA,UAAU,CAACra,KAAK,GAC5C,IAAI,CAACoa,WAAW,GAAG,IAAI,CAACA,WAAW,CAACpa,KAAK,GACzC,EAAE,CAAA;EACN,GAAA;IAEA,IAAaA,KAAKA,CAAEA,KAAa,EAAE;MACjC,IAAI,IAAI,CAACqa,UAAU,EAAE;EACnB,MAAA,IAAI,CAACA,UAAU,CAACra,KAAK,GAAGA,KAAK,CAAA;EAC7B,MAAA,IAAI,CAACoa,WAAW,GAAG,IAAI,CAACC,UAAU,CAAA;QAClC,IAAI,CAACK,cAAc,EAAE,CAAA;EACvB,KAAC,MACI,KAAK,CAAC1a,KAAK,GAAGA,KAAK,CAAA;EAC1B,GAAA;IAEA,IAAa6I,aAAaA,GAAY;MACpC,OAAO,IAAI,CAACwR,UAAU,GAAG,IAAI,CAACA,UAAU,CAACxR,aAAa,GACpD,IAAI,CAACuR,WAAW,GAAG,IAAI,CAACA,WAAW,CAACvR,aAAa,GACjD,EAAE,CAAA;EACN,GAAA;IAEA,IAAaA,aAAaA,CAAEA,aAAqB,EAAE;MACjD,IAAI,IAAI,CAACwR,UAAU,EAAE;EACnB,MAAA,IAAI,CAACA,UAAU,CAACxR,aAAa,GAAGA,aAAa,CAAA;EAC7C,MAAA,IAAI,CAACuR,WAAW,GAAG,IAAI,CAACC,UAAU,CAAA;QAClC,IAAI,CAACK,cAAc,EAAE,CAAA;EACvB,KAAC,MACI,KAAK,CAAC7R,aAAa,GAAGA,aAAa,CAAA;EAC1C,GAAA;IAEA,IAAaG,UAAUA,GAAW;MAChC,OAAO,IAAI,CAACqR,UAAU,GAAG,IAAI,CAACA,UAAU,CAACrR,UAAU,GACjD,IAAI,CAACoR,WAAW,GAAG,IAAI,CAACA,WAAW,CAACpR,UAAU,GAC9C,EAAE,CAAA;EACN,GAAA;IAEA,IAAaA,UAAUA,CAAEA,UAAiB,EAAE;MAC1C,IAAI,IAAI,CAACqR,UAAU,EAAE;EACnB,MAAA,IAAI,CAACA,UAAU,CAACrR,UAAU,GAAGA,UAAU,CAAA;EACvC,MAAA,IAAI,CAACoR,WAAW,GAAG,IAAI,CAACC,UAAU,CAAA;QAClC,IAAI,CAACK,cAAc,EAAE,CAAA;EACrB,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAI7R,aAAa,GAAG/L,MAAM,CAACkM,UAAU,CAAC,CAAA;;EAEtC;MACA,IAAI,IAAI,CAACoR,WAAW,EAAE;EACpB,MAAA,IAAI,CAACA,WAAW,CAACpR,UAAU,GAAGA,UAAU,CAAA;EACxCH,MAAAA,aAAa,GAAG,IAAI,CAACuR,WAAW,CAACvR,aAAa,CAAA;EAChD,KAAA;MACA,IAAI,CAACA,aAAa,GAAGA,aAAa,CAAA;EACpC,GAAA;IAEA,IAAaM,YAAYA,GAAY;MACnC,OAAO,IAAI,CAACiR,WAAW,GAAG,IAAI,CAACA,WAAW,CAACjR,YAAY,GAAG,EAAE,CAAA;EAC9D,GAAA;IAEA,IAAa2B,UAAUA,GAAa;EAAA,IAAA,IAAAyQ,kBAAA,CAAA;MAClC,OAAOhP,OAAO,CAAAgP,CAAAA,kBAAA,GAAC,IAAI,CAACnB,WAAW,KAAhBmB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,kBAAA,CAAkBzQ,UAAU,CAAC,CAAA;EAC9C,GAAA;IAEA,IAAa+C,QAAQA,GAAa;EAAA,IAAA,IAAA2N,kBAAA,CAAA;MAChC,OAAOjP,OAAO,CAAAiP,CAAAA,kBAAA,GAAC,IAAI,CAACpB,WAAW,KAAhBoB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,kBAAA,CAAkB3N,QAAQ,CAAC,CAAA;EAC5C,GAAA;EAES8B,EAAAA,MAAMA,CAAE5B,OAAgB,EAAEC,KAAc,EAAiB;EAChE,IAAA,MAAMzC,OAAsB,GAAG,IAAIQ,aAAa,EAAE,CAAA;MAElD,IAAI,IAAI,CAACqO,WAAW,EAAE;EACpB7O,MAAAA,OAAO,CAACc,SAAS,CAAC,IAAI,CAAC+N,WAAW,CAACzK,MAAM,CAAC5B,OAAO,EAAEC,KAAK,CAAC,CAAA;EACvD;EAAA,OACC3B,SAAS,CAAC,IAAI,CAACqO,cAAc,EAAE,CAAC,CAAA;EACrC,KAAA;EAEA,IAAA,OAAOnP,OAAO,CAAA;EAChB,GAAA;IAEA,IAAatE,KAAKA,GAAwB;EAAA,IAAA,IAAAwU,kBAAA,CAAA;MACxC,OAAO;QACL,GAAG,KAAK,CAACxU,KAAK;QACdY,cAAc,EAAE,IAAI,CAACkB,aAAa;EAClCuR,MAAAA,aAAa,EAAE,IAAI,CAACA,aAAa,CAACvJ,GAAG,CAAC6I,CAAC,IAAIA,CAAC,CAAC3S,KAAK,CAAC;QACnDkU,cAAc,EAAE,IAAI,CAACf,WAAW;QAChCA,WAAW,EAAA,CAAAqB,kBAAA,GAAE,IAAI,CAACrB,WAAW,KAAA,IAAA,GAAA,KAAA,CAAA,GAAhBqB,kBAAA,CAAkBxU,KAAAA;OAChC,CAAA;EACH,GAAA;IAEA,IAAaA,KAAKA,CAAEA,KAAkB,EAAE;MACtC,MAAM;QAAEqT,aAAa;QAAEa,cAAc;QAAEf,WAAW;QAAE,GAAGnE,WAAAA;EAAY,KAAC,GAAGhP,KAA8B,CAAA;MACrG,IAAIqT,aAAa,EAAE,IAAI,CAACA,aAAa,CAAC9Q,OAAO,CAAC,CAACoQ,CAAC,EAAE8B,EAAE,KAAK9B,CAAC,CAAC3S,KAAK,GAAGqT,aAAa,CAACoB,EAAE,CAAC,CAAC,CAAA;MACrF,IAAIP,cAAc,IAAI,IAAI,EAAE;QAC1B,IAAI,CAACf,WAAW,GAAGe,cAAc,CAAA;EACjC,MAAA,IAAI,CAACf,WAAW,CAACnT,KAAK,GAAGmT,WAAW,CAAA;EACtC,KAAA;MACA,KAAK,CAACnT,KAAK,GAAGgP,WAAW,CAAA;EAC3B,GAAA;EAESrI,EAAAA,YAAYA,CAAEG,OAAgB,EAAEC,KAAc,EAAEP,KAAoB,EAAU;EACrF,IAAA,OAAO,IAAI,CAAC2M,WAAW,GACrB,IAAI,CAACA,WAAW,CAACxM,YAAY,CAACG,OAAO,EAAEC,KAAK,EAAEP,KAAK,CAAC,GACpD,EAAE,CAAA;EACN,GAAA;EAESQ,EAAAA,WAAWA,CAAEF,OAAgB,EAAEC,KAAc,EAAe;MACnE,OAAO,IAAI,CAACoM,WAAW,GACrB,IAAI,CAACA,WAAW,CAACnM,WAAW,CAACF,OAAO,EAAEC,KAAK,CAAC,GAC5C,KAAK,CAACC,WAAW,CAACF,OAAO,EAAEC,KAAK,CAAC,CAAA;EACrC,GAAA;EAESrC,EAAAA,QAAQA,GAAI;MACnB,IAAI,IAAI,CAACyO,WAAW,EAAE,IAAI,CAACA,WAAW,CAACzO,QAAQ,EAAE,CAAA;MACjD,KAAK,CAACA,QAAQ,EAAE,CAAA;EAClB,GAAA;EAESX,EAAAA,eAAeA,CAACzK,SAAiB,EAAE/B,SAAqB,EAAU;MACzE,OAAO,IAAI,CAAC4b,WAAW,GACrB,IAAI,CAACA,WAAW,CAACpP,eAAe,CAACzK,SAAS,EAAE/B,SAAS,CAAC,GACtD,KAAK,CAACwM,eAAe,CAACzK,SAAS,EAAE/B,SAAS,CAAC,CAAA;EAC/C,GAAA;IAEA,IAAa0Q,SAASA,GAAmC;EACvD,IAAA,OAAO,IAAI,CAACkL,WAAW,GACrB,IAAI,CAACA,WAAW,CAAClL,SAAS,GAC1B,IAAI,CAACqL,UAAU,CAAA;EACnB,GAAA;IAEA,IAAarL,SAASA,CAAEA,SAAwC,EAAE;MAChE,IAAI,CAACqL,UAAU,GAAGrL,SAAS,CAAA;EAC7B,GAAA;IAEA,IAAaQ,KAAKA,GAA+C;EAC/D,IAAA,OAAO,IAAI,CAAC0K,WAAW,GACrB,IAAI,CAACA,WAAW,CAAC1K,KAAK,GACtB,IAAI,CAAC8K,MAAM,CAAA;EACf,GAAA;IAEA,IAAa9K,KAAKA,CAAEA,KAAgD,EAAE;MACpE,IAAI,CAAC8K,MAAM,GAAG9K,KAAK,CAAA;EACrB,GAAA;IAEA,IAAaQ,WAAWA,GAAyB;EAC/C,IAAA,OAAO,IAAI,CAACkK,WAAW,GACrB,IAAI,CAACA,WAAW,CAAClK,WAAW,GAC5B,IAAI,CAACuK,YAAY,CAAA;EACrB,GAAA;IAEA,IAAavK,WAAWA,CAAEA,WAAgC,EAAE;MAC1D,IAAI,CAACuK,YAAY,GAAGvK,WAAW,CAAA;EACjC,GAAA;IAEA,IAAazB,OAAOA,GAAiC;EACnD,IAAA,OAAO,IAAI,CAAC2L,WAAW,GACrB,IAAI,CAACA,WAAW,CAAC3L,OAAO,GACxB,IAAI,CAACkN,QAAQ,CAAA;EACjB,GAAA;IAEA,IAAalN,OAAOA,CAAEA,OAAoC,EAAE;MAC1D,IAAI,CAACkN,QAAQ,GAAGlN,OAAO,CAAA;EACzB,GAAA;IAES/F,UAAUA,CAAElH,IAAS,EAAW;EACvC,IAAA,OAAOlE,KAAK,CAACC,OAAO,CAACiE,IAAI,CAAC,GACxB,IAAI,CAAC8Y,aAAa,CAACnE,KAAK,CAAC,CAACyD,CAAC,EAAE8B,EAAE,KAAK;EAClC,MAAA,IAAI,CAACla,IAAI,CAACka,EAAE,CAAC,EAAE,OAAA;QAEf,MAAM;EAAEla,QAAAA,IAAI,EAAEoa,OAAO;UAAE,GAAGvR,QAAAA;EAAS,OAAC,GAAG7I,IAAI,CAACka,EAAE,CAAC,CAAA;EAC/C,MAAA,OAAO/c,cAAc,CAACib,CAAC,EAAEvP,QAAQ,CAAC,IAAIuP,CAAC,CAAClR,UAAU,CAACkT,OAAO,CAAC,CAAA;EAC7D,KAAC,CAAC,GAAG,KAAK,CAAClT,UAAU,CAAClH,IAAI,CAAC,CAAA;EAC/B,GAAA;IAES0H,gBAAgBA,CAAElJ,KAAU,EAAW;EAAA,IAAA,IAAA6b,kBAAA,CAAA;EAC9C,IAAA,OAAOtP,OAAO,CAAA,CAAAsP,kBAAA,GAAC,IAAI,CAACzB,WAAW,KAAhByB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,kBAAA,CAAkB3S,gBAAgB,CAAClJ,KAAK,CAAC,CAAC,CAAA;EAC3D,GAAA;EACF,CAAA;EA3YE;EAEA;EAEA;EAEA;EARI+B,aAAa,CAgBVqL,QAAQ,GAA6D;IAC1E,GAAGpL,MAAM,CAACoL,QAAQ;IAClBgO,QAAQ,EAAEA,CAACrM,QAAQ,EAAEtH,MAAM,EAAEgG,KAAK,EAAEvM,IAAI,KAAK;EAC3C,IAAA,IAAI,CAACuG,MAAM,CAAC6S,aAAa,CAACrb,MAAM,EAAE,OAAA;EAElC,IAAA,MAAM2b,UAAU,GAAGnT,MAAM,CAACsB,aAAa,CAAA;;EAEvC;EACA,IAAA,MAAM+S,MAAM,GAAGrU,MAAM,CAAC6S,aAAa,CAACvJ,GAAG,CAAC,CAAC6I,CAAC,EAAElI,KAAK,KAAK;EACpD,MAAA,MAAMqK,SAAS,GAAGtU,MAAM,CAAC2S,WAAW,KAAKR,CAAC,CAAA;QAC1C,MAAMoC,aAAa,GAAGD,SAAS,GAAGnC,CAAC,CAACzQ,YAAY,CAAClK,MAAM,GAAG2a,CAAC,CAAC5O,eAAe,CAAC4O,CAAC,CAACzQ,YAAY,CAAClK,MAAM,EAAEhB,SAAS,CAACG,UAAU,CAAC,CAAA;EAExH,MAAA,IAAIwb,CAAC,CAAC7Q,aAAa,KAAK6R,UAAU,EAAE;UAClChB,CAAC,CAACrM,KAAK,EAAE,CAAA;EACTqM,QAAAA,CAAC,CAAC9M,MAAM,CAAC8N,UAAU,EAAE;EAAElP,UAAAA,GAAG,EAAE,IAAA;EAAK,SAAC,CAAC,CAAA;EACrC,OAAC,MAAM,IAAI,CAACqQ,SAAS,EAAE;EACrBnC,QAAAA,CAAC,CAACjK,MAAM,CAACqM,aAAa,CAAC,CAAA;EACzB,OAAA;QACApC,CAAC,CAAC9M,MAAM,CAACiC,QAAQ,EAAEtH,MAAM,CAAC2M,gBAAgB,CAAC3G,KAAK,CAAC,CAAC,CAAA;EAClDmM,MAAAA,CAAC,CAAC1L,UAAU,CAAChN,IAAI,CAAC,CAAA;QAElB,OAAO;UACLwQ,KAAK;EACLuK,QAAAA,MAAM,EAAErC,CAAC,CAAC7Q,aAAa,CAAC9J,MAAM;UAC9B6O,mBAAmB,EAAE8L,CAAC,CAAC9L,mBAAmB,CACxC,CAAC,EACDrN,IAAI,CAACK,GAAG,CAACkb,aAAa,EAAEpC,CAAC,CAAC5O,eAAe,CAAC4O,CAAC,CAACzQ,YAAY,CAAClK,MAAM,EAAEhB,SAAS,CAACG,UAAU,CAAC,CACxF,CAAA;SACD,CAAA;EACH,KAAC,CAAC,CAAA;;EAEF;MACA0d,MAAM,CAACxG,IAAI,CAAC,CAAC4G,EAAE,EAAEC,EAAE,KAAKA,EAAE,CAACF,MAAM,GAAGC,EAAE,CAACD,MAAM,IAAIE,EAAE,CAACrO,mBAAmB,GAAGoO,EAAE,CAACpO,mBAAmB,CAAC,CAAA;MAEjG,OAAOrG,MAAM,CAAC6S,aAAa,CAACwB,MAAM,CAAC,CAAC,CAAC,CAACpK,KAAK,CAAC,CAAA;EAC9C,GAAA;EACF,CAAC,CAAA;EA4VHtQ,KAAK,CAACW,aAAa,GAAGA,aAAa;;EC/ZnC;EAEA,MAAMqa,UAAU,SAASza,aAAa,CAAC;IAUrCzE,WAAWA,CAAE4C,IAAwB,EAAE;EACrC,IAAA,KAAK,CAAC;QACJ,GAAGsc,UAAU,CAAChP,QAAQ;QACtB,GAAGtN,IAAAA;OACoB,CAAC,CAAC;EAC7B,GAAA;IAES8I,aAAaA,CAAE9I,IAAgC,EAAE;EACxD,IAAA,KAAK,CAAC8I,aAAa,CAAC9I,IAAI,CAAC,CAAA;EAC3B,GAAA;IAESqN,OAAOA,CAAErN,IAAgC,EAAE;MAClD,MAAM;EAAEuc,MAAAA,IAAI,EAAEC,KAAK;QAAE,GAAGC,KAAAA;EAAgC,KAAC,GAAGzc,IAAI,CAAA;EAEhE,IAAA,IAAIwc,KAAK,EAAE;QACT,MAAME,OAAO,GAAGF,KAAK,CAACvL,GAAG,CAAClM,CAAC,IAAIA,CAAC,CAAC5F,MAAM,CAAC,CAAA;QACxC,MAAMwd,cAAc,GAAGhc,IAAI,CAACC,GAAG,CAAC,GAAG8b,OAAO,CAAC,CAAA;QAC3C,MAAME,cAAc,GAAGjc,IAAI,CAACK,GAAG,CAAC,GAAG0b,OAAO,CAAC,GAAGC,cAAc,CAAA;QAE5DF,KAAK,CAAC/a,IAAI,GAAG,GAAG,CAACgU,MAAM,CAACiH,cAAc,CAAC,CAAA;EACvC,MAAA,IAAIC,cAAc,EAAEH,KAAK,CAAC/a,IAAI,IAAI,GAAG,GAAG,GAAG,CAACgU,MAAM,CAACkH,cAAc,CAAC,GAAG,GAAG,CAAA;QAExE,IAAI,CAACL,IAAI,GAAGC,KAAK,CAAA;EACnB,KAAA;EAEA,IAAA,KAAK,CAACnP,OAAO,CAACoP,KAAK,CAAC,CAAA;EACtB,GAAA;EAESpO,EAAAA,cAAcA,CAAEC,EAAU,EAAEX,KAAsC,EAAoB;EAAA,IAAA,IAA1DA,KAAsC,KAAA,KAAA,CAAA,EAAA;QAAtCA,KAAsC,GAAC,EAAE,CAAA;EAAA,KAAA;MAC5E,MAAMkP,SAAS,GAAGlc,IAAI,CAACC,GAAG,CAAC,IAAI,CAACsK,eAAe,CAAC,CAAC,EAAE/M,SAAS,CAACK,WAAW,CAAC,EAAE,IAAI,CAAC0B,KAAK,CAACf,MAAM,CAAC,CAAA;MAE7F,MAAM2d,OAAO,GAAG,IAAI,CAACP,IAAI,CAACjH,MAAM,CAACvQ,CAAC,IAAI,IAAI,CAACgY,UAAU,CAAChY,CAAC,EAAE,IAAI,CAACgE,aAAa,GAAGuF,EAAE,EAAEuO,SAAS,CAAC,CAAC,CAAA;MAE7F,IAAIC,OAAO,CAAC3d,MAAM,EAAE;EAClB,MAAA,IAAI2d,OAAO,CAAC3d,MAAM,KAAK,CAAC,EAAE;EACxB,QAAA,IAAI,CAACwX,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAACzW,KAAK,CAACf,MAAM,EAAE,CAACL,CAAC,EAAEsX,EAAE,KAAK;YAC1D,MAAM4G,GAAG,GAAGF,OAAO,CAAC,CAAC,CAAC,CAAC1G,EAAE,CAAC,CAAA;EAC1B,UAAA,IAAIA,EAAE,IAAI,IAAI,CAAClW,KAAK,CAACf,MAAM,IAAI6d,GAAG,KAAKle,CAAC,CAACoB,KAAK,EAAE,OAAA;YAEhDpB,CAAC,CAAC2O,KAAK,EAAE,CAAA;EACT3O,UAAAA,CAAC,CAACyP,WAAW,CAACyO,GAAG,EAAErP,KAAK,CAAC,CAAA;EAC3B,SAAC,CAAC,CAAA;EACJ,OAAA;EAEA,MAAA,MAAM+B,CAAC,GAAG,KAAK,CAACrB,cAAc,CAACyO,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC5c,KAAK,CAACf,MAAM,CAAC,EAAEwO,KAAK,CAAC,CAAA;EAEpE,MAAA,IAAImP,OAAO,CAAC3d,MAAM,KAAK,CAAC,EAAE;EACxB2d,QAAAA,OAAO,CAAC,CAAC,CAAC,CAAC3c,KAAK,CAAC,IAAI,CAAC4I,aAAa,CAAC5J,MAAM,CAAC,CAACoa,KAAK,CAAC,EAAE,CAAC,CAAC7P,OAAO,CAACsT,GAAG,IAAItN,CAAC,CAACnD,SAAS,CAAC,KAAK,CAAC8B,cAAc,CAAC2O,GAAG,CAAC,CAAC,CAAC,CAAA;EAC9G,OAAA;EAEA,MAAA,OAAOtN,CAAC,CAAA;EACV,KAAA;MAEA,OAAO,IAAIzD,aAAa,CAAC;QAAEK,IAAI,EAAE,CAAC,IAAI,CAACtB,UAAAA;EAAW,KAAC,CAAC,CAAA;EACtD,GAAA;EAESmD,EAAAA,WAAWA,CAAEF,OAAe,EAAIC,KAAa,EAAwC;EAAA,IAAA,IAAxED,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;EAC7E;EACA,IAAA,OAAO,IAAIwN,qBAAqB,CAAC,EAAE,EAAEsB,OAAO,CAAC,CAAA;EAC/C,GAAA;EAES4B,EAAAA,MAAMA,CAAE5B,OAAe,EAAIC,KAAa,EAA0C;EAAA,IAAA,IAA1ED,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;MACxE,IAAI8O,OAAO,KAAKC,KAAK,EAAE,OAAO,IAAIjC,aAAa,EAAE,CAAA;MAEjD,MAAM4Q,SAAS,GAAGlc,IAAI,CAACC,GAAG,CAAC,KAAK,CAACsK,eAAe,CAAC,CAAC,EAAE/M,SAAS,CAACK,WAAW,CAAC,EAAE,IAAI,CAAC0B,KAAK,CAACf,MAAM,CAAC,CAAA;EAE9F,IAAA,IAAI2K,GAAW,CAAA;MACf,KAAKA,GAAG,GAAGmE,OAAO,EAAEnE,GAAG,IAAI,CAAC,EAAE,EAAEA,GAAG,EAAE;EACnC,MAAA,MAAMgT,OAAO,GAAG,IAAI,CAACP,IAAI,CAACjH,MAAM,CAACvQ,CAAC,IAAI,IAAI,CAACgY,UAAU,CAAChY,CAAC,EAAE,IAAI,CAAC7E,KAAK,CAACC,KAAK,CAAC0c,SAAS,EAAE/S,GAAG,CAAC,EAAE+S,SAAS,CAAC,CAAC,CAAA;EACtG,MAAA,IAAIC,OAAO,CAAC3d,MAAM,GAAG,CAAC,EAAE,MAAA;EAC1B,KAAA;MAEA,MAAMsM,OAAO,GAAG,KAAK,CAACoE,MAAM,CAAC/F,GAAG,EAAEoE,KAAK,CAAC,CAAA;EACxCzC,IAAAA,OAAO,CAACY,SAAS,IAAIvC,GAAG,GAAGmE,OAAO,CAAA;EAElC,IAAA,OAAOxC,OAAO,CAAA;EAChB,GAAA;IAEA,IAAaT,UAAUA,GAAa;MAClC,OAAO,IAAI,CAACuR,IAAI,CAAChR,OAAO,CAAC,IAAI,CAACrL,KAAK,CAAC,IAAI,CAAC,CAAA;EAC3C,GAAA;EACF,CAAA;EAzFE;EAFIoc,UAAU,CAKPhP,QAAQ,GAAmE;IAChF,GAAGzL,aAAa,CAACyL,QAAQ;EACzByP,EAAAA,UAAU,EAAEA,CAACE,IAAI,EAAEC,IAAI,EAAEL,SAAS,KAAKI,IAAI,CAAC1R,OAAO,CAAC2R,IAAI,EAAEL,SAAS,CAAC,KAAKA,SAAAA;EAC3E,CAAC,CAAA;EAsFHvb,KAAK,CAACgb,UAAU,GAAGA,UAAU;;ECzG7B;EAEA,MAAMla,cAAc,SAAoBF,MAAM,CAAQ;EACpD;;EAEA;;EAEA;;EAEA;;EAEA;;IAGS4G,aAAaA,CAAE9I,IAAoC,EAAE;EAC5D,IAAA,KAAK,CAAC8I,aAAa,CAAC9I,IAAI,CAAC,CAAA;EAC3B,GAAA;IAESqN,OAAOA,CAAErN,IAAoC,EAAE;MACtD,KAAK,CAACqN,OAAO,CAAC;EACZ,MAAA,GAAGrN,IAAI;QACPuQ,QAAQ,EAAEvQ,IAAI,CAAC0B,IAAAA;EACjB,KAAC,CAAC,CAAA;EACJ,GAAA;EACF,CAAA;EAGAJ,KAAK,CAACc,cAAc,GAAGA,cAAc;;;ECdrC;EAEA,MAAMJ,YAAY,SAASE,MAAM,CAAS;EAmBxC;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;EAEA;;IAQA9E,WAAWA,CAAE4C,IAA0B,EAAE;EACvC,IAAA,KAAK,CAAC;QACJ,GAAGgC,YAAY,CAACsL,QAAQ;QACxB,GAAGtN,IAAAA;EACL,KAAC,CAAC,CAAA;EACJ,GAAA;IAES8I,aAAaA,CAAE9I,IAAkC,EAAE;EAC1D,IAAA,KAAK,CAAC8I,aAAa,CAAC9I,IAAI,CAAC,CAAA;EAC3B,GAAA;IAESqN,OAAOA,CAAErN,IAAkC,EAAE;EACpD,IAAA,KAAK,CAACqN,OAAO,CAACrN,IAAI,CAAC,CAAA;MACnB,IAAI,CAACmd,cAAc,EAAE,CAAA;EACvB,GAAA;EAEAA,EAAAA,cAAcA,GAAI;MAChB,MAAM5c,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC6c,aAAa,GAAG,UAAU,GAAG,EAAE,CAAC,CAAA;MAC1D,MAAMC,GAAG,GAAG,MAAM,CAAA;MAClB,MAAM3c,GAAG,GAAG,CAAC,IAAI,CAAC4c,KAAK,GAAA,GAAA,GACjB3e,YAAY,CAAC,IAAI,CAAC4e,KAAK,CAAC,cAAS,IAAI,CAACD,KAAK,GAC/C,KAAA,GAAA,EAAE,IAAI,GAAG,CAAA;MAEX,IAAI,CAACE,aAAa,GAAG,IAAI/d,MAAM,CAACc,KAAK,GAAG8c,GAAG,GAAG3c,GAAG,CAAC,CAAA;MAClD,IAAI,CAAC+c,iBAAiB,GAAG,IAAIhe,MAAM,CAAK,GAAA,GAAA,IAAI,CAACie,UAAU,CAACzM,GAAG,CAACtS,YAAY,CAAC,CAACuS,IAAI,CAAC,EAAE,CAAC,GAAK,GAAA,EAAA,GAAG,CAAC,CAAA;EAC3F,IAAA,IAAI,CAACyM,yBAAyB,GAAG,IAAIle,MAAM,CAACd,YAAY,CAAC,IAAI,CAACif,kBAAkB,CAAC,EAAE,GAAG,CAAC,CAAA;EACzF,GAAA;IAEAC,0BAA0BA,CAAE3d,KAAa,EAAU;MACjD,OAAOA,KAAK,CAACtB,OAAO,CAAC,IAAI,CAAC+e,yBAAyB,EAAE,EAAE,CAAC,CAAA;EAC1D,GAAA;IAEAG,0BAA0BA,CAAE5d,KAAa,EAAU;EACjD;MACA,MAAM6d,KAAK,GAAG7d,KAAK,CAACqZ,KAAK,CAAC,IAAI,CAACgE,KAAK,CAAC,CAAA;EACrCQ,IAAAA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACnf,OAAO,CAAC,uBAAuB,EAAE,IAAI,CAACgf,kBAAkB,CAAC,CAAA;EAC7E,IAAA,OAAOG,KAAK,CAAC7M,IAAI,CAAC,IAAI,CAACqM,KAAK,CAAC,CAAA;EAC/B,GAAA;EAES7O,EAAAA,aAAaA,CAAEJ,EAAU,EAAEX,KAAkB,EAA8B;EAAA,IAAA,IAAhDA,KAAkB,KAAA,KAAA,CAAA,EAAA;QAAlBA,KAAkB,GAAC,EAAE,CAAA;EAAA,KAAA;MACvD,MAAM,CAACqQ,MAAM,EAAEvS,OAAO,CAAC,GAAG,KAAK,CAACiD,aAAa,CAAC,IAAI,CAACmP,0BAA0B,CAC3E,IAAI,CAACP,KAAK,IAAI,IAAI,CAACI,UAAU,CAACve,MAAM;EAClC;EACR;EACA;EACA;EACA;EACA;EACA;EACA;EACQwO,IAAAA,KAAK,CAACjK,KAAK,IAAIiK,KAAK,CAAC/B,GAAG,IACxB,CAAC+B,KAAK,CAACjK,KAAK,IAAI,CAACiK,KAAK,CAAC/B,GAAG,CAC3B,GAAG0C,EAAE,CAAC1P,OAAO,CAAC,IAAI,CAAC6e,iBAAiB,EAAE,IAAI,CAACF,KAAK,CAAC,GAAGjP,EACvD,CAAC,EAAEX,KAAK,CAAC,CAAA;MACT,IAAIW,EAAE,IAAI,CAAC0P,MAAM,EAAEvS,OAAO,CAACa,IAAI,GAAG,IAAI,CAAA;MAEtC,IAAI0R,MAAM,IAAI,CAAC,IAAI,CAACC,aAAa,IAAI,CAAC,IAAI,CAAC/d,KAAK,IAAI8d,MAAM,KAAK,GAAG,EAAEvS,OAAO,CAACc,SAAS,CAAC,IAAI,CAACgC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAA;EAE5G,IAAA,OAAO,CAACyP,MAAM,EAAEvS,OAAO,CAAC,CAAA;EAC1B,GAAA;EAEAyS,EAAAA,gBAAgBA,CAAE1F,EAAU,EAAE2F,kBAA2B,EAAgB;EAAA,IAAA,IAA3CA,kBAA2B,KAAA,KAAA,CAAA,EAAA;EAA3BA,MAAAA,kBAA2B,GAAC,KAAK,CAAA;EAAA,KAAA;MAC7D,IAAIC,KAAK,GAAG,CAAC,CAAA;MAEb,KAAK,IAAItU,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG0O,EAAE,EAAE,EAAE1O,GAAG,EAAE;EACjC,MAAA,IAAI,IAAI,CAACjC,MAAM,CAAC0D,OAAO,CAAC,IAAI,CAACqS,kBAAkB,EAAE9T,GAAG,CAAC,KAAKA,GAAG,EAAE;EAC7D,QAAA,EAAEsU,KAAK,CAAA;UACP,IAAID,kBAAkB,EAAE3F,EAAE,IAAI,IAAI,CAACoF,kBAAkB,CAACze,MAAM,CAAA;EAC9D,OAAA;EACF,KAAA;EAEA,IAAA,OAAOif,KAAK,CAAA;EACd,GAAA;IAEAC,yBAAyBA,CAAEle,KAAa,EAAsB;EAAA,IAAA,IAAnCA,KAAa,KAAA,KAAA,CAAA,EAAA;QAAbA,KAAa,GAAC,IAAI,CAAC0H,MAAM,CAAA;EAAA,KAAA;EAClD,IAAA,OAAO,IAAI,CAACqW,gBAAgB,CAAC,IAAI,CAACL,0BAA0B,CAAC1d,KAAK,CAAC,CAAChB,MAAM,EAAE,IAAI,CAAC,CAAA;EACnF,GAAA;EAES2O,EAAAA,YAAYA,CAAEG,OAAe,EAAIC,KAAa,EAA2BP,KAAoB,EAAU;EAAA,IAAA,IAAzFM,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;EAC9E,IAAA,CAAC8O,OAAO,EAAEC,KAAK,CAAC,GAAG,IAAI,CAACoQ,0BAA0B,CAACrQ,OAAO,EAAEC,KAAK,CAAC,CAAA;EAElE,IAAA,OAAO,IAAI,CAAC2P,0BAA0B,CAAC,KAAK,CAAC/P,YAAY,CAACG,OAAO,EAAEC,KAAK,EAAEP,KAAK,CAAC,CAAC,CAAA;EACnF,GAAA;EAGSU,EAAAA,cAAcA,CAAEC,EAAU,EAAEX,KAAkB,EAAoB;EAAA,IAAA,IAAtCA,KAAkB,KAAA,KAAA,CAAA,EAAA;QAAlBA,KAAkB,GAAC,EAAE,CAAA;EAAA,KAAA;EACxD,IAAA,MAAM4Q,mBAAmB,GAAG5Q,KAAK,CAACvM,IAAI,IAAIuM,KAAK,CAAC4B,gBAAgB,GAC9D5B,KAAK,CAAC4B,gBAAgB,CAAC1H,MAAM,GAC7B,IAAI,CAACA,MAAM,CAAA;EACb,IAAA,MAAM2W,6BAA6B,GAAG,IAAI,CAACH,yBAAyB,CAACE,mBAAmB,CAAC,CAAA;MACzF,IAAI,CAAC1W,MAAM,GAAG,IAAI,CAACgW,0BAA0B,CAAC,IAAI,CAAC3d,KAAK,CAAC,CAAA;EAEzD,IAAA,MAAMG,QAAQ,GAAG,IAAI,CAACwH,MAAM,CAAA;MAE5B,IAAI,CAACA,MAAM,IAAIyG,EAAE,CAAA;EAEjB,IAAA,MAAM4K,GAAG,GAAG,IAAI,CAACuF,MAAM,CAAA;EACvB,IAAA,IAAIC,QAAQ,GAAG,CAACC,KAAK,CAACzF,GAAG,CAAC,CAAA;MAC1B,IAAI5M,IAAI,GAAG,KAAK,CAAA;EAEhB,IAAA,IAAIoS,QAAQ,EAAE;EACZ,MAAA,IAAIE,QAAQ,CAAA;QACZ,IAAI,IAAI,CAAChe,GAAG,IAAI,IAAI,IAAI,IAAI,CAACA,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC6d,MAAM,GAAG,IAAI,CAAC7d,GAAG,EAAEge,QAAQ,GAAG,IAAI,CAAChe,GAAG,CAAA;QACnF,IAAI,IAAI,CAACI,GAAG,IAAI,IAAI,IAAI,IAAI,CAACA,GAAG,GAAG,CAAC,IAAI,IAAI,CAACyd,MAAM,GAAG,IAAI,CAACzd,GAAG,EAAE4d,QAAQ,GAAG,IAAI,CAAC5d,GAAG,CAAA;QAEnF,IAAI4d,QAAQ,IAAI,IAAI,EAAE;UACpB,IAAI,IAAI,CAACjQ,OAAO,EAAE;YAChB,IAAI,CAAC9G,MAAM,GAAG,IAAI,CAACgG,MAAM,CAAC+Q,QAAQ,EAAE,IAAI,CAAC,CAAChgB,OAAO,CAACoD,YAAY,CAAC6c,cAAc,EAAE,IAAI,CAACtB,KAAK,CAAC,CAAA;EAC1FjR,UAAAA,IAAI,KAAJA,IAAI,GAAKjM,QAAQ,KAAK,IAAI,CAACwH,MAAM,IAAI,CAAC8F,KAAK,CAACvM,IAAI,EAAC;EACnD,SAAC,MAAM;EACLsd,UAAAA,QAAQ,GAAG,KAAK,CAAA;EAClB,SAAA;EACF,OAAA;EACAA,MAAAA,QAAQ,KAARA,QAAQ,GAAKjS,OAAO,CAAC,IAAI,CAAC5E,MAAM,CAACsR,KAAK,CAAC,IAAI,CAACqE,aAAa,CAAC,CAAC,CAAA,CAAA;EAC7D,KAAA;EAEA,IAAA,IAAIsB,aAAa,CAAA;MACjB,IAAI,CAACJ,QAAQ,EAAE;QACb,IAAI,CAAC7W,MAAM,GAAGxH,QAAQ,CAAA;EACtBye,MAAAA,aAAa,GAAG,IAAI7S,aAAa,EAAE,CAAA;EACrC,KAAC,MAAM;QACL6S,aAAa,GAAG,IAAI7S,aAAa,CAAC;UAChCpL,QAAQ,EAAE,IAAI,CAACgH,MAAM,CAAC1H,KAAK,CAACE,QAAQ,CAAClB,MAAM,CAAC;EAC5CiN,QAAAA,WAAW,EAAEE,IAAI,GAAG,EAAE,GAAGgC,EAAE;EAC3BhC,QAAAA,IAAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAA;MAEA,IAAI,CAACzE,MAAM,GAAG,IAAI,CAACiW,0BAA0B,CAAC,IAAI,CAACjW,MAAM,CAAC,CAAA;EAC1D,IAAA,MAAMkX,eAAe,GAAGpR,KAAK,CAACvM,IAAI,IAAIuM,KAAK,CAAC4B,gBAAgB,GAC1D5B,KAAK,CAAC4B,gBAAgB,CAAC1H,MAAM,GAC7B,IAAI,CAACA,MAAM,CAAA;EACb,IAAA,MAAMmX,yBAAyB,GAAG,IAAI,CAACX,yBAAyB,CAACU,eAAe,CAAC,CAAA;EAEjFD,IAAAA,aAAa,CAACzS,SAAS,IAAI,CAAC2S,yBAAyB,GAAGR,6BAA6B,IAAI,IAAI,CAACZ,kBAAkB,CAACze,MAAM,CAAA;EACvH,IAAA,OAAO2f,aAAa,CAAA;EACtB,GAAA;IAEAG,oBAAoBA,CAAEnV,GAAW,EAAU;MACzC,IAAI,IAAI,CAAC8T,kBAAkB,EAAE;QAC3B,MAAMsB,UAAU,GAAGpV,GAAG,GAAG,IAAI,CAAC8T,kBAAkB,CAACze,MAAM,GAAG,CAAC,CAAA;EAC3D,MAAA,MAAMggB,YAAY,GAAG,IAAI,CAACjf,KAAK,CAACqL,OAAO,CAAC,IAAI,CAACqS,kBAAkB,EAAEsB,UAAU,CAAC,CAAA;EAC5E,MAAA,IAAIC,YAAY,IAAIrV,GAAG,EAAE,OAAOqV,YAAY,CAAA;EAC9C,KAAA;EAEA,IAAA,OAAO,CAAC,CAAC,CAAA;EACX,GAAA;EAEAb,EAAAA,0BAA0BA,CAAE1R,IAAY,EAAE4L,EAAU,EAAoB;EACtE,IAAA,MAAM4G,sBAAsB,GAAG,IAAI,CAACH,oBAAoB,CAACrS,IAAI,CAAC,CAAA;EAC9D,IAAA,IAAIwS,sBAAsB,IAAI,CAAC,EAAExS,IAAI,GAAGwS,sBAAsB,CAAA;EAE9D,IAAA,MAAMC,oBAAoB,GAAG,IAAI,CAACJ,oBAAoB,CAACzG,EAAE,CAAC,CAAA;EAC1D,IAAA,IAAI6G,oBAAoB,IAAI,CAAC,EAAE7G,EAAE,GAAG6G,oBAAoB,GAAG,IAAI,CAACzB,kBAAkB,CAACze,MAAM,CAAA;EACzF,IAAA,OAAO,CAACyN,IAAI,EAAE4L,EAAE,CAAC,CAAA;EACnB,GAAA;EAGS3I,EAAAA,MAAMA,CAAE5B,OAAe,EAAIC,KAAa,EAA0C;EAAA,IAAA,IAA1ED,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;EACxE,IAAA,CAAC8O,OAAO,EAAEC,KAAK,CAAC,GAAG,IAAI,CAACoQ,0BAA0B,CAACrQ,OAAO,EAAEC,KAAK,CAAC,CAAA;MAElE,MAAMoR,cAAc,GAAG,IAAI,CAACpf,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE8N,OAAO,CAAC,CAAA;MACnD,MAAMsR,aAAa,GAAG,IAAI,CAACrf,KAAK,CAACC,KAAK,CAAC+N,KAAK,CAAC,CAAA;MAE7C,MAAMsQ,6BAA6B,GAAG,IAAI,CAACN,gBAAgB,CAACoB,cAAc,CAACngB,MAAM,CAAC,CAAA;EAClF,IAAA,IAAI,CAAC0I,MAAM,GAAG,IAAI,CAACiW,0BAA0B,CAAC,IAAI,CAACD,0BAA0B,CAACyB,cAAc,GAAGC,aAAa,CAAC,CAAC,CAAA;EAC9G,IAAA,MAAMP,yBAAyB,GAAG,IAAI,CAACX,yBAAyB,CAACiB,cAAc,CAAC,CAAA;MAEhF,OAAO,IAAIrT,aAAa,CAAC;QACvBI,SAAS,EAAE,CAAC2S,yBAAyB,GAAGR,6BAA6B,IAAI,IAAI,CAACZ,kBAAkB,CAACze,MAAAA;EACnG,KAAC,CAAC,CAAA;EACJ,GAAA;EAES+L,EAAAA,eAAeA,CAAEzK,SAAiB,EAAE/B,SAAqB,EAAU;EAC1E,IAAA,IAAI,CAAC,IAAI,CAACkf,kBAAkB,EAAE,OAAOnd,SAAS,CAAA;EAE9C,IAAA,QAAQ/B,SAAS;QACf,KAAKP,SAAS,CAACC,IAAI,CAAA;QACnB,KAAKD,SAAS,CAACE,IAAI,CAAA;QACnB,KAAKF,SAAS,CAACG,UAAU;EAAE,QAAA;YACzB,MAAMkhB,kBAAkB,GAAG,IAAI,CAACP,oBAAoB,CAACxe,SAAS,GAAG,CAAC,CAAC,CAAA;YACnE,IAAI+e,kBAAkB,IAAI,CAAC,EAAE;cAC3B,MAAMC,qBAAqB,GAAGD,kBAAkB,GAAG,IAAI,CAAC5B,kBAAkB,CAACze,MAAM,CAAA;EACjF,YAAA,IAAIsB,SAAS,GAAGgf,qBAAqB,IACnC,IAAI,CAACvf,KAAK,CAACf,MAAM,IAAIsgB,qBAAqB,IAC1C/gB,SAAS,KAAKP,SAAS,CAACG,UAAU,EAClC;EACA,cAAA,OAAOkhB,kBAAkB,CAAA;EAC3B,aAAA;EACF,WAAA;EACA,UAAA,MAAA;EACF,SAAA;QACA,KAAKrhB,SAAS,CAACI,KAAK,CAAA;QACpB,KAAKJ,SAAS,CAACK,WAAW;EAAE,QAAA;EAC1B,UAAA,MAAMkhB,mBAAmB,GAAG,IAAI,CAACT,oBAAoB,CAACxe,SAAS,CAAC,CAAA;YAChE,IAAIif,mBAAmB,IAAI,CAAC,EAAE;EAC5B,YAAA,OAAOA,mBAAmB,GAAG,IAAI,CAAC9B,kBAAkB,CAACze,MAAM,CAAA;EAC7D,WAAA;EACF,SAAA;EACF,KAAA;EAEA,IAAA,OAAOsB,SAAS,CAAA;EAClB,GAAA;EAESoL,EAAAA,QAAQA,GAAI;MACnB,IAAI,IAAI,CAAC3L,KAAK,EAAE;EACd,MAAA,MAAMue,MAAM,GAAG,IAAI,CAACA,MAAM,CAAA;QAC1B,IAAIkB,QAAQ,GAAGlB,MAAM,CAAA;;EAErB;EACA,MAAA,IAAI,IAAI,CAAC7d,GAAG,IAAI,IAAI,EAAE+e,QAAQ,GAAGhf,IAAI,CAACK,GAAG,CAAC2e,QAAQ,EAAE,IAAI,CAAC/e,GAAG,CAAC,CAAA;EAC7D,MAAA,IAAI,IAAI,CAACI,GAAG,IAAI,IAAI,EAAE2e,QAAQ,GAAGhf,IAAI,CAACC,GAAG,CAAC+e,QAAQ,EAAE,IAAI,CAAC3e,GAAG,CAAC,CAAA;EAE7D,MAAA,IAAI2e,QAAQ,KAAKlB,MAAM,EAAE,IAAI,CAAC1V,aAAa,GAAG,IAAI,CAAC8E,MAAM,CAAC8R,QAAQ,EAAE,IAAI,CAAC,CAAA;EAEzE,MAAA,IAAIC,SAAS,GAAG,IAAI,CAAC1f,KAAK,CAAA;QAE1B,IAAI,IAAI,CAAC2f,cAAc,EAAED,SAAS,GAAG,IAAI,CAACE,eAAe,CAACF,SAAS,CAAC,CAAA;EACpE,MAAA,IAAI,IAAI,CAACG,kBAAkB,IAAI,IAAI,CAACzC,KAAK,GAAG,CAAC,EAAEsC,SAAS,GAAG,IAAI,CAACI,mBAAmB,CAACJ,SAAS,CAAC,CAAA;QAE9F,IAAI,CAAC/X,MAAM,GAAG+X,SAAS,CAAA;EACzB,KAAA;MAEA,KAAK,CAAC/T,QAAQ,EAAE,CAAA;EAClB,GAAA;IAEAiU,eAAeA,CAAE5f,KAAa,EAAU;EACtC,IAAA,MAAM6d,KAAK,GAAG,IAAI,CAACF,0BAA0B,CAAC3d,KAAK,CAAC,CAACqZ,KAAK,CAAC,IAAI,CAACgE,KAAK,CAAC,CAAA;;EAEtE;MACAQ,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACnf,OAAO,CAAC,iBAAiB,EAAE,CAACua,KAAK,EAAE8G,IAAI,EAAEC,KAAK,EAAEhH,GAAG,KAAK+G,IAAI,GAAG/G,GAAG,CAAC,CAAA;EACvF;MACA,IAAIhZ,KAAK,CAACf,MAAM,IAAI,CAAC,KAAK,CAACghB,IAAI,CAACpC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;EAEpE,IAAA,IAAIA,KAAK,CAAC5e,MAAM,GAAG,CAAC,EAAE;EACpB4e,MAAAA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACnf,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;EACvC,MAAA,IAAI,CAACmf,KAAK,CAAC,CAAC,CAAC,CAAC5e,MAAM,EAAE4e,KAAK,CAAC5e,MAAM,GAAG,CAAC,CAAC;EACzC,KAAA;EAEA,IAAA,OAAO,IAAI,CAAC2e,0BAA0B,CAACC,KAAK,CAAC7M,IAAI,CAAC,IAAI,CAACqM,KAAK,CAAC,CAAC,CAAA;EAChE,GAAA;IAEAyC,mBAAmBA,CAAE9f,KAAa,EAAU;EAC1C,IAAA,IAAI,CAACA,KAAK,EAAE,OAAOA,KAAK,CAAA;MAExB,MAAM6d,KAAK,GAAG7d,KAAK,CAACqZ,KAAK,CAAC,IAAI,CAACgE,KAAK,CAAC,CAAA;MACrC,IAAIQ,KAAK,CAAC5e,MAAM,GAAG,CAAC,EAAE4e,KAAK,CAAC7W,IAAI,CAAC,EAAE,CAAC,CAAA;EACpC6W,IAAAA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC3E,MAAM,CAAC,IAAI,CAACkE,KAAK,EAAE,GAAG,CAAC,CAAA;EAC3C,IAAA,OAAOS,KAAK,CAAC7M,IAAI,CAAC,IAAI,CAACqM,KAAK,CAAC,CAAA;EAC/B,GAAA;EAES5N,EAAAA,aAAaA,CAAErB,EAAU,EAAEX,KAAkB,EAAKa,SAAuB,EAAW;EAAA,IAAA,IAAzDb,KAAkB,KAAA,KAAA,CAAA,EAAA;QAAlBA,KAAkB,GAAC,EAAE,CAAA;EAAA,KAAA;EACvD,IAAA,MAAMyS,cAAc,GAAG,IAAI,CAAC9C,KAAK,KAAK,CAAC,IAAIhP,EAAE,KAAK,IAAI,CAACsP,kBAAkB,KACvEtP,EAAE,KAAK,IAAI,CAACiP,KAAK,IACjBjP,EAAE,KAAKtM,YAAY,CAAC6c,cAAc,IAClC,IAAI,CAACnB,UAAU,CAAC9f,QAAQ,CAAC0Q,EAAE,CAAC,CAC7B,CAAA;EACD,IAAA,OAAO,KAAK,CAACqB,aAAa,CAACrB,EAAE,EAAEX,KAAK,EAAEa,SAAS,CAAC,IAAI,CAAC4R,cAAc,CAAA;EACrE,GAAA;IAEA,IAAarX,aAAaA,GAAY;MACpC,OAAO,IAAI,CAAC8U,0BAA0B,CAAC,IAAI,CAACiC,eAAe,CAAC,IAAI,CAAC5f,KAAK,CAAC,CAAC,CACrEtB,OAAO,CAAC,IAAI,CAAC2e,KAAK,EAAEvb,YAAY,CAAC6c,cAAc,CAAC,CAAA;EACrD,GAAA;IAEA,IAAa9V,aAAaA,CAAEA,aAAqB,EAAE;MACjD,KAAK,CAACA,aAAa,GAAGA,aAAa,CAAA;EACrC,GAAA;IAEA,IAAaG,UAAUA,GAAY;MACjC,OAAO,IAAI,CAAC0E,KAAK,CAAC,IAAI,CAAC7E,aAAa,EAAE,IAAI,CAAC,CAAA;EAC7C,GAAA;IAEA,IAAaG,UAAUA,CAAEmX,CAAS,EAAE;MAClC,IAAI,CAACpX,aAAa,GAAG,IAAI,CAAC4E,MAAM,CAACwS,CAAC,EAAE,IAAI,CAAC,CAACzhB,OAAO,CAACoD,YAAY,CAAC6c,cAAc,EAAE,IAAI,CAACtB,KAAK,CAAC,CAAA;EAC5F,GAAA;;EAEA;IACA,IAAIkB,MAAMA,GAAY;MACpB,OAAO,IAAI,CAACvV,UAAU,CAAA;EACxB,GAAA;IAEA,IAAIuV,MAAMA,CAAEA,MAAc,EAAE;MAC1B,IAAI,CAACvV,UAAU,GAAGuV,MAAM,CAAA;EAC1B,GAAA;IAEA,IAAIrB,aAAaA,GAAa;MAC5B,OAAQ,IAAI,CAACxc,GAAG,IAAI,IAAI,IAAI,IAAI,CAACA,GAAG,GAAG,CAAC,IAAM,IAAI,CAACI,GAAG,IAAI,IAAI,IAAI,IAAI,CAACA,GAAG,GAAG,CAAE,CAAA;EACjF,GAAA;IAEA,IAAIid,aAAaA,GAAa;MAC5B,OAAQ,IAAI,CAACrd,GAAG,IAAI,IAAI,IAAI,IAAI,CAACA,GAAG,GAAG,CAAC,IAAM,IAAI,CAACI,GAAG,IAAI,IAAI,IAAI,IAAI,CAACA,GAAG,GAAG,CAAE,CAAA;EACjF,GAAA;IAESoI,gBAAgBA,CAAElJ,KAAU,EAAW;EAC9C;EACA;EACA,IAAA,OAAO,CACL,KAAK,CAACkJ,gBAAgB,CAAClJ,KAAK,CAAC,IAC7B8B,YAAY,CAAC8O,YAAY,CAAClT,QAAQ,CAACsC,KAAK,CAAC,IAAI8B,YAAY,CAAC8O,YAAY,CAAClT,QAAQ,CAAC,IAAI,CAACsL,UAAU,CAAC,KAC7F,EAAEhJ,KAAK,KAAK,CAAC,IAAI,IAAI,CAACA,KAAK,KAAK,EAAE,CAAC,CAAA;EAC1C,GAAA;EACF,CAAA;EAACogB,aAAA,GAnWKte,YAAY,CAAA;EAAZA,YAAY,CACT6c,cAAc,GAAG,GAAG,CAAA;EADvB7c,YAAY,CAET8O,YAAY,GAA8C,CAAC,GAAG5O,MAAM,CAAC4O,YAAY,EAAE,CAAC,CAAC,CAAA;EAFxF9O,YAAY,CAGTsL,QAAQ,GAAG;IAChB,GAAGpL,MAAM,CAACoL,QAAQ;EAClB5L,EAAAA,IAAI,EAAEK,MAAM;EACZwb,EAAAA,KAAK,EAAE,GAAG;EACVK,EAAAA,kBAAkB,EAAE,EAAE;EACtBF,EAAAA,UAAU,EAAE,CAAC1b,aAAY,CAAC6c,cAAc,CAAC;IACzCje,GAAG,EAAEmB,MAAM,CAACwe,gBAAgB;IAC5Bvf,GAAG,EAAEe,MAAM,CAACye,gBAAgB;EAC5BlD,EAAAA,KAAK,EAAE,CAAC;EACRuC,EAAAA,cAAc,EAAE,IAAI;EACpBE,EAAAA,kBAAkB,EAAE,KAAK;EACzBnS,EAAAA,KAAK,EAAE7L,MAAM;IACb8L,MAAM,EAAGwS,CAAS,IAAKA,CAAC,CAACI,cAAc,CAAC,OAAO,EAAE;EAAEC,IAAAA,WAAW,EAAE,KAAK;EAAEC,IAAAA,qBAAqB,EAAE,EAAA;KAAI,CAAA;EACpG,CAAC,CAAA;EAsVHrf,KAAK,CAACU,YAAY,GAAGA,YAAY;;ECxXjC;AAEA,QAAM4e,SAAS,GAAG;EAChBC,EAAAA,MAAM,EAAE,OAAO;EACfC,EAAAA,QAAQ,EAAE,eAAe;EACzBC,EAAAA,KAAK,EAAE,YAAA;EACT,EAAU;EAYV;EAEA,SAASC,UAAUA,CAKjBC,GAAQ,EACRrU,IAAU,EACV4L,EAAM,EACN;EAAA,EAAA,IAFA5L,IAAU,KAAA,KAAA,CAAA,EAAA;MAAVA,IAAU,GAACgU,SAAS,CAACC,MAAM,CAAA;EAAA,GAAA;EAAA,EAAA,IAC3BrI,EAAM,KAAA,KAAA,CAAA,EAAA;MAANA,EAAM,GAACoI,SAAS,CAACC,MAAM,CAAA;EAAA,GAAA;EAEvB,EAAA,MAAMlZ,MAAM,GAAG/E,UAAU,CAACqe,GAAG,CAAC,CAAA;EAC9B,EAAA,OAAQ/gB,KAA8B,IAAKyH,MAAM,CAACuI,WAAW,CAAC4J,CAAC,IAAI;EACjEA,IAAAA,CAAC,CAAClN,IAAI,CAAC,GAAG1M,KAAK,CAAA;MACf,OAAO4Z,CAAC,CAACtB,EAAE,CAAC,CAAA;EACd,GAAC,CAAC,CAAA;EACJ,CAAA;;EAEA;EAEA,SAAS0I,IAAIA,CAKXhhB,KAA8B,EAC9BwB,IAAS,EACTkL,IAAW,EACX4L,EAAO,EACP;IACA,OAAOwI,UAAU,CAACtf,IAAI,EAAEkL,IAAI,EAAE4L,EAAE,CAAC,CAACtY,KAAK,CAAC,CAAA;EAC1C,CAAA;EAGAoB,KAAK,CAACsf,SAAS,GAAGA,SAAS,CAAA;EAC3Btf,KAAK,CAAC0f,UAAU,GAAGA,UAAU,CAAA;EAC7B1f,KAAK,CAAC4f,IAAI,GAAGA,IAAI;;EC3CjB;EAEA,MAAMpL,WAAW,SAA+BjU,aAAa,CAAC;IAK5D,IAAIsf,UAAUA,GAAY;EAAA,IAAA,IAAAljB,IAAA,CAAA;EACxB,IAAA,OAAA,CAAAA,IAAA,GACET,KAAK,CAACC,OAAO,CAAC,IAAI,CAACiY,MAAM,CAAC,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAC3C,IAAI,CAACA,MAAM,KAAK0L,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC1L,MAAM,KAAAzX,IAAAA,GAAAA,IAAA,GACvC,CAAC,CAAA;EACR,GAAA;IAEA,IAAIojB,QAAQA,GAAY;EAAA,IAAA,IAAAC,KAAA,CAAA;MACtB,OAAAA,CAAAA,KAAA,GAAQ9jB,KAAK,CAACC,OAAO,CAAC,IAAI,CAACiY,MAAM,CAAC,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,MAAM,KAAA,IAAA,GAAA4L,KAAA,GAAKF,QAAQ,CAAA;EAChF,GAAA;IAEAhkB,WAAWA,CAAE4C,IAAwB,EAAE;MACrC,KAAK,CAACA,IAA4B,CAAC,CAAA;EACrC,GAAA;IAES8I,aAAaA,CAAE9I,IAAoC,EAAE;EAC5D,IAAA,KAAK,CAAC8I,aAAa,CAAC9I,IAA4B,CAAC,CAAA;EACnD,GAAA;IAESqN,OAAOA,CAAErN,IAA6C,EAAE;EAAA,IAAA,IAAAuhB,KAAA,EAAAC,KAAA,EAAAC,aAAA,CAAA;MAC/D,MAAM;QAAE/L,MAAM;QAAE,GAAGE,SAAAA;EAAU,KAAC,GAAGrT,aAAa,CAACvC,IAAI,CAAQ,CAAC;EAC5D,IAAA,IAAI,CAAC0hB,UAAU,GAAG7jB,MAAM,CAACoC,MAAM,CAAC,EAAE,EAAE,IAAI,CAACyhB,UAAU,EAAE9L,SAAS,CAAC,CAAA;EAC/D,IAAA,MAAMrD,KAAK,GAAG3P,UAAU,CAAC,IAAI,CAAC8e,UAAU,CAAC,CAAA;MACzC,IAAI,CAAChM,MAAM,GAAA,CAAA6L,KAAA,GAAA,CAAAC,KAAA,GAAG9L,MAAM,IAANA,IAAAA,GAAAA,MAAM,GAAKnD,KAAK,CAASmD,MAAM,KAAA,IAAA,GAAA8L,KAAA,GAAI,IAAI,CAAC9L,MAAM,KAAA,IAAA,GAAA6L,KAAA,GAAIH,QAAQ,CAAC;;MAEzE,KAAK,CAAC/T,OAAO,CAAC;EACZ3L,MAAAA,IAAI,EAAE,GAAG,CAACgU,MAAM,CAAC/U,IAAI,CAACK,GAAG,CAAC,IAAI,CAACqgB,QAAQ,KAAKD,QAAQ,KAAA,CAAAK,aAAA,GAAI,IAAI,CAAC3P,OAAO,KAAA,IAAA,GAAA,KAAA,CAAA,GAAZ2P,aAAA,CAActiB,MAAM,CAAI,IAAA,CAAC,EAAE,IAAI,CAACgiB,UAAU,CAAC,CAAC;EACpGhM,MAAAA,MAAM,EAAE;EAAE2E,QAAAA,CAAC,EAAEvH,KAAAA;SAAO;QACpB3C,KAAK,EAAE2C,KAAK,CAAC3C,KAAK;QAClBR,SAAS,EAAEmD,KAAK,CAACnD,SAAS;QAC1BgB,WAAW,EAAEmC,KAAK,CAACnC,WAAW;QAC9BgE,IAAI,EAAG7B,KAAK,CAAmB6B,IAAI;QACnCF,eAAe,EAAG3B,KAAK,CAAmB2B,eAAe;QACzDC,WAAW,EAAG5B,KAAK,CAAmB4B,WAAAA;EACxC,KAAC,CAAC,CAAA;EACJ,GAAA;IAEAwN,cAAcA,CAAEvL,EAAU,EAA4B;EACpD,IAAA,IAAIA,EAAE,GAAG,IAAI,CAACtE,OAAO,CAAC3S,MAAM,EAAE,OAAO,IAAI,CAAC2S,OAAO,CAACsE,EAAE,CAAC,CAAA;EACrD,IAAA,IAAI,IAAI,CAACiL,QAAQ,KAAKD,QAAQ,IAAI,IAAI,CAACtP,OAAO,CAAC3S,MAAM,GAAG,IAAI,CAACkiB,QAAQ,EAAE;QACrE,IAAI,CAACvP,OAAO,CAAC5K,IAAI,CAACtE,UAAU,CAAC,IAAI,CAAC8e,UAAU,CAAC,CAAC,CAAA;QAC9C,IAAI,CAAChgB,IAAI,IAAI,GAAG,CAAA;QAChB,OAAO,IAAI,CAACoQ,OAAO,CAAC,IAAI,CAACA,OAAO,CAAC3S,MAAM,GAAG,CAAC,CAAC,CAAA;EAC9C,KAAA;EACF,GAAA;EAESkP,EAAAA,cAAcA,CAAEC,EAAU,EAAEX,KAAsC,EAAoB;EAAA,IAAA,IAA1DA,KAAsC,KAAA,KAAA,CAAA,EAAA;QAAtCA,KAAsC,GAAC,EAAE,CAAA;EAAA,KAAA;EAC5E,IAAA,MAAMlC,OAAO,GAAG,IAAIQ,aAAa,EAAE,CAAA;EAEnC,IAAA,KACE,IAAImK,EAAE,GAAA,CAAAwL,qBAAA,GAAAtL,CAAAA,oBAAA,GAAC,IAAI,CAAC5E,cAAc,CAAC,IAAI,CAACrI,YAAY,CAAClK,MAAM,CAAC,qBAA7CmX,oBAAA,CAA+C1E,KAAK,KAAA,IAAA,GAAAgQ,qBAAA,GAAIjhB,IAAI,CAACK,GAAG,CAAC,IAAI,CAAC8Q,OAAO,CAAC3S,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEoT,KAAK,EAAEsP,SAAS;EACrH;EACA;MACCtP,KAAK,GAAA,CAAAuP,gBAAA,GAAG,IAAI,CAAChQ,OAAO,CAACsE,EAAE,CAAC,KAAA0L,IAAAA,GAAAA,gBAAA,GAAKD,SAAS,GAAG,CAACA,SAAS,IAAI,IAAI,CAACF,cAAc,CAACvL,EAAE,CAAE,EAChF,EAAEA,EAAE,EACJ;EAAA,MAAA,IAAAwL,qBAAA,EAAAtL,oBAAA,EAAAwL,gBAAA,EAAAtN,qBAAA,CAAA;EACA,MAAA,MAAMiC,YAAY,GAAGlE,KAAK,CAAChE,WAAW,CAACD,EAAE,EAAE;EAAE,QAAA,GAAGX,KAAK;EAAE4B,QAAAA,gBAAgB,EAAAiF,CAAAA,qBAAA,GAAE7G,KAAK,CAAC4B,gBAAgB,KAAA,IAAA,IAAA,CAAAiF,qBAAA,GAAtBA,qBAAA,CAAwB1C,OAAO,KAA/B0C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAAkC4B,EAAE,CAAA;EAAE,OAAC,CAAC,CAAA;EAEjH,MAAA,IAAIK,YAAY,CAACnK,IAAI,IAAIuV,SAAS,EAAE;EAClC;EACA,QAAA,IAAI,CAAC/P,OAAO,CAACc,GAAG,EAAE,CAAA;UAClB,IAAI,CAAClR,IAAI,GAAG,IAAI,CAACA,IAAI,CAACvB,KAAK,CAAC,CAAC,CAAC,CAAA;EAC9B,QAAA,MAAA;EACF,OAAA;EAEAsL,MAAAA,OAAO,CAACc,SAAS,CAACkK,YAAY,CAAC,CAAA;EAE/B,MAAA,IAAIA,YAAY,CAACjK,QAAQ,EAAE,MAAM;EACnC,KAAA;EAEA,IAAA,OAAOf,OAAO,CAAA;EAChB,GAAA;EAEAsW,EAAAA,cAAcA,CAAE9T,OAAe,EAAIC,KAAc,EAAQ;MAAA,IAAA8T,qBAAA,EAAAC,qBAAA,CAAA;EAAA,IAAA,IAAzChU,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;MAC/B,MAAMiU,eAAe,GAAGvhB,IAAI,CAACK,GAAG,CAAC,CAAA,CAAAghB,qBAAA,GAAA,IAAI,CAACtQ,cAAc,CAACzD,OAAO,CAAC,KAA5B+T,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAA8BpQ,KAAK,KAAI,CAAC,EAAE,IAAI,CAACuP,UAAU,EAAE,CAAC,CAAC,CAAA;EAC9F,IAAA,IAAIgB,cAAc,CAAA;EAClB,IAAA,IAAIjU,KAAK,IAAI,IAAI,EAAEiU,cAAc,IAAAF,qBAAA,GAAG,IAAI,CAACvQ,cAAc,CAACxD,KAAK,CAAC,KAA1B+T,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAA,CAA4BrQ,KAAK,CAAA;EACrE,IAAA,IAAIuQ,cAAc,IAAI,IAAI,EAAEA,cAAc,GAAG,IAAI,CAACrQ,OAAO,CAAC3S,MAAM,GAAG,CAAC,CAAA;MAEpE,IAAIijB,WAAW,GAAG,CAAC,CAAA;EACnB,IAAA,KAAK,IAAI7Q,UAAU,GAAG4Q,cAAc,EAAED,eAAe,IAAI3Q,UAAU,EAAE,EAAEA,UAAU,EAAE,EAAE6Q,WAAW,EAAE;QAChG,IAAI,IAAI,CAACtQ,OAAO,CAACP,UAAU,CAAC,CAACxI,aAAa,EAAE,MAAA;EAC9C,KAAA;EAEA,IAAA,IAAIqZ,WAAW,EAAE;EACf,MAAA,IAAI,CAACtQ,OAAO,CAACtG,MAAM,CAAC2W,cAAc,GAAGC,WAAW,GAAG,CAAC,EAAEA,WAAW,CAAC,CAAA;QAClE,IAAI,CAAC1gB,IAAI,GAAG,IAAI,CAACA,IAAI,CAACvB,KAAK,CAACiiB,WAAW,CAAC,CAAA;EAC1C,KAAA;EACF,GAAA;EAES3U,EAAAA,KAAKA,GAAI;MAChB,KAAK,CAACA,KAAK,EAAE,CAAA;MACb,IAAI,CAACsU,cAAc,EAAE,CAAA;EACvB,GAAA;EAESlS,EAAAA,MAAMA,CAAE5B,OAAe,EAAIC,KAAa,EAA0C;EAAA,IAAA,IAA1ED,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;EAAA,IAAA,IAAEC,KAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,MAAAA,KAAa,GAAC,IAAI,CAAC7E,YAAY,CAAClK,MAAM,CAAA;EAAA,KAAA;MACxE,MAAM0Y,aAAa,GAAG,KAAK,CAAChI,MAAM,CAAC5B,OAAO,EAAEC,KAAK,CAAC,CAAA;EAClD,IAAA,IAAI,CAAC6T,cAAc,CAAC9T,OAAO,EAAEC,KAAK,CAAC,CAAA;EACnC,IAAA,OAAO2J,aAAa,CAAA;EACtB,GAAA;EAES7J,EAAAA,mBAAmBA,CAAEC,OAAe,EAAIC,KAAc,EAAU;EAAA,IAAA,IAA3CD,OAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,MAAAA,OAAe,GAAC,CAAC,CAAA;EAAA,KAAA;MAC7C,IAAIC,KAAK,IAAI,IAAI,IAAI,IAAI,CAACmT,QAAQ,KAAKD,QAAQ,EAAE,OAAOA,QAAQ,CAAA;EAChE,IAAA,OAAO,KAAK,CAACpT,mBAAmB,CAACC,OAAO,EAAEC,KAAK,CAAC,CAAA;EAClD,GAAA;IAEA,IAAa/G,KAAKA,GAAwB;MACxC,OAAO,KAAK,CAACA,KAAK,CAAA;EACpB,GAAA;IAEA,IAAaA,KAAKA,CAAEA,KAAyB,EAAE;MAC7C,IAAI,CAAC2K,OAAO,CAAC3S,MAAM,GAAGgI,KAAK,CAAC2K,OAAO,CAAC3S,MAAM,CAAA;EAC1C,IAAA,IAAI,CAACuC,IAAI,GAAG,IAAI,CAACA,IAAI,CAACvB,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC2R,OAAO,CAAC3S,MAAM,CAAC,CAAA;MACnD,KAAK,CAACgI,KAAK,GAAGA,KAAK,CAAA;EACrB,GAAA;EACF,CAAA;EAGA7F,KAAK,CAACwU,WAAW,GAAGA,WAAW;;ECnG/B,IAAI;IAAGuM,UAAU,CAAS/gB,KAAK,GAAGA,KAAK,CAAA;EAAE,CAAC,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}