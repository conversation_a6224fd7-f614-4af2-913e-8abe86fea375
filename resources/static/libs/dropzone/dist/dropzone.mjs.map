{"mappings": ";;;;;;MCIqB,wCAAO;IAC1B,EAAwC,AAAxC,sCAAwC;IACxC,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC;QACb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;QAAA,CAAC;QACvC,EAAkC,AAAlC,gCAAkC;QAClC,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,GACxB,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,CAAC;QAE7B,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;QAC9B,MAAM,CAAC,IAAI;IACb,CAAC;IAED,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;QAAA,CAAC;QACvC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK;QAErC,EAAE,EAAE,SAAS,EACX,GAAG,EAAE,GAAG,CAAC,QAAQ,IAAI,SAAS,CAC5B,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI;QAG7B,EAAoC,AAApC,kCAAoC;QACpC,EAAE,EAAE,IAAI,CAAC,OAAO,EACd,IAAI,CAAC,OAAO,CAAC,aAAa,CACxB,IAAI,CAAC,SAAS,CAAC,CAAW,aAAG,KAAK,EAAE,CAAC;YAAC,IAAI,EAAE,IAAI;QAAC,CAAC;QAGtD,MAAM,CAAC,IAAI;IACb,CAAC;IAED,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC;QAC5B,GAAG,CAAC,MAAM,GAAG,CAAC;YAAC,OAAO,EAAE,IAAI;YAAE,UAAU,EAAE,IAAI;YAAE,MAAM,EAAE,MAAM;QAAC,CAAC;QAEhE,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,WAAW,KAAK,CAAU,WAC1C,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM;aACnC,CAAC;YACN,EAAgB,AAAhB,cAAgB;YAChB,EAA2E,AAA3E,yEAA2E;YAC3E,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAa;YAC5C,GAAG,CAAC,eAAe,CACjB,SAAS,EACT,MAAM,CAAC,OAAO,EACd,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,MAAM;YAEf,MAAM,CAAC,GAAG;QACZ,CAAC;IACH,CAAC;IAED,EAA0E,AAA1E,wEAA0E;IAC1E,EAAwE,AAAxE,sEAAwE;IACxE,EAAmC,AAAnC,iCAAmC;IACnC,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC;QACd,EAAE,GAAG,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/C,IAAI,CAAC,UAAU,GAAG,CAAC;YAAA,CAAC;YACpB,MAAM,CAAC,IAAI;QACb,CAAC;QAED,EAAiB,AAAjB,eAAiB;QACjB,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK;QACrC,EAAE,GAAG,SAAS,EACZ,MAAM,CAAC,IAAI;QAGb,EAAsB,AAAtB,oBAAsB;QACtB,EAAE,EAAE,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK;YAC5B,MAAM,CAAC,IAAI;QACb,CAAC;QAED,EAA0B,AAA1B,wBAA0B;QAC1B,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;YAC1C,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC;YAC1B,EAAE,EAAE,QAAQ,KAAK,EAAE,EAAE,CAAC;gBACpB,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;gBACrB,KAAK;YACP,CAAC;QACH,CAAC;QAED,MAAM,CAAC,IAAI;IACb,CAAC;;;;;;AEpFH,yBAAc,GAAG,CAAkB;;;ADGnC,GAAG,CAAC,oCAAc,GAAG,CAAC;IACpB,EAMG,AANH;;;;;;GAMG,AANH,EAMG,CACH,GAAG,EAAE,IAAI;IAET,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,MAAM,EAAE,CAAM;IAEd,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,eAAe,EAAE,KAAK;IAEtB,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,OAAO,EAAE,IAAI;IAEb,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,eAAe,EAAE,CAAC;IAElB,EAMG,AANH;;;;;;GAMG,AANH,EAMG,CACH,cAAc,EAAE,KAAK;IAErB,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,QAAQ,EAAE,KAAK;IAEf,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,aAAa,EAAE,KAAK;IAEpB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,SAAS,EAAE,OAAe;IAE1B,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,oBAAoB,EAAE,KAAK;IAE3B,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,WAAW,EAAE,KAAK;IAElB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,gBAAgB,EAAE,CAAC;IAEnB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,WAAW,EAAE,GAAG;IAEhB,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,SAAS,EAAE,CAAM;IAEjB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,qBAAqB,EAAE,IAAI;IAE3B,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,oBAAoB,EAAE,EAAE;IAExB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,cAAc,EAAE,GAAG;IAEnB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,eAAe,EAAE,GAAG;IAEpB,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,eAAe,EAAE,CAAM;IAEvB,EAOG,AAPH;;;;;;;GAOG,AAPH,EAOG,CACH,WAAW,EAAE,IAAI;IAEjB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,YAAY,EAAE,IAAI;IAElB,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,cAAc,EAAE,IAAI;IAEpB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,aAAa,EAAE,GAAG;IAElB,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,YAAY,EAAE,CAAS;IAEvB,EAMG,AANH;;;;;;GAMG,AANH,EAMG,CACH,YAAY,EAAE,IAAI;IAElB,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,QAAQ,EAAE,IAAI;IAEd,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,OAAO,EAAE,IAAI;IAEb,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,cAAc,EAAE,IAAI;IAEpB,EAOG,AAPH;;;;;;;GAOG,AAPH,EAOG,CACH,SAAS,EAAE,IAAI;IAEf,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,iBAAiB,EAAE,IAAI;IAEvB,EAUG,AAVH;;;;;;;;;;GAUG,AAVH,EAUG,CACH,aAAa,EAAE,IAAI;IAEnB,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,iBAAiB,EAAE,IAAI;IAEvB,EASG,AATH;;;;;;;;;GASG,AATH,EASG,CACH,gBAAgB,EAAE,IAAI;IAEtB,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,SAAS,EAAE,IAAI;IAEf,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,cAAc,EAAE,KAAK;IAErB,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,iBAAiB,EAAE,IAAI;IAEvB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,eAAe,EAAE,KAAK;IAEtB,EAMG,AANH;;;;;;GAMG,AANH,EAMG,CACH,oBAAoB,EAAE,CAAM;IAE5B,EAOG,AAPH;;;;;;;GAOG,AAPH,EAOG,CACH,OAAO,EAAE,IAAI;IAEb,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,cAAc,EAAE,IAAI;IAEpB,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,UAAU,EAAE,IAAI;IAEhB,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,aAAa,EAAE,KAAK;IAEpB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,kBAAkB,EAAE,CAA2B;IAE/C,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,mBAAmB,EACjB,CAAyD;IAE3D,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,gBAAgB,EACd,CAAiF;IAEnF,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,cAAc,EACZ,CAAsE;IAExE,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,mBAAmB,EAAE,CAAsC;IAE3D,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,iBAAiB,EAAE,CAA4C;IAE/D,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,gBAAgB,EAAE,CAAe;IAEjC,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,kBAAkB,EAAE,CAAkB;IAEtC,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,4BAA4B,EAAE,CAA8C;IAE5E,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,cAAc,EAAE,CAAa;IAE7B,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,0BAA0B,EAAE,IAAI;IAEhC,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,oBAAoB,EAAE,CAAoC;IAE1D,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,iBAAiB,EAAE,CAAC;QAAC,EAAE,EAAE,CAAI;QAAE,EAAE,EAAE,CAAI;QAAE,EAAE,EAAE,CAAI;QAAE,EAAE,EAAE,CAAI;QAAE,CAAC,EAAE,CAAG;IAAC,CAAC;IACrE,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,IAAI,IAAG,CAAC;IAAA,CAAC;IAET,EASG,AATH;;;;;;;;;GASG,AATH,EASG,CACH,MAAM,EAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;QACzB,EAAE,EAAE,KAAK,EACP,MAAM,CAAC,CAAC;YACN,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI;YAC9B,YAAY,EAAE,KAAK,CAAC,KAAK;YACzB,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;YAChC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;YACnC,iBAAiB,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe;YACpD,iBAAiB,EAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;QACzD,CAAC;IAEL,CAAC;IAED,EAQG,AARH;;;;;;;;GAQG,AARH,EAQG,CACH,MAAM,EAAC,IAAI,EAAE,IAAI,EAAE,CAAC;QAClB,MAAM,CAAC,IAAI;IACb,CAAC;IAED,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,cAAc,EAAE,QAAQ,CAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACrC,IAAI;IACN,CAAC;IAED,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,UAAU,EAAE,KAAK;IAEjB,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,QAAQ,IAAG,CAAC;QACV,EAAqC,AAArC,mCAAqC;QACrC,GAAG,CAAC,cAAc;QAClB,IAAI,CAAC,OAAO,CAAC,SAAS,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,yBAAyB;QAE5E,GAAG,EAAE,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAK,MACvD,EAAE,yBAAyB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC;YACjD,cAAc,GAAG,KAAK;YACtB,KAAK,CAAC,SAAS,GAAG,CAAY,YAAE,CAAiC,AAAjC,EAAiC,AAAjC,+BAAiC;YACjE,KAAK;QACP,CAAC;QAEH,EAAE,GAAG,cAAc,EAAE,CAAC;YACpB,cAAc,GAAG,wCAAQ,CAAC,aAAa,CACrC,CAA6C;YAE/C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,cAAc;QACzC,CAAC;QAED,GAAG,CAAC,IAAI,GAAG,cAAc,CAAC,oBAAoB,CAAC,CAAM,OAAE,CAAC;QACxD,EAAE,EAAE,IAAI,EAAE,CAAC;YACT,EAAE,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI,EAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB;iBAC9C,EAAE,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,EAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB;QAErD,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe;IACtD,CAAC;IAED,EAWG,AAXH;;;;;;;;;;;GAWG,AAXH,EAWG,CACH,MAAM,EAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;QACzC,GAAG,CAAC,IAAI,GAAG,CAAC;YACV,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,IAAI,CAAC,KAAK;YACpB,SAAS,EAAE,IAAI,CAAC,MAAM;QACxB,CAAC;QAED,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM;QAEvC,EAAsD,AAAtD,oDAAsD;QACtD,EAAE,EAAE,KAAK,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACpC,KAAK,GAAG,IAAI,CAAC,QAAQ;YACrB,MAAM,GAAG,IAAI,CAAC,SAAS;QACzB,CAAC,MAAM,EAAE,EAAE,KAAK,IAAI,IAAI,EACtB,KAAK,GAAG,MAAM,GAAG,QAAQ;aACpB,EAAE,EAAE,MAAM,IAAI,IAAI,EACvB,MAAM,GAAG,KAAK,GAAG,QAAQ;QAG3B,EAAmC,AAAnC,iCAAmC;QACnC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ;QACrC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS;QAExC,GAAG,CAAC,QAAQ,GAAG,KAAK,GAAG,MAAM;QAE7B,EAAE,EAAE,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,IAAI,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC;YACrD,EAAsC,AAAtC,oCAAsC;YACtC,EAAE,EAAE,YAAY,KAAK,CAAM;gBACzB,EAAE,EAAE,QAAQ,GAAG,QAAQ,EAAE,CAAC;oBACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM;oBAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,QAAQ;gBAC3C,CAAC,MAAM,CAAC;oBACN,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK;oBAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ;gBAC3C,CAAC;mBACI,EAAE,EAAE,YAAY,KAAK,CAAS;gBACnC,EAAmB,AAAnB,iBAAmB;gBACnB,EAAE,EAAE,QAAQ,GAAG,QAAQ,EACrB,MAAM,GAAG,KAAK,GAAG,QAAQ;qBAEzB,KAAK,GAAG,MAAM,GAAG,QAAQ;mBAG3B,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,sBAAsB,EAAE,YAAY,CAAC,CAAC;QAE3D,CAAC;QAED,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC;QAC5C,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC;QAE9C,IAAI,CAAC,QAAQ,GAAG,KAAK;QACrB,IAAI,CAAC,SAAS,GAAG,MAAM;QAEvB,MAAM,CAAC,IAAI;IACb,CAAC;IAED,EAQG,AARH;;;;;;;;GAQG,AARH,EAQG,CACH,aAAa,EAAC,IAAI,EAAE,IAAI,EAAE,CAAC;QACzB,EAAE,GACC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,KACtD,IAAI,CAAC,IAAI,CAAC,KAAK,aAEf,MAAM,CAAC,IAAI,CAAC,WAAW,CACrB,IAAI,EACJ,IAAI,CAAC,OAAO,CAAC,WAAW,EACxB,IAAI,CAAC,OAAO,CAAC,YAAY,EACzB,IAAI,CAAC,OAAO,CAAC,YAAY,EACzB,IAAI;aAGN,MAAM,CAAC,IAAI,CAAC,IAAI;IAEpB,CAAC;IAED,EAaG,AAbH;;;;;;;;;;;;;GAaG,AAbH,EAaG,CACH,eAAe,EAAE,gEAAsB;IAEvC,EAOG,AAPH;;;;;;;GAOG,AAPH,EAOG,CAEH,EAA+D,AAA/D,6DAA+D;IAC/D,IAAI,EAAC,CAAC,EAAE,CAAC;QACP,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAe;IACtD,CAAC;IACD,SAAS,EAAC,CAAC,EAAE,CAAC;IAAA,CAAC;IACf,OAAO,EAAC,CAAC,EAAE,CAAC;QACV,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAe;IACtD,CAAC;IACD,SAAS,EAAC,CAAC,EAAE,CAAC;QACZ,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAe;IACnD,CAAC;IACD,QAAQ,EAAC,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAe;IACnD,CAAC;IACD,SAAS,EAAC,CAAC,EAAE,CAAC;QACZ,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAe;IACtD,CAAC;IAED,KAAK,EAAC,CAAC,EAAE,CAAC;IAAA,CAAC;IAEX,EAA2E,AAA3E,yEAA2E;IAC3E,EAA2D,AAA3D,yDAA2D;IAC3D,KAAK,IAAG,CAAC;QACP,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAY;IACnD,CAAC;IAED,EAA2C,AAA3C,yCAA2C;IAC3C,EAAkB,AAAlB,gBAAkB;IAClB,SAAS,EAAC,IAAI,EAAE,CAAC;QACf,EAAE,EAAE,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,iBAAiB,EACzC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAY;QAGzC,EAAE,EAAE,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5D,IAAI,CAAC,cAAc,GAAG,wCAAQ,CAAC,aAAa,CAC1C,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI;YAEnC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAE,CAA0B,AAA1B,EAA0B,AAA1B,wBAA0B;YAEtE,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc;YACtD,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAgB,iBACpE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI;YAE9B,GAAG,EAAE,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAgB,iBAChE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;YAG1C,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAChC,IAAI,CAAC,WAAW,GAAG,wCAAQ,CAAC,aAAa,EACtC,iEAAiE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI;gBAEtG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW;YAClD,CAAC;YAED,GAAG,CAAC,eAAe,IAAI,CAAC,GAAK,CAAC;gBAC5B,CAAC,CAAC,cAAc;gBAChB,CAAC,CAAC,eAAe;gBACjB,EAAE,EAAE,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,SAAS,EACpC,MAAM,CAAC,wCAAQ,CAAC,OAAO,CACrB,IAAI,CAAC,OAAO,CAAC,4BAA4B,MACnC,IAAI,CAAC,UAAU,CAAC,IAAI;;qBAEvB,CAAC;oBACN,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,0BAA0B,EACzC,MAAM,CAAC,wCAAQ,CAAC,OAAO,CACrB,IAAI,CAAC,OAAO,CAAC,0BAA0B,MACjC,IAAI,CAAC,UAAU,CAAC,IAAI;;yBAG5B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI;gBAE/B,CAAC;YACH,CAAC;YAED,GAAG,EAAE,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CACzD,CAAkB,mBAElB,UAAU,CAAC,gBAAgB,CAAC,CAAO,QAAE,eAAe;QAExD,CAAC;IACH,CAAC;IAED,EAAqC,AAArC,mCAAqC;IACrC,WAAW,EAAC,IAAI,EAAE,CAAC;QACjB,EAAE,EAAE,IAAI,CAAC,cAAc,IAAI,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,IAAI,IAAI,EACvE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc;QAEhE,MAAM,CAAC,IAAI,CAAC,2BAA2B;IACzC,CAAC;IAED,EAA6C,AAA7C,2CAA6C;IAC7C,EAAgC,AAAhC,8BAAgC;IAChC,SAAS,EAAC,IAAI,EAAE,OAAO,EAAE,CAAC;QACxB,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAiB;YACtD,GAAG,EAAE,GAAG,CAAC,gBAAgB,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAC/D,CAAqB,sBACpB,CAAC;gBACF,gBAAgB,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI;gBAChC,gBAAgB,CAAC,GAAG,GAAG,OAAO;YAChC,CAAC;YAED,MAAM,CAAC,UAAU,KACT,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAkB;cAC1D,CAAC;QAEL,CAAC;IACH,CAAC;IAED,EAAkC,AAAlC,gCAAkC;IAClC,EAAgC,AAAhC,8BAAgC;IAChC,KAAK,EAAC,IAAI,EAAE,OAAO,EAAE,CAAC;QACpB,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAU;YAC5C,EAAE,EAAE,MAAM,CAAC,OAAO,KAAK,CAAQ,WAAI,OAAO,CAAC,KAAK,EAC9C,OAAO,GAAG,OAAO,CAAC,KAAK;YAEzB,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CACnD,CAAwB,yBAExB,IAAI,CAAC,WAAW,GAAG,OAAO;QAE9B,CAAC;IACH,CAAC;IAED,aAAa,IAAG,CAAC;IAAA,CAAC;IAElB,EAAyE,AAAzE,uEAAyE;IACzE,EAAmC,AAAnC,iCAAmC;IACnC,EAAkB,AAAlB,gBAAkB;IAClB,UAAU,EAAC,IAAI,EAAE,CAAC;QAChB,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAe;YACjD,EAAE,EAAE,IAAI,CAAC,WAAW,EAClB,MAAM,CAAE,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB;QAEtE,CAAC;IACH,CAAC;IAED,kBAAkB,IAAG,CAAC;IAAA,CAAC;IAEvB,EAAoD,AAApD,kDAAoD;IACpD,EAAkE,AAAlE,gEAAkE;IAClE,EAAgE,AAAhE,8DAAgE;IAChE,cAAc,EAAC,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;QACzC,EAAE,EAAE,IAAI,CAAC,cAAc,EACrB,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CACnD,CAA0B,2BAE1B,IAAI,CAAC,QAAQ,KAAK,CAAU,YACvB,IAAI,CAAC,KAAK,GAAG,QAAQ,GACrB,IAAI,CAAC,KAAK,CAAC,KAAK,MAAM,QAAQ,CAAC,CAAC;IAG3C,CAAC;IAED,EAA0D,AAA1D,wDAA0D;IAC1D,EAAyE,AAAzE,uEAAyE;IACzE,mBAAmB,IAAG,CAAC;IAAA,CAAC;IAExB,EAAuE,AAAvE,qEAAuE;IACvE,EAA0E,AAA1E,wEAA0E;IAC1E,EAAmD,AAAnD,iDAAmD;IACnD,OAAO,IAAG,CAAC;IAAA,CAAC;IAEZ,eAAe,IAAG,CAAC;IAAA,CAAC;IAEpB,EAAsD,AAAtD,oDAAsD;IACtD,EAAkB,AAAlB,gBAAkB;IAClB,OAAO,EAAC,IAAI,EAAE,CAAC;QACb,EAAE,EAAE,IAAI,CAAC,cAAc,EACrB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAY;IAEzD,CAAC;IAED,eAAe,IAAG,CAAC;IAAA,CAAC;IAEpB,EAA+B,AAA/B,6BAA+B;IAC/B,QAAQ,EAAC,IAAI,EAAE,CAAC;QACd,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAO,QAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB;IACjE,CAAC;IAED,gBAAgB,IAAG,CAAC;IAAA,CAAC;IAErB,EAAgE,AAAhE,8DAAgE;IAChE,EAAkB,AAAlB,gBAAkB;IAClB,QAAQ,EAAC,IAAI,EAAE,CAAC;QACd,EAAE,EAAE,IAAI,CAAC,WAAW,EAClB,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc;QAE1D,EAAE,EAAE,IAAI,CAAC,cAAc,EACrB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAa;IAE1D,CAAC;IAED,gBAAgB,IAAG,CAAC;IAAA,CAAC;IAErB,gBAAgB,IAAG,CAAC;IAAA,CAAC;IAErB,eAAe,IAAG,CAAC;IAAA,CAAC;IAEpB,aAAa,IAAG,CAAC;IAAA,CAAC;IAElB,UAAU,IAAG,CAAC;IAAA,CAAC;AACjB,CAAC;IAED,wCAA8B,GAAf,oCAAc;;;MFhxBR,wCAAQ,SAAS,wCAAO;WACpC,SAAS,GAAG,CAAC;QAClB,EAA+C,AAA/C,6CAA+C;QAC/C,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,wCAAO;QAEhC,EAOG,AAPH;;;;;;;KAOG,AAPH,EAOG,CACH,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;YACvB,CAAM;YACN,CAAW;YACX,CAAS;YACT,CAAW;YACX,CAAU;YACV,CAAW;YACX,CAAW;YACX,CAAY;YACZ,CAAa;YACb,CAAW;YACX,CAAO;YACP,CAAe;YACf,CAAY;YACZ,CAAoB;YACpB,CAAgB;YAChB,CAAqB;YACrB,CAAS;YACT,CAAiB;YACjB,CAAS;YACT,CAAiB;YACjB,CAAU;YACV,CAAkB;YAClB,CAAU;YACV,CAAkB;YAClB,CAAO;YACP,CAAkB;YAClB,CAAiB;YACjB,CAAe;QACjB,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC,oBAAoB,GAAG,KAAK;IAC7C,CAAC;IAyHD,EAA4C,AAA5C,0CAA4C;IAC5C,gBAAgB,GAAG,CAAC;QAClB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,GAAK,IAAI,CAAC,QAAQ;UAAE,GAAG,EAAE,IAAI,GAAK,IAAI;;IACtE,CAAC;IAED,EAA4C,AAA5C,0CAA4C;IAC5C,EAAuE,AAAvE,qEAAuE;IACvE,gBAAgB,GAAG,CAAC;QAClB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,IAAM,IAAI,CAAC,QAAQ;UAAE,GAAG,EAAE,IAAI,GAAK,IAAI;;IACvE,CAAC;IAED,kBAAkB,CAAC,MAAM,EAAE,CAAC;QAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CACd,MAAM,EAAE,IAAI,GAAK,IAAI,CAAC,MAAM,KAAK,MAAM;UACvC,GAAG,EAAE,IAAI,GAAK,IAAI;;IACvB,CAAC;IAED,EAA0C,AAA1C,wCAA0C;IAC1C,cAAc,GAAG,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,wCAAQ,CAAC,MAAM;IAChD,CAAC;IAED,iBAAiB,GAAG,CAAC;QACnB,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,wCAAQ,CAAC,SAAS;IACnD,CAAC;IAED,aAAa,GAAG,CAAC;QACf,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,wCAAQ,CAAC,KAAK;IAC/C,CAAC;IAED,EAA4C,AAA5C,0CAA4C;IAC5C,cAAc,GAAG,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,KAAK,CACd,MAAM,EACJ,IAAI,GACH,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,MAAM;UAExE,GAAG,EAAE,IAAI,GAAK,IAAI;;IACvB,CAAC;IAED,EAAkE,AAAlE,gEAAkE;IAClE,EAA+D,AAA/D,6DAA+D;IAC/D,IAAI,GAAG,CAAC;QACN,EAA+B,AAA/B,6BAA+B;QAC/B,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAM,OACjC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAS,UAAE,CAAqB;QAG5D,EAAE,EACA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAU,eACzC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAa,eAEzC,IAAI,CAAC,OAAO,CAAC,WAAW,CACtB,wCAAQ,CAAC,aAAa,EACnB,2EAA2E,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,eAAe;QAKnI,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;YAClC,GAAG,CAAC,oBAAoB,OAAS,CAAC;gBAChC,EAAE,EAAE,IAAI,CAAC,eAAe,EACtB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe;gBAElE,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAO;gBACrD,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAM,OAAE,CAAM;gBAChD,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,EAC7D,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAU,WAAE,CAAU;gBAE1D,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,CAAiB;gBAElD,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,IAAI,EACrC,IAAI,CAAC,eAAe,CAAC,YAAY,CAC/B,CAAQ,SACR,IAAI,CAAC,OAAO,CAAC,aAAa;gBAG9B,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,EAC/B,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAS,UAAE,IAAI,CAAC,OAAO,CAAC,OAAO;gBAGnE,EAAqD,AAArD,mDAAqD;gBACrD,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAU,WAAE,CAAI;gBAElD,EAAyE,AAAzE,uEAAyE;gBACzE,EAAqC,AAArC,mCAAqC;gBACrC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,UAAU,GAAG,CAAQ;gBAChD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAU;gBAChD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,CAAG;gBACpC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,GAAG,CAAG;gBACrC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,CAAG;gBACvC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,CAAG;gBACtC,wCAAQ,CAAC,UAAU,CACjB,IAAI,CAAC,OAAO,CAAC,oBAAoB,EACjC,CAAsB,uBACtB,WAAW,CAAC,IAAI,CAAC,eAAe;gBAClC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAQ,aAAQ,CAAC;oBACrD,GAAG,CAAC,CAAC,QAAC,KAAK,EAAC,CAAC,GAAG,IAAI,CAAC,eAAe;oBACpC,EAAE,EAAE,KAAK,CAAC,MAAM,EACd,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK,CACpB,IAAI,CAAC,OAAO,CAAC,IAAI;oBAGrB,IAAI,CAAC,IAAI,CAAC,CAAY,aAAE,KAAK;oBAC7B,oBAAoB;gBACtB,CAAC;YACH,CAAC;YACD,oBAAoB;QACtB,CAAC;QAED,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,KAAK,IAAI,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS;QAE9D,EAA2D,AAA3D,yDAA2D;QAC3D,EAA0E,AAA1E,wEAA0E;QAC1E,EAAyC,AAAzC,uCAAyC;QACzC,GAAG,EAAE,GAAG,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAC/B,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;QAG3C,IAAI,CAAC,EAAE,CAAC,CAAgB,qBAAQ,IAAI,CAAC,yBAAyB;;QAE9D,IAAI,CAAC,EAAE,CAAC,CAAa,kBAAQ,IAAI,CAAC,yBAAyB;;QAE3D,IAAI,CAAC,EAAE,CAAC,CAAU,YAAG,IAAI,GAAK,IAAI,CAAC,IAAI,CAAC,CAAU,WAAE,IAAI;;QAExD,EAAgE,AAAhE,8DAAgE;QAChE,IAAI,CAAC,EAAE,CAAC,CAAU,YAAG,IAAI,GAAK,CAAC;YAC7B,EAAE,EACA,IAAI,CAAC,aAAa,GAAG,MAAM,KAAK,CAAC,IACjC,IAAI,CAAC,iBAAiB,GAAG,MAAM,KAAK,CAAC,IACrC,IAAI,CAAC,cAAc,GAAG,MAAM,KAAK,CAAC,EAElC,EAAqF,AAArF,mFAAqF;YACrF,MAAM,CAAC,UAAU,KAAO,IAAI,CAAC,IAAI,CAAC,CAAe;cAAG,CAAC;QAEzD,CAAC;QAED,KAAK,CAAC,aAAa,GAAG,QAAQ,CAAE,CAAC,EAAE,CAAC;YAClC,EAAE,EAAE,CAAC,CAAC,YAAY,CAAC,KAAK,EACtB,EAA+C,AAA/C,6CAA+C;YAC/C,EAA8C,AAA9C,4CAA8C;YAC9C,EAAoC,AAApC,kCAAoC;YACpC,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;gBACrD,EAAE,EAAE,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAO,QAAE,MAAM,CAAC,IAAI;YACtD,CAAC;YAEH,MAAM,CAAC,KAAK;QACd,CAAC;QAED,GAAG,CAAC,aAAa,GAAG,QAAQ,CAAE,CAAC,EAAE,CAAC;YAChC,EAA+C,AAA/C,6CAA+C;YAC/C,EAA+C,AAA/C,6CAA+C;YAC/C,EAA2B,AAA3B,yBAA2B;YAC3B,EAAE,GAAG,aAAa,CAAC,CAAC,GAAG,MAAM;YAC7B,CAAC,CAAC,eAAe;YACjB,EAAE,EAAE,CAAC,CAAC,cAAc,EAClB,MAAM,CAAC,CAAC,CAAC,cAAc;iBAEvB,MAAM,CAAE,CAAC,CAAC,WAAW,GAAG,KAAK;QAEjC,CAAC;QAED,EAAuB,AAAvB,qBAAuB;QACvB,IAAI,CAAC,SAAS,GAAG,CAAC;YAChB,CAAC;gBACC,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM,EAAE,CAAC;oBACP,SAAS,GAAG,CAAC,GAAK,CAAC;wBACjB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAW,YAAE,CAAC;oBACjC,CAAC;oBACD,SAAS,GAAG,CAAC,GAAK,CAAC;wBACjB,aAAa,CAAC,CAAC;wBACf,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAW,YAAE,CAAC;oBACjC,CAAC;oBACD,QAAQ,GAAG,CAAC,GAAK,CAAC;wBAChB,EAA6D,AAA7D,2DAA6D;wBAC7D,EAAmG,AAAnG,iGAAmG;wBACnG,EAAiF,AAAjF,+EAAiF;wBACjF,GAAG,CAAC,IAAI;wBACR,GAAG,CAAC,CAAC;4BACH,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,aAAa;wBACrC,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;wBAAA,CAAC;wBAClB,CAAC,CAAC,YAAY,CAAC,UAAU,GACvB,CAAM,UAAK,IAAI,IAAI,CAAU,cAAK,IAAI,GAAG,CAAM,QAAG,CAAM;wBAE1D,aAAa,CAAC,CAAC;wBACf,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAU,WAAE,CAAC;oBAChC,CAAC;oBACD,SAAS,GAAG,CAAC,GAAK,CAAC;wBACjB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAW,YAAE,CAAC;oBACjC,CAAC;oBACD,IAAI,GAAG,CAAC,GAAK,CAAC;wBACZ,aAAa,CAAC,CAAC;wBACf,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACpB,CAAC;oBACD,OAAO,GAAG,CAAC,GAAK,CAAC;wBACf,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAS,UAAE,CAAC;oBAC/B,CAAC;gBACH,CAAC;YAMH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,gBAAgB,GAAK,CAAC;YACpD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC1B,OAAO,EAAE,gBAAgB;gBACzB,MAAM,EAAE,CAAC;oBACP,KAAK,GAAG,GAAG,GAAK,CAAC;wBACf,EAAgF,AAAhF,8EAAgF;wBAChF,EAAE,EACA,gBAAgB,KAAK,IAAI,CAAC,OAAO,IACjC,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC,OAAO,IAC3B,wCAAQ,CAAC,aAAa,CACpB,GAAG,CAAC,MAAM,EACV,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAa,gBAG1C,IAAI,CAAC,eAAe,CAAC,KAAK,GAAI,CAAoB,AAApB,EAAoB,AAApB,kBAAoB;wBAEpD,MAAM,CAAC,IAAI;oBACb,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM;QAEX,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;IACpC,CAAC;IAED,EAAuB,AAAvB,qBAAuB;IACvB,OAAO,GAAG,CAAC;QACT,IAAI,CAAC,OAAO;QACZ,IAAI,CAAC,cAAc,CAAC,IAAI;QACxB,EAAE,EACA,IAAI,CAAC,eAAe,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,SAAS,EAC1E,CAAC;YACD,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe;YAChE,IAAI,CAAC,eAAe,GAAG,IAAI;QAC7B,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;QAC5B,MAAM,CAAC,wCAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,wCAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC;IACtE,CAAC;IAED,yBAAyB,GAAG,CAAC;QAC3B,GAAG,CAAC,mBAAmB;QACvB,GAAG,CAAC,cAAc,GAAG,CAAC;QACtB,GAAG,CAAC,UAAU,GAAG,CAAC;QAElB,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc;QAErC,EAAE,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC;YACvB,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,cAAc,GAAI,CAAC;gBACvC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;gBACvC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;YACjC,CAAC;YACD,mBAAmB,GAAI,GAAG,GAAG,cAAc,GAAI,UAAU;QAC3D,CAAC,MACC,mBAAmB,GAAG,GAAG;QAG3B,MAAM,CAAC,IAAI,CAAC,IAAI,CACd,CAAqB,sBACrB,mBAAmB,EACnB,UAAU,EACV,cAAc;IAElB,CAAC;IAED,EAAkF,AAAlF,gFAAkF;IAClF,EAAuF,AAAvF,qFAAuF;IACvF,aAAa,CAAC,CAAC,EAAE,CAAC;QAChB,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,CAAU,WAC9C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;aAE/B,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAC9B,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAE;IAGjD,CAAC;IAED,EAAwC,AAAxC,sCAAwC;IACxC,EAAwF,AAAxF,sFAAwF;IACxF,WAAW,CAAC,IAAI,EAAE,CAAC;QACjB,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,CAAU,WAC/C,MAAM,CAAC,IAAI,CAAC,IAAI;QAElB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI;IACrC,CAAC;IAED,EAAwF,AAAxF,sFAAwF;IACxF,EAAE;IACF,EAAkI,AAAlI,gIAAkI;IAClI,EAAkC,AAAlC,gCAAkC;IAClC,eAAe,GAAG,CAAC;QACjB,GAAG,CAAC,gBAAgB,EAAE,IAAI;QAC1B,EAAE,EAAG,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,IAC9C,MAAM,CAAC,gBAAgB;QAGzB,GAAG,CAAC,YAAY,GAAG,CAA2B;QAC9C,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC/B,YAAY,KAAK,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI;QAE1D,YAAY,KAAK,yBAAyB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,EAClE,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,CAAqB,uBAAG,SAAS,CAChE,8CAA8C;QAE/C,GAAG,CAAC,MAAM,GAAG,wCAAQ,CAAC,aAAa,CAAC,YAAY;QAChD,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAM,OAAE,CAAC;YACpC,IAAI,GAAG,wCAAQ,CAAC,aAAa,EAC1B,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS;YAE3G,IAAI,CAAC,WAAW,CAAC,MAAM;QACzB,CAAC,MAAM,CAAC;YACN,EAAoE,AAApE,kEAAoE;YACpE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAS,UAAE,CAAqB;YAC1D,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAQ,SAAE,IAAI,CAAC,OAAO,CAAC,MAAM;QACzD,CAAC;QACD,MAAM,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM;IACrC,CAAC;IAED,EAAsD,AAAtD,oDAAsD;IACtD,EAAE;IACF,EAAkC,AAAlC,gCAAkC;IAClC,mBAAmB,GAAG,CAAC;QACrB,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAE,QAAQ,EAAE,CAAC;YACrC,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAE,CAAC;gBACxB,EAAE,uBAAuB,IAAI,CAAC,EAAE,CAAC,SAAS,GACxC,MAAM,CAAC,EAAE;YAEb,CAAC;QACH,CAAC;QAED,GAAG,EAAE,GAAG,CAAC,OAAO,IAAI,CAAC;YAAA,CAAK;YAAE,CAAM;QAAA,CAAC,CAAE,CAAC;YACpC,GAAG,CAAC,QAAQ;YACZ,EAAE,EACC,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,IAEjE,MAAM,CAAC,QAAQ;QAEnB,CAAC;IACH,CAAC;IAED,EAA+C,AAA/C,6CAA+C;IAC/C,mBAAmB,GAAG,CAAC;QACrB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,gBAAgB,QAClC,CAAC;gBACN,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;gBACf,GAAG,CAAE,GAAG,CAAC,KAAK,IAAI,gBAAgB,CAAC,MAAM,CAAE,CAAC;oBAC1C,GAAG,CAAC,QAAQ,GAAG,gBAAgB,CAAC,MAAM,CAAC,KAAK;oBAC5C,MAAM,CAAC,IAAI,CACT,gBAAgB,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK;gBAEpE,CAAC;gBACD,MAAM,CAAC,MAAM;YACf,CAAC;;IAEL,CAAC;IAED,EAAiD,AAAjD,+CAAiD;IACjD,oBAAoB,GAAG,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,gBAAgB,QAClC,CAAC;gBACN,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;gBACf,GAAG,CAAE,GAAG,CAAC,KAAK,IAAI,gBAAgB,CAAC,MAAM,CAAE,CAAC;oBAC1C,GAAG,CAAC,QAAQ,GAAG,gBAAgB,CAAC,MAAM,CAAC,KAAK;oBAC5C,MAAM,CAAC,IAAI,CACT,gBAAgB,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK;gBAEvE,CAAC;gBACD,MAAM,CAAC,MAAM;YACf,CAAC;;IAEL,CAAC;IAED,EAAqF,AAArF,mFAAqF;IACrF,OAAO,GAAG,CAAC;QACT,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,GACrC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAc;;QAEzC,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,QAAQ,GAAG,IAAI;QAEpB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,GAAK,IAAI,CAAC,YAAY,CAAC,IAAI;;IACxD,CAAC;IAED,MAAM,GAAG,CAAC;QACR,MAAM,CAAC,IAAI,CAAC,QAAQ;QACpB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,GACrC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAc;;QAEtC,MAAM,CAAC,IAAI,CAAC,mBAAmB;IACjC,CAAC;IAED,EAAsC,AAAtC,oCAAsC;IACtC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACd,GAAG,CAAC,YAAY,GAAG,CAAC;QACpB,GAAG,CAAC,YAAY,GAAG,CAAG;QAEtB,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC;YACb,GAAG,CAAC,KAAK,GAAG,CAAC;gBAAA,CAAI;gBAAE,CAAI;gBAAE,CAAI;gBAAE,CAAI;gBAAE,CAAG;YAAA,CAAC;YAEzC,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;gBACtC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;gBAClB,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE;gBAE5D,EAAE,EAAE,IAAI,IAAI,MAAM,EAAE,CAAC;oBACnB,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC;oBAC/D,YAAY,GAAG,IAAI;oBACnB,KAAK;gBACP,CAAC;YACH,CAAC;YAED,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,YAAY,IAAI,EAAE,CAAE,CAAoB,AAApB,EAAoB,AAApB,kBAAoB;QACzE,CAAC;QAED,MAAM,EAAE,QAAQ,EAAE,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAY;IACxF,CAAC;IAED,EAAkE,AAAlE,gEAAkE;IAClE,2BAA2B,GAAG,CAAC;QAC7B,EAAE,EACA,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,IAC7B,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EACvD,CAAC;YACD,EAAE,EAAE,IAAI,CAAC,gBAAgB,GAAG,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,EAC1D,IAAI,CAAC,IAAI,CAAC,CAAiB,kBAAE,IAAI,CAAC,KAAK;YAEzC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAsB;QAC1D,CAAC,MACC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAsB;IAE/D,CAAC;IAED,IAAI,CAAC,CAAC,EAAE,CAAC;QACP,EAAE,GAAG,CAAC,CAAC,YAAY,EACjB,MAAM;QAER,IAAI,CAAC,IAAI,CAAC,CAAM,OAAE,CAAC;QAEnB,EAAmC,AAAnC,iCAAmC;QACnC,EAA6B,AAA7B,2BAA6B;QAC7B,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;QACd,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAChD,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAGnC,EAAgE,AAAhE,8DAAgE;QAChE,EAAE,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;YACjB,GAAG,CAAC,CAAC,QAAC,KAAK,EAAC,CAAC,GAAG,CAAC,CAAC,YAAY;YAC9B,EAAE,EAAE,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,gBAAgB,IAAI,IAAI,EAC5D,EAA6E,AAA7E,2EAA6E;YAC7E,IAAI,CAAC,kBAAkB,CAAC,KAAK;iBAE7B,IAAI,CAAC,WAAW,CAAC,KAAK;QAE1B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,CAAY,aAAE,KAAK;IAC/B,CAAC;IAED,KAAK,CAAC,CAAC,EAAE,CAAC;QACR,EAAE,EACA,+BAAS,CAAC,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,aAAa,GAAG,SAAS,GAAG,CAAC,GAAK,CAAC,CAAC,KAAK;aAAK,IAAI,EAE1E,MAAM;QAGR,IAAI,CAAC,IAAI,CAAC,CAAO,QAAE,CAAC;QACpB,GAAG,CAAC,CAAC,QAAC,KAAK,EAAC,CAAC,GAAG,CAAC,CAAC,aAAa;QAE/B,EAAE,EAAE,KAAK,CAAC,MAAM,EACd,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK;IAExC,CAAC;IAED,WAAW,CAAC,KAAK,EAAE,CAAC;QAClB,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK,CACpB,IAAI,CAAC,OAAO,CAAC,IAAI;IAErB,CAAC;IAED,EAAwE,AAAxE,sEAAwE;IACxE,EAAoB,AAApB,kBAAoB;IACpB,kBAAkB,CAAC,KAAK,EAAE,CAAC;QACzB,MAAM,MAAQ,CAAC;YACb,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;YACf,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK,CAAE,CAAC;gBACvB,GAAG,CAAC,KAAK;gBACT,EAAE,EACA,IAAI,CAAC,gBAAgB,IAAI,IAAI,KAC5B,KAAK,GAAG,IAAI,CAAC,gBAAgB,KAC9B,CAAC;oBACD,EAAE,EAAE,KAAK,CAAC,MAAM,EACd,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS;yBAClC,EAAE,EAAE,KAAK,CAAC,WAAW,EAC1B,EAAgD,AAAhD,8CAAgD;oBAChD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI;yBAEzD,MAAM,CAAC,IAAI,CAAC,SAAS;gBAEzB,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI;oBAC/B,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,CAAM,OAC3C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS;yBAEvC,MAAM,CAAC,IAAI,CAAC,SAAS;uBAGvB,MAAM,CAAC,IAAI,CAAC,SAAS;YAEzB,CAAC;YACD,MAAM,CAAC,MAAM;QACf,CAAC;IACH,CAAC;IAED,EAAsE,AAAtE,oEAAsE;IACtE,sBAAsB,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC;QACvC,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,YAAY;QAEtC,GAAG,CAAC,YAAY,IAAI,KAAK,GACvB,qCAAe,CAAC,OAAO,EAAE,CAAK,OAAG,CAAC,GAAK,CAAC,CAAC,GAAG,CAAC,KAAK;;;QAEpD,GAAG,CAAC,WAAW,OAAS,CAAC;YACvB,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,OAAO,GAAK,CAAC;gBACzC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACvB,GAAG,EAAE,GAAG,CAAC,KAAK,IAAI,OAAO,CAAE,CAAC;wBAC1B,EAAE,EAAE,KAAK,CAAC,MAAM,EACd,KAAK,CAAC,IAAI,EAAE,IAAI,GAAK,CAAC;4BACpB,EAAE,EACA,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAC9B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,MAAM,CAAG,IAEjC,MAAM;4BAER,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI;4BACpC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;wBAC1B,CAAC;6BACI,EAAE,EAAE,KAAK,CAAC,WAAW,EAC1B,IAAI,CAAC,sBAAsB,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI;oBAE5D,CAAC;oBAED,EAAkE,AAAlE,gEAAkE;oBAClE,EAAyB,AAAzB,uBAAyB;oBACzB,EAAoF,AAApF,kFAAoF;oBACpF,WAAW;gBACb,CAAC;gBACD,MAAM,CAAC,IAAI;YACb,CAAC,EAAE,YAAY;QACjB,CAAC;QAED,MAAM,CAAC,WAAW;IACpB,CAAC;IAED,EAA8D,AAA9D,4DAA8D;IAC9D,EAA6D,AAA7D,2DAA6D;IAC7D,EAA4C,AAA5C,0CAA4C;IAC5C,EAAE;IACF,EAAqE,AAArE,mEAAqE;IACrE,EAAyB,AAAzB,uBAAyB;IACzB,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;QAClB,EAAE,EACA,IAAI,CAAC,OAAO,CAAC,WAAW,IACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,GAAxB,OAAsC,EAElD,IAAI,CACF,IAAI,CAAC,OAAO,CAAC,cAAc,CACxB,OAAO,CAAC,CAAc,eAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,EAClE,OAAO,CAAC,CAAiB,kBAAE,IAAI,CAAC,OAAO,CAAC,WAAW;aAEnD,EAAE,GAAG,wCAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,GAC/D,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB;aAChC,EAAE,EACP,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,IAC7B,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EACvD,CAAC;YACD,IAAI,CACF,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CACvC,CAAc,eACd,IAAI,CAAC,OAAO,CAAC,QAAQ;YAGzB,IAAI,CAAC,IAAI,CAAC,CAAkB,mBAAE,IAAI;QACpC,CAAC,MACC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;IAE7C,CAAC;IAED,OAAO,CAAC,IAAI,EAAE,CAAC;QACb,IAAI,CAAC,MAAM,GAAG,CAAC;YACb,IAAI,EAAE,wCAAQ,CAAC,MAAM;YACrB,QAAQ,EAAE,CAAC;YACX,EAA+D,AAA/D,6DAA+D;YAC/D,EAAyD,AAAzD,uDAAyD;YACzD,KAAK,EAAE,IAAI,CAAC,IAAI;YAChB,SAAS,EAAE,CAAC;YACZ,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;QAIjC,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI;QAEpB,IAAI,CAAC,MAAM,GAAG,wCAAQ,CAAC,KAAK;QAE5B,IAAI,CAAC,IAAI,CAAC,CAAW,YAAE,IAAI;QAE3B,IAAI,CAAC,iBAAiB,CAAC,IAAI;QAE3B,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,KAAK,GAAK,CAAC;YAC5B,EAAE,EAAE,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,QAAQ,GAAG,KAAK;gBACrB,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBAAA,IAAI;gBAAA,CAAC,EAAE,KAAK,EAAG,CAA2B,AAA3B,EAA2B,AAA3B,yBAA2B;YACnE,CAAC,MAAM,CAAC;gBACN,IAAI,CAAC,QAAQ,GAAG,IAAI;gBACpB,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EACxB,IAAI,CAAC,WAAW,CAAC,IAAI;gBACrB,CAA4B,AAA5B,EAA4B,AAA5B,0BAA4B;YAChC,CAAC;YACD,IAAI,CAAC,2BAA2B;QAClC,CAAC;IACH,CAAC;IAED,EAA0B,AAA1B,wBAA0B;IAC1B,YAAY,CAAC,KAAK,EAAE,CAAC;QACnB,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK,CACpB,IAAI,CAAC,WAAW,CAAC,IAAI;QAEvB,MAAM,CAAC,IAAI;IACb,CAAC;IAED,WAAW,CAAC,IAAI,EAAE,CAAC;QACjB,EAAE,EAAE,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC7D,IAAI,CAAC,MAAM,GAAG,wCAAQ,CAAC,MAAM;YAC7B,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC/B,MAAM,CAAC,UAAU,KAAO,IAAI,CAAC,YAAY;cAAI,CAAC,EAAG,CAAqB,AAArB,EAAqB,AAArB,mBAAqB;QAE1E,CAAC,MACC,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,CAAkF;IAGxF,CAAC;IAED,iBAAiB,CAAC,IAAI,EAAE,CAAC;QACvB,EAAE,EACA,IAAI,CAAC,OAAO,CAAC,qBAAqB,IAClC,IAAI,CAAC,IAAI,CAAC,KAAK,eACf,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAjC,OAA+C,EAC5D,CAAC;YACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI;YAC9B,MAAM,CAAC,UAAU,KAAO,IAAI,CAAC,sBAAsB;cAAI,CAAC,EAAG,CAAqB,AAArB,EAAqB,AAArB,mBAAqB;QAClF,CAAC;IACH,CAAC;IAED,sBAAsB,GAAG,CAAC;QACxB,EAAE,EAAE,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAChE,MAAM;QAGR,IAAI,CAAC,oBAAoB,GAAG,IAAI;QAChC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK;QACrC,MAAM,CAAC,IAAI,CAAC,eAAe,CACzB,IAAI,EACJ,IAAI,CAAC,OAAO,CAAC,cAAc,EAC3B,IAAI,CAAC,OAAO,CAAC,eAAe,EAC5B,IAAI,CAAC,OAAO,CAAC,eAAe,EAC5B,IAAI,GACH,OAAO,GAAK,CAAC;YACZ,IAAI,CAAC,IAAI,CAAC,CAAW,YAAE,IAAI,EAAE,OAAO;YACpC,IAAI,CAAC,oBAAoB,GAAG,KAAK;YACjC,MAAM,CAAC,IAAI,CAAC,sBAAsB;QACpC,CAAC;IAEL,CAAC;IAED,EAA6C,AAA7C,2CAA6C;IAC7C,UAAU,CAAC,IAAI,EAAE,CAAC;QAChB,EAAE,EAAE,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,SAAS,EACpC,IAAI,CAAC,YAAY,CAAC,IAAI;QAExB,IAAI,CAAC,KAAK,GAAG,6BAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI;QAErC,IAAI,CAAC,IAAI,CAAC,CAAa,cAAE,IAAI;QAC7B,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EACzB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAO;IAE5B,CAAC;IAED,EAAkE,AAAlE,gEAAkE;IAClE,cAAc,CAAC,iBAAiB,EAAE,CAAC;QACjC,EAAsE,AAAtE,oEAAsE;QACtE,EAAE,EAAE,iBAAiB,IAAI,IAAI,EAC3B,iBAAiB,GAAG,KAAK;QAE3B,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,GAC/B,EAAE,EAAE,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,SAAS,IAAI,iBAAiB,EACzD,IAAI,CAAC,UAAU,CAAC,IAAI;QAGxB,MAAM,CAAC,IAAI;IACb,CAAC;IAED,EAA+F,AAA/F,6FAA+F;IAC/F,EAAmG,AAAnG,iGAAmG;IACnG,EAAoB,AAApB,kBAAoB;IACpB,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC;QACxD,MAAM,CAAC,IAAI,CAAC,eAAe,CACzB,IAAI,EACJ,KAAK,EACL,MAAM,EACN,YAAY,EACZ,IAAI,GACH,OAAO,EAAE,MAAM,GAAK,CAAC;YACpB,EAAE,EAAE,MAAM,IAAI,IAAI,EAChB,EAAiC,AAAjC,+BAAiC;YACjC,MAAM,CAAC,QAAQ,CAAC,IAAI;iBACf,CAAC;gBACN,GAAG,CAAC,CAAC,iBAAC,cAAc,EAAC,CAAC,GAAG,IAAI,CAAC,OAAO;gBACrC,EAAE,EAAE,cAAc,IAAI,IAAI,EACxB,cAAc,GAAG,IAAI,CAAC,IAAI;gBAE5B,GAAG,CAAC,cAAc,GAAG,MAAM,CAAC,SAAS,CACnC,cAAc,EACd,IAAI,CAAC,OAAO,CAAC,aAAa;gBAE5B,EAAE,EACA,cAAc,KAAK,CAAY,eAC/B,cAAc,KAAK,CAAW,YAE9B,EAAwC,AAAxC,sCAAwC;gBACxC,cAAc,GAAG,iCAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc;gBAEnE,MAAM,CAAC,QAAQ,CAAC,wCAAQ,CAAC,aAAa,CAAC,cAAc;YACvD,CAAC;QACH,CAAC;IAEL,CAAC;IAED,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,CAAC;QAC5E,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU;QAE/B,UAAU,CAAC,MAAM,OAAS,CAAC;YACzB,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,MAAM;YAEhC,EAAwE,AAAxE,sEAAwE;YACxE,EAAE,EAAE,IAAI,CAAC,IAAI,KAAK,CAAe,gBAAE,CAAC;gBAClC,EAAE,EAAE,QAAQ,IAAI,IAAI,EAClB,QAAQ,CAAC,UAAU,CAAC,MAAM;gBAE5B,MAAM;YACR,CAAC;YAED,IAAI,CAAC,sBAAsB,CACzB,IAAI,EACJ,KAAK,EACL,MAAM,EACN,YAAY,EACZ,cAAc,EACd,QAAQ;QAEZ,CAAC;QAED,UAAU,CAAC,aAAa,CAAC,IAAI;IAC/B,CAAC;IAED,EAA6C,AAA7C,2CAA6C;IAC7C,EAAE;IACF,EAAkD,AAAlD,gDAAkD;IAClD,EAAE;IACF,EAA+E,AAA/E,6EAA+E;IAC/E,EAAwE,AAAxE,sEAAwE;IACxE,mBAAmB,CACjB,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,eAAe,GAAG,IAAI,EACtB,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,CAAW,YAAE,QAAQ;QAC/B,IAAI,CAAC,IAAI,CAAC,CAAU,WAAE,QAAQ;QAE9B,EAAE,GAAG,eAAe,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,CAAW,YAAE,QAAQ,EAAE,QAAQ;YACzC,EAAE,EAAE,QAAQ,EAAE,QAAQ;QACxB,CAAC,MAAM,CAAC;YACN,GAAG,CAAC,MAAM,IAAI,SAAS,GAAK,CAAC;gBAC3B,IAAI,CAAC,IAAI,CAAC,CAAW,YAAE,QAAQ,EAAE,SAAS;gBAC1C,EAAE,EAAE,QAAQ,EAAE,QAAQ;YACxB,CAAC;YACD,QAAQ,CAAC,OAAO,GAAG,QAAQ;YAE3B,IAAI,CAAC,sBAAsB,CACzB,QAAQ,EACR,IAAI,CAAC,OAAO,CAAC,cAAc,EAC3B,IAAI,CAAC,OAAO,CAAC,eAAe,EAC5B,IAAI,CAAC,OAAO,CAAC,eAAe,EAC5B,IAAI,CAAC,OAAO,CAAC,cAAc,EAC3B,MAAM,EACN,WAAW;QAEf,CAAC;IACH,CAAC;IAED,sBAAsB,CACpB,IAAI,EACJ,KAAK,EACL,MAAM,EACN,YAAY,EACZ,cAAc,EACd,QAAQ,EACR,WAAW,EACX,CAAC;QACD,EAAyE,AAAzE,uEAAyE;QACzE,EAAgD,AAAhD,8CAAgD;QAChD,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAK;QAEtC,EAAE,EAAE,WAAW,EACb,GAAG,CAAC,WAAW,GAAG,WAAW;QAG/B,EAA+E,AAA/E,6EAA+E;QAC/E,cAAc,GACZ,gBAAgB,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAkB,sBAAK,CAAY,cAC/D,KAAK,GACL,cAAc;QAEpB,GAAG,CAAC,MAAM,OAAS,CAAC;YAClB,GAAG,CAAC,QAAQ,IAAI,QAAQ,GAAK,QAAQ,CAAC,CAAC;;YACvC,EAAE,EAAE,MAAM,CAAC,IAAI,KAAK,CAAW,cAAI,IAAI,KAAK,IAAI,IAAI,cAAc,EAChE,QAAQ,IAAI,QAAQ,GAClB,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,GAAI,CAAC;oBAC7B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAa;gBACjD,CAAC;;YAGL,MAAM,CAAC,QAAQ,EAAE,WAAW,GAAK,CAAC;gBAChC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK;gBACtB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM;gBAExB,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CACvC,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,MAAM,EACN,YAAY;gBAGd,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAQ;gBAC5C,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,CAAI;gBAEhC,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,QAAQ;gBAClC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,SAAS;gBAEpC,EAAE,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;oBACpB,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,SAAS;oBACnC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,QAAQ;gBACrC,CAAC;gBAED,MAAM,CAAE,WAAW;oBACjB,IAAI,CAAC,CAAC;wBACJ,EAAkB,AAAlB,gBAAkB;wBAClB,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;wBAC7B,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;wBACf,KAAK;oBACP,IAAI,CAAC,CAAC;wBACJ,EAAmB,AAAnB,kBAAoB;wBACnB,GAAE,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM;wBACzC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;wBAClB,KAAK;oBACP,IAAI,CAAC,CAAC;wBACJ,EAAgB,AAAhB,cAAgB;wBAChB,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM;wBAC9B,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE;wBACf,KAAK;oBACP,IAAI,CAAC,CAAC;wBACJ,EAAkC,AAAlC,gCAAkC;wBAClC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;wBACxB,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE;wBACf,KAAK;oBACP,IAAI,CAAC,CAAC;wBACJ,EAAmB,AAAnB,kBAAoB;wBACnB,GAAE,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;wBACxB,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK;wBAC9B,KAAK;oBACP,IAAI,CAAC,CAAC;wBACJ,EAAoC,AAApC,kCAAoC;wBACpC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;wBACxB,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK;wBAC1C,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;wBACf,KAAK;oBACP,IAAI,CAAC,CAAC;wBACJ,EAAkB,AAAlB,iBAAkB;wBAClB,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE;wBACzB,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;wBAC/B,KAAK;;gBAGT,EAAyC,AAAzC,uCAAyC;gBACzC,qCAAe,CACb,GAAG,EACH,GAAG,EACH,UAAU,CAAC,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,EAC7C,UAAU,CAAC,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,EAC7C,UAAU,CAAC,QAAQ,EACnB,UAAU,CAAC,SAAS,EACpB,UAAU,CAAC,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,EAC7C,UAAU,CAAC,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,EAC7C,UAAU,CAAC,QAAQ,EACnB,UAAU,CAAC,SAAS;gBAGtB,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAW;gBAE5C,EAAE,EAAE,QAAQ,IAAI,IAAI,EAClB,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM;YAErC,CAAC;QACH,CAAC;QAED,EAAE,EAAE,QAAQ,IAAI,IAAI,EAClB,GAAG,CAAC,OAAO,GAAG,QAAQ;QAGxB,MAAM,CAAE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;IAChC,CAAC;IAED,EAA+E,AAA/E,6EAA+E;IAC/E,YAAY,GAAG,CAAC;QACd,GAAG,CAAC,CAAC,kBAAC,eAAe,EAAC,CAAC,GAAG,IAAI,CAAC,OAAO;QACtC,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,GAAG,MAAM;QACtD,GAAG,CAAC,CAAC,GAAG,gBAAgB;QAExB,EAAoE,AAApE,kEAAoE;QACpE,EAAE,EAAE,gBAAgB,IAAI,eAAe,EACrC,MAAM;QAGR,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc;QAErC,EAAE,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,GAC1B,MAAM;QAGR,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAC7B,EAA8C,AAA9C,4CAA8C;QAC9C,MAAM,CAAC,IAAI,CAAC,YAAY,CACtB,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,GAAG,gBAAgB;mBAGlD,CAAC,GAAG,eAAe,CAAE,CAAC;YAC3B,EAAE,GAAG,WAAW,CAAC,MAAM,EACrB,MAAM;YACN,CAA0B,AAA1B,EAA0B,AAA1B,wBAA0B;YAC5B,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK;YAClC,CAAC;QACH,CAAC;IAEL,CAAC;IAED,EAA6B,AAA7B,2BAA6B;IAC7B,WAAW,CAAC,IAAI,EAAE,CAAC;QACjB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAAA,IAAI;QAAA,CAAC;IACjC,CAAC;IAED,EAA+C,AAA/C,6CAA+C;IAC/C,YAAY,CAAC,KAAK,EAAE,CAAC;QACnB,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK,CAAE,CAAC;YACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAE,CAA0B,AAA1B,EAA0B,AAA1B,wBAA0B;YAClD,IAAI,CAAC,MAAM,GAAG,wCAAQ,CAAC,SAAS;YAEhC,IAAI,CAAC,IAAI,CAAC,CAAY,aAAE,IAAI;QAC9B,CAAC;QAED,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAC7B,IAAI,CAAC,IAAI,CAAC,CAAoB,qBAAE,KAAK;QAGvC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK;IAC/B,CAAC;IAED,gBAAgB,CAAC,GAAG,EAAE,CAAC;QACrB,GAAG,CAAC,KAAK;QACT,MAAM,CAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CACvB,MAAM,EAAE,IAAI,GAAK,IAAI,CAAC,GAAG,KAAK,GAAG;UACjC,GAAG,EAAE,IAAI,GAAK,IAAI;;IACvB,CAAC;IAED,EAA0D,AAA1D,wDAA0D;IAC1D,EAA8C,AAA9C,4CAA8C;IAC9C,EAA+E,AAA/E,6EAA+E;IAC/E,EAAmB,AAAnB,iBAAmB;IACnB,YAAY,CAAC,IAAI,EAAE,CAAC;QAClB,EAAE,EAAE,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,SAAS,EAAE,CAAC;YACvC,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG;YACjD,GAAG,EAAE,GAAG,CAAC,WAAW,IAAI,YAAY,CAClC,WAAW,CAAC,MAAM,GAAG,wCAAQ,CAAC,QAAQ;YAExC,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAW,YACjC,IAAI,CAAC,GAAG,CAAC,KAAK;YAEhB,GAAG,EAAE,GAAG,CAAC,YAAW,IAAI,YAAY,CAClC,IAAI,CAAC,IAAI,CAAC,CAAU,WAAE,YAAW;YAEnC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAC7B,IAAI,CAAC,IAAI,CAAC,CAAkB,mBAAE,YAAY;QAE9C,CAAC,MAAM,EAAE,EACP,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,KAAK,IAC9B,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,MAAM,EAC/B,CAAC;YACD,IAAI,CAAC,MAAM,GAAG,wCAAQ,CAAC,QAAQ;YAC/B,IAAI,CAAC,IAAI,CAAC,CAAU,WAAE,IAAI;YAC1B,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAC7B,IAAI,CAAC,IAAI,CAAC,CAAkB,mBAAE,CAAC;gBAAA,IAAI;YAAA,CAAC;QAExC,CAAC;QAED,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC/B,MAAM,CAAC,IAAI,CAAC,YAAY;IAE5B,CAAC;IAED,aAAa,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;QAC9B,EAAE,EAAE,MAAM,CAAC,MAAM,KAAK,CAAU,WAC9B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI;QAEhC,MAAM,CAAC,MAAM;IACf,CAAC;IAED,UAAU,CAAC,IAAI,EAAE,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAAA,IAAI;QAAA,CAAC;IAChC,CAAC;IAED,WAAW,CAAC,KAAK,EAAE,CAAC;QAClB,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,gBAAgB,GAAK,CAAC;YACjD,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAC1B,EAAsE,AAAtE,oEAAsE;gBACtE,EAAkC,AAAlC,gCAAkC;gBAClC,GAAG,CAAC,eAAe,GAAG,gBAAgB,CAAC,CAAC;gBACxC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,GACrB,IAAI,CAAC,OAAO,CAAC,QAAQ,KACpB,IAAI,CAAC,OAAO,CAAC,aAAa,IACzB,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;gBACjD,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,IAAI,CACzC,eAAe,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;YAEjD,CAAC;YAED,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC5B,EAAsC,AAAtC,oCAAsC;gBAEtC,EAAwF,AAAxF,sFAAwF;gBACxF,EAAkD,AAAlD,gDAAkD;gBAClD,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;gBAClB,GAAG,CAAC,eAAe,GAAG,gBAAgB,CAAC,CAAC;gBACxC,GAAG,CAAC,iBAAiB,GAAG,CAAC;gBAEzB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;gBAEvB,GAAG,CAAC,eAAe,OAAS,CAAC;oBAC3B,GAAG,CAAC,UAAU,GAAG,CAAC;oBAElB,EAAoE,AAApE,kEAAoE;0BAC7D,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,MAAM,SAAS,CACjD,UAAU;oBAGZ,EAAyD,AAAzD,uDAAyD;oBACzD,EAAE,EAAE,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM;oBAErD,iBAAiB;oBAEjB,GAAG,CAAC,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;oBAC/C,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAChB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAC9B,eAAe,CAAC,IAAI;oBAGtB,GAAG,CAAC,SAAS,GAAG,CAAC;wBACf,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;wBAC1B,IAAI,EAAE,eAAe,CAAC,WAAW,GAC7B,eAAe,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,IACtC,eAAe,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG;wBACpC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;wBAC9B,UAAU,EAAE,UAAU;oBACxB,CAAC;oBAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC;wBAChC,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,UAAU;wBACjB,SAAS,EAAE,SAAS;wBACpB,MAAM,EAAE,wCAAQ,CAAC,SAAS;wBAC1B,QAAQ,EAAE,CAAC;wBACX,OAAO,EAAE,CAAC;oBACZ,CAAC;oBAED,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;wBAAA,SAAS;oBAAA,CAAC;gBACrC,CAAC;gBAED,IAAI,CAAC,MAAM,CAAC,mBAAmB,IAAI,KAAK,EAAE,QAAQ,GAAK,CAAC;oBACtD,GAAG,CAAC,WAAW,GAAG,IAAI;oBACtB,KAAK,CAAC,MAAM,GAAG,wCAAQ,CAAC,OAAO;oBAE/B,EAAgC,AAAhC,8BAAgC;oBAChC,KAAK,CAAC,SAAS,GAAG,IAAI;oBACtB,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,YAAY;oBACvC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC,GAAG,CAAC,qBAAqB;oBACvD,EAAyD,AAAzD,uDAAyD;oBACzD,KAAK,CAAC,GAAG,GAAG,IAAI;oBAEhB,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,GAAI,CAAC;wBACrD,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,SAAS,EACrC,MAAM,CAAC,eAAe;wBAExB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,wCAAQ,CAAC,OAAO,EACnD,WAAW,GAAG,KAAK;oBAEvB,CAAC;oBAED,EAAE,EAAE,WAAW,EACb,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,MAAQ,CAAC;wBACvC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI;oBACtC,CAAC;gBAEL,CAAC;gBAED,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB,EACnC,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,GAChD,eAAe;qBAGjB,eAAe;YAEnB,CAAC,MAAM,CAAC;gBACN,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC;gBACnB,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GACjC,UAAU,CAAC,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBAC1B,IAAI,EAAE,gBAAgB,CAAC,CAAC;oBACxB,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ;gBACpC,CAAC;gBAEH,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,UAAU;YACpC,CAAC;QACH,CAAC;IACH,CAAC;IAED,EAAkD,AAAlD,gDAAkD;IAClD,SAAS,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;QACpB,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,GAAI,CAAC;YACrD,EAAE,EACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,SAAS,IACnC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,KAAK,GAAG,EAEjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAE/B,CAAC;IACH,CAAC;IAED,EAA4D,AAA5D,0DAA4D;IAC5D,EAAE;IACF,EAA8E,AAA9E,4EAA8E;IAC9E,EAA6E,AAA7E,2EAA6E;IAC7E,EAAmD,AAAnD,iDAAmD;IACnD,WAAW,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC;QAC9B,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,cAAc;QAE5B,EAA2E,AAA3E,yEAA2E;QAC3E,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK,CACpB,IAAI,CAAC,GAAG,GAAG,GAAG;QAEhB,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EACzB,EAAwE,AAAxE,sEAAwE;QACxE,EAAmC,AAAnC,iCAAmC;QACnC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,UAAU,EAAE,GAAG,GAAG,GAAG;QAG5D,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU;QACtE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU;QAChE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI;QAE1B,EAAkG,AAAlG,gGAAkG;QAClG,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK;QAC5D,EAAE,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK;QAEzE,EAA6E,AAA7E,2EAA6E;QAC7E,GAAG,CAAC,eAAe,KAAK,IAAI,CAAC,OAAO,CAAC,eAAe;QAEpD,GAAG,CAAC,MAAM,IAAI,CAAC,GAAK,CAAC;YACnB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;QACvC,CAAC;QAED,GAAG,CAAC,SAAS,OAAS,CAAC;YACrB,IAAI,CAAC,kBAAkB,CACrB,KAAK,EACL,GAAG,GACF,uBAAuB,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ;QAElE,CAAC;QAED,GAAG,CAAC,OAAO,OAAS,CAAC;YACnB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG;QACpC,CAAC;QAED,EAAiD,AAAjD,+CAAiD;QACjD,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,MAAM,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG;QACvD,WAAW,CAAC,UAAU,IAAI,CAAC,GACzB,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;;QAE/C,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,GACrC,CAAC;YACC,MAAM,EAAE,CAAkB;YAC1B,CAAe,gBAAE,CAAU;YAC3B,CAAkB,mBAAE,CAAgB;QACtC,CAAC,GACD,CAAC;QAAA,CAAC;QAEN,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EACzB,OAAO,CAAC,CAAc,iBAAI,KAAK,CAAC,CAAC,EAAE,IAAI;QAGzC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EACtB,iBAAM,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;QAGtC,GAAG,CAAE,GAAG,CAAC,UAAU,IAAI,OAAO,CAAE,CAAC;YAC/B,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU;YACpC,EAAE,EAAE,WAAW,EACb,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,WAAW;QAEhD,CAAC;QAED,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YAC5B,EAAqE,AAArE,mEAAqE;YACrE,EAA2C,AAA3C,yCAA2C;YAC3C,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK,CACpB,IAAI,CAAC,IAAI,CAAC,CAAS,UAAE,IAAI,EAAE,GAAG;YAEhC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAC7B,IAAI,CAAC,IAAI,CAAC,CAAiB,kBAAE,KAAK,EAAE,GAAG;YAEzC,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK;QACrC,CAAC,MAAM,CAAC;YACN,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ;YAE3B,EAAiC,AAAjC,+BAAiC;YACjC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACxB,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;gBAC1C,EAAE,EAAE,MAAM,CAAC,gBAAgB,KAAK,CAAU,WACxC,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,CACtC,IAAI,EACJ,KAAK,EACL,GAAG,EACH,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;gBAIlE,GAAG,CAAE,GAAG,CAAC,GAAG,IAAI,gBAAgB,CAAE,CAAC;oBACjC,GAAG,CAAC,KAAK,GAAG,gBAAgB,CAAC,GAAG;oBAChC,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,GACrB,EAA8C,AAA9C,4CAA8C;oBAC9C,EAA+C,AAA/C,6CAA+C;oBAC/C,EAAgB,AAAhB,cAAgB;oBAChB,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GACjC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;yBAG9B,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK;gBAE9B,CAAC;YACH,CAAC;YAED,EAAgD,AAAhD,8CAAgD;YAChD,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK,CACpB,IAAI,CAAC,IAAI,CAAC,CAAS,UAAE,IAAI,EAAE,GAAG,EAAE,QAAQ;YAE1C,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAC7B,IAAI,CAAC,IAAI,CAAC,CAAiB,kBAAE,KAAK,EAAE,GAAG,EAAE,QAAQ;YAGnD,IAAI,CAAC,mBAAmB,CAAC,QAAQ;YAEjC,EAAwB,AAAxB,sBAAwB;YACxB,EAAwF,AAAxF,sFAAwF;YACxF,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;gBAC3C,GAAG,CAAC,SAAS,GAAG,UAAU,CAAC,CAAC;gBAC5B,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,QAAQ;YACpE,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK;QACzC,CAAC;IACH,CAAC;IAED,EAA8G,AAA9G,4GAA8G;IAC9G,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;QAC5B,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAC;QACzB,EAAyF,AAAzF,uFAAyF;QACzF,GAAG,CAAC,WAAW,GAAG,CAAC;QACnB,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GACjC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,eAAe,GAAK,CAAC;YACpE,gBAAgB,CAAC,CAAC,IAAI,eAAe;YACrC,EAAE,IAAI,WAAW,KAAK,KAAK,CAAC,MAAM,EAChC,IAAI,CAAC,gBAAgB;QAEzB,CAAC;IAEL,CAAC;IAED,EAA4E,AAA5E,0EAA4E;IAC5E,mBAAmB,CAAC,QAAQ,EAAE,CAAC;QAC7B,EAAoC,AAApC,kCAAoC;QACpC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAM,OACjC,GAAG,EAAE,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAC7C,CAAiC,kCAChC,CAAC;YACF,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC,YAAY,CAAC,CAAM;YACzC,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC,YAAY,CAAC,CAAM;YACzC,EAAE,EAAE,SAAS,EAAE,SAAS,GAAG,SAAS,CAAC,WAAW;YAEhD,EAAqD,AAArD,mDAAqD;YACrD,EAAE,EAAE,MAAM,CAAC,SAAS,KAAK,CAAW,cAAI,SAAS,KAAK,IAAI,EAAE,QAAQ;YAEpE,EAAE,EAAE,KAAK,CAAC,OAAO,KAAK,CAAQ,WAAI,KAAK,CAAC,YAAY,CAAC,CAAU,YAAG,CAAC;gBACjE,EAA2B,AAA3B,yBAA2B;gBAC3B,GAAG,EAAE,GAAG,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAC9B,EAAE,EAAE,MAAM,CAAC,QAAQ,EACjB,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK;YAG7C,CAAC,MAAM,EAAE,GACN,SAAS,IACT,SAAS,KAAK,CAAU,aAAI,SAAS,KAAK,CAAO,UAClD,KAAK,CAAC,OAAO,EAEb,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK;QAE1C,CAAC;IAEL,CAAC;IAED,EAAoE,AAApE,kEAAoE;IACpE,EAAmE,AAAnE,iEAAmE;IACnE,0BAA0B,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QACzC,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAC1B,EAAuC,AAAvC,qCAAuC;QACvC,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK,CAAE,CAAC;YACvB,EAAE,EACA,IAAI,CAAC,MAAM,CAAC,KAAK,IACjB,IAAI,CAAC,MAAM,CAAC,SAAS,IACrB,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAK1C,QAAQ;YAGV,EAAE,EAAE,CAAC,EAAE,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAI,GAAG,GAAG,CAAC,CAAC,MAAM,GAAI,CAAC,CAAC,KAAK;gBACjD,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK;gBAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,MAAM;YAClC,CAAC,MAAM,CAAC;gBACN,EAA6B,AAA7B,2BAA6B;gBAC7B,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,GAAG;gBAC1B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;YAC3C,CAAC;YAED,IAAI,CAAC,IAAI,CACP,CAAgB,iBAChB,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,QAAQ,EACpB,IAAI,CAAC,MAAM,CAAC,SAAS;QAEzB,CAAC;aACI,CAAC;YACN,EAA8B,AAA9B,4BAA8B;YAE9B,EAAwE,AAAxE,sEAAwE;YACxE,EAA6C,AAA7C,2CAA6C;YAC7C,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;YAElB,EAA0E,AAA1E,wEAA0E;YAC1E,EAAY,AAAZ,UAAY;YACZ,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG;YAEpC,EAAE,EAAE,CAAC,EAAE,CAAC;gBACN,KAAK,CAAC,QAAQ,GAAI,GAAG,GAAG,CAAC,CAAC,MAAM,GAAI,CAAC,CAAC,KAAK;gBAC3C,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK;gBACrB,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,MAAM;YAC5B,CAAC,MAAM,CAAC;gBACN,EAA6B,AAA7B,2BAA6B;gBAC7B,KAAK,CAAC,QAAQ,GAAG,GAAG;gBACpB,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK;YAC/B,CAAC;YAED,EAAkE,AAAlE,gEAAkE;YAClE,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC;YACzB,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,GAChD,EAAE,EACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KACpB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAW,YACrD,CAAC;gBACD,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ;gBACtD,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK;gBAChD,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS;YAC1D,CAAC;YAEH,EAAwE,AAAxE,sEAAwE;YACxE,EAAqB,AAArB,mBAAqB;YACrB,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe;YAEzE,IAAI,CAAC,IAAI,CACP,CAAgB,iBAChB,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,QAAQ,EACpB,IAAI,CAAC,MAAM,CAAC,SAAS;QAEzB,CAAC;IACH,CAAC;IAED,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QACjC,GAAG,CAAC,QAAQ;QAEZ,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,KAAK,wCAAQ,CAAC,QAAQ,EACvC,MAAM;QAGR,EAAE,EAAE,GAAG,CAAC,UAAU,KAAK,CAAC,EACtB,MAAM;QAGR,EAAE,EAAE,GAAG,CAAC,YAAY,KAAK,CAAa,gBAAI,GAAG,CAAC,YAAY,KAAK,CAAM,OAAE,CAAC;YACtE,QAAQ,GAAG,GAAG,CAAC,YAAY;YAE3B,EAAE,EACA,GAAG,CAAC,iBAAiB,CAAC,CAAc,mBACnC,GAAG,CAAC,iBAAiB,CAAC,CAAc,eAAE,OAAO,CAAC,CAAkB,oBAEjE,GAAG,CAAC,CAAC;gBACH,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ;YAChC,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;gBACf,CAAC,GAAG,KAAK;gBACT,QAAQ,GAAG,CAAoC;YACjD,CAAC;QAEL,CAAC;QAED,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,GAAG;QAE1C,EAAE,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,GACzC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ;aAE5C,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EACzB,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,mBAAmB,CACjC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAC5B,QAAQ;aAGV,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC;IAGvC,CAAC;IAED,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;QACxC,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,KAAK,wCAAQ,CAAC,QAAQ,EACvC,MAAM;QAGR,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACxD,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG;YACxC,EAAE,GAAE,KAAK,CAAC,OAAO,MAAK,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBACpD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;oBAAA,KAAK,CAAC,SAAS;gBAAA,CAAC;gBACzC,MAAM;YACR,CAAC,MACC,OAAO,CAAC,IAAI,CAAC,CAA0C;QAE3D,CAAC;QAED,IAAI,CAAC,gBAAgB,CACnB,KAAK,EACL,QAAQ,IACN,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAgB,iBAAE,GAAG,CAAC,MAAM,GACrE,GAAG;IAEP,CAAC;IAED,aAAa,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;QACnC,EAAE,EAAE,GAAG,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CACV,CAA+E;YAEjF,MAAM;QACR,CAAC;QACD,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;YACzB,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC5B,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG;gBAC1C,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI;YAC/B,CAAC,MACC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;eAGlB,GAAG,CAAC,IAAI,CAAC,QAAQ;IAErB,CAAC;IAED,EAAiD,AAAjD,+CAAiD;IACjD,EAAsE,AAAtE,oEAAsE;IACtE,SAAS,CAAC,KAAK,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;QACjC,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK,CAAE,CAAC;YACvB,IAAI,CAAC,MAAM,GAAG,wCAAQ,CAAC,OAAO;YAC9B,IAAI,CAAC,IAAI,CAAC,CAAS,UAAE,IAAI,EAAE,YAAY,EAAE,CAAC;YAC1C,IAAI,CAAC,IAAI,CAAC,CAAU,WAAE,IAAI;QAC5B,CAAC;QACD,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,CAAiB,kBAAE,KAAK,EAAE,YAAY,EAAE,CAAC;YACnD,IAAI,CAAC,IAAI,CAAC,CAAkB,mBAAE,KAAK;QACrC,CAAC;QAED,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC/B,MAAM,CAAC,IAAI,CAAC,YAAY;IAE5B,CAAC;IAED,EAAiD,AAAjD,+CAAiD;IACjD,EAAsE,AAAtE,oEAAsE;IACtE,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;QACrC,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK,CAAE,CAAC;YACvB,IAAI,CAAC,MAAM,GAAG,wCAAQ,CAAC,KAAK;YAC5B,IAAI,CAAC,IAAI,CAAC,CAAO,QAAE,IAAI,EAAE,OAAO,EAAE,GAAG;YACrC,IAAI,CAAC,IAAI,CAAC,CAAU,WAAE,IAAI;QAC5B,CAAC;QACD,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,CAAe,gBAAE,KAAK,EAAE,OAAO,EAAE,GAAG;YAC9C,IAAI,CAAC,IAAI,CAAC,CAAkB,mBAAE,KAAK;QACrC,CAAC;QAED,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC/B,MAAM,CAAC,IAAI,CAAC,YAAY;IAE5B,CAAC;WAEM,MAAM,GAAG,CAAC;QACf,MAAM,CAAC,CAAsC,sCAAC,OAAO,UAEnD,QAAQ,CAAE,CAAC,EAAE,CAAC;YACZ,GAAG,CAAC,CAAC,GAAI,IAAI,CAAC,MAAM,KAAK,EAAE,GAAI,CAAC,EAC9B,CAAC,GAAG,CAAC,KAAK,CAAG,KAAG,CAAC,GAAI,CAAC,GAAG,CAAG,GAAI,CAAG;YACrC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE;QACtB,CAAC;IAEL,CAAC;gBA7oDW,EAAE,EAAE,OAAO,CAAE,CAAC;QACxB,KAAK;QACL,GAAG,CAAC,QAAQ,EAAE,IAAI;QAClB,IAAI,CAAC,OAAO,GAAG,EAAE;QAEjB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAE,CAAY,AAAZ,EAAY,AAAZ,UAAY;QAE7B,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,KAAK,CAAQ,SAClC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO;QAGpD,EAAmF,AAAnF,iFAAmF;QACnF,EAAE,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,EAChD,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAA2B;QAG7C,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EACvB,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAA4B;QAG9C,EAA0C,AAA1C,wCAA0C;QAC1C,wCAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;QAE5B,EAA8C,AAA9C,4CAA8C;QAC9C,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI;QAE5B,GAAG,CAAC,cAAc,IACf,IAAI,GAAG,wCAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC;QAAA,CAAC;QAEvE,IAAI,CAAC,OAAO,GAAG,iBAAM,CACnB,IAAI,EACJ,CAAC;QAAA,CAAC,EACF,wCAAc,EACd,cAAc,EACd,OAAO,IAAI,IAAI,GAAG,OAAO,GAAG,CAAC;QAAA,CAAC;QAGhC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,SAEjE,CAAE;QAGJ,EAA0D,AAA1D,wDAA0D;QAC1D,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,wCAAQ,CAAC,kBAAkB,IAC5D,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QAGxC,EAAqE,AAArE,mEAAqE;QACrE,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,EAC1B,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAQ;QAGvD,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EACnB,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAkB;QAGpC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAC9D,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,CAAoG;QAIxG,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EACtD,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAmD;QAGrE,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EACxD,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAqD;QAGvE,EAA0B,AAA1B,wBAA0B;QAC1B,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB;YAC3D,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB;QACvC,CAAC;QAED,EAA0B,AAA1B,wBAA0B;QAC1B,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,EACrC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,GAC7B,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI;;QAG1D,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAQ,SACzC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW;QAGvD,EAAE,GAAG,QAAQ,GAAG,IAAI,CAAC,mBAAmB,OAAO,QAAQ,CAAC,UAAU,EAChE,EAAsB,AAAtB,oBAAsB;QACtB,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ;QAG1C,EAA2G,AAA3G,yGAA2G;QAC3G,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,KAAK,KAAK;YAC1C,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAChC,IAAI,CAAC,iBAAiB,GAAG,wCAAQ,CAAC,UAAU,CAC1C,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAC9B,CAAmB;iBAGrB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO;;QAIzC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;YACxB,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,IAAI,EACjC,IAAI,CAAC,iBAAiB,GAAG,CAAC;gBAAA,IAAI,CAAC,OAAO;YAAA,CAAC;iBAEvC,IAAI,CAAC,iBAAiB,GAAG,wCAAQ,CAAC,WAAW,CAC3C,IAAI,CAAC,OAAO,CAAC,SAAS,EACtB,CAAW;;QAKjB,IAAI,CAAC,IAAI;IACX,CAAC;;AA0hDH,wCAAQ,CAAC,SAAS;AAElB,EAA4E,AAA5E,0EAA4E;AAC5E,EAAsD,AAAtD,oDAAsD;AACtD,EAAE;AACF,EAAW,AAAX,SAAW;AACX,EAAE;AACF,EAAiE,AAAjE,+DAAiE;AACjE,EAAE;AACF,EAAe,AAAf,aAAe;AACf,EAAE;AACF,EAAkF,AAAlF,gFAAkF;AAClF,wCAAQ,CAAC,OAAO,GAAG,CAAC;AAAA,CAAC;AAErB,EAAqE,AAArE,mEAAqE;AACrE,wCAAQ,CAAC,iBAAiB,GAAG,QAAQ,CAAE,OAAO,EAAE,CAAC;IAC/C,EAAqE,AAArE,mEAAqE;IACrE,EAAE,EAAE,OAAO,CAAC,YAAY,CAAC,CAAI,MAC3B,MAAM,CAAC,wCAAQ,CAAC,OAAO,CAAC,8BAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAI;SAE1D,MAAM,CAAC,SAAS;AAEpB,CAAC;AAED,EAAyC,AAAzC,uCAAyC;AACzC,wCAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;AAEvB,EAAgD,AAAhD,8CAAgD;AAChD,wCAAQ,CAAC,UAAU,GAAG,QAAQ,CAAE,OAAO,EAAE,CAAC;IACxC,EAAE,EAAE,MAAM,CAAC,OAAO,KAAK,CAAQ,SAC7B,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO;IAE1C,EAAE,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,CAAC,QAAQ,GAAG,SAAS,KAAK,IAAI,EAC1D,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,CAAgN;IAGpN,MAAM,CAAC,OAAO,CAAC,QAAQ;AACzB,CAAC;AAED,EAAmE,AAAnE,iEAAmE;AACnE,wCAAQ,CAAC,QAAQ,GAAG,QAAQ,GAAI,CAAC;IAC/B,GAAG,CAAC,SAAS;IACb,EAAE,EAAE,QAAQ,CAAC,gBAAgB,EAC3B,SAAS,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAW;SAC5C,CAAC;QACN,SAAS,GAAG,CAAC,CAAC;QACd,EAAQ,AAAR,MAAQ;QACR,GAAG,CAAC,aAAa,IAAI,QAAQ,QACpB,CAAC;gBACN,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;gBACf,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI,QAAQ,CACrB,EAAE,uBAAuB,IAAI,CAAC,EAAE,CAAC,SAAS,GACxC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;qBAE7B,MAAM,CAAC,IAAI,CAAC,SAAS;gBAGzB,MAAM,CAAC,MAAM;YACf,CAAC;;QACH,aAAa,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAK;QACjD,aAAa,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAM;IACpD,CAAC;IAED,MAAM,MAAQ,CAAC;QACb,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;QACf,GAAG,EAAE,GAAG,CAAC,QAAQ,IAAI,SAAS,CAC5B,EAAgF,AAAhF,8EAAgF;QAChF,EAAE,EAAE,wCAAQ,CAAC,iBAAiB,CAAC,QAAQ,MAAM,KAAK,EAChD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,wCAAQ,CAAC,QAAQ;aAEjC,MAAM,CAAC,IAAI,CAAC,SAAS;QAGzB,MAAM,CAAC,MAAM;IACf,CAAC;AACH,CAAC;AAED,EAAwE,AAAxE,sEAAwE;AACxE,EAAE;AACF,EAA8E,AAA9E,4EAA8E;AAC9E,EAAyE,AAAzE,uEAAyE;AACzE,EAAiB,AAAjB,eAAiB;AACjB,EAAE;AACF,EAA2E,AAA3E,yEAA2E;AAC3E,EAAE;AACF,EAAsE,AAAtE,oEAAsE;AACtE,EAAiB,AAAjB,eAAiB;AACjB,wCAAQ,CAAC,eAAe,GAAG,CAAC;IAC1B,EAA0G,AAA1G,wGAA0G;;AAE5G,CAAC;AAED,EAAqC,AAArC,mCAAqC;AACrC,wCAAQ,CAAC,kBAAkB,GAAG,QAAQ,GAAI,CAAC;IACzC,GAAG,CAAC,cAAc,GAAG,IAAI;IAEzB,EAAE,EACA,MAAM,CAAC,IAAI,IACX,MAAM,CAAC,UAAU,IACjB,MAAM,CAAC,QAAQ,IACf,MAAM,CAAC,IAAI,IACX,MAAM,CAAC,QAAQ,IACf,QAAQ,CAAC,aAAa;QAEtB,EAAE,IAAI,CAAW,cAAI,QAAQ,CAAC,aAAa,CAAC,CAAG,MAC7C,cAAc,GAAG,KAAK;aACjB,CAAC;YACN,EAAE,EAAE,wCAAQ,CAAC,mBAAmB,KAAK,SAAS,EAC5C,EAAoE,AAApE,kEAAoE;YACpE,EAAiB,AAAjB,eAAiB;YACjB,wCAAQ,CAAC,eAAe,GAAG,wCAAQ,CAAC,mBAAmB;YAEzD,EAAoD,AAApD,kDAAoD;YACpD,GAAG,EAAE,GAAG,CAAC,KAAK,IAAI,wCAAQ,CAAC,eAAe,CACxC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC;gBACpC,cAAc,GAAG,KAAK;gBACtB,QAAQ;YACV,CAAC;QAEL,CAAC;WAED,cAAc,GAAG,KAAK;IAGxB,MAAM,CAAC,cAAc;AACvB,CAAC;AAED,wCAAQ,CAAC,aAAa,GAAG,QAAQ,CAAE,OAAO,EAAE,CAAC;IAC3C,EAAqD,AAArD,mDAAqD;IACrD,EAAsF,AAAtF,oFAAsF;IACtF,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAG,IAAE,CAAC;IAE1C,EAAkC,AAAlC,gCAAkC;IAClC,GAAG,CAAC,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAG,IAAE,CAAC,EAAE,KAAK,CAAC,CAAG,IAAE,CAAC,EAAE,KAAK,CAAC,CAAG,IAAE,CAAC;IAEjE,EAAkD,AAAlD,gDAAkD;IAClD,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM;IAC1C,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE;IAC1B,GAAG,CACD,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,GAAG,EAClD,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,EACzB,GAAG,GAAG,CAAC,KAAK,CAAC,GAEb,EAAE,CAAC,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;IAGjC,EAAkC,AAAlC,gCAAkC;IAClC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAAA,EAAE;IAAA,CAAC,EAAE,CAAC;QAAC,IAAI,EAAE,UAAU;IAAC,CAAC;AAC5C,CAAC;AAED,EAA6C,AAA7C,2CAA6C;AAC7C,KAAK,CAAC,6BAAO,IAAI,IAAI,EAAE,YAAY,GACjC,IAAI,CAAC,MAAM,EAAE,IAAI,GAAK,IAAI,KAAK,YAAY;MAAE,GAAG,EAAE,IAAI,GAAK,IAAI;;;AAEjE,EAA2B,AAA3B,yBAA2B;AAC3B,KAAK,CAAC,8BAAQ,IAAI,GAAG,GACnB,GAAG,CAAC,OAAO,gBAAgB,KAAK,GAAK,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW;;;AAElE,EAAiC,AAAjC,+BAAiC;AACjC,wCAAQ,CAAC,aAAa,GAAG,QAAQ,CAAE,MAAM,EAAE,CAAC;IAC1C,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAK;IACtC,GAAG,CAAC,SAAS,GAAG,MAAM;IACtB,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACzB,CAAC;AAED,EAAgE,AAAhE,8DAAgE;AAChE,wCAAQ,CAAC,aAAa,GAAG,QAAQ,CAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IACtD,EAAE,EAAE,OAAO,KAAK,SAAS,EACvB,MAAM,CAAC,IAAI;IACX,CAA8C,AAA9C,EAA8C,AAA9C,4CAA8C;UACxC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAG,CAAC;QACtC,EAAE,EAAE,OAAO,KAAK,SAAS,EACvB,MAAM,CAAC,IAAI;IAEf,CAAC;IACD,MAAM,CAAC,KAAK;AACd,CAAC;AAED,wCAAQ,CAAC,UAAU,GAAG,QAAQ,CAAE,EAAE,EAAE,IAAI,EAAE,CAAC;IACzC,GAAG,CAAC,OAAO;IACX,EAAE,EAAE,MAAM,CAAC,EAAE,KAAK,CAAQ,SACxB,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE;SAC9B,EAAE,EAAE,EAAE,CAAC,QAAQ,IAAI,IAAI,EAC5B,OAAO,GAAG,EAAE;IAEd,EAAE,EAAE,OAAO,IAAI,IAAI,EACjB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,UAAU,EAAE,IAAI,CAAC,0EAA0E;IAGhG,MAAM,CAAC,OAAO;AAChB,CAAC;AAED,wCAAQ,CAAC,WAAW,GAAG,QAAQ,CAAE,GAAG,EAAE,IAAI,EAAE,CAAC;IAC3C,GAAG,CAAC,EAAE,EAAE,QAAQ;IAChB,EAAE,EAAE,GAAG,YAAY,KAAK,EAAE,CAAC;QACzB,QAAQ,GAAG,CAAC,CAAC;QACb,GAAG,CAAC,CAAC;YACH,GAAG,EAAE,EAAE,IAAI,GAAG,CACZ,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI;QAE1C,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;YACX,QAAQ,GAAG,IAAI;QACjB,CAAC;IACH,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,GAAG,KAAK,CAAQ,SAAE,CAAC;QACnC,QAAQ,GAAG,CAAC,CAAC;QACb,GAAG,EAAE,EAAE,IAAI,QAAQ,CAAC,gBAAgB,CAAC,GAAG,EACtC,QAAQ,CAAC,IAAI,CAAC,EAAE;IAEpB,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,QAAQ,IAAI,IAAI,EAC7B,QAAQ,GAAG,CAAC;QAAA,GAAG;IAAA,CAAC;IAGlB,EAAE,EAAE,QAAQ,IAAI,IAAI,KAAK,QAAQ,CAAC,MAAM,EACtC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,UAAU,EAAE,IAAI,CAAC,2FAA2F;IAIjH,MAAM,CAAC,QAAQ;AACjB,CAAC;AAED,EAAwE,AAAxE,sEAAwE;AACxE,EAAE;AACF,EAA2E,AAA3E,yEAA2E;AAC3E,EAAwB,AAAxB,sBAAwB;AACxB,wCAAQ,CAAC,OAAO,GAAG,QAAQ,CAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;IAC1D,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,GACzB,MAAM,CAAC,QAAQ;SACV,EAAE,EAAE,QAAQ,IAAI,IAAI,EACzB,MAAM,CAAC,QAAQ;AAEnB,CAAC;AAED,EAAqC,AAArC,mCAAqC;AACrC,EAAE;AACF,EAA0E,AAA1E,wEAA0E;AAC1E,wCAAQ,CAAC,WAAW,GAAG,QAAQ,CAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IACrD,EAAE,GAAG,aAAa,EAChB,MAAM,CAAC,IAAI;IACX,CAA+C,AAA/C,EAA+C,AAA/C,6CAA+C;IACjD,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,CAAG;IAEvC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI;IACxB,GAAG,CAAC,YAAY,GAAG,QAAQ,CAAC,OAAO,UAAU,CAAE;IAE/C,GAAG,EAAE,GAAG,CAAC,SAAS,IAAI,aAAa,CAAE,CAAC;QACpC,SAAS,GAAG,SAAS,CAAC,IAAI;QAC1B,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAG,IAAE,CAAC;YAChC,EAAE,EACA,IAAI,CAAC,IAAI,CACN,WAAW,GACX,OAAO,CACN,SAAS,CAAC,WAAW,IACrB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,MAC/B,EAAE,EAEV,MAAM,CAAC,IAAI;QAEf,CAAC,MAAM,EAAE,UAAU,IAAI,CAAC,SAAS,GAAG,CAAC;YACnC,EAA6C,AAA7C,2CAA6C;YAC7C,EAAE,EAAE,YAAY,KAAK,SAAS,CAAC,OAAO,UAAU,CAAE,IAChD,MAAM,CAAC,IAAI;QAEf,CAAC,MAAM,CAAC;YACN,EAAE,EAAE,QAAQ,KAAK,SAAS,EACxB,MAAM,CAAC,IAAI;QAEf,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK;AACd,CAAC;AAED,EAAiB,AAAjB,eAAiB;AACjB,EAAE,EAAE,MAAM,CAAC,MAAM,KAAK,CAAW,cAAI,MAAM,KAAK,IAAI,EAClD,MAAM,CAAC,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAE,OAAO,EAAE,CAAC;IACvC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAI,CAAC;QAC5B,MAAM,CAAC,GAAG,CAAC,wCAAQ,CAAC,IAAI,EAAE,OAAO;IACnC,CAAC;AACH,CAAC;AAGH,EAA6B,AAA7B,2BAA6B;AAC7B,wCAAQ,CAAC,KAAK,GAAG,CAAO;AAExB,wCAAQ,CAAC,MAAM,GAAG,CAAQ;AAC1B,EAA8E,AAA9E,4EAA8E;AAC9E,EAAgB,AAAhB,cAAgB;AAChB,wCAAQ,CAAC,QAAQ,GAAG,wCAAQ,CAAC,MAAM;AAEnC,wCAAQ,CAAC,SAAS,GAAG,CAAW;AAChC,wCAAQ,CAAC,UAAU,GAAG,wCAAQ,CAAC,SAAS,CAAE,CAAQ,AAAR,EAAQ,AAAR,MAAQ;AAElD,wCAAQ,CAAC,QAAQ,GAAG,CAAU;AAC9B,wCAAQ,CAAC,KAAK,GAAG,CAAO;AACxB,wCAAQ,CAAC,OAAO,GAAG,CAAS;AAE5B,EAMG,AANH;;;;;;CAMG,AANH,EAMG,CAEH,EAA6C,AAA7C,2CAA6C;AAC7C,EAAuF,AAAvF,qFAAuF;AACvF,EAAuG,AAAvG,qGAAuG;AACvG,GAAG,CAAC,0CAAoB,GAAG,QAAQ,CAAE,GAAG,EAAE,CAAC;IACzC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,YAAY;IACzB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,aAAa;IAC1B,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAQ;IAC5C,MAAM,CAAC,KAAK,GAAG,CAAC;IAChB,MAAM,CAAC,MAAM,GAAG,EAAE;IAClB,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,CAAI;IAChC,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;IACvB,GAAG,CAAC,CAAC,OAAC,IAAI,EAAC,CAAC,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IAE3C,EAAsE,AAAtE,oEAAsE;IACtE,GAAG,CAAC,EAAE,GAAG,CAAC;IACV,GAAG,CAAC,EAAE,GAAG,EAAE;IACX,GAAG,CAAC,EAAE,GAAG,EAAE;UACJ,EAAE,GAAG,EAAE,CAAE,CAAC;QACf,GAAG,CAAC,KAAK,GAAG,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;QAEjC,EAAE,EAAE,KAAK,KAAK,CAAC,EACb,EAAE,GAAG,EAAE;aAEP,EAAE,GAAG,EAAE;QAGT,EAAE,GAAI,EAAE,GAAG,EAAE,IAAK,CAAC;IACrB,CAAC;IACD,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE;IAEnB,EAAE,EAAE,KAAK,KAAK,CAAC,EACb,MAAM,CAAC,CAAC;SAER,MAAM,CAAC,KAAK;AAEhB,CAAC;AAED,EAAsC,AAAtC,oCAAsC;AACtC,EAAyC,AAAzC,uCAAyC;AACzC,GAAG,CAAC,qCAAe,GAAG,QAAQ,CAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACzE,GAAG,CAAC,eAAe,GAAG,0CAAoB,CAAC,GAAG;IAC9C,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,eAAe;AAC5E,CAAC;AAED,EAAsB,AAAtB,oBAAsB;AACtB,EAAoD,AAApD,kDAAoD;AACpD,EAAmD,AAAnD,iDAAmD;MAC7C,iCAAW;WACR,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,OAAO,GACV,CAAmE;IACvE,CAAC;WAEM,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,GAAG,CAAC,MAAM,GAAG,CAAE;QACf,GAAG,CAAC,IAAI,GAAG,SAAS;QACpB,GAAG,CAAC,IAAI,GAAG,SAAS;QACpB,GAAG,CAAC,IAAI,GAAG,CAAE;QACb,GAAG,CAAC,IAAI,GAAG,SAAS;QACpB,GAAG,CAAC,IAAI,GAAG,SAAS;QACpB,GAAG,CAAC,IAAI,GAAG,SAAS;QACpB,GAAG,CAAC,IAAI,GAAG,CAAE;QACb,GAAG,CAAC,CAAC,GAAG,CAAC;cACF,IAAI,CAAE,CAAC;YACZ,IAAI,GAAG,KAAK,CAAC,CAAC;YACd,IAAI,GAAG,KAAK,CAAC,CAAC;YACd,IAAI,GAAG,KAAK,CAAC,CAAC;YACd,IAAI,GAAG,IAAI,IAAI,CAAC;YAChB,IAAI,IAAK,IAAI,GAAG,CAAC,KAAK,CAAC,GAAK,IAAI,IAAI,CAAC;YACrC,IAAI,IAAK,IAAI,GAAG,EAAE,KAAK,CAAC,GAAK,IAAI,IAAI,CAAC;YACtC,IAAI,GAAG,IAAI,GAAG,EAAE;YAChB,EAAE,EAAE,KAAK,CAAC,IAAI,GACZ,IAAI,GAAG,IAAI,GAAG,EAAE;iBACX,EAAE,EAAE,KAAK,CAAC,IAAI,GACnB,IAAI,GAAG,EAAE;YAEX,MAAM,GACJ,MAAM,GACN,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IACxB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IACxB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IACxB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI;YAC1B,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAE;YACvB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAE;YAC9B,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GACpB,KAAK;QAET,CAAC;QACD,MAAM,CAAC,MAAM;IACf,CAAC;WAEM,OAAO,CAAC,cAAc,EAAE,iBAAiB,EAAE,CAAC;QACjD,EAAE,GAAG,cAAc,CAAC,KAAK,CAAC,CAAyB,2BACjD,MAAM,CAAC,iBAAiB;QAE1B,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAC1B,cAAc,CAAC,OAAO,CAAC,CAAyB,0BAAE,CAAE;QAEtD,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ;QAC3C,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,QAAQ;QAC7D,MAAM,EAAE,uBAAuB,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK;IACtD,CAAC;WAEM,gBAAgB,CAAC,iBAAiB,EAAE,QAAQ,EAAE,CAAC;QACpD,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ;QAC1C,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,SAAS;QAChE,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,aAAa;QAC1C,MAAM,CAAC,OAAO;IAChB,CAAC;WAEM,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC7B,GAAG,CAAC,GAAG,GAAG,SAAS;QACnB,GAAG,CAAC,CAAC,GAAG,CAAC;cACF,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAE,CAAC;YAC3B,GAAG,GAAG,QAAQ,CAAC,CAAC;YAChB,EAAE,EAAG,GAAG,CAAC,CAAC,MAAM,GAAG,GAAK,GAAG,CAAC,CAAC,MAAM,GAAG,EACpC,MAAM,CAAC,GAAG;YAEZ,CAAC;QACH,CAAC;QACD,MAAM,CAAC,CAAC,CAAC;IACX,CAAC;WAEM,UAAU,CAAC,iBAAiB,EAAE,SAAS,EAAE,CAAC;QAC/C,GAAG,CAAC,SAAS,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAyB,0BAAE,CAAE;QACvE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS;QACjC,GAAG,CAAC,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QACtC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa;QACpC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa;QACjC,GAAG,CAAC,KAAK,GAAG,GAAG;QACf,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS;QAC9B,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG;QACxB,MAAM,CAAC,KAAK;IACd,CAAC;WAEM,cAAc,CAAC,aAAa,EAAE,CAAC;QACpC,GAAG,CAAC,IAAI,GAAG,CAAC;QACZ,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC;cACV,IAAI,CAAE,CAAC;YACZ,GAAG,CAAC,MAAM;YACV,EAAE,EAAG,aAAa,CAAC,IAAI,MAAM,GAAG,GAAK,aAAa,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,EAClE,KAAK;YAEP,EAAE,EAAG,aAAa,CAAC,IAAI,MAAM,GAAG,GAAK,aAAa,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,EAClE,IAAI,IAAI,CAAC;iBACJ,CAAC;gBACN,MAAM,GAAG,aAAa,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,GAAG,CAAC;gBAC/D,GAAG,CAAC,QAAQ,GAAG,IAAI,GAAG,MAAM,GAAG,CAAC;gBAChC,GAAG,CAAC,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ;gBAC5C,QAAQ,CAAC,IAAI,CAAC,GAAG;gBACjB,IAAI,GAAG,QAAQ;YACjB,CAAC;YACD,EAAE,EAAE,IAAI,GAAG,aAAa,CAAC,MAAM,EAC7B,KAAK;QAET,CAAC;QACD,MAAM,CAAC,QAAQ;IACjB,CAAC;WAEM,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,GAAG,CAAC,MAAM,GAAG,CAAE;QACf,GAAG,CAAC,IAAI,GAAG,SAAS;QACpB,GAAG,CAAC,IAAI,GAAG,SAAS;QACpB,GAAG,CAAC,IAAI,GAAG,CAAE;QACb,GAAG,CAAC,IAAI,GAAG,SAAS;QACpB,GAAG,CAAC,IAAI,GAAG,SAAS;QACpB,GAAG,CAAC,IAAI,GAAG,SAAS;QACpB,GAAG,CAAC,IAAI,GAAG,CAAE;QACb,GAAG,CAAC,CAAC,GAAG,CAAC;QACT,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QACZ,EAA+D,AAA/D,6DAA+D;QAC/D,GAAG,CAAC,UAAU;QACd,EAAE,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,GACvB,OAAO,CAAC,IAAI,CACV,CAAkJ;QAGtJ,KAAK,GAAG,KAAK,CAAC,OAAO,wBAAwB,CAAE;cACxC,IAAI,CAAE,CAAC;YACZ,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC1C,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC1C,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC1C,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC1C,IAAI,GAAI,IAAI,IAAI,CAAC,GAAK,IAAI,IAAI,CAAC;YAC/B,IAAI,IAAK,IAAI,GAAG,EAAE,KAAK,CAAC,GAAK,IAAI,IAAI,CAAC;YACtC,IAAI,IAAK,IAAI,GAAG,CAAC,KAAK,CAAC,GAAI,IAAI;YAC/B,GAAG,CAAC,IAAI,CAAC,IAAI;YACb,EAAE,EAAE,IAAI,KAAK,EAAE,EACb,GAAG,CAAC,IAAI,CAAC,IAAI;YAEf,EAAE,EAAE,IAAI,KAAK,EAAE,EACb,GAAG,CAAC,IAAI,CAAC,IAAI;YAEf,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAE;YACvB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAE;YAC9B,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GACpB,KAAK;QAET,CAAC;QACD,MAAM,CAAC,GAAG;IACZ,CAAC;;AAEH,iCAAW,CAAC,SAAS;AAErB,EAYG,AAZH;;;;;;;;;;;;CAYG,AAZH,EAYG,CAEH,EAAwB,AAAxB,sBAAwB;AACxB,EAAyB,AAAzB,uBAAyB;AACzB,GAAG,CAAC,mCAAa,GAAG,QAAQ,CAAE,GAAG,EAAE,EAAE,EAAE,CAAC;IACtC,GAAG,CAAC,IAAI,GAAG,KAAK;IAChB,GAAG,CAAC,GAAG,GAAG,IAAI;IACd,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ;IACtB,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,eAAe;IAC9B,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,gBAAgB,GAAG,CAAkB,oBAAG,CAAa;IACnE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,gBAAgB,GAAG,CAAqB,uBAAG,CAAa;IACtE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,gBAAgB,GAAG,CAAE,IAAG,CAAI;IAC1C,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAE,CAAC,EAAE,CAAC;QACvB,EAAE,EAAE,CAAC,CAAC,IAAI,KAAK,CAAkB,qBAAI,GAAG,CAAC,UAAU,KAAK,CAAU,WAChE,MAAM;SAEP,CAAC,CAAC,IAAI,KAAK,CAAM,QAAG,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK;QAC9D,EAAE,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GACvB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC;IAEnC,CAAC;IAED,GAAG,CAAC,IAAI,GAAG,QAAQ,GAAI,CAAC;QACtB,GAAG,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,CAAC,CAAM;QACtB,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;YACX,UAAU,CAAC,IAAI,EAAE,EAAE;YACnB,MAAM;QACR,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,CAAM;IACpB,CAAC;IAED,EAAE,EAAE,GAAG,CAAC,UAAU,KAAK,CAAU,WAAE,CAAC;QAClC,EAAE,EAAE,GAAG,CAAC,iBAAiB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC3C,GAAG,CAAC,CAAC;gBACH,GAAG,IAAI,GAAG,CAAC,YAAY;YACzB,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;YAAA,CAAC;YAClB,EAAE,EAAE,GAAG,EACL,IAAI;QAER,CAAC;QACD,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAkB,mBAAE,IAAI,EAAE,KAAK;QAC9C,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAkB,mBAAE,IAAI,EAAE,KAAK;QAC9C,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAM,OAAE,IAAI,EAAE,KAAK;IAC3C,CAAC;AACH,CAAC;SAEQ,+BAAS,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC;IACpC,MAAM,CAAC,MAAM,CAAC,KAAK,KAAK,CAAW,cAAI,KAAK,KAAK,IAAI,GACjD,SAAS,CAAC,KAAK,IACf,SAAS;AACf,CAAC;SACQ,qCAAe,CAAC,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;IACpD,EAAE,EACA,MAAM,CAAC,GAAG,KAAK,CAAW,cAC1B,GAAG,KAAK,IAAI,IACZ,MAAM,CAAC,GAAG,CAAC,UAAU,MAAM,CAAU,WAErC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,UAAU;SAEhC,MAAM,CAAC,SAAS;AAEpB,CAAC", "sources": ["src/dropzone.js", "src/emitter.js", "src/options.js", "node_modules/@parcel/runtime-js/lib/bundles/runtime-044f6de40395a564.js"], "sourcesContent": ["import extend from \"just-extend\";\nimport Emitter from \"./emitter\";\nimport defaultOptions from \"./options\";\n\nexport default class Dropzone extends Emitter {\n  static initClass() {\n    // Exposing the emitter class, mainly for tests\n    this.prototype.Emitter = Emitter;\n\n    /*\n     This is a list of all available events you can register on a dropzone object.\n\n     You can register an event handler like this:\n\n     dropzone.on(\"dragEnter\", function() { });\n\n     */\n    this.prototype.events = [\n      \"drop\",\n      \"dragstart\",\n      \"dragend\",\n      \"dragenter\",\n      \"dragover\",\n      \"dragleave\",\n      \"addedfile\",\n      \"addedfiles\",\n      \"removedfile\",\n      \"thumbnail\",\n      \"error\",\n      \"errormultiple\",\n      \"processing\",\n      \"processingmultiple\",\n      \"uploadprogress\",\n      \"totaluploadprogress\",\n      \"sending\",\n      \"sendingmultiple\",\n      \"success\",\n      \"successmultiple\",\n      \"canceled\",\n      \"canceledmultiple\",\n      \"complete\",\n      \"completemultiple\",\n      \"reset\",\n      \"maxfilesexceeded\",\n      \"maxfilesreached\",\n      \"queuecomplete\",\n    ];\n\n    this.prototype._thumbnailQueue = [];\n    this.prototype._processingThumbnail = false;\n  }\n\n  constructor(el, options) {\n    super();\n    let fallback, left;\n    this.element = el;\n\n    this.clickableElements = [];\n    this.listeners = [];\n    this.files = []; // All files\n\n    if (typeof this.element === \"string\") {\n      this.element = document.querySelector(this.element);\n    }\n\n    // Not checking if instance of HTMLElement or Element since IE9 is extremely weird.\n    if (!this.element || this.element.nodeType == null) {\n      throw new Error(\"Invalid dropzone element.\");\n    }\n\n    if (this.element.dropzone) {\n      throw new Error(\"Dropzone already attached.\");\n    }\n\n    // Now add this dropzone to the instances.\n    Dropzone.instances.push(this);\n\n    // Put the dropzone inside the element itself.\n    this.element.dropzone = this;\n\n    let elementOptions =\n      (left = Dropzone.optionsForElement(this.element)) != null ? left : {};\n\n    this.options = extend(\n      true,\n      {},\n      defaultOptions,\n      elementOptions,\n      options != null ? options : {}\n    );\n\n    this.options.previewTemplate = this.options.previewTemplate.replace(\n      /\\n*/g,\n      \"\"\n    );\n\n    // If the browser failed, just call the fallback and leave\n    if (this.options.forceFallback || !Dropzone.isBrowserSupported()) {\n      return this.options.fallback.call(this);\n    }\n\n    // @options.url = @element.getAttribute \"action\" unless @options.url?\n    if (this.options.url == null) {\n      this.options.url = this.element.getAttribute(\"action\");\n    }\n\n    if (!this.options.url) {\n      throw new Error(\"No URL provided.\");\n    }\n\n    if (this.options.acceptedFiles && this.options.acceptedMimeTypes) {\n      throw new Error(\n        \"You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.\"\n      );\n    }\n\n    if (this.options.uploadMultiple && this.options.chunking) {\n      throw new Error(\"You cannot set both: uploadMultiple and chunking.\");\n    }\n\n    if (this.options.binaryBody && this.options.uploadMultiple) {\n      throw new Error(\"You cannot set both: binaryBody and uploadMultiple.\");\n    }\n\n    // Backwards compatibility\n    if (this.options.acceptedMimeTypes) {\n      this.options.acceptedFiles = this.options.acceptedMimeTypes;\n      delete this.options.acceptedMimeTypes;\n    }\n\n    // Backwards compatibility\n    if (this.options.renameFilename != null) {\n      this.options.renameFile = (file) =>\n        this.options.renameFilename.call(this, file.name, file);\n    }\n\n    if (typeof this.options.method === \"string\") {\n      this.options.method = this.options.method.toUpperCase();\n    }\n\n    if ((fallback = this.getExistingFallback()) && fallback.parentNode) {\n      // Remove the fallback\n      fallback.parentNode.removeChild(fallback);\n    }\n\n    // Display previews in the previewsContainer element or the Dropzone element unless explicitly set to false\n    if (this.options.previewsContainer !== false) {\n      if (this.options.previewsContainer) {\n        this.previewsContainer = Dropzone.getElement(\n          this.options.previewsContainer,\n          \"previewsContainer\"\n        );\n      } else {\n        this.previewsContainer = this.element;\n      }\n    }\n\n    if (this.options.clickable) {\n      if (this.options.clickable === true) {\n        this.clickableElements = [this.element];\n      } else {\n        this.clickableElements = Dropzone.getElements(\n          this.options.clickable,\n          \"clickable\"\n        );\n      }\n    }\n\n    this.init();\n  }\n\n  // Returns all files that have been accepted\n  getAcceptedFiles() {\n    return this.files.filter((file) => file.accepted).map((file) => file);\n  }\n\n  // Returns all files that have been rejected\n  // Not sure when that's going to be useful, but added for completeness.\n  getRejectedFiles() {\n    return this.files.filter((file) => !file.accepted).map((file) => file);\n  }\n\n  getFilesWithStatus(status) {\n    return this.files\n      .filter((file) => file.status === status)\n      .map((file) => file);\n  }\n\n  // Returns all files that are in the queue\n  getQueuedFiles() {\n    return this.getFilesWithStatus(Dropzone.QUEUED);\n  }\n\n  getUploadingFiles() {\n    return this.getFilesWithStatus(Dropzone.UPLOADING);\n  }\n\n  getAddedFiles() {\n    return this.getFilesWithStatus(Dropzone.ADDED);\n  }\n\n  // Files that are either queued or uploading\n  getActiveFiles() {\n    return this.files\n      .filter(\n        (file) =>\n          file.status === Dropzone.UPLOADING || file.status === Dropzone.QUEUED\n      )\n      .map((file) => file);\n  }\n\n  // The function that gets called when Dropzone is initialized. You\n  // can (and should) setup event listeners inside this function.\n  init() {\n    // In case it isn't set already\n    if (this.element.tagName === \"form\") {\n      this.element.setAttribute(\"enctype\", \"multipart/form-data\");\n    }\n\n    if (\n      this.element.classList.contains(\"dropzone\") &&\n      !this.element.querySelector(\".dz-message\")\n    ) {\n      this.element.appendChild(\n        Dropzone.createElement(\n          `<div class=\"dz-default dz-message\"><button class=\"dz-button\" type=\"button\">${this.options.dictDefaultMessage}</button></div>`\n        )\n      );\n    }\n\n    if (this.clickableElements.length) {\n      let setupHiddenFileInput = () => {\n        if (this.hiddenFileInput) {\n          this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput);\n        }\n        this.hiddenFileInput = document.createElement(\"input\");\n        this.hiddenFileInput.setAttribute(\"type\", \"file\");\n        if (this.options.maxFiles === null || this.options.maxFiles > 1) {\n          this.hiddenFileInput.setAttribute(\"multiple\", \"multiple\");\n        }\n        this.hiddenFileInput.className = \"dz-hidden-input\";\n\n        if (this.options.acceptedFiles !== null) {\n          this.hiddenFileInput.setAttribute(\n            \"accept\",\n            this.options.acceptedFiles\n          );\n        }\n        if (this.options.capture !== null) {\n          this.hiddenFileInput.setAttribute(\"capture\", this.options.capture);\n        }\n\n        // Making sure that no one can \"tab\" into this field.\n        this.hiddenFileInput.setAttribute(\"tabindex\", \"-1\");\n\n        // Not setting `display=\"none\"` because some browsers don't accept clicks\n        // on elements that aren't displayed.\n        this.hiddenFileInput.style.visibility = \"hidden\";\n        this.hiddenFileInput.style.position = \"absolute\";\n        this.hiddenFileInput.style.top = \"0\";\n        this.hiddenFileInput.style.left = \"0\";\n        this.hiddenFileInput.style.height = \"0\";\n        this.hiddenFileInput.style.width = \"0\";\n        Dropzone.getElement(\n          this.options.hiddenInputContainer,\n          \"hiddenInputContainer\"\n        ).appendChild(this.hiddenFileInput);\n        this.hiddenFileInput.addEventListener(\"change\", () => {\n          let { files } = this.hiddenFileInput;\n          if (files.length) {\n            for (let file of files) {\n              this.addFile(file);\n            }\n          }\n          this.emit(\"addedfiles\", files);\n          setupHiddenFileInput();\n        });\n      };\n      setupHiddenFileInput();\n    }\n\n    this.URL = window.URL !== null ? window.URL : window.webkitURL;\n\n    // Setup all event listeners on the Dropzone object itself.\n    // They're not in @setupEventListeners() because they shouldn't be removed\n    // again when the dropzone gets disabled.\n    for (let eventName of this.events) {\n      this.on(eventName, this.options[eventName]);\n    }\n\n    this.on(\"uploadprogress\", () => this.updateTotalUploadProgress());\n\n    this.on(\"removedfile\", () => this.updateTotalUploadProgress());\n\n    this.on(\"canceled\", (file) => this.emit(\"complete\", file));\n\n    // Emit a `queuecomplete` event if all files finished uploading.\n    this.on(\"complete\", (file) => {\n      if (\n        this.getAddedFiles().length === 0 &&\n        this.getUploadingFiles().length === 0 &&\n        this.getQueuedFiles().length === 0\n      ) {\n        // This needs to be deferred so that `queuecomplete` really triggers after `complete`\n        return setTimeout(() => this.emit(\"queuecomplete\"), 0);\n      }\n    });\n\n    const containsFiles = function (e) {\n      if (e.dataTransfer.types) {\n        // Because e.dataTransfer.types is an Object in\n        // IE, we need to iterate like this instead of\n        // using e.dataTransfer.types.some()\n        for (var i = 0; i < e.dataTransfer.types.length; i++) {\n          if (e.dataTransfer.types[i] === \"Files\") return true;\n        }\n      }\n      return false;\n    };\n\n    let noPropagation = function (e) {\n      // If there are no files, we don't want to stop\n      // propagation so we don't interfere with other\n      // drag and drop behaviour.\n      if (!containsFiles(e)) return;\n      e.stopPropagation();\n      if (e.preventDefault) {\n        return e.preventDefault();\n      } else {\n        return (e.returnValue = false);\n      }\n    };\n\n    // Create the listeners\n    this.listeners = [\n      {\n        element: this.element,\n        events: {\n          dragstart: (e) => {\n            return this.emit(\"dragstart\", e);\n          },\n          dragenter: (e) => {\n            noPropagation(e);\n            return this.emit(\"dragenter\", e);\n          },\n          dragover: (e) => {\n            // Makes it possible to drag files from chrome's download bar\n            // http://stackoverflow.com/questions/19526430/drag-and-drop-file-uploads-from-chrome-downloads-bar\n            // Try is required to prevent bug in Internet Explorer 11 (SCRIPT65535 exception)\n            let efct;\n            try {\n              efct = e.dataTransfer.effectAllowed;\n            } catch (error) {}\n            e.dataTransfer.dropEffect =\n              \"move\" === efct || \"linkMove\" === efct ? \"move\" : \"copy\";\n\n            noPropagation(e);\n            return this.emit(\"dragover\", e);\n          },\n          dragleave: (e) => {\n            return this.emit(\"dragleave\", e);\n          },\n          drop: (e) => {\n            noPropagation(e);\n            return this.drop(e);\n          },\n          dragend: (e) => {\n            return this.emit(\"dragend\", e);\n          },\n        },\n\n        // This is disabled right now, because the browsers don't implement it properly.\n        // \"paste\": (e) =>\n        //   noPropagation e\n        //   @paste e\n      },\n    ];\n\n    this.clickableElements.forEach((clickableElement) => {\n      return this.listeners.push({\n        element: clickableElement,\n        events: {\n          click: (evt) => {\n            // Only the actual dropzone or the message element should trigger file selection\n            if (\n              clickableElement !== this.element ||\n              evt.target === this.element ||\n              Dropzone.elementInside(\n                evt.target,\n                this.element.querySelector(\".dz-message\")\n              )\n            ) {\n              this.hiddenFileInput.click(); // Forward the click\n            }\n            return true;\n          },\n        },\n      });\n    });\n\n    this.enable();\n\n    return this.options.init.call(this);\n  }\n\n  // Not fully tested yet\n  destroy() {\n    this.disable();\n    this.removeAllFiles(true);\n    if (\n      this.hiddenFileInput != null ? this.hiddenFileInput.parentNode : undefined\n    ) {\n      this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput);\n      this.hiddenFileInput = null;\n    }\n    delete this.element.dropzone;\n    return Dropzone.instances.splice(Dropzone.instances.indexOf(this), 1);\n  }\n\n  updateTotalUploadProgress() {\n    let totalUploadProgress;\n    let totalBytesSent = 0;\n    let totalBytes = 0;\n\n    let activeFiles = this.getActiveFiles();\n\n    if (activeFiles.length) {\n      for (let file of this.getActiveFiles()) {\n        totalBytesSent += file.upload.bytesSent;\n        totalBytes += file.upload.total;\n      }\n      totalUploadProgress = (100 * totalBytesSent) / totalBytes;\n    } else {\n      totalUploadProgress = 100;\n    }\n\n    return this.emit(\n      \"totaluploadprogress\",\n      totalUploadProgress,\n      totalBytes,\n      totalBytesSent\n    );\n  }\n\n  // @options.paramName can be a function taking one parameter rather than a string.\n  // A parameter name for a file is obtained simply by calling this with an index number.\n  _getParamName(n) {\n    if (typeof this.options.paramName === \"function\") {\n      return this.options.paramName(n);\n    } else {\n      return `${this.options.paramName}${\n        this.options.uploadMultiple ? `[${n}]` : \"\"\n      }`;\n    }\n  }\n\n  // If @options.renameFile is a function,\n  // the function will be used to rename the file.name before appending it to the formData\n  _renameFile(file) {\n    if (typeof this.options.renameFile !== \"function\") {\n      return file.name;\n    }\n    return this.options.renameFile(file);\n  }\n\n  // Returns a form that can be used as fallback if the browser does not support DragnDrop\n  //\n  // If the dropzone is already a form, only the input field and button are returned. Otherwise a complete form element is provided.\n  // This code has to pass in IE7 :(\n  getFallbackForm() {\n    let existingFallback, form;\n    if ((existingFallback = this.getExistingFallback())) {\n      return existingFallback;\n    }\n\n    let fieldsString = '<div class=\"dz-fallback\">';\n    if (this.options.dictFallbackText) {\n      fieldsString += `<p>${this.options.dictFallbackText}</p>`;\n    }\n    fieldsString += `<input type=\"file\" name=\"${this._getParamName(0)}\" ${\n      this.options.uploadMultiple ? 'multiple=\"multiple\"' : undefined\n    } /><input type=\"submit\" value=\"Upload!\"></div>`;\n\n    let fields = Dropzone.createElement(fieldsString);\n    if (this.element.tagName !== \"FORM\") {\n      form = Dropzone.createElement(\n        `<form action=\"${this.options.url}\" enctype=\"multipart/form-data\" method=\"${this.options.method}\"></form>`\n      );\n      form.appendChild(fields);\n    } else {\n      // Make sure that the enctype and method attributes are set properly\n      this.element.setAttribute(\"enctype\", \"multipart/form-data\");\n      this.element.setAttribute(\"method\", this.options.method);\n    }\n    return form != null ? form : fields;\n  }\n\n  // Returns the fallback elements if they exist already\n  //\n  // This code has to pass in IE7 :(\n  getExistingFallback() {\n    let getFallback = function (elements) {\n      for (let el of elements) {\n        if (/(^| )fallback($| )/.test(el.className)) {\n          return el;\n        }\n      }\n    };\n\n    for (let tagName of [\"div\", \"form\"]) {\n      var fallback;\n      if (\n        (fallback = getFallback(this.element.getElementsByTagName(tagName)))\n      ) {\n        return fallback;\n      }\n    }\n  }\n\n  // Activates all listeners stored in @listeners\n  setupEventListeners() {\n    return this.listeners.map((elementListeners) =>\n      (() => {\n        let result = [];\n        for (let event in elementListeners.events) {\n          let listener = elementListeners.events[event];\n          result.push(\n            elementListeners.element.addEventListener(event, listener, false)\n          );\n        }\n        return result;\n      })()\n    );\n  }\n\n  // Deactivates all listeners stored in @listeners\n  removeEventListeners() {\n    return this.listeners.map((elementListeners) =>\n      (() => {\n        let result = [];\n        for (let event in elementListeners.events) {\n          let listener = elementListeners.events[event];\n          result.push(\n            elementListeners.element.removeEventListener(event, listener, false)\n          );\n        }\n        return result;\n      })()\n    );\n  }\n\n  // Removes all event listeners and cancels all files in the queue or being processed.\n  disable() {\n    this.clickableElements.forEach((element) =>\n      element.classList.remove(\"dz-clickable\")\n    );\n    this.removeEventListeners();\n    this.disabled = true;\n\n    return this.files.map((file) => this.cancelUpload(file));\n  }\n\n  enable() {\n    delete this.disabled;\n    this.clickableElements.forEach((element) =>\n      element.classList.add(\"dz-clickable\")\n    );\n    return this.setupEventListeners();\n  }\n\n  // Returns a nicely formatted filesize\n  filesize(size) {\n    let selectedSize = 0;\n    let selectedUnit = \"b\";\n\n    if (size > 0) {\n      let units = [\"tb\", \"gb\", \"mb\", \"kb\", \"b\"];\n\n      for (let i = 0; i < units.length; i++) {\n        let unit = units[i];\n        let cutoff = Math.pow(this.options.filesizeBase, 4 - i) / 10;\n\n        if (size >= cutoff) {\n          selectedSize = size / Math.pow(this.options.filesizeBase, 4 - i);\n          selectedUnit = unit;\n          break;\n        }\n      }\n\n      selectedSize = Math.round(10 * selectedSize) / 10; // Cutting of digits\n    }\n\n    return `<strong>${selectedSize}</strong> ${this.options.dictFileSizeUnits[selectedUnit]}`;\n  }\n\n  // Adds or removes the `dz-max-files-reached` class from the form.\n  _updateMaxFilesReachedClass() {\n    if (\n      this.options.maxFiles != null &&\n      this.getAcceptedFiles().length >= this.options.maxFiles\n    ) {\n      if (this.getAcceptedFiles().length === this.options.maxFiles) {\n        this.emit(\"maxfilesreached\", this.files);\n      }\n      return this.element.classList.add(\"dz-max-files-reached\");\n    } else {\n      return this.element.classList.remove(\"dz-max-files-reached\");\n    }\n  }\n\n  drop(e) {\n    if (!e.dataTransfer) {\n      return;\n    }\n    this.emit(\"drop\", e);\n\n    // Convert the FileList to an Array\n    // This is necessary for IE11\n    let files = [];\n    for (let i = 0; i < e.dataTransfer.files.length; i++) {\n      files[i] = e.dataTransfer.files[i];\n    }\n\n    // Even if it's a folder, files.length will contain the folders.\n    if (files.length) {\n      let { items } = e.dataTransfer;\n      if (items && items.length && items[0].webkitGetAsEntry != null) {\n        // The browser supports dropping of folders, so handle items instead of files\n        this._addFilesFromItems(items);\n      } else {\n        this.handleFiles(files);\n      }\n    }\n\n    this.emit(\"addedfiles\", files);\n  }\n\n  paste(e) {\n    if (\n      __guard__(e != null ? e.clipboardData : undefined, (x) => x.items) == null\n    ) {\n      return;\n    }\n\n    this.emit(\"paste\", e);\n    let { items } = e.clipboardData;\n\n    if (items.length) {\n      return this._addFilesFromItems(items);\n    }\n  }\n\n  handleFiles(files) {\n    for (let file of files) {\n      this.addFile(file);\n    }\n  }\n\n  // When a folder is dropped (or files are pasted), items must be handled\n  // instead of files.\n  _addFilesFromItems(items) {\n    return (() => {\n      let result = [];\n      for (let item of items) {\n        var entry;\n        if (\n          item.webkitGetAsEntry != null &&\n          (entry = item.webkitGetAsEntry())\n        ) {\n          if (entry.isFile) {\n            result.push(this.addFile(item.getAsFile()));\n          } else if (entry.isDirectory) {\n            // Append all files from that directory to files\n            result.push(this._addFilesFromDirectory(entry, entry.name));\n          } else {\n            result.push(undefined);\n          }\n        } else if (item.getAsFile != null) {\n          if (item.kind == null || item.kind === \"file\") {\n            result.push(this.addFile(item.getAsFile()));\n          } else {\n            result.push(undefined);\n          }\n        } else {\n          result.push(undefined);\n        }\n      }\n      return result;\n    })();\n  }\n\n  // Goes through the directory, and adds each file it finds recursively\n  _addFilesFromDirectory(directory, path) {\n    let dirReader = directory.createReader();\n\n    let errorHandler = (error) =>\n      __guardMethod__(console, \"log\", (o) => o.log(error));\n\n    var readEntries = () => {\n      return dirReader.readEntries((entries) => {\n        if (entries.length > 0) {\n          for (let entry of entries) {\n            if (entry.isFile) {\n              entry.file((file) => {\n                if (\n                  this.options.ignoreHiddenFiles &&\n                  file.name.substring(0, 1) === \".\"\n                ) {\n                  return;\n                }\n                file.fullPath = `${path}/${file.name}`;\n                return this.addFile(file);\n              });\n            } else if (entry.isDirectory) {\n              this._addFilesFromDirectory(entry, `${path}/${entry.name}`);\n            }\n          }\n\n          // Recursively call readEntries() again, since browser only handle\n          // the first 100 entries.\n          // See: https://developer.mozilla.org/en-US/docs/Web/API/DirectoryReader#readEntries\n          readEntries();\n        }\n        return null;\n      }, errorHandler);\n    };\n\n    return readEntries();\n  }\n\n  // If `done()` is called without argument the file is accepted\n  // If you call it with an error message, the file is rejected\n  // (This allows for asynchronous validation)\n  //\n  // This function checks the filesize, and if the file.type passes the\n  // `acceptedFiles` check.\n  accept(file, done) {\n    if (\n      this.options.maxFilesize &&\n      file.size > this.options.maxFilesize * 1024 * 1024\n    ) {\n      done(\n        this.options.dictFileTooBig\n          .replace(\"{{filesize}}\", Math.round(file.size / 1024 / 10.24) / 100)\n          .replace(\"{{maxFilesize}}\", this.options.maxFilesize)\n      );\n    } else if (!Dropzone.isValidFile(file, this.options.acceptedFiles)) {\n      done(this.options.dictInvalidFileType);\n    } else if (\n      this.options.maxFiles != null &&\n      this.getAcceptedFiles().length >= this.options.maxFiles\n    ) {\n      done(\n        this.options.dictMaxFilesExceeded.replace(\n          \"{{maxFiles}}\",\n          this.options.maxFiles\n        )\n      );\n      this.emit(\"maxfilesexceeded\", file);\n    } else {\n      this.options.accept.call(this, file, done);\n    }\n  }\n\n  addFile(file) {\n    file.upload = {\n      uuid: Dropzone.uuidv4(),\n      progress: 0,\n      // Setting the total upload size to file.size for the beginning\n      // It's actual different than the size to be transmitted.\n      total: file.size,\n      bytesSent: 0,\n      filename: this._renameFile(file),\n      // Not setting chunking information here, because the acutal data — and\n      // thus the chunks — might change if `options.transformFile` is set\n      // and does something to the data.\n    };\n    this.files.push(file);\n\n    file.status = Dropzone.ADDED;\n\n    this.emit(\"addedfile\", file);\n\n    this._enqueueThumbnail(file);\n\n    this.accept(file, (error) => {\n      if (error) {\n        file.accepted = false;\n        this._errorProcessing([file], error); // Will set the file.status\n      } else {\n        file.accepted = true;\n        if (this.options.autoQueue) {\n          this.enqueueFile(file);\n        } // Will set .accepted = true\n      }\n      this._updateMaxFilesReachedClass();\n    });\n  }\n\n  // Wrapper for enqueueFile\n  enqueueFiles(files) {\n    for (let file of files) {\n      this.enqueueFile(file);\n    }\n    return null;\n  }\n\n  enqueueFile(file) {\n    if (file.status === Dropzone.ADDED && file.accepted === true) {\n      file.status = Dropzone.QUEUED;\n      if (this.options.autoProcessQueue) {\n        return setTimeout(() => this.processQueue(), 0); // Deferring the call\n      }\n    } else {\n      throw new Error(\n        \"This file can't be queued because it has already been processed or was rejected.\"\n      );\n    }\n  }\n\n  _enqueueThumbnail(file) {\n    if (\n      this.options.createImageThumbnails &&\n      file.type.match(/image.*/) &&\n      file.size <= this.options.maxThumbnailFilesize * 1024 * 1024\n    ) {\n      this._thumbnailQueue.push(file);\n      return setTimeout(() => this._processThumbnailQueue(), 0); // Deferring the call\n    }\n  }\n\n  _processThumbnailQueue() {\n    if (this._processingThumbnail || this._thumbnailQueue.length === 0) {\n      return;\n    }\n\n    this._processingThumbnail = true;\n    let file = this._thumbnailQueue.shift();\n    return this.createThumbnail(\n      file,\n      this.options.thumbnailWidth,\n      this.options.thumbnailHeight,\n      this.options.thumbnailMethod,\n      true,\n      (dataUrl) => {\n        this.emit(\"thumbnail\", file, dataUrl);\n        this._processingThumbnail = false;\n        return this._processThumbnailQueue();\n      }\n    );\n  }\n\n  // Can be called by the user to remove a file\n  removeFile(file) {\n    if (file.status === Dropzone.UPLOADING) {\n      this.cancelUpload(file);\n    }\n    this.files = without(this.files, file);\n\n    this.emit(\"removedfile\", file);\n    if (this.files.length === 0) {\n      return this.emit(\"reset\");\n    }\n  }\n\n  // Removes all files that aren't currently processed from the list\n  removeAllFiles(cancelIfNecessary) {\n    // Create a copy of files since removeFile() changes the @files array.\n    if (cancelIfNecessary == null) {\n      cancelIfNecessary = false;\n    }\n    for (let file of this.files.slice()) {\n      if (file.status !== Dropzone.UPLOADING || cancelIfNecessary) {\n        this.removeFile(file);\n      }\n    }\n    return null;\n  }\n\n  // Resizes an image before it gets sent to the server. This function is the default behavior of\n  // `options.transformFile` if `resizeWidth` or `resizeHeight` are set. The callback is invoked with\n  // the resized blob.\n  resizeImage(file, width, height, resizeMethod, callback) {\n    return this.createThumbnail(\n      file,\n      width,\n      height,\n      resizeMethod,\n      true,\n      (dataUrl, canvas) => {\n        if (canvas == null) {\n          // The image has not been resized\n          return callback(file);\n        } else {\n          let { resizeMimeType } = this.options;\n          if (resizeMimeType == null) {\n            resizeMimeType = file.type;\n          }\n          let resizedDataURL = canvas.toDataURL(\n            resizeMimeType,\n            this.options.resizeQuality\n          );\n          if (\n            resizeMimeType === \"image/jpeg\" ||\n            resizeMimeType === \"image/jpg\"\n          ) {\n            // Now add the original EXIF information\n            resizedDataURL = ExifRestore.restore(file.dataURL, resizedDataURL);\n          }\n          return callback(Dropzone.dataURItoBlob(resizedDataURL));\n        }\n      }\n    );\n  }\n\n  createThumbnail(file, width, height, resizeMethod, fixOrientation, callback) {\n    let fileReader = new FileReader();\n\n    fileReader.onload = () => {\n      file.dataURL = fileReader.result;\n\n      // Don't bother creating a thumbnail for SVG images since they're vector\n      if (file.type === \"image/svg+xml\") {\n        if (callback != null) {\n          callback(fileReader.result);\n        }\n        return;\n      }\n\n      this.createThumbnailFromUrl(\n        file,\n        width,\n        height,\n        resizeMethod,\n        fixOrientation,\n        callback\n      );\n    };\n\n    fileReader.readAsDataURL(file);\n  }\n\n  // `mockFile` needs to have these attributes:\n  //\n  //     { name: 'name', size: 12345, imageUrl: '' }\n  //\n  // `callback` will be invoked when the image has been downloaded and displayed.\n  // `crossOrigin` will be added to the `img` tag when accessing the file.\n  displayExistingFile(\n    mockFile,\n    imageUrl,\n    callback,\n    crossOrigin,\n    resizeThumbnail = true\n  ) {\n    this.emit(\"addedfile\", mockFile);\n    this.emit(\"complete\", mockFile);\n\n    if (!resizeThumbnail) {\n      this.emit(\"thumbnail\", mockFile, imageUrl);\n      if (callback) callback();\n    } else {\n      let onDone = (thumbnail) => {\n        this.emit(\"thumbnail\", mockFile, thumbnail);\n        if (callback) callback();\n      };\n      mockFile.dataURL = imageUrl;\n\n      this.createThumbnailFromUrl(\n        mockFile,\n        this.options.thumbnailWidth,\n        this.options.thumbnailHeight,\n        this.options.thumbnailMethod,\n        this.options.fixOrientation,\n        onDone,\n        crossOrigin\n      );\n    }\n  }\n\n  createThumbnailFromUrl(\n    file,\n    width,\n    height,\n    resizeMethod,\n    fixOrientation,\n    callback,\n    crossOrigin\n  ) {\n    // Not using `new Image` here because of a bug in latest Chrome versions.\n    // See https://github.com/enyo/dropzone/pull/226\n    let img = document.createElement(\"img\");\n\n    if (crossOrigin) {\n      img.crossOrigin = crossOrigin;\n    }\n\n    // fixOrientation is not needed anymore with browsers handling imageOrientation\n    fixOrientation =\n      getComputedStyle(document.body)[\"imageOrientation\"] == \"from-image\"\n        ? false\n        : fixOrientation;\n\n    img.onload = () => {\n      let loadExif = (callback) => callback(1);\n      if (typeof EXIF !== \"undefined\" && EXIF !== null && fixOrientation) {\n        loadExif = (callback) =>\n          EXIF.getData(img, function () {\n            return callback(EXIF.getTag(this, \"Orientation\"));\n          });\n      }\n\n      return loadExif((orientation) => {\n        file.width = img.width;\n        file.height = img.height;\n\n        let resizeInfo = this.options.resize.call(\n          this,\n          file,\n          width,\n          height,\n          resizeMethod\n        );\n\n        let canvas = document.createElement(\"canvas\");\n        let ctx = canvas.getContext(\"2d\");\n\n        canvas.width = resizeInfo.trgWidth;\n        canvas.height = resizeInfo.trgHeight;\n\n        if (orientation > 4) {\n          canvas.width = resizeInfo.trgHeight;\n          canvas.height = resizeInfo.trgWidth;\n        }\n\n        switch (orientation) {\n          case 2:\n            // horizontal flip\n            ctx.translate(canvas.width, 0);\n            ctx.scale(-1, 1);\n            break;\n          case 3:\n            // 180° rotate left\n            ctx.translate(canvas.width, canvas.height);\n            ctx.rotate(Math.PI);\n            break;\n          case 4:\n            // vertical flip\n            ctx.translate(0, canvas.height);\n            ctx.scale(1, -1);\n            break;\n          case 5:\n            // vertical flip + 90 rotate right\n            ctx.rotate(0.5 * Math.PI);\n            ctx.scale(1, -1);\n            break;\n          case 6:\n            // 90° rotate right\n            ctx.rotate(0.5 * Math.PI);\n            ctx.translate(0, -canvas.width);\n            break;\n          case 7:\n            // horizontal flip + 90 rotate right\n            ctx.rotate(0.5 * Math.PI);\n            ctx.translate(canvas.height, -canvas.width);\n            ctx.scale(-1, 1);\n            break;\n          case 8:\n            // 90° rotate left\n            ctx.rotate(-0.5 * Math.PI);\n            ctx.translate(-canvas.height, 0);\n            break;\n        }\n\n        // This is a bugfix for iOS' scaling bug.\n        drawImageIOSFix(\n          ctx,\n          img,\n          resizeInfo.srcX != null ? resizeInfo.srcX : 0,\n          resizeInfo.srcY != null ? resizeInfo.srcY : 0,\n          resizeInfo.srcWidth,\n          resizeInfo.srcHeight,\n          resizeInfo.trgX != null ? resizeInfo.trgX : 0,\n          resizeInfo.trgY != null ? resizeInfo.trgY : 0,\n          resizeInfo.trgWidth,\n          resizeInfo.trgHeight\n        );\n\n        let thumbnail = canvas.toDataURL(\"image/png\");\n\n        if (callback != null) {\n          return callback(thumbnail, canvas);\n        }\n      });\n    };\n\n    if (callback != null) {\n      img.onerror = callback;\n    }\n\n    return (img.src = file.dataURL);\n  }\n\n  // Goes through the queue and processes files if there aren't too many already.\n  processQueue() {\n    let { parallelUploads } = this.options;\n    let processingLength = this.getUploadingFiles().length;\n    let i = processingLength;\n\n    // There are already at least as many files uploading than should be\n    if (processingLength >= parallelUploads) {\n      return;\n    }\n\n    let queuedFiles = this.getQueuedFiles();\n\n    if (!(queuedFiles.length > 0)) {\n      return;\n    }\n\n    if (this.options.uploadMultiple) {\n      // The files should be uploaded in one request\n      return this.processFiles(\n        queuedFiles.slice(0, parallelUploads - processingLength)\n      );\n    } else {\n      while (i < parallelUploads) {\n        if (!queuedFiles.length) {\n          return;\n        } // Nothing left to process\n        this.processFile(queuedFiles.shift());\n        i++;\n      }\n    }\n  }\n\n  // Wrapper for `processFiles`\n  processFile(file) {\n    return this.processFiles([file]);\n  }\n\n  // Loads the file, then calls finishedLoading()\n  processFiles(files) {\n    for (let file of files) {\n      file.processing = true; // Backwards compatibility\n      file.status = Dropzone.UPLOADING;\n\n      this.emit(\"processing\", file);\n    }\n\n    if (this.options.uploadMultiple) {\n      this.emit(\"processingmultiple\", files);\n    }\n\n    return this.uploadFiles(files);\n  }\n\n  _getFilesWithXhr(xhr) {\n    let files;\n    return (files = this.files\n      .filter((file) => file.xhr === xhr)\n      .map((file) => file));\n  }\n\n  // Cancels the file upload and sets the status to CANCELED\n  // **if** the file is actually being uploaded.\n  // If it's still in the queue, the file is being removed from it and the status\n  // set to CANCELED.\n  cancelUpload(file) {\n    if (file.status === Dropzone.UPLOADING) {\n      let groupedFiles = this._getFilesWithXhr(file.xhr);\n      for (let groupedFile of groupedFiles) {\n        groupedFile.status = Dropzone.CANCELED;\n      }\n      if (typeof file.xhr !== \"undefined\") {\n        file.xhr.abort();\n      }\n      for (let groupedFile of groupedFiles) {\n        this.emit(\"canceled\", groupedFile);\n      }\n      if (this.options.uploadMultiple) {\n        this.emit(\"canceledmultiple\", groupedFiles);\n      }\n    } else if (\n      file.status === Dropzone.ADDED ||\n      file.status === Dropzone.QUEUED\n    ) {\n      file.status = Dropzone.CANCELED;\n      this.emit(\"canceled\", file);\n      if (this.options.uploadMultiple) {\n        this.emit(\"canceledmultiple\", [file]);\n      }\n    }\n\n    if (this.options.autoProcessQueue) {\n      return this.processQueue();\n    }\n  }\n\n  resolveOption(option, ...args) {\n    if (typeof option === \"function\") {\n      return option.apply(this, args);\n    }\n    return option;\n  }\n\n  uploadFile(file) {\n    return this.uploadFiles([file]);\n  }\n\n  uploadFiles(files) {\n    this._transformFiles(files, (transformedFiles) => {\n      if (this.options.chunking) {\n        // Chunking is not allowed to be used with `uploadMultiple` so we know\n        // that there is only __one__file.\n        let transformedFile = transformedFiles[0];\n        files[0].upload.chunked =\n          this.options.chunking &&\n          (this.options.forceChunking ||\n            transformedFile.size > this.options.chunkSize);\n        files[0].upload.totalChunkCount = Math.ceil(\n          transformedFile.size / this.options.chunkSize\n        );\n      }\n\n      if (files[0].upload.chunked) {\n        // This file should be sent in chunks!\n\n        // If the chunking option is set, we **know** that there can only be **one** file, since\n        // uploadMultiple is not allowed with this option.\n        let file = files[0];\n        let transformedFile = transformedFiles[0];\n        let startedChunkCount = 0;\n\n        file.upload.chunks = [];\n\n        let handleNextChunk = () => {\n          let chunkIndex = 0;\n\n          // Find the next item in file.upload.chunks that is not defined yet.\n          while (file.upload.chunks[chunkIndex] !== undefined) {\n            chunkIndex++;\n          }\n\n          // This means, that all chunks have already been started.\n          if (chunkIndex >= file.upload.totalChunkCount) return;\n\n          startedChunkCount++;\n\n          let start = chunkIndex * this.options.chunkSize;\n          let end = Math.min(\n            start + this.options.chunkSize,\n            transformedFile.size\n          );\n\n          let dataBlock = {\n            name: this._getParamName(0),\n            data: transformedFile.webkitSlice\n              ? transformedFile.webkitSlice(start, end)\n              : transformedFile.slice(start, end),\n            filename: file.upload.filename,\n            chunkIndex: chunkIndex,\n          };\n\n          file.upload.chunks[chunkIndex] = {\n            file: file,\n            index: chunkIndex,\n            dataBlock: dataBlock, // In case we want to retry.\n            status: Dropzone.UPLOADING,\n            progress: 0,\n            retries: 0, // The number of times this block has been retried.\n          };\n\n          this._uploadData(files, [dataBlock]);\n        };\n\n        file.upload.finishedChunkUpload = (chunk, response) => {\n          let allFinished = true;\n          chunk.status = Dropzone.SUCCESS;\n\n          // Clear the data from the chunk\n          chunk.dataBlock = null;\n          chunk.response = chunk.xhr.responseText;\n          chunk.responseHeaders = chunk.xhr.getAllResponseHeaders();\n          // Leaving this reference to xhr will cause memory leaks.\n          chunk.xhr = null;\n\n          for (let i = 0; i < file.upload.totalChunkCount; i++) {\n            if (file.upload.chunks[i] === undefined) {\n              return handleNextChunk();\n            }\n            if (file.upload.chunks[i].status !== Dropzone.SUCCESS) {\n              allFinished = false;\n            }\n          }\n\n          if (allFinished) {\n            this.options.chunksUploaded(file, () => {\n              this._finished(files, response, null);\n            });\n          }\n        };\n\n        if (this.options.parallelChunkUploads) {\n          for (let i = 0; i < file.upload.totalChunkCount; i++) {\n            handleNextChunk();\n          }\n        } else {\n          handleNextChunk();\n        }\n      } else {\n        let dataBlocks = [];\n        for (let i = 0; i < files.length; i++) {\n          dataBlocks[i] = {\n            name: this._getParamName(i),\n            data: transformedFiles[i],\n            filename: files[i].upload.filename,\n          };\n        }\n        this._uploadData(files, dataBlocks);\n      }\n    });\n  }\n\n  /// Returns the right chunk for given file and xhr\n  _getChunk(file, xhr) {\n    for (let i = 0; i < file.upload.totalChunkCount; i++) {\n      if (\n        file.upload.chunks[i] !== undefined &&\n        file.upload.chunks[i].xhr === xhr\n      ) {\n        return file.upload.chunks[i];\n      }\n    }\n  }\n\n  // This function actually uploads the file(s) to the server.\n  //\n  //  If dataBlocks contains the actual data to upload (meaning, that this could\n  // either be transformed files, or individual chunks for chunked upload) then\n  // they will be used for the actual data to upload.\n  _uploadData(files, dataBlocks) {\n    let xhr = new XMLHttpRequest();\n\n    // Put the xhr object in the file objects to be able to reference it later.\n    for (let file of files) {\n      file.xhr = xhr;\n    }\n    if (files[0].upload.chunked) {\n      // Put the xhr object in the right chunk object, so it can be associated\n      // later, and found with _getChunk.\n      files[0].upload.chunks[dataBlocks[0].chunkIndex].xhr = xhr;\n    }\n\n    let method = this.resolveOption(this.options.method, files, dataBlocks);\n    let url = this.resolveOption(this.options.url, files, dataBlocks);\n    xhr.open(method, url, true);\n\n    // Setting the timeout after open because of IE11 issue: https://gitlab.com/meno/dropzone/issues/8\n    let timeout = this.resolveOption(this.options.timeout, files);\n    if (timeout) xhr.timeout = this.resolveOption(this.options.timeout, files);\n\n    // Has to be after `.open()`. See https://github.com/enyo/dropzone/issues/179\n    xhr.withCredentials = !!this.options.withCredentials;\n\n    xhr.onload = (e) => {\n      this._finishedUploading(files, xhr, e);\n    };\n\n    xhr.ontimeout = () => {\n      this._handleUploadError(\n        files,\n        xhr,\n        `Request timedout after ${this.options.timeout / 1000} seconds`\n      );\n    };\n\n    xhr.onerror = () => {\n      this._handleUploadError(files, xhr);\n    };\n\n    // Some browsers do not have the .upload property\n    let progressObj = xhr.upload != null ? xhr.upload : xhr;\n    progressObj.onprogress = (e) =>\n      this._updateFilesUploadProgress(files, xhr, e);\n\n    let headers = this.options.defaultHeaders\n      ? {\n          Accept: \"application/json\",\n          \"Cache-Control\": \"no-cache\",\n          \"X-Requested-With\": \"XMLHttpRequest\",\n        }\n      : {};\n\n    if (this.options.binaryBody) {\n      headers[\"Content-Type\"] = files[0].type;\n    }\n\n    if (this.options.headers) {\n      extend(headers, this.options.headers);\n    }\n\n    for (let headerName in headers) {\n      let headerValue = headers[headerName];\n      if (headerValue) {\n        xhr.setRequestHeader(headerName, headerValue);\n      }\n    }\n\n    if (this.options.binaryBody) {\n      // Since the file is going to be sent as binary body, it doesn't make\n      // any sense to generate `FormData` for it.\n      for (let file of files) {\n        this.emit(\"sending\", file, xhr);\n      }\n      if (this.options.uploadMultiple) {\n        this.emit(\"sendingmultiple\", files, xhr);\n      }\n      this.submitRequest(xhr, null, files);\n    } else {\n      let formData = new FormData();\n\n      // Adding all @options parameters\n      if (this.options.params) {\n        let additionalParams = this.options.params;\n        if (typeof additionalParams === \"function\") {\n          additionalParams = additionalParams.call(\n            this,\n            files,\n            xhr,\n            files[0].upload.chunked ? this._getChunk(files[0], xhr) : null\n          );\n        }\n\n        for (let key in additionalParams) {\n          let value = additionalParams[key];\n          if (Array.isArray(value)) {\n            // The additional parameter contains an array,\n            // so lets iterate over it to attach each value\n            // individually.\n            for (let i = 0; i < value.length; i++) {\n              formData.append(key, value[i]);\n            }\n          } else {\n            formData.append(key, value);\n          }\n        }\n      }\n\n      // Let the user add additional data if necessary\n      for (let file of files) {\n        this.emit(\"sending\", file, xhr, formData);\n      }\n      if (this.options.uploadMultiple) {\n        this.emit(\"sendingmultiple\", files, xhr, formData);\n      }\n\n      this._addFormElementData(formData);\n\n      // Finally add the files\n      // Has to be last because some servers (eg: S3) expect the file to be the last parameter\n      for (let i = 0; i < dataBlocks.length; i++) {\n        let dataBlock = dataBlocks[i];\n        formData.append(dataBlock.name, dataBlock.data, dataBlock.filename);\n      }\n\n      this.submitRequest(xhr, formData, files);\n    }\n  }\n\n  // Transforms all files with this.options.transformFile and invokes done with the transformed files when done.\n  _transformFiles(files, done) {\n    let transformedFiles = [];\n    // Clumsy way of handling asynchronous calls, until I get to add a proper Future library.\n    let doneCounter = 0;\n    for (let i = 0; i < files.length; i++) {\n      this.options.transformFile.call(this, files[i], (transformedFile) => {\n        transformedFiles[i] = transformedFile;\n        if (++doneCounter === files.length) {\n          done(transformedFiles);\n        }\n      });\n    }\n  }\n\n  // Takes care of adding other input elements of the form to the AJAX request\n  _addFormElementData(formData) {\n    // Take care of other input elements\n    if (this.element.tagName === \"FORM\") {\n      for (let input of this.element.querySelectorAll(\n        \"input, textarea, select, button\"\n      )) {\n        let inputName = input.getAttribute(\"name\");\n        let inputType = input.getAttribute(\"type\");\n        if (inputType) inputType = inputType.toLowerCase();\n\n        // If the input doesn't have a name, we can't use it.\n        if (typeof inputName === \"undefined\" || inputName === null) continue;\n\n        if (input.tagName === \"SELECT\" && input.hasAttribute(\"multiple\")) {\n          // Possibly multiple values\n          for (let option of input.options) {\n            if (option.selected) {\n              formData.append(inputName, option.value);\n            }\n          }\n        } else if (\n          !inputType ||\n          (inputType !== \"checkbox\" && inputType !== \"radio\") ||\n          input.checked\n        ) {\n          formData.append(inputName, input.value);\n        }\n      }\n    }\n  }\n\n  // Invoked when there is new progress information about given files.\n  // If e is not provided, it is assumed that the upload is finished.\n  _updateFilesUploadProgress(files, xhr, e) {\n    if (!files[0].upload.chunked) {\n      // Handle file uploads without chunking\n      for (let file of files) {\n        if (\n          file.upload.total &&\n          file.upload.bytesSent &&\n          file.upload.bytesSent == file.upload.total\n        ) {\n          // If both, the `total` and `bytesSent` have already been set, and\n          // they are equal (meaning progress is at 100%), we can skip this\n          // file, since an upload progress shouldn't go down.\n          continue;\n        }\n\n        if (e) {\n          file.upload.progress = (100 * e.loaded) / e.total;\n          file.upload.total = e.total;\n          file.upload.bytesSent = e.loaded;\n        } else {\n          // No event, so we're at 100%\n          file.upload.progress = 100;\n          file.upload.bytesSent = file.upload.total;\n        }\n\n        this.emit(\n          \"uploadprogress\",\n          file,\n          file.upload.progress,\n          file.upload.bytesSent\n        );\n      }\n    } else {\n      // Handle chunked file uploads\n\n      // Chunked upload is not compatible with uploading multiple files in one\n      // request, so we know there's only one file.\n      let file = files[0];\n\n      // Since this is a chunked upload, we need to update the appropriate chunk\n      // progress.\n      let chunk = this._getChunk(file, xhr);\n\n      if (e) {\n        chunk.progress = (100 * e.loaded) / e.total;\n        chunk.total = e.total;\n        chunk.bytesSent = e.loaded;\n      } else {\n        // No event, so we're at 100%\n        chunk.progress = 100;\n        chunk.bytesSent = chunk.total;\n      }\n\n      // Now tally the *file* upload progress from its individual chunks\n      file.upload.progress = 0;\n      file.upload.total = 0;\n      file.upload.bytesSent = 0;\n      for (let i = 0; i < file.upload.totalChunkCount; i++) {\n        if (\n          file.upload.chunks[i] &&\n          typeof file.upload.chunks[i].progress !== \"undefined\"\n        ) {\n          file.upload.progress += file.upload.chunks[i].progress;\n          file.upload.total += file.upload.chunks[i].total;\n          file.upload.bytesSent += file.upload.chunks[i].bytesSent;\n        }\n      }\n      // Since the process is a percentage, we need to divide by the amount of\n      // chunks we've used.\n      file.upload.progress = file.upload.progress / file.upload.totalChunkCount;\n\n      this.emit(\n        \"uploadprogress\",\n        file,\n        file.upload.progress,\n        file.upload.bytesSent\n      );\n    }\n  }\n\n  _finishedUploading(files, xhr, e) {\n    let response;\n\n    if (files[0].status === Dropzone.CANCELED) {\n      return;\n    }\n\n    if (xhr.readyState !== 4) {\n      return;\n    }\n\n    if (xhr.responseType !== \"arraybuffer\" && xhr.responseType !== \"blob\") {\n      response = xhr.responseText;\n\n      if (\n        xhr.getResponseHeader(\"content-type\") &&\n        ~xhr.getResponseHeader(\"content-type\").indexOf(\"application/json\")\n      ) {\n        try {\n          response = JSON.parse(response);\n        } catch (error) {\n          e = error;\n          response = \"Invalid JSON response from server.\";\n        }\n      }\n    }\n\n    this._updateFilesUploadProgress(files, xhr);\n\n    if (!(200 <= xhr.status && xhr.status < 300)) {\n      this._handleUploadError(files, xhr, response);\n    } else {\n      if (files[0].upload.chunked) {\n        files[0].upload.finishedChunkUpload(\n          this._getChunk(files[0], xhr),\n          response\n        );\n      } else {\n        this._finished(files, response, e);\n      }\n    }\n  }\n\n  _handleUploadError(files, xhr, response) {\n    if (files[0].status === Dropzone.CANCELED) {\n      return;\n    }\n\n    if (files[0].upload.chunked && this.options.retryChunks) {\n      let chunk = this._getChunk(files[0], xhr);\n      if (chunk.retries++ < this.options.retryChunksLimit) {\n        this._uploadData(files, [chunk.dataBlock]);\n        return;\n      } else {\n        console.warn(\"Retried this chunk too often. Giving up.\");\n      }\n    }\n\n    this._errorProcessing(\n      files,\n      response ||\n        this.options.dictResponseError.replace(\"{{statusCode}}\", xhr.status),\n      xhr\n    );\n  }\n\n  submitRequest(xhr, formData, files) {\n    if (xhr.readyState != 1) {\n      console.warn(\n        \"Cannot send this request because the XMLHttpRequest.readyState is not OPENED.\"\n      );\n      return;\n    }\n    if (this.options.binaryBody) {\n      if (files[0].upload.chunked) {\n        const chunk = this._getChunk(files[0], xhr);\n        xhr.send(chunk.dataBlock.data);\n      } else {\n        xhr.send(files[0]);\n      }\n    } else {\n      xhr.send(formData);\n    }\n  }\n\n  // Called internally when processing is finished.\n  // Individual callbacks have to be called in the appropriate sections.\n  _finished(files, responseText, e) {\n    for (let file of files) {\n      file.status = Dropzone.SUCCESS;\n      this.emit(\"success\", file, responseText, e);\n      this.emit(\"complete\", file);\n    }\n    if (this.options.uploadMultiple) {\n      this.emit(\"successmultiple\", files, responseText, e);\n      this.emit(\"completemultiple\", files);\n    }\n\n    if (this.options.autoProcessQueue) {\n      return this.processQueue();\n    }\n  }\n\n  // Called internally when processing is finished.\n  // Individual callbacks have to be called in the appropriate sections.\n  _errorProcessing(files, message, xhr) {\n    for (let file of files) {\n      file.status = Dropzone.ERROR;\n      this.emit(\"error\", file, message, xhr);\n      this.emit(\"complete\", file);\n    }\n    if (this.options.uploadMultiple) {\n      this.emit(\"errormultiple\", files, message, xhr);\n      this.emit(\"completemultiple\", files);\n    }\n\n    if (this.options.autoProcessQueue) {\n      return this.processQueue();\n    }\n  }\n\n  static uuidv4() {\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(\n      /[xy]/g,\n      function (c) {\n        let r = (Math.random() * 16) | 0,\n          v = c === \"x\" ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n      }\n    );\n  }\n}\nDropzone.initClass();\n\n// This is a map of options for your different dropzones. Add configurations\n// to this object for your different dropzone elemens.\n//\n// Example:\n//\n//     Dropzone.options.myDropzoneElementId = { maxFilesize: 1 };\n//\n// And in html:\n//\n//     <form action=\"/upload\" id=\"my-dropzone-element-id\" class=\"dropzone\"></form>\nDropzone.options = {};\n\n// Returns the options for an element or undefined if none available.\nDropzone.optionsForElement = function (element) {\n  // Get the `Dropzone.options.elementId` for this element if it exists\n  if (element.getAttribute(\"id\")) {\n    return Dropzone.options[camelize(element.getAttribute(\"id\"))];\n  } else {\n    return undefined;\n  }\n};\n\n// Holds a list of all dropzone instances\nDropzone.instances = [];\n\n// Returns the dropzone for given element if any\nDropzone.forElement = function (element) {\n  if (typeof element === \"string\") {\n    element = document.querySelector(element);\n  }\n  if ((element != null ? element.dropzone : undefined) == null) {\n    throw new Error(\n      \"No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.\"\n    );\n  }\n  return element.dropzone;\n};\n\n// Looks for all .dropzone elements and creates a dropzone for them\nDropzone.discover = function () {\n  let dropzones;\n  if (document.querySelectorAll) {\n    dropzones = document.querySelectorAll(\".dropzone\");\n  } else {\n    dropzones = [];\n    // IE :(\n    let checkElements = (elements) =>\n      (() => {\n        let result = [];\n        for (let el of elements) {\n          if (/(^| )dropzone($| )/.test(el.className)) {\n            result.push(dropzones.push(el));\n          } else {\n            result.push(undefined);\n          }\n        }\n        return result;\n      })();\n    checkElements(document.getElementsByTagName(\"div\"));\n    checkElements(document.getElementsByTagName(\"form\"));\n  }\n\n  return (() => {\n    let result = [];\n    for (let dropzone of dropzones) {\n      // Create a dropzone unless auto discover has been disabled for specific element\n      if (Dropzone.optionsForElement(dropzone) !== false) {\n        result.push(new Dropzone(dropzone));\n      } else {\n        result.push(undefined);\n      }\n    }\n    return result;\n  })();\n};\n\n// Some browsers support drag and drog functionality, but not correctly.\n//\n// So I created a blocklist of userAgents. Yes, yes. Browser sniffing, I know.\n// But what to do when browsers *theoretically* support an API, but crash\n// when using it.\n//\n// This is a list of regular expressions tested against navigator.userAgent\n//\n// ** It should only be used on browser that *do* support the API, but\n// incorrectly **\nDropzone.blockedBrowsers = [\n  // The mac os and windows phone version of opera 12 seems to have a problem with the File drag'n'drop API.\n  /opera.*(Macintosh|Windows Phone).*version\\/12/i,\n];\n\n// Checks if the browser is supported\nDropzone.isBrowserSupported = function () {\n  let capableBrowser = true;\n\n  if (\n    window.File &&\n    window.FileReader &&\n    window.FileList &&\n    window.Blob &&\n    window.FormData &&\n    document.querySelector\n  ) {\n    if (!(\"classList\" in document.createElement(\"a\"))) {\n      capableBrowser = false;\n    } else {\n      if (Dropzone.blacklistedBrowsers !== undefined) {\n        // Since this has been renamed, this makes sure we don't break older\n        // configuration.\n        Dropzone.blockedBrowsers = Dropzone.blacklistedBrowsers;\n      }\n      // The browser supports the API, but may be blocked.\n      for (let regex of Dropzone.blockedBrowsers) {\n        if (regex.test(navigator.userAgent)) {\n          capableBrowser = false;\n          continue;\n        }\n      }\n    }\n  } else {\n    capableBrowser = false;\n  }\n\n  return capableBrowser;\n};\n\nDropzone.dataURItoBlob = function (dataURI) {\n  // convert base64 to raw binary data held in a string\n  // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this\n  let byteString = atob(dataURI.split(\",\")[1]);\n\n  // separate out the mime component\n  let mimeString = dataURI.split(\",\")[0].split(\":\")[1].split(\";\")[0];\n\n  // write the bytes of the string to an ArrayBuffer\n  let ab = new ArrayBuffer(byteString.length);\n  let ia = new Uint8Array(ab);\n  for (\n    let i = 0, end = byteString.length, asc = 0 <= end;\n    asc ? i <= end : i >= end;\n    asc ? i++ : i--\n  ) {\n    ia[i] = byteString.charCodeAt(i);\n  }\n\n  // write the ArrayBuffer to a blob\n  return new Blob([ab], { type: mimeString });\n};\n\n// Returns an array without the rejected item\nconst without = (list, rejectedItem) =>\n  list.filter((item) => item !== rejectedItem).map((item) => item);\n\n// abc-def_ghi -> abcDefGhi\nconst camelize = (str) =>\n  str.replace(/[\\-_](\\w)/g, (match) => match.charAt(1).toUpperCase());\n\n// Creates an element from string\nDropzone.createElement = function (string) {\n  let div = document.createElement(\"div\");\n  div.innerHTML = string;\n  return div.childNodes[0];\n};\n\n// Tests if given element is inside (or simply is) the container\nDropzone.elementInside = function (element, container) {\n  if (element === container) {\n    return true;\n  } // Coffeescript doesn't support do/while loops\n  while ((element = element.parentNode)) {\n    if (element === container) {\n      return true;\n    }\n  }\n  return false;\n};\n\nDropzone.getElement = function (el, name) {\n  let element;\n  if (typeof el === \"string\") {\n    element = document.querySelector(el);\n  } else if (el.nodeType != null) {\n    element = el;\n  }\n  if (element == null) {\n    throw new Error(\n      `Invalid \\`${name}\\` option provided. Please provide a CSS selector or a plain HTML element.`\n    );\n  }\n  return element;\n};\n\nDropzone.getElements = function (els, name) {\n  let el, elements;\n  if (els instanceof Array) {\n    elements = [];\n    try {\n      for (el of els) {\n        elements.push(this.getElement(el, name));\n      }\n    } catch (e) {\n      elements = null;\n    }\n  } else if (typeof els === \"string\") {\n    elements = [];\n    for (el of document.querySelectorAll(els)) {\n      elements.push(el);\n    }\n  } else if (els.nodeType != null) {\n    elements = [els];\n  }\n\n  if (elements == null || !elements.length) {\n    throw new Error(\n      `Invalid \\`${name}\\` option provided. Please provide a CSS selector, a plain HTML element or a list of those.`\n    );\n  }\n\n  return elements;\n};\n\n// Asks the user the question and calls accepted or rejected accordingly\n//\n// The default implementation just uses `window.confirm` and then calls the\n// appropriate callback.\nDropzone.confirm = function (question, accepted, rejected) {\n  if (window.confirm(question)) {\n    return accepted();\n  } else if (rejected != null) {\n    return rejected();\n  }\n};\n\n// Validates the mime type like this:\n//\n// https://developer.mozilla.org/en-US/docs/HTML/Element/input#attr-accept\nDropzone.isValidFile = function (file, acceptedFiles) {\n  if (!acceptedFiles) {\n    return true;\n  } // If there are no accepted mime types, it's OK\n  acceptedFiles = acceptedFiles.split(\",\");\n\n  let mimeType = file.type;\n  let baseMimeType = mimeType.replace(/\\/.*$/, \"\");\n\n  for (let validType of acceptedFiles) {\n    validType = validType.trim();\n    if (validType.charAt(0) === \".\") {\n      if (\n        file.name\n          .toLowerCase()\n          .indexOf(\n            validType.toLowerCase(),\n            file.name.length - validType.length\n          ) !== -1\n      ) {\n        return true;\n      }\n    } else if (/\\/\\*$/.test(validType)) {\n      // This is something like a image/* mime type\n      if (baseMimeType === validType.replace(/\\/.*$/, \"\")) {\n        return true;\n      }\n    } else {\n      if (mimeType === validType) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n};\n\n// Augment jQuery\nif (typeof jQuery !== \"undefined\" && jQuery !== null) {\n  jQuery.fn.dropzone = function (options) {\n    return this.each(function () {\n      return new Dropzone(this, options);\n    });\n  };\n}\n\n// Dropzone file status codes\nDropzone.ADDED = \"added\";\n\nDropzone.QUEUED = \"queued\";\n// For backwards compatibility. Now, if a file is accepted, it's either queued\n// or uploading.\nDropzone.ACCEPTED = Dropzone.QUEUED;\n\nDropzone.UPLOADING = \"uploading\";\nDropzone.PROCESSING = Dropzone.UPLOADING; // alias\n\nDropzone.CANCELED = \"canceled\";\nDropzone.ERROR = \"error\";\nDropzone.SUCCESS = \"success\";\n\n/*\n\n Bugfix for iOS 6 and 7\n Source: http://stackoverflow.com/questions/11929099/html5-canvas-drawimage-ratio-bug-ios\n based on the work of https://github.com/stomita/ios-imagefile-megapixel\n\n */\n\n// Detecting vertical squash in loaded image.\n// Fixes a bug which squash image vertically while drawing into canvas for some images.\n// This is a bug in iOS6 devices. This function from https://github.com/stomita/ios-imagefile-megapixel\nlet detectVerticalSquash = function (img) {\n  let iw = img.naturalWidth;\n  let ih = img.naturalHeight;\n  let canvas = document.createElement(\"canvas\");\n  canvas.width = 1;\n  canvas.height = ih;\n  let ctx = canvas.getContext(\"2d\");\n  ctx.drawImage(img, 0, 0);\n  let { data } = ctx.getImageData(1, 0, 1, ih);\n\n  // search image edge pixel position in case it is squashed vertically.\n  let sy = 0;\n  let ey = ih;\n  let py = ih;\n  while (py > sy) {\n    let alpha = data[(py - 1) * 4 + 3];\n\n    if (alpha === 0) {\n      ey = py;\n    } else {\n      sy = py;\n    }\n\n    py = (ey + sy) >> 1;\n  }\n  let ratio = py / ih;\n\n  if (ratio === 0) {\n    return 1;\n  } else {\n    return ratio;\n  }\n};\n\n// A replacement for context.drawImage\n// (args are for source and destination).\nvar drawImageIOSFix = function (ctx, img, sx, sy, sw, sh, dx, dy, dw, dh) {\n  let vertSquashRatio = detectVerticalSquash(img);\n  return ctx.drawImage(img, sx, sy, sw, sh, dx, dy, dw, dh / vertSquashRatio);\n};\n\n// Based on MinifyJpeg\n// Source: http://www.perry.cz/files/ExifRestorer.js\n// http://elicon.blog57.fc2.com/blog-entry-206.html\nclass ExifRestore {\n  static initClass() {\n    this.KEY_STR =\n      \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n  }\n\n  static encode64(input) {\n    let output = \"\";\n    let chr1 = undefined;\n    let chr2 = undefined;\n    let chr3 = \"\";\n    let enc1 = undefined;\n    let enc2 = undefined;\n    let enc3 = undefined;\n    let enc4 = \"\";\n    let i = 0;\n    while (true) {\n      chr1 = input[i++];\n      chr2 = input[i++];\n      chr3 = input[i++];\n      enc1 = chr1 >> 2;\n      enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n      enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);\n      enc4 = chr3 & 63;\n      if (isNaN(chr2)) {\n        enc3 = enc4 = 64;\n      } else if (isNaN(chr3)) {\n        enc4 = 64;\n      }\n      output =\n        output +\n        this.KEY_STR.charAt(enc1) +\n        this.KEY_STR.charAt(enc2) +\n        this.KEY_STR.charAt(enc3) +\n        this.KEY_STR.charAt(enc4);\n      chr1 = chr2 = chr3 = \"\";\n      enc1 = enc2 = enc3 = enc4 = \"\";\n      if (!(i < input.length)) {\n        break;\n      }\n    }\n    return output;\n  }\n\n  static restore(origFileBase64, resizedFileBase64) {\n    if (!origFileBase64.match(\"data:image/jpeg;base64,\")) {\n      return resizedFileBase64;\n    }\n    let rawImage = this.decode64(\n      origFileBase64.replace(\"data:image/jpeg;base64,\", \"\")\n    );\n    let segments = this.slice2Segments(rawImage);\n    let image = this.exifManipulation(resizedFileBase64, segments);\n    return `data:image/jpeg;base64,${this.encode64(image)}`;\n  }\n\n  static exifManipulation(resizedFileBase64, segments) {\n    let exifArray = this.getExifArray(segments);\n    let newImageArray = this.insertExif(resizedFileBase64, exifArray);\n    let aBuffer = new Uint8Array(newImageArray);\n    return aBuffer;\n  }\n\n  static getExifArray(segments) {\n    let seg = undefined;\n    let x = 0;\n    while (x < segments.length) {\n      seg = segments[x];\n      if ((seg[0] === 255) & (seg[1] === 225)) {\n        return seg;\n      }\n      x++;\n    }\n    return [];\n  }\n\n  static insertExif(resizedFileBase64, exifArray) {\n    let imageData = resizedFileBase64.replace(\"data:image/jpeg;base64,\", \"\");\n    let buf = this.decode64(imageData);\n    let separatePoint = buf.indexOf(255, 3);\n    let mae = buf.slice(0, separatePoint);\n    let ato = buf.slice(separatePoint);\n    let array = mae;\n    array = array.concat(exifArray);\n    array = array.concat(ato);\n    return array;\n  }\n\n  static slice2Segments(rawImageArray) {\n    let head = 0;\n    let segments = [];\n    while (true) {\n      var length;\n      if ((rawImageArray[head] === 255) & (rawImageArray[head + 1] === 218)) {\n        break;\n      }\n      if ((rawImageArray[head] === 255) & (rawImageArray[head + 1] === 216)) {\n        head += 2;\n      } else {\n        length = rawImageArray[head + 2] * 256 + rawImageArray[head + 3];\n        let endPoint = head + length + 2;\n        let seg = rawImageArray.slice(head, endPoint);\n        segments.push(seg);\n        head = endPoint;\n      }\n      if (head > rawImageArray.length) {\n        break;\n      }\n    }\n    return segments;\n  }\n\n  static decode64(input) {\n    let output = \"\";\n    let chr1 = undefined;\n    let chr2 = undefined;\n    let chr3 = \"\";\n    let enc1 = undefined;\n    let enc2 = undefined;\n    let enc3 = undefined;\n    let enc4 = \"\";\n    let i = 0;\n    let buf = [];\n    // remove all characters that are not A-Z, a-z, 0-9, +, /, or =\n    let base64test = /[^A-Za-z0-9\\+\\/\\=]/g;\n    if (base64test.exec(input)) {\n      console.warn(\n        \"There were invalid base64 characters in the input text.\\nValid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\\nExpect errors in decoding.\"\n      );\n    }\n    input = input.replace(/[^A-Za-z0-9\\+\\/\\=]/g, \"\");\n    while (true) {\n      enc1 = this.KEY_STR.indexOf(input.charAt(i++));\n      enc2 = this.KEY_STR.indexOf(input.charAt(i++));\n      enc3 = this.KEY_STR.indexOf(input.charAt(i++));\n      enc4 = this.KEY_STR.indexOf(input.charAt(i++));\n      chr1 = (enc1 << 2) | (enc2 >> 4);\n      chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\n      chr3 = ((enc3 & 3) << 6) | enc4;\n      buf.push(chr1);\n      if (enc3 !== 64) {\n        buf.push(chr2);\n      }\n      if (enc4 !== 64) {\n        buf.push(chr3);\n      }\n      chr1 = chr2 = chr3 = \"\";\n      enc1 = enc2 = enc3 = enc4 = \"\";\n      if (!(i < input.length)) {\n        break;\n      }\n    }\n    return buf;\n  }\n}\nExifRestore.initClass();\n\n/*\n * contentloaded.js\n *\n * Author: Diego Perini (diego.perini at gmail.com)\n * Summary: cross-browser wrapper for DOMContentLoaded\n * Updated: 20101020\n * License: MIT\n * Version: 1.2\n *\n * URL:\n * http://javascript.nwbox.com/ContentLoaded/\n * http://javascript.nwbox.com/ContentLoaded/MIT-LICENSE\n */\n\n// @win window reference\n// @fn function reference\nlet contentLoaded = function (win, fn) {\n  let done = false;\n  let top = true;\n  let doc = win.document;\n  let root = doc.documentElement;\n  let add = doc.addEventListener ? \"addEventListener\" : \"attachEvent\";\n  let rem = doc.addEventListener ? \"removeEventListener\" : \"detachEvent\";\n  let pre = doc.addEventListener ? \"\" : \"on\";\n  var init = function (e) {\n    if (e.type === \"readystatechange\" && doc.readyState !== \"complete\") {\n      return;\n    }\n    (e.type === \"load\" ? win : doc)[rem](pre + e.type, init, false);\n    if (!done && (done = true)) {\n      return fn.call(win, e.type || e);\n    }\n  };\n\n  var poll = function () {\n    try {\n      root.doScroll(\"left\");\n    } catch (e) {\n      setTimeout(poll, 50);\n      return;\n    }\n    return init(\"poll\");\n  };\n\n  if (doc.readyState !== \"complete\") {\n    if (doc.createEventObject && root.doScroll) {\n      try {\n        top = !win.frameElement;\n      } catch (error) {}\n      if (top) {\n        poll();\n      }\n    }\n    doc[add](pre + \"DOMContentLoaded\", init, false);\n    doc[add](pre + \"readystatechange\", init, false);\n    return win[add](pre + \"load\", init, false);\n  }\n};\n\nfunction __guard__(value, transform) {\n  return typeof value !== \"undefined\" && value !== null\n    ? transform(value)\n    : undefined;\n}\nfunction __guardMethod__(obj, methodName, transform) {\n  if (\n    typeof obj !== \"undefined\" &&\n    obj !== null &&\n    typeof obj[methodName] === \"function\"\n  ) {\n    return transform(obj, methodName);\n  } else {\n    return undefined;\n  }\n}\n\nexport { Dropzone };\n", "// The Emitter class provides the ability to call `.on()` on Dropzone to listen\n// to events.\n// It is strongly based on component's emitter class, and I removed the\n// functionality because of the dependency hell with different frameworks.\nexport default class Emitter {\n  // Add an event listener for given event\n  on(event, fn) {\n    this._callbacks = this._callbacks || {};\n    // Create namespace for this event\n    if (!this._callbacks[event]) {\n      this._callbacks[event] = [];\n    }\n    this._callbacks[event].push(fn);\n    return this;\n  }\n\n  emit(event, ...args) {\n    this._callbacks = this._callbacks || {};\n    let callbacks = this._callbacks[event];\n\n    if (callbacks) {\n      for (let callback of callbacks) {\n        callback.apply(this, args);\n      }\n    }\n    // trigger a corresponding DOM event\n    if (this.element) {\n      this.element.dispatchEvent(\n        this.makeEvent(\"dropzone:\" + event, { args: args })\n      );\n    }\n    return this;\n  }\n\n  makeEvent(eventName, detail) {\n    let params = { bubbles: true, cancelable: true, detail: detail };\n\n    if (typeof window.CustomEvent === \"function\") {\n      return new CustomEvent(eventName, params);\n    } else {\n      // IE 11 support\n      // https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent/CustomEvent\n      var evt = document.createEvent(\"CustomEvent\");\n      evt.initCustomEvent(\n        eventName,\n        params.bubbles,\n        params.cancelable,\n        params.detail\n      );\n      return evt;\n    }\n  }\n\n  // Remove event listener for given event. If fn is not provided, all event\n  // listeners for that event will be removed. If neither is provided, all\n  // event listeners will be removed.\n  off(event, fn) {\n    if (!this._callbacks || arguments.length === 0) {\n      this._callbacks = {};\n      return this;\n    }\n\n    // specific event\n    let callbacks = this._callbacks[event];\n    if (!callbacks) {\n      return this;\n    }\n\n    // remove all handlers\n    if (arguments.length === 1) {\n      delete this._callbacks[event];\n      return this;\n    }\n\n    // remove specific handler\n    for (let i = 0; i < callbacks.length; i++) {\n      let callback = callbacks[i];\n      if (callback === fn) {\n        callbacks.splice(i, 1);\n        break;\n      }\n    }\n\n    return this;\n  }\n}\n", "import Dropzone from \"./dropzone\";\nimport defaultPreviewTemplate from \"bundle-text:./preview-template.html\";\n\nlet defaultOptions = {\n  /**\n   * Has to be specified on elements other than form (or when the form doesn't\n   * have an `action` attribute).\n   *\n   * You can also provide a function that will be called with `files` and\n   * `dataBlocks`  and must return the url as string.\n   */\n  url: null,\n\n  /**\n   * Can be changed to `\"put\"` if necessary. You can also provide a function\n   * that will be called with `files` and must return the method (since `v3.12.0`).\n   */\n  method: \"post\",\n\n  /**\n   * Will be set on the XHRequest.\n   */\n  withCredentials: false,\n\n  /**\n   * The timeout for the XHR requests in milliseconds (since `v4.4.0`).\n   * If set to null or 0, no timeout is going to be set.\n   */\n  timeout: null,\n\n  /**\n   * How many file uploads to process in parallel (See the\n   * Enqueuing file uploads documentation section for more info)\n   */\n  parallelUploads: 2,\n\n  /**\n   * Whether to send multiple files in one request. If\n   * this it set to true, then the fallback file input element will\n   * have the `multiple` attribute as well. This option will\n   * also trigger additional events (like `processingmultiple`). See the events\n   * documentation section for more information.\n   */\n  uploadMultiple: false,\n\n  /**\n   * Whether you want files to be uploaded in chunks to your server. This can't be\n   * used in combination with `uploadMultiple`.\n   *\n   * See [chunksUploaded](#config-chunksUploaded) for the callback to finalise an upload.\n   */\n  chunking: false,\n\n  /**\n   * If `chunking` is enabled, this defines whether **every** file should be chunked,\n   * even if the file size is below chunkSize. This means, that the additional chunk\n   * form data will be submitted and the `chunksUploaded` callback will be invoked.\n   */\n  forceChunking: false,\n\n  /**\n   * If `chunking` is `true`, then this defines the chunk size in bytes.\n   */\n  chunkSize: 2 * 1024 * 1024,\n\n  /**\n   * If `true`, the individual chunks of a file are being uploaded simultaneously.\n   */\n  parallelChunkUploads: false,\n\n  /**\n   * Whether a chunk should be retried if it fails.\n   */\n  retryChunks: false,\n\n  /**\n   * If `retryChunks` is true, how many times should it be retried.\n   */\n  retryChunksLimit: 3,\n\n  /**\n   * The maximum filesize (in MiB) that is allowed to be uploaded.\n   */\n  maxFilesize: 256,\n\n  /**\n   * The name of the file param that gets transferred.\n   * **NOTE**: If you have the option  `uploadMultiple` set to `true`, then\n   * Dropzone will append `[]` to the name.\n   */\n  paramName: \"file\",\n\n  /**\n   * Whether thumbnails for images should be generated\n   */\n  createImageThumbnails: true,\n\n  /**\n   * In MB. When the filename exceeds this limit, the thumbnail will not be generated.\n   */\n  maxThumbnailFilesize: 10,\n\n  /**\n   * If `null`, the ratio of the image will be used to calculate it.\n   */\n  thumbnailWidth: 120,\n\n  /**\n   * The same as `thumbnailWidth`. If both are null, images will not be resized.\n   */\n  thumbnailHeight: 120,\n\n  /**\n   * How the images should be scaled down in case both, `thumbnailWidth` and `thumbnailHeight` are provided.\n   * Can be either `contain` or `crop`.\n   */\n  thumbnailMethod: \"crop\",\n\n  /**\n   * If set, images will be resized to these dimensions before being **uploaded**.\n   * If only one, `resizeWidth` **or** `resizeHeight` is provided, the original aspect\n   * ratio of the file will be preserved.\n   *\n   * The `options.transformFile` function uses these options, so if the `transformFile` function\n   * is overridden, these options don't do anything.\n   */\n  resizeWidth: null,\n\n  /**\n   * See `resizeWidth`.\n   */\n  resizeHeight: null,\n\n  /**\n   * The mime type of the resized image (before it gets uploaded to the server).\n   * If `null` the original mime type will be used. To force jpeg, for example, use `image/jpeg`.\n   * See `resizeWidth` for more information.\n   */\n  resizeMimeType: null,\n\n  /**\n   * The quality of the resized images. See `resizeWidth`.\n   */\n  resizeQuality: 0.8,\n\n  /**\n   * How the images should be scaled down in case both, `resizeWidth` and `resizeHeight` are provided.\n   * Can be either `contain` or `crop`.\n   */\n  resizeMethod: \"contain\",\n\n  /**\n   * The base that is used to calculate the **displayed** filesize. You can\n   * change this to 1024 if you would rather display kibibytes, mebibytes,\n   * etc... 1024 is technically incorrect, because `1024 bytes` are `1 kibibyte`\n   * not `1 kilobyte`. You can change this to `1024` if you don't care about\n   * validity.\n   */\n  filesizeBase: 1000,\n\n  /**\n   * If not `null` defines how many files this Dropzone handles. If it exceeds,\n   * the event `maxfilesexceeded` will be called. The dropzone element gets the\n   * class `dz-max-files-reached` accordingly so you can provide visual\n   * feedback.\n   */\n  maxFiles: null,\n\n  /**\n   * An optional object to send additional headers to the server. Eg:\n   * `{ \"My-Awesome-Header\": \"header value\" }`\n   */\n  headers: null,\n\n  /**\n   * Should the default headers be set or not?\n   * Accept: application/json <- for requesting json response\n   * Cache-Control: no-cache <- Request shouldnt be cached\n   * X-Requested-With: XMLHttpRequest <- We sent the request via XMLHttpRequest\n   */\n  defaultHeaders: true,\n\n  /**\n   * If `true`, the dropzone element itself will be clickable, if `false`\n   * nothing will be clickable.\n   *\n   * You can also pass an HTML element, a CSS selector (for multiple elements)\n   * or an array of those. In that case, all of those elements will trigger an\n   * upload when clicked.\n   */\n  clickable: true,\n\n  /**\n   * Whether hidden files in directories should be ignored.\n   */\n  ignoreHiddenFiles: true,\n\n  /**\n   * The default implementation of `accept` checks the file's mime type or\n   * extension against this list. This is a comma separated list of mime\n   * types or file extensions.\n   *\n   * Eg.: `image/*,application/pdf,.psd`\n   *\n   * If the Dropzone is `clickable` this option will also be used as\n   * [`accept`](https://developer.mozilla.org/en-US/docs/HTML/Element/input#attr-accept)\n   * parameter on the hidden file input as well.\n   */\n  acceptedFiles: null,\n\n  /**\n   * **Deprecated!**\n   * Use acceptedFiles instead.\n   */\n  acceptedMimeTypes: null,\n\n  /**\n   * If false, files will be added to the queue but the queue will not be\n   * processed automatically.\n   * This can be useful if you need some additional user input before sending\n   * files (or if you want want all files sent at once).\n   * If you're ready to send the file simply call `myDropzone.processQueue()`.\n   *\n   * See the [enqueuing file uploads](#enqueuing-file-uploads) documentation\n   * section for more information.\n   */\n  autoProcessQueue: true,\n\n  /**\n   * If false, files added to the dropzone will not be queued by default.\n   * You'll have to call `enqueueFile(file)` manually.\n   */\n  autoQueue: true,\n\n  /**\n   * If `true`, this will add a link to every file preview to remove or cancel (if\n   * already uploading) the file. The `dictCancelUpload`, `dictCancelUploadConfirmation`\n   * and `dictRemoveFile` options are used for the wording.\n   */\n  addRemoveLinks: false,\n\n  /**\n   * Defines where to display the file previews – if `null` the\n   * Dropzone element itself is used. Can be a plain `HTMLElement` or a CSS\n   * selector. The element should have the `dropzone-previews` class so\n   * the previews are displayed properly.\n   */\n  previewsContainer: null,\n\n  /**\n   * Set this to `true` if you don't want previews to be shown.\n   */\n  disablePreviews: false,\n\n  /**\n   * This is the element the hidden input field (which is used when clicking on the\n   * dropzone to trigger file selection) will be appended to. This might\n   * be important in case you use frameworks to switch the content of your page.\n   *\n   * Can be a selector string, or an element directly.\n   */\n  hiddenInputContainer: \"body\",\n\n  /**\n   * If null, no capture type will be specified\n   * If camera, mobile devices will skip the file selection and choose camera\n   * If microphone, mobile devices will skip the file selection and choose the microphone\n   * If camcorder, mobile devices will skip the file selection and choose the camera in video mode\n   * On apple devices multiple must be set to false.  AcceptedFiles may need to\n   * be set to an appropriate mime type (e.g. \"image/*\", \"audio/*\", or \"video/*\").\n   */\n  capture: null,\n\n  /**\n   * **Deprecated**. Use `renameFile` instead.\n   */\n  renameFilename: null,\n\n  /**\n   * A function that is invoked before the file is uploaded to the server and renames the file.\n   * This function gets the `File` as argument and can use the `file.name`. The actual name of the\n   * file that gets used during the upload can be accessed through `file.upload.filename`.\n   */\n  renameFile: null,\n\n  /**\n   * If `true` the fallback will be forced. This is very useful to test your server\n   * implementations first and make sure that everything works as\n   * expected without dropzone if you experience problems, and to test\n   * how your fallbacks will look.\n   */\n  forceFallback: false,\n\n  /**\n   * The text used before any files are dropped.\n   */\n  dictDefaultMessage: \"Drop files here to upload\",\n\n  /**\n   * The text that replaces the default message text it the browser is not supported.\n   */\n  dictFallbackMessage:\n    \"Your browser does not support drag'n'drop file uploads.\",\n\n  /**\n   * The text that will be added before the fallback form.\n   * If you provide a  fallback element yourself, or if this option is `null` this will\n   * be ignored.\n   */\n  dictFallbackText:\n    \"Please use the fallback form below to upload your files like in the olden days.\",\n\n  /**\n   * If the filesize is too big.\n   * `{{filesize}}` and `{{maxFilesize}}` will be replaced with the respective configuration values.\n   */\n  dictFileTooBig:\n    \"File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.\",\n\n  /**\n   * If the file doesn't match the file type.\n   */\n  dictInvalidFileType: \"You can't upload files of this type.\",\n\n  /**\n   * If the server response was invalid.\n   * `{{statusCode}}` will be replaced with the servers status code.\n   */\n  dictResponseError: \"Server responded with {{statusCode}} code.\",\n\n  /**\n   * If `addRemoveLinks` is true, the text to be used for the cancel upload link.\n   */\n  dictCancelUpload: \"Cancel upload\",\n\n  /**\n   * The text that is displayed if an upload was manually canceled\n   */\n  dictUploadCanceled: \"Upload canceled.\",\n\n  /**\n   * If `addRemoveLinks` is true, the text to be used for confirmation when cancelling upload.\n   */\n  dictCancelUploadConfirmation: \"Are you sure you want to cancel this upload?\",\n\n  /**\n   * If `addRemoveLinks` is true, the text to be used to remove a file.\n   */\n  dictRemoveFile: \"Remove file\",\n\n  /**\n   * If this is not null, then the user will be prompted before removing a file.\n   */\n  dictRemoveFileConfirmation: null,\n\n  /**\n   * Displayed if `maxFiles` is st and exceeded.\n   * The string `{{maxFiles}}` will be replaced by the configuration value.\n   */\n  dictMaxFilesExceeded: \"You can not upload any more files.\",\n\n  /**\n   * Allows you to translate the different units. Starting with `tb` for terabytes and going down to\n   * `b` for bytes.\n   */\n  dictFileSizeUnits: { tb: \"TB\", gb: \"GB\", mb: \"MB\", kb: \"KB\", b: \"b\" },\n  /**\n   * Called when dropzone initialized\n   * You can add event listeners here\n   */\n  init() {},\n\n  /**\n   * Can be an **object** of additional parameters to transfer to the server, **or** a `Function`\n   * that gets invoked with the `files`, `xhr` and, if it's a chunked upload, `chunk` arguments. In case\n   * of a function, this needs to return a map.\n   *\n   * The default implementation does nothing for normal uploads, but adds relevant information for\n   * chunked uploads.\n   *\n   * This is the same as adding hidden input fields in the form element.\n   */\n  params(files, xhr, chunk) {\n    if (chunk) {\n      return {\n        dzuuid: chunk.file.upload.uuid,\n        dzchunkindex: chunk.index,\n        dztotalfilesize: chunk.file.size,\n        dzchunksize: this.options.chunkSize,\n        dztotalchunkcount: chunk.file.upload.totalChunkCount,\n        dzchunkbyteoffset: chunk.index * this.options.chunkSize,\n      };\n    }\n  },\n\n  /**\n   * A function that gets a [file](https://developer.mozilla.org/en-US/docs/DOM/File)\n   * and a `done` function as parameters.\n   *\n   * If the done function is invoked without arguments, the file is \"accepted\" and will\n   * be processed. If you pass an error message, the file is rejected, and the error\n   * message will be displayed.\n   * This function will not be called if the file is too big or doesn't match the mime types.\n   */\n  accept(file, done) {\n    return done();\n  },\n\n  /**\n   * The callback that will be invoked when all chunks have been uploaded for a file.\n   * It gets the file for which the chunks have been uploaded as the first parameter,\n   * and the `done` function as second. `done()` needs to be invoked when everything\n   * needed to finish the upload process is done.\n   */\n  chunksUploaded: function (file, done) {\n    done();\n  },\n\n  /**\n   * Sends the file as binary blob in body instead of form data.\n   * If this is set, the `params` option will be ignored.\n   * It's an error to set this to `true` along with `uploadMultiple` since\n   * multiple files cannot be in a single binary body.\n   */\n  binaryBody: false,\n\n  /**\n   * Gets called when the browser is not supported.\n   * The default implementation shows the fallback input field and adds\n   * a text.\n   */\n  fallback() {\n    // This code should pass in IE7... :(\n    let messageElement;\n    this.element.className = `${this.element.className} dz-browser-not-supported`;\n\n    for (let child of this.element.getElementsByTagName(\"div\")) {\n      if (/(^| )dz-message($| )/.test(child.className)) {\n        messageElement = child;\n        child.className = \"dz-message\"; // Removes the 'dz-default' class\n        break;\n      }\n    }\n    if (!messageElement) {\n      messageElement = Dropzone.createElement(\n        '<div class=\"dz-message\"><span></span></div>'\n      );\n      this.element.appendChild(messageElement);\n    }\n\n    let span = messageElement.getElementsByTagName(\"span\")[0];\n    if (span) {\n      if (span.textContent != null) {\n        span.textContent = this.options.dictFallbackMessage;\n      } else if (span.innerText != null) {\n        span.innerText = this.options.dictFallbackMessage;\n      }\n    }\n\n    return this.element.appendChild(this.getFallbackForm());\n  },\n\n  /**\n   * Gets called to calculate the thumbnail dimensions.\n   *\n   * It gets `file`, `width` and `height` (both may be `null`) as parameters and must return an object containing:\n   *\n   *  - `srcWidth` & `srcHeight` (required)\n   *  - `trgWidth` & `trgHeight` (required)\n   *  - `srcX` & `srcY` (optional, default `0`)\n   *  - `trgX` & `trgY` (optional, default `0`)\n   *\n   * Those values are going to be used by `ctx.drawImage()`.\n   */\n  resize(file, width, height, resizeMethod) {\n    let info = {\n      srcX: 0,\n      srcY: 0,\n      srcWidth: file.width,\n      srcHeight: file.height,\n    };\n\n    let srcRatio = file.width / file.height;\n\n    // Automatically calculate dimensions if not specified\n    if (width == null && height == null) {\n      width = info.srcWidth;\n      height = info.srcHeight;\n    } else if (width == null) {\n      width = height * srcRatio;\n    } else if (height == null) {\n      height = width / srcRatio;\n    }\n\n    // Make sure images aren't upscaled\n    width = Math.min(width, info.srcWidth);\n    height = Math.min(height, info.srcHeight);\n\n    let trgRatio = width / height;\n\n    if (info.srcWidth > width || info.srcHeight > height) {\n      // Image is bigger and needs rescaling\n      if (resizeMethod === \"crop\") {\n        if (srcRatio > trgRatio) {\n          info.srcHeight = file.height;\n          info.srcWidth = info.srcHeight * trgRatio;\n        } else {\n          info.srcWidth = file.width;\n          info.srcHeight = info.srcWidth / trgRatio;\n        }\n      } else if (resizeMethod === \"contain\") {\n        // Method 'contain'\n        if (srcRatio > trgRatio) {\n          height = width / srcRatio;\n        } else {\n          width = height * srcRatio;\n        }\n      } else {\n        throw new Error(`Unknown resizeMethod '${resizeMethod}'`);\n      }\n    }\n\n    info.srcX = (file.width - info.srcWidth) / 2;\n    info.srcY = (file.height - info.srcHeight) / 2;\n\n    info.trgWidth = width;\n    info.trgHeight = height;\n\n    return info;\n  },\n\n  /**\n   * Can be used to transform the file (for example, resize an image if necessary).\n   *\n   * The default implementation uses `resizeWidth` and `resizeHeight` (if provided) and resizes\n   * images according to those dimensions.\n   *\n   * Gets the `file` as the first parameter, and a `done()` function as the second, that needs\n   * to be invoked with the file when the transformation is done.\n   */\n  transformFile(file, done) {\n    if (\n      (this.options.resizeWidth || this.options.resizeHeight) &&\n      file.type.match(/image.*/)\n    ) {\n      return this.resizeImage(\n        file,\n        this.options.resizeWidth,\n        this.options.resizeHeight,\n        this.options.resizeMethod,\n        done\n      );\n    } else {\n      return done(file);\n    }\n  },\n\n  /**\n   * A string that contains the template used for each dropped\n   * file. Change it to fulfill your needs but make sure to properly\n   * provide all elements.\n   *\n   * If you want to use an actual HTML element instead of providing a String\n   * as a config option, you could create a div with the id `tpl`,\n   * put the template inside it and provide the element like this:\n   *\n   *     document\n   *       .querySelector('#tpl')\n   *       .innerHTML\n   *\n   */\n  previewTemplate: defaultPreviewTemplate,\n\n  /*\n   Those functions register themselves to the events on init and handle all\n   the user interface specific stuff. Overwriting them won't break the upload\n   but can break the way it's displayed.\n   You can overwrite them if you don't like the default behavior. If you just\n   want to add an additional event handler, register it on the dropzone object\n   and don't overwrite those options.\n   */\n\n  // Those are self explanatory and simply concern the DragnDrop.\n  drop(e) {\n    return this.element.classList.remove(\"dz-drag-hover\");\n  },\n  dragstart(e) {},\n  dragend(e) {\n    return this.element.classList.remove(\"dz-drag-hover\");\n  },\n  dragenter(e) {\n    return this.element.classList.add(\"dz-drag-hover\");\n  },\n  dragover(e) {\n    return this.element.classList.add(\"dz-drag-hover\");\n  },\n  dragleave(e) {\n    return this.element.classList.remove(\"dz-drag-hover\");\n  },\n\n  paste(e) {},\n\n  // Called whenever there are no files left in the dropzone anymore, and the\n  // dropzone should be displayed as if in the initial state.\n  reset() {\n    return this.element.classList.remove(\"dz-started\");\n  },\n\n  // Called when a file is added to the queue\n  // Receives `file`\n  addedfile(file) {\n    if (this.element === this.previewsContainer) {\n      this.element.classList.add(\"dz-started\");\n    }\n\n    if (this.previewsContainer && !this.options.disablePreviews) {\n      file.previewElement = Dropzone.createElement(\n        this.options.previewTemplate.trim()\n      );\n      file.previewTemplate = file.previewElement; // Backwards compatibility\n\n      this.previewsContainer.appendChild(file.previewElement);\n      for (var node of file.previewElement.querySelectorAll(\"[data-dz-name]\")) {\n        node.textContent = file.name;\n      }\n      for (node of file.previewElement.querySelectorAll(\"[data-dz-size]\")) {\n        node.innerHTML = this.filesize(file.size);\n      }\n\n      if (this.options.addRemoveLinks) {\n        file._removeLink = Dropzone.createElement(\n          `<a class=\"dz-remove\" href=\"javascript:undefined;\" data-dz-remove>${this.options.dictRemoveFile}</a>`\n        );\n        file.previewElement.appendChild(file._removeLink);\n      }\n\n      let removeFileEvent = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        if (file.status === Dropzone.UPLOADING) {\n          return Dropzone.confirm(\n            this.options.dictCancelUploadConfirmation,\n            () => this.removeFile(file)\n          );\n        } else {\n          if (this.options.dictRemoveFileConfirmation) {\n            return Dropzone.confirm(\n              this.options.dictRemoveFileConfirmation,\n              () => this.removeFile(file)\n            );\n          } else {\n            return this.removeFile(file);\n          }\n        }\n      };\n\n      for (let removeLink of file.previewElement.querySelectorAll(\n        \"[data-dz-remove]\"\n      )) {\n        removeLink.addEventListener(\"click\", removeFileEvent);\n      }\n    }\n  },\n\n  // Called whenever a file is removed.\n  removedfile(file) {\n    if (file.previewElement != null && file.previewElement.parentNode != null) {\n      file.previewElement.parentNode.removeChild(file.previewElement);\n    }\n    return this._updateMaxFilesReachedClass();\n  },\n\n  // Called when a thumbnail has been generated\n  // Receives `file` and `dataUrl`\n  thumbnail(file, dataUrl) {\n    if (file.previewElement) {\n      file.previewElement.classList.remove(\"dz-file-preview\");\n      for (let thumbnailElement of file.previewElement.querySelectorAll(\n        \"[data-dz-thumbnail]\"\n      )) {\n        thumbnailElement.alt = file.name;\n        thumbnailElement.src = dataUrl;\n      }\n\n      return setTimeout(\n        () => file.previewElement.classList.add(\"dz-image-preview\"),\n        1\n      );\n    }\n  },\n\n  // Called whenever an error occurs\n  // Receives `file` and `message`\n  error(file, message) {\n    if (file.previewElement) {\n      file.previewElement.classList.add(\"dz-error\");\n      if (typeof message !== \"string\" && message.error) {\n        message = message.error;\n      }\n      for (let node of file.previewElement.querySelectorAll(\n        \"[data-dz-errormessage]\"\n      )) {\n        node.textContent = message;\n      }\n    }\n  },\n\n  errormultiple() {},\n\n  // Called when a file gets processed. Since there is a cue, not all added\n  // files are processed immediately.\n  // Receives `file`\n  processing(file) {\n    if (file.previewElement) {\n      file.previewElement.classList.add(\"dz-processing\");\n      if (file._removeLink) {\n        return (file._removeLink.innerHTML = this.options.dictCancelUpload);\n      }\n    }\n  },\n\n  processingmultiple() {},\n\n  // Called whenever the upload progress gets updated.\n  // Receives `file`, `progress` (percentage 0-100) and `bytesSent`.\n  // To get the total number of bytes of the file, use `file.size`\n  uploadprogress(file, progress, bytesSent) {\n    if (file.previewElement) {\n      for (let node of file.previewElement.querySelectorAll(\n        \"[data-dz-uploadprogress]\"\n      )) {\n        node.nodeName === \"PROGRESS\"\n          ? (node.value = progress)\n          : (node.style.width = `${progress}%`);\n      }\n    }\n  },\n\n  // Called whenever the total upload progress gets updated.\n  // Called with totalUploadProgress (0-100), totalBytes and totalBytesSent\n  totaluploadprogress() {},\n\n  // Called just before the file is sent. Gets the `xhr` object as second\n  // parameter, so you can modify it (for example to add a CSRF token) and a\n  // `formData` object to add additional information.\n  sending() {},\n\n  sendingmultiple() {},\n\n  // When the complete upload is finished and successful\n  // Receives `file`\n  success(file) {\n    if (file.previewElement) {\n      return file.previewElement.classList.add(\"dz-success\");\n    }\n  },\n\n  successmultiple() {},\n\n  // When the upload is canceled.\n  canceled(file) {\n    return this.emit(\"error\", file, this.options.dictUploadCanceled);\n  },\n\n  canceledmultiple() {},\n\n  // When the upload is finished, either with success or an error.\n  // Receives `file`\n  complete(file) {\n    if (file._removeLink) {\n      file._removeLink.innerHTML = this.options.dictRemoveFile;\n    }\n    if (file.previewElement) {\n      return file.previewElement.classList.add(\"dz-complete\");\n    }\n  },\n\n  completemultiple() {},\n\n  maxfilesexceeded() {},\n\n  maxfilesreached() {},\n\n  queuecomplete() {},\n\n  addedfiles() {},\n};\n\nexport default defaultOptions;\n", "module.exports = \"a1acf319c471fa03\";"], "names": [], "version": 3, "file": "dropzone.mjs.map"}