$(() => {
    const createToast = (message, type) => {
        const wrapper = document.createElement('div')
        wrapper.innerHTML = `
            <div class="toast fade show text-${type} bg-${type} border-${type}">
                <div class="toast-header">
                    <span class="me-auto">${message}</span>
                </div>
            </div>`;

        return wrapper;
    }


    const showToast = function (message, type = 'success') {
        let container = this.find('div.toast-container');
        if (container.length < 1) {
            container = document.createElement('div');
            container.innerHTML = `<div className="toast-container p-3 top-0 start-50 translate-middle-x" data-original-class="toast-container p-3"></div>`;
            this.append(container);
        } else {
            container.empty();
        }


        const toast = createToast(message, type)
        container.append(toast);

        this.clearQueue().prepend(container).show();

        return $(container);
    }

    const createAlert = (message, type) => {
        const alertWrapper = document.createElement('div')
        alertWrapper.innerHTML = [
            `<div class="alert alert-${type} alert-dismissible" role="alert">`,
            `   <div>${message}</div>`,
            '   <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>',
            '</div>'
        ].join('')

        return alertWrapper;
    }

    const appendAlert = function(message, type = 'danger')  {
        const alert = createAlert(message, type)

        console.log('appendAlert', this);
        this.clearQueue().show().append(alert);

        return this;
    }

    const showAlert = function(message, type = 'danger') {
        this.empty();

        const alert = createAlert(message, type)

        this.clearQueue().show().append(alert);

        return this;
    }

    const autoDismiss = function(dismissAfter = 5000) {
        if(isNaN(dismissAfter) || dismissAfter < 1) {
            dismissAfter = 5000;
        }

        this.delay(dismissAfter).fadeOut(2000);

        return this;
    }

    function getSelector($element) {
        if (!$element.length) return '';
        let tag = $element.prop('tagName').toLowerCase();
        let id = $element.attr('id');
        let classes = $element.attr('class')?.split(/\s+/).filter(c => c).map(c => `.${c}`).join('');
        return id ? `#${id}` : `${tag}${classes}`;
    }

    const bootstrapModal = $.fn.modal;
    const superModal = function(modalOptions = {}, callbackOptions = null, targetWindow = null, targetSelectorInParent = '#unifiedModalContainer') {
        if(callbackOptions === null) {
            return bootstrapModal.bind(this)(modalOptions);
        }

        let $targetWindow = (targetWindow && targetWindow.jQuery) ? targetWindow.jQuery : window.top.jQuery;
        // let $targetWindow = window.parent.jQuery;

        // var $targetWindow = window.parent.jQuery;
        let thisSelector = getSelector(this);
        console.log('thisSelector', thisSelector)

        targetSelectorInParent = targetSelectorInParent ?? '#unifiedModalContainer'

        let $modalContainer = $targetWindow(targetSelectorInParent);
        $modalContainer.empty();

        // 插入 modal HTML 到父页面 body
        let html = $('<div>').append(this.clone()).html();
        $modalContainer.append(html);

        Object.entries(callbackOptions ?? {}).forEach(([selector, eventHandlers]) => {
            if(typeof eventHandlers === 'function') {
                $targetWindow(selector).on('click', function(e) { eventHandlers(e, $targetWindow(thisSelector)) });
            } else {
                Object.entries(eventHandlers ?? {}).forEach(([eventName, handler]) => {
                    console.log(`setting up event handler: ${selector}.${eventName} => `, handler);
                    $targetWindow(selector).on(eventName, handler);
                });
            }
        });

        $targetWindow(thisSelector).on('hidden.bs.modal', (e) => {
            e.stopPropagation();
            console.log('$targetWindow(thisSelector).event hidden.bs.modal', e)
            $targetWindow('#unifiedModalContainer').empty();
        });

        return $targetWindow(thisSelector).modal(modalOptions);
    }

    const openGlobalIFrameModal = function (url, modalOptions = {}, callbackOptions = null, refreshOptions = null) {
        // if(callbackOptions === null) {
        //     return bootstrapModal.bind(this)(modalOptions);
        // }

        let $targetWindow = window.top.jQuery;
        let $globalModalContainer = $targetWindow('#unifiedModalContainer');
        let $globalIFrameModalDialogTemplate = $('.globalIFrameModalDialog');
        let iframeSelector = 'iframe.globalIFrameModalDialog_IFrame'

        if($globalModalContainer.length < 1) {
            $globalModalContainer = $('<div id="unifiedModalContainer">')
            $targetWindow('body').append($globalModalContainer);
        }
        $globalModalContainer.empty();

        // 插入 modal HTML 到父页面 body
        let html = $('<div>').append($globalIFrameModalDialogTemplate.clone()).html();
        $globalModalContainer.append(html);

        let $globalIFrameModalDialog = $targetWindow('#unifiedModalContainer .globalIFrameModalDialog');
        let $globalIFrameModalDialogIFrame = $targetWindow('#unifiedModalContainer .globalIFrameModalDialog iframe.globalIFrameModalDialog_IFrame');

        Object.entries(callbackOptions ?? {}).forEach(([selector, eventHandlers]) => {
            if(typeof eventHandlers === 'function') {
                $targetWindow(selector).on('click', function(e) { eventHandlers(e) });
            } else {
                Object.entries(eventHandlers ?? {}).forEach(([eventName, handler]) => {
                    console.log(`setting up event handler: ${selector}.${eventName} => `, handler);
                    $targetWindow(selector).on(eventName, handler);
                });
            }
        });


        $globalIFrameModalDialog.on('shown.bs.modal', (e) => {
            e.stopPropagation();
            console.log('$targetWindow(thisSelector).event shown.bs.modal', e)
            $globalIFrameModalDialogIFrame.on('load', () => {
                console.log('$globalIFrameModalDialogIFrame.event loaded', e)
                $globalIFrameModalDialog.modal('handleUpdate');
            })

            $globalIFrameModalDialogIFrame.prop('src', url);
        });

        $globalIFrameModalDialog.on('hidden.bs.modal', (e) => {
            e.stopPropagation();
            console.log('$targetWindow(thisSelector).event hidden.bs.modal', e)
            $targetWindow('#unifiedModalContainer').empty();

            if (refreshOptions && refreshOptions.enabled) {
                console.log('Refresh options:', refreshOptions);
                setTimeout(() => {
                    if (refreshOptions.type === 'iframe' && refreshOptions.selector) {
                        // 刷新指定的iframe
                        let targetIframe = $targetWindow(refreshOptions.selector);
                        if (targetIframe.length > 0) {
                            console.log('Refreshing iframe:', refreshOptions.selector);
                            targetIframe[0].contentWindow.location.reload();
                        } else {
                            console.log('Target iframe not found, falling back to window refresh');
                            window.top.location.reload();
                        }
                    } else if (refreshOptions.type === 'window') {
                        // 刷新整个窗口
                        console.log('Refreshing window');
                        window.top.location.reload();
                    } else if (refreshOptions.type === 'custom' && typeof refreshOptions.callback === 'function') {
                        // 执行自定义刷新回调
                        console.log('Executing custom refresh callback');
                        refreshOptions.callback();
                    }
                }, refreshOptions.delay || 100);
            }
        });

        return $globalIFrameModalDialog.modal(modalOptions);
    }

    const closeGlobalIFrameModal = function (shouldRefresh = false) {
        let $targetWindow = window.top.jQuery;
        let $globalIFrameModalDialog = $targetWindow('#unifiedModalContainer .globalIFrameModalDialog');

        if ($globalIFrameModalDialog.length > 0) {
            $globalIFrameModalDialog.modal('hide');

            if (shouldRefresh) {
                $targetWindow.location.reload();
            }
        }
    }

    const allChecked = function() {
        $('#editUserModal').superModal(options);

        return this.length === this.filter(':checked').length;
    }

    const allSelected = function() {
        return this.length === this.filter(':selected').length;
    }

    const anyChecked = function() {
        return this.filter(':checked').length > 0;
    }
    const anySelected = function() {
        return this.filter(':selected').length > 0;
    }

    const noneChecked = function() {
        return this.filter(':checked').length === 0;
    }
    const noneSelected = function() {
        return this.filter(':selected').length === 0;
    }

    $.openGlobalIFrameModal = openGlobalIFrameModal;
    $.closeGlobalIFrameModal = closeGlobalIFrameModal;

    $.fn.extend({
        showToast: showToast,
        appendAlert: appendAlert,
        showAlert: showAlert,
        autoDismiss: autoDismiss,
        superModal: superModal,
        bootstrapModal: bootstrapModal,

        allChecked: allChecked,
        allSelected: allSelected,
        anyChecked: anyChecked,
        anySelected: anySelected,
        noneChecked: noneChecked,
        noneSelected: noneSelected,
    });
})
