/*!
 * Tabler v1.2.0 (https://tabler.io)
 * Copyright 2018-2025 The Tabler Authors
 * Copyright 2018-2025 codecalm.net Paweł <PERSON>
 * Licensed under MIT (https://github.com/tabler/tabler/blob/master/LICENSE)
 */
/**
 * Converts a given value to a percentage string.
 *
 * @param {Number} $value - The value to be converted to a percentage.
 * @return {String} - The percentage representation of the value.
 */
/**
 * Generates a transparent version of the given color.
 *
 * @param {Color} $color - The base color to be made transparent.
 * @param {Number} $alpha - The level of transparency, ranging from 0 (fully transparent) to 1 (fully opaque). Default is 1.
 * @return {Color} - The resulting color with the specified transparency.
 */
[data-bs-theme-base=slate] {
  --tblr-gray-50: #f8fafc;
  --tblr-gray-100: #f1f5f9;
  --tblr-gray-200: #e2e8f0;
  --tblr-gray-300: #cbd5e1;
  --tblr-gray-400: #94a3b8;
  --tblr-gray-500: #64748b;
  --tblr-gray-600: #475569;
  --tblr-gray-700: #334155;
  --tblr-gray-800: #1e293b;
  --tblr-gray-900: #0f172a;
  --tblr-gray-950: #020617;
}

[data-bs-theme-base=gray] {
  --tblr-gray-50: #f9fafb;
  --tblr-gray-100: #f3f4f6;
  --tblr-gray-200: #e5e7eb;
  --tblr-gray-300: #d1d5db;
  --tblr-gray-400: #9ca3af;
  --tblr-gray-500: #6b7280;
  --tblr-gray-600: #4b5563;
  --tblr-gray-700: #374151;
  --tblr-gray-800: #1f2937;
  --tblr-gray-900: #111827;
  --tblr-gray-950: #030712;
}

[data-bs-theme-base=zinc] {
  --tblr-gray-50: #fafafa;
  --tblr-gray-100: #f4f4f5;
  --tblr-gray-200: #e4e4e7;
  --tblr-gray-300: #d4d4d8;
  --tblr-gray-400: #a1a1aa;
  --tblr-gray-500: #71717a;
  --tblr-gray-600: #52525b;
  --tblr-gray-700: #3f3f46;
  --tblr-gray-800: #27272a;
  --tblr-gray-900: #18181b;
  --tblr-gray-950: #09090b;
}

[data-bs-theme-base=neutral] {
  --tblr-gray-50: #fafafa;
  --tblr-gray-100: #f5f5f5;
  --tblr-gray-200: #e5e5e5;
  --tblr-gray-300: #d4d4d4;
  --tblr-gray-400: #a3a3a3;
  --tblr-gray-500: #737373;
  --tblr-gray-600: #525252;
  --tblr-gray-700: #404040;
  --tblr-gray-800: #262626;
  --tblr-gray-900: #171717;
  --tblr-gray-950: #0a0a0a;
}

[data-bs-theme-base=stone] {
  --tblr-gray-50: #fafaf9;
  --tblr-gray-100: #f5f5f4;
  --tblr-gray-200: #e7e5e4;
  --tblr-gray-300: #d6d3d1;
  --tblr-gray-400: #a8a29e;
  --tblr-gray-500: #78716c;
  --tblr-gray-600: #57534e;
  --tblr-gray-700: #44403c;
  --tblr-gray-800: #292524;
  --tblr-gray-900: #1c1917;
  --tblr-gray-950: #0c0a09;
}

[data-bs-theme-base=pink] {
  --tblr-gray-50: #fdf2f8;
  --tblr-gray-100: #fce7f3;
  --tblr-gray-200: #fbcfe8;
  --tblr-gray-300: #f9a8d4;
  --tblr-gray-400: #f472b6;
  --tblr-gray-500: #ec4899;
  --tblr-gray-600: #db2777;
  --tblr-gray-700: #be185d;
  --tblr-gray-800: #9d174d;
  --tblr-gray-900: #831843;
  --tblr-gray-950: #500724;
}

[data-bs-theme-primary=blue] {
  --tblr-primary: #066fd1;
  --tblr-primary-rgb: 6, 111, 209;
}

[data-bs-theme-primary=azure] {
  --tblr-primary: #4299e1;
  --tblr-primary-rgb: 66, 153, 225;
}

[data-bs-theme-primary=indigo] {
  --tblr-primary: #4263eb;
  --tblr-primary-rgb: 66, 99, 235;
}

[data-bs-theme-primary=purple] {
  --tblr-primary: #ae3ec9;
  --tblr-primary-rgb: 174, 62, 201;
}

[data-bs-theme-primary=pink] {
  --tblr-primary: #d6336c;
  --tblr-primary-rgb: 214, 51, 108;
}

[data-bs-theme-primary=red] {
  --tblr-primary: #d63939;
  --tblr-primary-rgb: 214, 57, 57;
}

[data-bs-theme-primary=orange] {
  --tblr-primary: #f76707;
  --tblr-primary-rgb: 247, 103, 7;
}

[data-bs-theme-primary=yellow] {
  --tblr-primary: #f59f00;
  --tblr-primary-rgb: 245, 159, 0;
}

[data-bs-theme-primary=lime] {
  --tblr-primary: #74b816;
  --tblr-primary-rgb: 116, 184, 22;
}

[data-bs-theme-primary=green] {
  --tblr-primary: #2fb344;
  --tblr-primary-rgb: 47, 179, 68;
}

[data-bs-theme-primary=teal] {
  --tblr-primary: #0ca678;
  --tblr-primary-rgb: 12, 166, 120;
}

[data-bs-theme-primary=cyan] {
  --tblr-primary: #17a2b8;
  --tblr-primary-rgb: 23, 162, 184;
}

[data-bs-theme-radius="0"] {
  --tblr-border-radius-scale: 0;
}

[data-bs-theme-radius="0.5"] {
  --tblr-border-radius-scale: 0.5;
}

[data-bs-theme-radius="1"] {
  --tblr-border-radius-scale: 1;
}

[data-bs-theme-radius="1.5"] {
  --tblr-border-radius-scale: 1.5;
}

[data-bs-theme-radius="2"] {
  --tblr-border-radius-scale: 2;
}

[data-bs-theme-primary=inverted] {
  --tblr-primary: var(--tblr-gray-800);
  --tblr-primary-fg: var(--tblr-light);
  --tblr-primary-rgb: 31, 41, 55;
}
[data-bs-theme-primary=inverted][data-bs-theme=dark],
[data-bs-theme-primary=inverted] [data-bs-theme=dark] {
  --tblr-primary: #f9fafb;
  --tblr-primary-fg: var(--tblr-dark);
  --tblr-primary-rgb: 249, 250, 251;
}

[data-bs-theme-font=monospace] {
  --tblr-body-font-family: var(--tblr-font-monospace);
  --tblr-body-font-size: 80%;
}

[data-bs-theme-font=sans-serif] {
  --tblr-body-font-family: var(--tblr-font-sans-serif);
}

[data-bs-theme-font=serif] {
  --tblr-body-font-family: var(--tblr-font-serif);
}

[data-bs-theme-font=comic] {
  --tblr-body-font-family: var(--tblr-font-comic);
}

/*# sourceMappingURL=tabler-themes.css.map */