/*!
 * Tabler v1.2.0 (https://tabler.io)
 * Copyright 2018-2025 The Tabler Authors
 * Copyright 2018-2025 codecalm.net Paweł <PERSON>
 * Licensed under MIT (https://github.com/tabler/tabler/blob/master/LICENSE)
 */
const themeConfig={theme:"light","theme-base":"gray","theme-font":"sans-serif","theme-primary":"blue","theme-radius":"1"},params=new Proxy(new URLSearchParams(window.location.search),{get:(e,t)=>e.get(t)});for(const e in themeConfig){const t=params[e];let a;if(t)localStorage.setItem("tabler-"+e,t),a=t;else{a=localStorage.getItem("tabler-"+e)||themeConfig[e]}a!==themeConfig[e]?document.documentElement.setAttribute("data-bs-"+e,a):document.documentElement.removeAttribute("data-bs-"+e)}
//# sourceMappingURL=tabler-theme.esm.min.js.map