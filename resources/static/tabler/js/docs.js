/*! @docsearch/js 3.9.0 | MIT License | © Algolia, Inc. and contributors | https://docsearch.algolia.com */
function e() {
  return e = Object.assign ? Object.assign.bind() : function (e) {
    for (var t = 1; t < arguments.length; t++) {
      var n = arguments[t];
      for (var r in n) ({}).hasOwnProperty.call(n, r) && (e[r] = n[r]);
    }
    return e;
  }, e.apply(null, arguments);
}
function t(e) {
  return t = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (e) {
    return typeof e;
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e;
  }, t(e);
}
var n,
  r,
  o,
  i,
  a,
  c,
  u,
  l,
  s,
  f,
  p,
  m,
  v = {},
  h = [],
  d = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,
  y = Array.isArray;
function _(e, t) {
  for (var n in t) e[n] = t[n];
  return e;
}
function g(e) {
  e && e.parentNode && e.parentNode.removeChild(e);
}
function b(e, t, r) {
  var o,
    i,
    a,
    c = {};
  for (a in t) "key" == a ? o = t[a] : "ref" == a ? i = t[a] : c[a] = t[a];
  if (arguments.length > 2 && (c.children = arguments.length > 3 ? n.call(arguments, 2) : r), "function" == typeof e && null != e.defaultProps) for (a in e.defaultProps) void 0 === c[a] && (c[a] = e.defaultProps[a]);
  return S(e, c, o, i, null);
}
function S(e, t, n, i, a) {
  var c = {
    type: e,
    props: t,
    key: n,
    ref: i,
    __k: null,
    __: null,
    __b: 0,
    __e: null,
    __c: null,
    constructor: void 0,
    __v: null == a ? ++o : a,
    __i: -1,
    __u: 0
  };
  return null == a && null != r.vnode && r.vnode(c), c;
}
function O(e) {
  return e.children;
}
function w(e, t) {
  this.props = e, this.context = t;
}
function E(e, t) {
  if (null == t) return e.__ ? E(e.__, e.__i + 1) : null;
  for (var n; t < e.__k.length; t++) if (null != (n = e.__k[t]) && null != n.__e) return n.__e;
  return "function" == typeof e.type ? E(e) : null;
}
function j(e) {
  var t, n;
  if (null != (e = e.__) && null != e.__c) {
    for (e.__e = e.__c.base = null, t = 0; t < e.__k.length; t++) if (null != (n = e.__k[t]) && null != n.__e) {
      e.__e = e.__c.base = n.__e;
      break;
    }
    return j(e);
  }
}
function P(e) {
  (!e.__d && (e.__d = true) && i.push(e) && !I.__r++ || a !== r.debounceRendering) && ((a = r.debounceRendering) || c)(I);
}
function I() {
  var e, t, n, o, a, c, l, s;
  for (i.sort(u); e = i.shift();) e.__d && (t = i.length, o = void 0, c = (a = (n = e).__v).__e, l = [], s = [], n.__P && ((o = _({}, a)).__v = a.__v + 1, r.vnode && r.vnode(o), R(n.__P, o, a, n.__n, n.__P.namespaceURI, 32 & a.__u ? [c] : null, l, null == c ? E(a) : c, !!(32 & a.__u), s), o.__v = a.__v, o.__.__k[o.__i] = o, L(l, o, s), o.__e != c && j(o)), i.length > t && i.sort(u));
  I.__r = 0;
}
function k(e, t, n, r, o, i, a, c, u, l, s) {
  var f,
    p,
    m,
    d,
    _,
    g,
    b = r && r.__k || h,
    w = t.length;
  for (u = function (e, t, n, r, o) {
    var i,
      a,
      c,
      u,
      l,
      s = n.length,
      f = s,
      p = 0;
    for (e.__k = new Array(o), i = 0; i < o; i++) null != (a = t[i]) && "boolean" != typeof a && "function" != typeof a ? (u = i + p, (a = e.__k[i] = "string" == typeof a || "number" == typeof a || "bigint" == typeof a || a.constructor == String ? S(null, a, null, null, null) : y(a) ? S(O, {
      children: a
    }, null, null, null) : void 0 === a.constructor && a.__b > 0 ? S(a.type, a.props, a.key, a.ref ? a.ref : null, a.__v) : a).__ = e, a.__b = e.__b + 1, c = null, -1 !== (l = a.__i = x(a, n, u, f)) && (f--, (c = n[l]) && (c.__u |= 2)), null == c || null === c.__v ? (-1 == l && p--, "function" != typeof a.type && (a.__u |= 4)) : l != u && (l == u - 1 ? p-- : l == u + 1 ? p++ : (l > u ? p-- : p++, a.__u |= 4))) : e.__k[i] = null;
    if (f) for (i = 0; i < s; i++) null != (c = n[i]) && !(2 & c.__u) && (c.__e == r && (r = E(c)), H(c, c));
    return r;
  }(n, t, b, u, w), f = 0; f < w; f++) null != (m = n.__k[f]) && (p = -1 === m.__i ? v : b[m.__i] || v, m.__i = f, g = R(e, m, p, o, i, a, c, u, l, s), d = m.__e, m.ref && p.ref != m.ref && (p.ref && M(p.ref, null, m), s.push(m.ref, m.__c || d, m)), null == _ && null != d && (_ = d), 4 & m.__u || p.__k === m.__k ? u = D(m, u, e) : "function" == typeof m.type && void 0 !== g ? u = g : d && (u = d.nextSibling), m.__u &= -7);
  return n.__e = _, u;
}
function D(e, t, n) {
  var r, o;
  if ("function" == typeof e.type) {
    for (r = e.__k, o = 0; r && o < r.length; o++) r[o] && (r[o].__ = e, t = D(r[o], t, n));
    return t;
  }
  e.__e != t && (t && e.type && !n.contains(t) && (t = E(e)), n.insertBefore(e.__e, t || null), t = e.__e);
  do {
    t = t && t.nextSibling;
  } while (null != t && 8 == t.nodeType);
  return t;
}
function C(e, t) {
  return t = t || [], null == e || "boolean" == typeof e || (y(e) ? e.some(function (e) {
    C(e, t);
  }) : t.push(e)), t;
}
function x(e, t, n, r) {
  var o,
    i,
    a = e.key,
    c = e.type,
    u = t[n];
  if (null === u || u && a == u.key && c === u.type && !(2 & u.__u)) return n;
  if (r > (null == u || 2 & u.__u ? 0 : 1)) for (o = n - 1, i = n + 1; o >= 0 || i < t.length;) {
    if (o >= 0) {
      if ((u = t[o]) && !(2 & u.__u) && a == u.key && c === u.type) return o;
      o--;
    }
    if (i < t.length) {
      if ((u = t[i]) && !(2 & u.__u) && a == u.key && c === u.type) return i;
      i++;
    }
  }
  return -1;
}
function A(e, t, n) {
  "-" == t[0] ? e.setProperty(t, null == n ? "" : n) : e[t] = null == n ? "" : "number" != typeof n || d.test(t) ? n : n + "px";
}
function N(e, t, n, r, o) {
  var i;
  e: if ("style" == t) {
    if ("string" == typeof n) e.style.cssText = n;else {
      if ("string" == typeof r && (e.style.cssText = r = ""), r) for (t in r) n && t in n || A(e.style, t, "");
      if (n) for (t in n) r && n[t] === r[t] || A(e.style, t, n[t]);
    }
  } else if ("o" == t[0] && "n" == t[1]) i = t != (t = t.replace(l, "$1")), t = t.toLowerCase() in e || "onFocusOut" == t || "onFocusIn" == t ? t.toLowerCase().slice(2) : t.slice(2), e.l || (e.l = {}), e.l[t + i] = n, n ? r ? n.u = r.u : (n.u = s, e.addEventListener(t, i ? p : f, i)) : e.removeEventListener(t, i ? p : f, i);else {
    if ("http://www.w3.org/2000/svg" == o) t = t.replace(/xlink(H|:h)/, "h").replace(/sName$/, "s");else if ("width" != t && "height" != t && "href" != t && "list" != t && "form" != t && "tabIndex" != t && "download" != t && "rowSpan" != t && "colSpan" != t && "role" != t && "popover" != t && t in e) try {
      e[t] = null == n ? "" : n;
      break e;
    } catch (e) {}
    "function" == typeof n || (null == n || false === n && "-" != t[4] ? e.removeAttribute(t) : e.setAttribute(t, "popover" == t && 1 == n ? "" : n));
  }
}
function T(e) {
  return function (t) {
    if (this.l) {
      var n = this.l[t.type + e];
      if (null == t.t) t.t = s++;else if (t.t < n.u) return;
      return n(r.event ? r.event(t) : t);
    }
  };
}
function R(e, t, n, o, i, a, c, u, l, s) {
  var f,
    p,
    m,
    v,
    h,
    d,
    b,
    S,
    E,
    j,
    P,
    I,
    D,
    C,
    x,
    A,
    N,
    T = t.type;
  if (void 0 !== t.constructor) return null;
  128 & n.__u && (l = !!(32 & n.__u), a = [u = t.__e = n.__e]), (f = r.__b) && f(t);
  e: if ("function" == typeof T) try {
    if (S = t.props, E = "prototype" in T && T.prototype.render, j = (f = T.contextType) && o[f.__c], P = f ? j ? j.props.value : f.__ : o, n.__c ? b = (p = t.__c = n.__c).__ = p.__E : (E ? t.__c = p = new T(S, P) : (t.__c = p = new w(S, P), p.constructor = T, p.render = U), j && j.sub(p), p.props = S, p.state || (p.state = {}), p.context = P, p.__n = o, m = p.__d = !0, p.__h = [], p._sb = []), E && null == p.__s && (p.__s = p.state), E && null != T.getDerivedStateFromProps && (p.__s == p.state && (p.__s = _({}, p.__s)), _(p.__s, T.getDerivedStateFromProps(S, p.__s))), v = p.props, h = p.state, p.__v = t, m) E && null == T.getDerivedStateFromProps && null != p.componentWillMount && p.componentWillMount(), E && null != p.componentDidMount && p.__h.push(p.componentDidMount);else {
      if (E && null == T.getDerivedStateFromProps && S !== v && null != p.componentWillReceiveProps && p.componentWillReceiveProps(S, P), !p.__e && (null != p.shouldComponentUpdate && !1 === p.shouldComponentUpdate(S, p.__s, P) || t.__v == n.__v)) {
        for (t.__v != n.__v && (p.props = S, p.state = p.__s, p.__d = !1), t.__e = n.__e, t.__k = n.__k, t.__k.some(function (e) {
          e && (e.__ = t);
        }), I = 0; I < p._sb.length; I++) p.__h.push(p._sb[I]);
        p._sb = [], p.__h.length && c.push(p);
        break e;
      }
      null != p.componentWillUpdate && p.componentWillUpdate(S, p.__s, P), E && null != p.componentDidUpdate && p.__h.push(function () {
        p.componentDidUpdate(v, h, d);
      });
    }
    if (p.context = P, p.props = S, p.__P = e, p.__e = !1, D = r.__r, C = 0, E) {
      for (p.state = p.__s, p.__d = !1, D && D(t), f = p.render(p.props, p.state, p.context), x = 0; x < p._sb.length; x++) p.__h.push(p._sb[x]);
      p._sb = [];
    } else do {
      p.__d = !1, D && D(t), f = p.render(p.props, p.state, p.context), p.state = p.__s;
    } while (p.__d && ++C < 25);
    p.state = p.__s, null != p.getChildContext && (o = _(_({}, o), p.getChildContext())), E && !m && null != p.getSnapshotBeforeUpdate && (d = p.getSnapshotBeforeUpdate(v, h)), u = k(e, y(A = null != f && f.type === O && null == f.key ? f.props.children : f) ? A : [A], t, n, o, i, a, c, u, l, s), p.base = t.__e, t.__u &= -161, p.__h.length && c.push(p), b && (p.__E = p.__ = null);
  } catch (e) {
    if (t.__v = null, l || null != a) {
      if (e.then) {
        for (t.__u |= l ? 160 : 128; u && 8 == u.nodeType && u.nextSibling;) u = u.nextSibling;
        a[a.indexOf(u)] = null, t.__e = u;
      } else for (N = a.length; N--;) g(a[N]);
    } else t.__e = n.__e, t.__k = n.__k;
    r.__e(e, t, n);
  } else null == a && t.__v == n.__v ? (t.__k = n.__k, t.__e = n.__e) : u = t.__e = q(n.__e, t, n, o, i, a, c, l, s);
  return (f = r.diffed) && f(t), 128 & t.__u ? void 0 : u;
}
function L(e, t, n) {
  for (var o = 0; o < n.length; o++) M(n[o], n[++o], n[++o]);
  r.__c && r.__c(t, e), e.some(function (t) {
    try {
      e = t.__h, t.__h = [], e.some(function (e) {
        e.call(t);
      });
    } catch (e) {
      r.__e(e, t.__v);
    }
  });
}
function q(e, t, o, i, a, c, u, l, s) {
  var f,
    p,
    m,
    h,
    d,
    _,
    b,
    S = o.props,
    O = t.props,
    w = t.type;
  if ("svg" == w ? a = "http://www.w3.org/2000/svg" : "math" == w ? a = "http://www.w3.org/1998/Math/MathML" : a || (a = "http://www.w3.org/1999/xhtml"), null != c) for (f = 0; f < c.length; f++) if ((d = c[f]) && "setAttribute" in d == !!w && (w ? d.localName == w : 3 == d.nodeType)) {
    e = d, c[f] = null;
    break;
  }
  if (null == e) {
    if (null == w) return document.createTextNode(O);
    e = document.createElementNS(a, w, O.is && O), l && (r.__m && r.__m(t, c), l = false), c = null;
  }
  if (null === w) S === O || l && e.data === O || (e.data = O);else {
    if (c = c && n.call(e.childNodes), S = o.props || v, !l && null != c) for (S = {}, f = 0; f < e.attributes.length; f++) S[(d = e.attributes[f]).name] = d.value;
    for (f in S) if (d = S[f], "children" == f) ;else if ("dangerouslySetInnerHTML" == f) m = d;else if (!(f in O)) {
      if ("value" == f && "defaultValue" in O || "checked" == f && "defaultChecked" in O) continue;
      N(e, f, null, d, a);
    }
    for (f in O) d = O[f], "children" == f ? h = d : "dangerouslySetInnerHTML" == f ? p = d : "value" == f ? _ = d : "checked" == f ? b = d : l && "function" != typeof d || S[f] === d || N(e, f, d, S[f], a);
    if (p) l || m && (p.__html === m.__html || p.__html === e.innerHTML) || (e.innerHTML = p.__html), t.__k = [];else if (m && (e.innerHTML = ""), k(e, y(h) ? h : [h], t, o, i, "foreignObject" == w ? "http://www.w3.org/1999/xhtml" : a, c, u, c ? c[0] : o.__k && E(o, 0), l, s), null != c) for (f = c.length; f--;) g(c[f]);
    l || (f = "value", "progress" == w && null == _ ? e.removeAttribute("value") : void 0 !== _ && (_ !== e[f] || "progress" == w && !_ || "option" == w && _ !== S[f]) && N(e, f, _, S[f], a), f = "checked", void 0 !== b && b !== e[f] && N(e, f, b, S[f], a));
  }
  return e;
}
function M(e, t, n) {
  try {
    if ("function" == typeof e) {
      var o = "function" == typeof e.__u;
      o && e.__u(), o && null == t || (e.__u = e(t));
    } else e.current = t;
  } catch (e) {
    r.__e(e, n);
  }
}
function H(e, t, n) {
  var o, i;
  if (r.unmount && r.unmount(e), (o = e.ref) && (o.current && o.current !== e.__e || M(o, null, t)), null != (o = e.__c)) {
    if (o.componentWillUnmount) try {
      o.componentWillUnmount();
    } catch (e) {
      r.__e(e, t);
    }
    o.base = o.__P = null;
  }
  if (o = e.__k) for (i = 0; i < o.length; i++) o[i] && H(o[i], t, n || "function" != typeof e.type);
  n || g(e.__e), e.__c = e.__ = e.__e = void 0;
}
function U(e, t, n) {
  return this.constructor(e, n);
}
function F(e, t, o) {
  var i, a, c, u;
  t == document && (t = document.documentElement), r.__ && r.__(e, t), a = (i = "function" == typeof o) ? null : o && o.__k || t.__k, c = [], u = [], R(t, e = (!i && o || t).__k = b(O, null, [e]), a || v, v, t.namespaceURI, !i && o ? [o] : a ? null : t.firstChild ? n.call(t.childNodes) : null, c, !i && o ? o : a ? a.__e : t.firstChild, i, u), L(c, e, u);
}
function B(e, t) {
  F(e, t, B);
}
function V(e, t, r) {
  var o,
    i,
    a,
    c,
    u = _({}, e.props);
  for (a in e.type && e.type.defaultProps && (c = e.type.defaultProps), t) "key" == a ? o = t[a] : "ref" == a ? i = t[a] : u[a] = void 0 === t[a] && void 0 !== c ? c[a] : t[a];
  return arguments.length > 2 && (u.children = arguments.length > 3 ? n.call(arguments, 2) : r), S(e.type, u, o || e.key, i || e.ref, null);
}
n = h.slice, r = {
  __e: function (e, t, n, r) {
    for (var o, i, a; t = t.__;) if ((o = t.__c) && !o.__) try {
      if ((i = o.constructor) && null != i.getDerivedStateFromError && (o.setState(i.getDerivedStateFromError(e)), a = o.__d), null != o.componentDidCatch && (o.componentDidCatch(e, r || {}), a = o.__d), a) return o.__E = o;
    } catch (t) {
      e = t;
    }
    throw e;
  }
}, o = 0, w.prototype.setState = function (e, t) {
  var n;
  n = null != this.__s && this.__s !== this.state ? this.__s : this.__s = _({}, this.state), "function" == typeof e && (e = e(_({}, n), this.props)), e && _(n, e), null != e && this.__v && (t && this._sb.push(t), P(this));
}, w.prototype.forceUpdate = function (e) {
  this.__v && (this.__e = true, e && this.__h.push(e), P(this));
}, w.prototype.render = O, i = [], c = "function" == typeof Promise ? Promise.prototype.then.bind(Promise.resolve()) : setTimeout, u = function (e, t) {
  return e.__v.__b - t.__v.__b;
}, I.__r = 0, l = /(PointerCapture)$|Capture$/i, s = 0, f = T(false), p = T(true), m = 0;
var K,
  W,
  z,
  J,
  Q = 0,
  $ = [],
  Z = r,
  G = Z.__b,
  Y = Z.__r,
  X = Z.diffed,
  ee = Z.__c,
  te = Z.unmount,
  ne = Z.__;
function re(e, t) {
  Z.__h && Z.__h(W, e, Q || t), Q = 0;
  var n = W.__H || (W.__H = {
    __: [],
    __h: []
  });
  return e >= n.__.length && n.__.push({}), n.__[e];
}
function oe(e) {
  return Q = 1, ie(Se, e);
}
function ie(e, t, n) {
  var r = re(K++, 2);
  if (r.t = e, !r.__c && (r.__ = [n ? n(t) : Se(void 0, t), function (e) {
    var t = r.__N ? r.__N[0] : r.__[0],
      n = r.t(t, e);
    t !== n && (r.__N = [n, r.__[1]], r.__c.setState({}));
  }], r.__c = W, !W.u)) {
    var o = function (e, t, n) {
      if (!r.__c.__H) return true;
      var o = r.__c.__H.__.filter(function (e) {
        return !!e.__c;
      });
      if (o.every(function (e) {
        return !e.__N;
      })) return !i || i.call(this, e, t, n);
      var a = r.__c.props !== e;
      return o.forEach(function (e) {
        if (e.__N) {
          var t = e.__[0];
          e.__ = e.__N, e.__N = void 0, t !== e.__[0] && (a = true);
        }
      }), i && i.call(this, e, t, n) || a;
    };
    W.u = true;
    var i = W.shouldComponentUpdate,
      a = W.componentWillUpdate;
    W.componentWillUpdate = function (e, t, n) {
      if (this.__e) {
        var r = i;
        i = void 0, o(e, t, n), i = r;
      }
      a && a.call(this, e, t, n);
    }, W.shouldComponentUpdate = o;
  }
  return r.__N || r.__;
}
function ae(e, t) {
  var n = re(K++, 3);
  !Z.__s && be(n.__H, t) && (n.__ = e, n.i = t, W.__H.__h.push(n));
}
function ce(e, t) {
  var n = re(K++, 4);
  !Z.__s && be(n.__H, t) && (n.__ = e, n.i = t, W.__h.push(n));
}
function ue(e) {
  return Q = 5, se(function () {
    return {
      current: e
    };
  }, []);
}
function le(e, t, n) {
  Q = 6, ce(function () {
    return "function" == typeof e ? (e(t()), function () {
      return e(null);
    }) : e ? (e.current = t(), function () {
      return e.current = null;
    }) : void 0;
  }, null == n ? n : n.concat(e));
}
function se(e, t) {
  var n = re(K++, 7);
  return be(n.__H, t) && (n.__ = e(), n.__H = t, n.__h = e), n.__;
}
function fe(e, t) {
  return Q = 8, se(function () {
    return e;
  }, t);
}
function pe(e) {
  var t = W.context[e.__c],
    n = re(K++, 9);
  return n.c = e, t ? (null == n.__ && (n.__ = true, t.sub(W)), t.props.value) : e.__;
}
function me(e, t) {
  Z.useDebugValue && Z.useDebugValue(t ? t(e) : e);
}
function ve() {
  var e = re(K++, 11);
  if (!e.__) {
    for (var t = W.__v; null !== t && !t.__m && null !== t.__;) t = t.__;
    var n = t.__m || (t.__m = [0, 0]);
    e.__ = "P" + n[0] + "-" + n[1]++;
  }
  return e.__;
}
function he() {
  for (var e; e = $.shift();) if (e.__P && e.__H) try {
    e.__H.__h.forEach(_e), e.__H.__h.forEach(ge), e.__H.__h = [];
  } catch (t) {
    e.__H.__h = [], Z.__e(t, e.__v);
  }
}
Z.__b = function (e) {
  W = null, G && G(e);
}, Z.__ = function (e, t) {
  e && t.__k && t.__k.__m && (e.__m = t.__k.__m), ne && ne(e, t);
}, Z.__r = function (e) {
  Y && Y(e), K = 0;
  var t = (W = e.__c).__H;
  t && (z === W ? (t.__h = [], W.__h = [], t.__.forEach(function (e) {
    e.__N && (e.__ = e.__N), e.i = e.__N = void 0;
  })) : (t.__h.forEach(_e), t.__h.forEach(ge), t.__h = [], K = 0)), z = W;
}, Z.diffed = function (e) {
  X && X(e);
  var t = e.__c;
  t && t.__H && (t.__H.__h.length && (1 !== $.push(t) && J === Z.requestAnimationFrame || ((J = Z.requestAnimationFrame) || ye)(he)), t.__H.__.forEach(function (e) {
    e.i && (e.__H = e.i), e.i = void 0;
  })), z = W = null;
}, Z.__c = function (e, t) {
  t.some(function (e) {
    try {
      e.__h.forEach(_e), e.__h = e.__h.filter(function (e) {
        return !e.__ || ge(e);
      });
    } catch (n) {
      t.some(function (e) {
        e.__h && (e.__h = []);
      }), t = [], Z.__e(n, e.__v);
    }
  }), ee && ee(e, t);
}, Z.unmount = function (e) {
  te && te(e);
  var t,
    n = e.__c;
  n && n.__H && (n.__H.__.forEach(function (e) {
    try {
      _e(e);
    } catch (e) {
      t = e;
    }
  }), n.__H = void 0, t && Z.__e(t, n.__v));
};
var de = "function" == typeof requestAnimationFrame;
function ye(e) {
  var t,
    n = function () {
      clearTimeout(r), de && cancelAnimationFrame(t), setTimeout(e);
    },
    r = setTimeout(n, 100);
  de && (t = requestAnimationFrame(n));
}
function _e(e) {
  var t = W,
    n = e.__c;
  "function" == typeof n && (e.__c = void 0, n()), W = t;
}
function ge(e) {
  var t = W;
  e.__c = e.__(), W = t;
}
function be(e, t) {
  return !e || e.length !== t.length || t.some(function (t, n) {
    return t !== e[n];
  });
}
function Se(e, t) {
  return "function" == typeof t ? t(e) : t;
}
function Oe(e, t) {
  for (var n in t) e[n] = t[n];
  return e;
}
function we(e, t) {
  for (var n in e) if ("__source" !== n && !(n in t)) return true;
  for (var r in t) if ("__source" !== r && e[r] !== t[r]) return true;
  return false;
}
function Ee(e, t) {
  var n = t(),
    r = oe({
      t: {
        __: n,
        u: t
      }
    }),
    o = r[0].t,
    i = r[1];
  return ce(function () {
    o.__ = n, o.u = t, je(o) && i({
      t: o
    });
  }, [e, n, t]), ae(function () {
    return je(o) && i({
      t: o
    }), e(function () {
      je(o) && i({
        t: o
      });
    });
  }, [e]), n;
}
function je(e) {
  var t,
    n,
    r = e.u,
    o = e.__;
  try {
    var i = r();
    return !((t = o) === (n = i) && (0 !== t || 1 / t == 1 / n) || t != t && n != n);
  } catch (e) {
    return true;
  }
}
function Pe(e) {
  e();
}
function Ie(e) {
  return e;
}
function ke() {
  return [false, Pe];
}
var De = ce;
function Ce(e, t) {
  this.props = e, this.context = t;
}
(Ce.prototype = new w()).isPureReactComponent = true, Ce.prototype.shouldComponentUpdate = function (e, t) {
  return we(this.props, e) || we(this.state, t);
};
var xe = r.__b;
r.__b = function (e) {
  e.type && e.type.__f && e.ref && (e.props.ref = e.ref, e.ref = null), xe && xe(e);
};
var Ae = "undefined" != typeof Symbol && Symbol.for && Symbol.for("react.forward_ref") || 3911,
  Ne = function (e, t) {
    return null == e ? null : C(C(e).map(t));
  },
  Te = {
    map: Ne,
    forEach: Ne,
    count: function (e) {
      return e ? C(e).length : 0;
    },
    only: function (e) {
      var t = C(e);
      if (1 !== t.length) throw "Children.only";
      return t[0];
    },
    toArray: C
  },
  Re = r.__e;
r.__e = function (e, t, n, r) {
  if (e.then) for (var o, i = t; i = i.__;) if ((o = i.__c) && o.__c) return null == t.__e && (t.__e = n.__e, t.__k = n.__k), o.__c(e, t);
  Re(e, t, n, r);
};
var Le = r.unmount;
function qe(e, t, n) {
  return e && (e.__c && e.__c.__H && (e.__c.__H.__.forEach(function (e) {
    "function" == typeof e.__c && e.__c();
  }), e.__c.__H = null), null != (e = Oe({}, e)).__c && (e.__c.__P === n && (e.__c.__P = t), e.__c = null), e.__k = e.__k && e.__k.map(function (e) {
    return qe(e, t, n);
  })), e;
}
function Me(e, t, n) {
  return e && n && (e.__v = null, e.__k = e.__k && e.__k.map(function (e) {
    return Me(e, t, n);
  }), e.__c && e.__c.__P === t && (e.__e && n.appendChild(e.__e), e.__c.__e = true, e.__c.__P = n)), e;
}
function He() {
  this.__u = 0, this.o = null, this.__b = null;
}
function Ue(e) {
  var t = e.__.__c;
  return t && t.__a && t.__a(e);
}
function Fe() {
  this.i = null, this.l = null;
}
r.unmount = function (e) {
  var t = e.__c;
  t && t.__R && t.__R(), t && 32 & e.__u && (e.type = null), Le && Le(e);
}, (He.prototype = new w()).__c = function (e, t) {
  var n = t.__c,
    r = this;
  null == r.o && (r.o = []), r.o.push(n);
  var o = Ue(r.__v),
    i = false,
    a = function () {
      i || (i = true, n.__R = null, o ? o(c) : c());
    };
  n.__R = a;
  var c = function () {
    if (! --r.__u) {
      if (r.state.__a) {
        var e = r.state.__a;
        r.__v.__k[0] = Me(e, e.__c.__P, e.__c.__O);
      }
      var t;
      for (r.setState({
        __a: r.__b = null
      }); t = r.o.pop();) t.forceUpdate();
    }
  };
  r.__u++ || 32 & t.__u || r.setState({
    __a: r.__b = r.__v.__k[0]
  }), e.then(a, a);
}, He.prototype.componentWillUnmount = function () {
  this.o = [];
}, He.prototype.render = function (e, t) {
  if (this.__b) {
    if (this.__v.__k) {
      var n = document.createElement("div"),
        r = this.__v.__k[0].__c;
      this.__v.__k[0] = qe(this.__b, n, r.__O = r.__P);
    }
    this.__b = null;
  }
  var o = t.__a && b(O, null, e.fallback);
  return o && (o.__u &= -33), [b(O, null, t.__a ? null : e.children), o];
};
var Be = function (e, t, n) {
  if (++n[1] === n[0] && e.l.delete(t), e.props.revealOrder && ("t" !== e.props.revealOrder[0] || !e.l.size)) for (n = e.i; n;) {
    for (; n.length > 3;) n.pop()();
    if (n[1] < n[0]) break;
    e.i = n = n[2];
  }
};
function Ve(e) {
  return this.getChildContext = function () {
    return e.context;
  }, e.children;
}
function Ke(e) {
  var t = this,
    n = e.h;
  t.componentWillUnmount = function () {
    F(null, t.v), t.v = null, t.h = null;
  }, t.h && t.h !== n && t.componentWillUnmount(), t.v || (t.h = n, t.v = {
    nodeType: 1,
    parentNode: n,
    childNodes: [],
    contains: function () {
      return true;
    },
    appendChild: function (e) {
      this.childNodes.push(e), t.h.appendChild(e);
    },
    insertBefore: function (e, n) {
      this.childNodes.push(e), t.h.insertBefore(e, n);
    },
    removeChild: function (e) {
      this.childNodes.splice(this.childNodes.indexOf(e) >>> 1, 1), t.h.removeChild(e);
    }
  }), F(b(Ve, {
    context: t.context
  }, e.__v), t.v);
}
function We(e, t) {
  var n = b(Ke, {
    __v: e,
    h: t
  });
  return n.containerInfo = t, n;
}
(Fe.prototype = new w()).__a = function (e) {
  var t = this,
    n = Ue(t.__v),
    r = t.l.get(e);
  return r[0]++, function (o) {
    var i = function () {
      t.props.revealOrder ? (r.push(o), Be(t, e, r)) : o();
    };
    n ? n(i) : i();
  };
}, Fe.prototype.render = function (e) {
  this.i = null, this.l = new Map();
  var t = C(e.children);
  e.revealOrder && "b" === e.revealOrder[0] && t.reverse();
  for (var n = t.length; n--;) this.l.set(t[n], this.i = [1, 0, this.i]);
  return e.children;
}, Fe.prototype.componentDidUpdate = Fe.prototype.componentDidMount = function () {
  var e = this;
  this.l.forEach(function (t, n) {
    Be(e, n, t);
  });
};
var ze = "undefined" != typeof Symbol && Symbol.for && Symbol.for("react.element") || 60103,
  Je = /^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,
  Qe = /^on(Ani|Tra|Tou|BeforeInp|Compo)/,
  $e = /[A-Z0-9]/g,
  Ze = "undefined" != typeof document,
  Ge = function (e) {
    return ("undefined" != typeof Symbol && "symbol" == t(Symbol()) ? /fil|che|rad/ : /fil|che|ra/).test(e);
  };
function Ye(e, t, n) {
  return null == t.__k && (t.textContent = ""), F(e, t), "function" == typeof n && n(), e ? e.__c : null;
}
w.prototype.isReactComponent = {}, ["componentWillMount", "componentWillReceiveProps", "componentWillUpdate"].forEach(function (e) {
  Object.defineProperty(w.prototype, e, {
    configurable: true,
    get: function () {
      return this["UNSAFE_" + e];
    },
    set: function (t) {
      Object.defineProperty(this, e, {
        configurable: true,
        writable: true,
        value: t
      });
    }
  });
});
var Xe = r.event;
function et() {}
function tt() {
  return this.cancelBubble;
}
function nt() {
  return this.defaultPrevented;
}
r.event = function (e) {
  return Xe && (e = Xe(e)), e.persist = et, e.isPropagationStopped = tt, e.isDefaultPrevented = nt, e.nativeEvent = e;
};
var rt,
  ot = {
    enumerable: false,
    configurable: true,
    get: function () {
      return this.class;
    }
  },
  it = r.vnode;
r.vnode = function (e) {
  "string" == typeof e.type && function (e) {
    var t = e.props,
      n = e.type,
      r = {},
      o = -1 === n.indexOf("-");
    for (var i in t) {
      var a = t[i];
      if (!("value" === i && "defaultValue" in t && null == a || Ze && "children" === i && "noscript" === n || "class" === i || "className" === i)) {
        var c = i.toLowerCase();
        "defaultValue" === i && "value" in t && null == t.value ? i = "value" : "download" === i && true === a ? a = "" : "translate" === c && "no" === a ? a = false : "o" === c[0] && "n" === c[1] ? "ondoubleclick" === c ? i = "ondblclick" : "onchange" !== c || "input" !== n && "textarea" !== n || Ge(t.type) ? "onfocus" === c ? i = "onfocusin" : "onblur" === c ? i = "onfocusout" : Qe.test(i) && (i = c) : c = i = "oninput" : o && Je.test(i) ? i = i.replace($e, "-$&").toLowerCase() : null === a && (a = void 0), "oninput" === c && r[i = c] && (i = "oninputCapture"), r[i] = a;
      }
    }
    "select" == n && r.multiple && Array.isArray(r.value) && (r.value = C(t.children).forEach(function (e) {
      e.props.selected = -1 != r.value.indexOf(e.props.value);
    })), "select" == n && null != r.defaultValue && (r.value = C(t.children).forEach(function (e) {
      e.props.selected = r.multiple ? -1 != r.defaultValue.indexOf(e.props.value) : r.defaultValue == e.props.value;
    })), t.class && !t.className ? (r.class = t.class, Object.defineProperty(r, "className", ot)) : (t.className && !t.class || t.class && t.className) && (r.class = r.className = t.className), e.props = r;
  }(e), e.$$typeof = ze, it && it(e);
};
var at = r.__r;
r.__r = function (e) {
  at && at(e), rt = e.__c;
};
var ct = r.diffed;
r.diffed = function (e) {
  ct && ct(e);
  var t = e.props,
    n = e.__e;
  null != n && "textarea" === e.type && "value" in t && t.value !== n.value && (n.value = null == t.value ? "" : t.value), rt = null;
};
var ut = {
  ReactCurrentDispatcher: {
    current: {
      readContext: function (e) {
        return rt.__n[e.__c].props.value;
      },
      useCallback: fe,
      useContext: pe,
      useDebugValue: me,
      useDeferredValue: Ie,
      useEffect: ae,
      useId: ve,
      useImperativeHandle: le,
      useInsertionEffect: De,
      useLayoutEffect: ce,
      useMemo: se,
      useReducer: ie,
      useRef: ue,
      useState: oe,
      useSyncExternalStore: Ee,
      useTransition: ke
    }
  }
};
function lt(e) {
  return !!e && e.$$typeof === ze;
}
var st = {
  useState: oe,
  useId: ve,
  useReducer: ie,
  useEffect: ae,
  useLayoutEffect: ce,
  useInsertionEffect: De,
  useTransition: ke,
  useDeferredValue: Ie,
  useSyncExternalStore: Ee,
  startTransition: Pe,
  useRef: ue,
  useImperativeHandle: le,
  useMemo: se,
  useCallback: fe,
  useContext: pe,
  useDebugValue: me,
  version: "18.3.1",
  Children: Te,
  render: Ye,
  hydrate: function (e, t, n) {
    return B(e, t), "function" == typeof n && n(), e ? e.__c : null;
  },
  unmountComponentAtNode: function (e) {
    return !!e.__k && (F(null, e), true);
  },
  createPortal: We,
  createElement: b,
  createContext: function (e, t) {
    var n = {
      __c: t = "__cC" + m++,
      __: e,
      Consumer: function (e, t) {
        return e.children(t);
      },
      Provider: function (e) {
        var n, r;
        return this.getChildContext || (n = new Set(), (r = {})[t] = this, this.getChildContext = function () {
          return r;
        }, this.componentWillUnmount = function () {
          n = null;
        }, this.shouldComponentUpdate = function (e) {
          this.props.value !== e.value && n.forEach(function (e) {
            e.__e = true, P(e);
          });
        }, this.sub = function (e) {
          n.add(e);
          var t = e.componentWillUnmount;
          e.componentWillUnmount = function () {
            n && n.delete(e), t && t.call(e);
          };
        }), e.children;
      }
    };
    return n.Provider.__ = n.Consumer.contextType = n;
  },
  createFactory: function (e) {
    return b.bind(null, e);
  },
  cloneElement: function (e) {
    return lt(e) ? V.apply(null, arguments) : e;
  },
  createRef: function () {
    return {
      current: null
    };
  },
  Fragment: O,
  isValidElement: lt,
  isElement: lt,
  isFragment: function (e) {
    return lt(e) && e.type === O;
  },
  isMemo: function (e) {
    return !!e && !!e.displayName && ("string" == typeof e.displayName || e.displayName instanceof String) && e.displayName.startsWith("Memo(");
  },
  findDOMNode: function (e) {
    return e && (e.base || 1 === e.nodeType && e) || null;
  },
  Component: w,
  PureComponent: Ce,
  memo: function (e, t) {
    function n(e) {
      var n = this.props.ref,
        r = n == e.ref;
      return !r && n && (n.call ? n(null) : n.current = null), t ? !t(this.props, e) || !r : we(this.props, e);
    }
    function r(t) {
      return this.shouldComponentUpdate = n, b(e, t);
    }
    return r.displayName = "Memo(" + (e.displayName || e.name) + ")", r.prototype.isReactComponent = true, r.__f = true, r;
  },
  forwardRef: function (e) {
    function t(t) {
      var n = Oe({}, t);
      return delete n.ref, e(n, t.ref || null);
    }
    return t.$$typeof = Ae, t.render = t, t.prototype.isReactComponent = t.__f = true, t.displayName = "ForwardRef(" + (e.displayName || e.name) + ")", t;
  },
  flushSync: function (e, t) {
    return e(t);
  },
  unstable_batchedUpdates: function (e, t) {
    return e(t);
  },
  StrictMode: O,
  Suspense: He,
  SuspenseList: Fe,
  lazy: function (e) {
    var t, n, r;
    function o(o) {
      if (t || (t = e()).then(function (e) {
        n = e.default || e;
      }, function (e) {
        r = e;
      }), r) throw r;
      if (!n) throw t;
      return b(n, o);
    }
    return o.displayName = "Lazy", o.__f = true, o;
  },
  __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED: ut
};
function ft(e, t) {
  (null == t || t > e.length) && (t = e.length);
  for (var n = 0, r = Array(t); n < t; n++) r[n] = e[n];
  return r;
}
function pt(e, t, n, r, o, i, a) {
  try {
    var c = e[i](a),
      u = c.value;
  } catch (e) {
    return void n(e);
  }
  c.done ? t(u) : Promise.resolve(u).then(r, o);
}
function mt(e) {
  return function () {
    var t = this,
      n = arguments;
    return new Promise(function (r, o) {
      var i = e.apply(t, n);
      function a(e) {
        pt(i, r, o, a, c, "next", e);
      }
      function c(e) {
        pt(i, r, o, a, c, "throw", e);
      }
      a(void 0);
    });
  };
}
function vt(e, n, r) {
  return n = gt(n), function (e, n) {
    if (n && ("object" == t(n) || "function" == typeof n)) return n;
    if (void 0 !== n) throw new TypeError("Derived constructors may only return object or undefined");
    return function (e) {
      if (void 0 === e) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
      return e;
    }(e);
  }(e, St() ? Reflect.construct(n, r || [], gt(e).constructor) : n.apply(e, r));
}
function ht(e, t) {
  if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function");
}
function dt(e, t, n) {
  return Object.defineProperty(e, "prototype", {
    writable: false
  }), e;
}
function yt(e, n, r) {
  return (n = function (e) {
    var n = function (e) {
      if ("object" != t(e) || !e) return e;
      var n = e[Symbol.toPrimitive];
      if (void 0 !== n) {
        var r = n.call(e, "string");
        if ("object" != t(r)) return r;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" == t(n) ? n : n + "";
  }(n)) in e ? Object.defineProperty(e, n, {
    value: r,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[n] = r, e;
}
function _t() {
  return _t = Object.assign ? Object.assign.bind() : function (e) {
    for (var t = 1; t < arguments.length; t++) {
      var n = arguments[t];
      for (var r in n) ({}).hasOwnProperty.call(n, r) && (e[r] = n[r]);
    }
    return e;
  }, _t.apply(null, arguments);
}
function gt(e) {
  return gt = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (e) {
    return e.__proto__ || Object.getPrototypeOf(e);
  }, gt(e);
}
function bt(e, t) {
  if ("function" != typeof t && null !== t) throw new TypeError("Super expression must either be null or a function");
  e.prototype = Object.create(t && t.prototype, {
    constructor: {
      value: e,
      writable: true,
      configurable: true
    }
  }), Object.defineProperty(e, "prototype", {
    writable: false
  }), t && Pt(e, t);
}
function St() {
  try {
    var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));
  } catch (e) {}
  return (St = function () {
    return !!e;
  })();
}
function Ot(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function wt(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? Ot(Object(n), true).forEach(function (t) {
      yt(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Ot(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function Et(e, t) {
  if (null == e) return {};
  var n,
    r,
    o = function (e, t) {
      if (null == e) return {};
      var n = {};
      for (var r in e) if ({}.hasOwnProperty.call(e, r)) {
        if (t.includes(r)) continue;
        n[r] = e[r];
      }
      return n;
    }(e, t);
  if (Object.getOwnPropertySymbols) {
    var i = Object.getOwnPropertySymbols(e);
    for (r = 0; r < i.length; r++) n = i[r], t.includes(n) || {}.propertyIsEnumerable.call(e, n) && (o[n] = e[n]);
  }
  return o;
}
function jt() {
  jt = function () {
    return n;
  };
  var e,
    n = {},
    r = Object.prototype,
    o = r.hasOwnProperty,
    i = Object.defineProperty || function (e, t, n) {
      e[t] = n.value;
    },
    a = "function" == typeof Symbol ? Symbol : {},
    c = a.iterator || "@@iterator",
    u = a.asyncIterator || "@@asyncIterator",
    l = a.toStringTag || "@@toStringTag";
  function s(e, t, n) {
    return Object.defineProperty(e, t, {
      value: n,
      enumerable: true,
      configurable: true,
      writable: true
    }), e[t];
  }
  try {
    s({}, "");
  } catch (e) {
    s = function (e, t, n) {
      return e[t] = n;
    };
  }
  function f(e, t, n, r) {
    var o = t && t.prototype instanceof _ ? t : _,
      a = Object.create(o.prototype),
      c = new x(r || []);
    return i(a, "_invoke", {
      value: I(e, n, c)
    }), a;
  }
  function p(e, t, n) {
    try {
      return {
        type: "normal",
        arg: e.call(t, n)
      };
    } catch (e) {
      return {
        type: "throw",
        arg: e
      };
    }
  }
  n.wrap = f;
  var m = "suspendedStart",
    v = "suspendedYield",
    h = "executing",
    d = "completed",
    y = {};
  function _() {}
  function g() {}
  function b() {}
  var S = {};
  s(S, c, function () {
    return this;
  });
  var O = Object.getPrototypeOf,
    w = O && O(O(A([])));
  w && w !== r && o.call(w, c) && (S = w);
  var E = b.prototype = _.prototype = Object.create(S);
  function j(e) {
    ["next", "throw", "return"].forEach(function (t) {
      s(e, t, function (e) {
        return this._invoke(t, e);
      });
    });
  }
  function P(e, n) {
    function r(i, a, c, u) {
      var l = p(e[i], e, a);
      if ("throw" !== l.type) {
        var s = l.arg,
          f = s.value;
        return f && "object" == t(f) && o.call(f, "__await") ? n.resolve(f.__await).then(function (e) {
          r("next", e, c, u);
        }, function (e) {
          r("throw", e, c, u);
        }) : n.resolve(f).then(function (e) {
          s.value = e, c(s);
        }, function (e) {
          return r("throw", e, c, u);
        });
      }
      u(l.arg);
    }
    var a;
    i(this, "_invoke", {
      value: function (e, t) {
        function o() {
          return new n(function (n, o) {
            r(e, t, n, o);
          });
        }
        return a = a ? a.then(o, o) : o();
      }
    });
  }
  function I(t, n, r) {
    var o = m;
    return function (i, a) {
      if (o === h) throw Error("Generator is already running");
      if (o === d) {
        if ("throw" === i) throw a;
        return {
          value: e,
          done: true
        };
      }
      for (r.method = i, r.arg = a;;) {
        var c = r.delegate;
        if (c) {
          var u = k(c, r);
          if (u) {
            if (u === y) continue;
            return u;
          }
        }
        if ("next" === r.method) r.sent = r._sent = r.arg;else if ("throw" === r.method) {
          if (o === m) throw o = d, r.arg;
          r.dispatchException(r.arg);
        } else "return" === r.method && r.abrupt("return", r.arg);
        o = h;
        var l = p(t, n, r);
        if ("normal" === l.type) {
          if (o = r.done ? d : v, l.arg === y) continue;
          return {
            value: l.arg,
            done: r.done
          };
        }
        "throw" === l.type && (o = d, r.method = "throw", r.arg = l.arg);
      }
    };
  }
  function k(t, n) {
    var r = n.method,
      o = t.iterator[r];
    if (o === e) return n.delegate = null, "throw" === r && t.iterator.return && (n.method = "return", n.arg = e, k(t, n), "throw" === n.method) || "return" !== r && (n.method = "throw", n.arg = new TypeError("The iterator does not provide a '" + r + "' method")), y;
    var i = p(o, t.iterator, n.arg);
    if ("throw" === i.type) return n.method = "throw", n.arg = i.arg, n.delegate = null, y;
    var a = i.arg;
    return a ? a.done ? (n[t.resultName] = a.value, n.next = t.nextLoc, "return" !== n.method && (n.method = "next", n.arg = e), n.delegate = null, y) : a : (n.method = "throw", n.arg = new TypeError("iterator result is not an object"), n.delegate = null, y);
  }
  function D(e) {
    var t = {
      tryLoc: e[0]
    };
    1 in e && (t.catchLoc = e[1]), 2 in e && (t.finallyLoc = e[2], t.afterLoc = e[3]), this.tryEntries.push(t);
  }
  function C(e) {
    var t = e.completion || {};
    t.type = "normal", delete t.arg, e.completion = t;
  }
  function x(e) {
    this.tryEntries = [{
      tryLoc: "root"
    }], e.forEach(D, this), this.reset(true);
  }
  function A(n) {
    if (n || "" === n) {
      var r = n[c];
      if (r) return r.call(n);
      if ("function" == typeof n.next) return n;
      if (!isNaN(n.length)) {
        var i = -1,
          a = function t() {
            for (; ++i < n.length;) if (o.call(n, i)) return t.value = n[i], t.done = false, t;
            return t.value = e, t.done = true, t;
          };
        return a.next = a;
      }
    }
    throw new TypeError(t(n) + " is not iterable");
  }
  return g.prototype = b, i(E, "constructor", {
    value: b,
    configurable: true
  }), i(b, "constructor", {
    value: g,
    configurable: true
  }), g.displayName = s(b, l, "GeneratorFunction"), n.isGeneratorFunction = function (e) {
    var t = "function" == typeof e && e.constructor;
    return !!t && (t === g || "GeneratorFunction" === (t.displayName || t.name));
  }, n.mark = function (e) {
    return Object.setPrototypeOf ? Object.setPrototypeOf(e, b) : (e.__proto__ = b, s(e, l, "GeneratorFunction")), e.prototype = Object.create(E), e;
  }, n.awrap = function (e) {
    return {
      __await: e
    };
  }, j(P.prototype), s(P.prototype, u, function () {
    return this;
  }), n.AsyncIterator = P, n.async = function (e, t, r, o, i) {
    void 0 === i && (i = Promise);
    var a = new P(f(e, t, r, o), i);
    return n.isGeneratorFunction(t) ? a : a.next().then(function (e) {
      return e.done ? e.value : a.next();
    });
  }, j(E), s(E, l, "Generator"), s(E, c, function () {
    return this;
  }), s(E, "toString", function () {
    return "[object Generator]";
  }), n.keys = function (e) {
    var t = Object(e),
      n = [];
    for (var r in t) n.push(r);
    return n.reverse(), function e() {
      for (; n.length;) {
        var r = n.pop();
        if (r in t) return e.value = r, e.done = false, e;
      }
      return e.done = true, e;
    };
  }, n.values = A, x.prototype = {
    constructor: x,
    reset: function (t) {
      if (this.prev = 0, this.next = 0, this.sent = this._sent = e, this.done = false, this.delegate = null, this.method = "next", this.arg = e, this.tryEntries.forEach(C), !t) for (var n in this) "t" === n.charAt(0) && o.call(this, n) && !isNaN(+n.slice(1)) && (this[n] = e);
    },
    stop: function () {
      this.done = true;
      var e = this.tryEntries[0].completion;
      if ("throw" === e.type) throw e.arg;
      return this.rval;
    },
    dispatchException: function (t) {
      if (this.done) throw t;
      var n = this;
      function r(r, o) {
        return c.type = "throw", c.arg = t, n.next = r, o && (n.method = "next", n.arg = e), !!o;
      }
      for (var i = this.tryEntries.length - 1; i >= 0; --i) {
        var a = this.tryEntries[i],
          c = a.completion;
        if ("root" === a.tryLoc) return r("end");
        if (a.tryLoc <= this.prev) {
          var u = o.call(a, "catchLoc"),
            l = o.call(a, "finallyLoc");
          if (u && l) {
            if (this.prev < a.catchLoc) return r(a.catchLoc, true);
            if (this.prev < a.finallyLoc) return r(a.finallyLoc);
          } else if (u) {
            if (this.prev < a.catchLoc) return r(a.catchLoc, true);
          } else {
            if (!l) throw Error("try statement without catch or finally");
            if (this.prev < a.finallyLoc) return r(a.finallyLoc);
          }
        }
      }
    },
    abrupt: function (e, t) {
      for (var n = this.tryEntries.length - 1; n >= 0; --n) {
        var r = this.tryEntries[n];
        if (r.tryLoc <= this.prev && o.call(r, "finallyLoc") && this.prev < r.finallyLoc) {
          var i = r;
          break;
        }
      }
      i && ("break" === e || "continue" === e) && i.tryLoc <= t && t <= i.finallyLoc && (i = null);
      var a = i ? i.completion : {};
      return a.type = e, a.arg = t, i ? (this.method = "next", this.next = i.finallyLoc, y) : this.complete(a);
    },
    complete: function (e, t) {
      if ("throw" === e.type) throw e.arg;
      return "break" === e.type || "continue" === e.type ? this.next = e.arg : "return" === e.type ? (this.rval = this.arg = e.arg, this.method = "return", this.next = "end") : "normal" === e.type && t && (this.next = t), y;
    },
    finish: function (e) {
      for (var t = this.tryEntries.length - 1; t >= 0; --t) {
        var n = this.tryEntries[t];
        if (n.finallyLoc === e) return this.complete(n.completion, n.afterLoc), C(n), y;
      }
    },
    catch: function (e) {
      for (var t = this.tryEntries.length - 1; t >= 0; --t) {
        var n = this.tryEntries[t];
        if (n.tryLoc === e) {
          var r = n.completion;
          if ("throw" === r.type) {
            var o = r.arg;
            C(n);
          }
          return o;
        }
      }
      throw Error("illegal catch attempt");
    },
    delegateYield: function (t, n, r) {
      return this.delegate = {
        iterator: A(t),
        resultName: n,
        nextLoc: r
      }, "next" === this.method && (this.arg = e), y;
    }
  }, n;
}
function Pt(e, t) {
  return Pt = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (e, t) {
    return e.__proto__ = t, e;
  }, Pt(e, t);
}
function It(e, t) {
  return function (e) {
    if (Array.isArray(e)) return e;
  }(e) || function (e, t) {
    var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
    if (null != n) {
      var r,
        o,
        i,
        a,
        c = [],
        u = true,
        l = false;
      try {
        if (i = (n = n.call(e)).next, 0 === t) {
          if (Object(n) !== n) return;
          u = !1;
        } else for (; !(u = (r = i.call(n)).done) && (c.push(r.value), c.length !== t); u = !0);
      } catch (e) {
        l = true, o = e;
      } finally {
        try {
          if (!u && null != n.return && (a = n.return(), Object(a) !== a)) return;
        } finally {
          if (l) throw o;
        }
      }
      return c;
    }
  }(e, t) || Dt(e, t) || function () {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function kt(e) {
  return function (e) {
    if (Array.isArray(e)) return ft(e);
  }(e) || function (e) {
    if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e);
  }(e) || Dt(e) || function () {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function Dt(e, t) {
  if (e) {
    if ("string" == typeof e) return ft(e, t);
    var n = {}.toString.call(e).slice(8, -1);
    return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? ft(e, t) : void 0;
  }
}
function Ct(e) {
  var t = "function" == typeof Map ? new Map() : void 0;
  return Ct = function (e) {
    if (null === e || !function (e) {
      try {
        return -1 !== Function.toString.call(e).indexOf("[native code]");
      } catch (t) {
        return "function" == typeof e;
      }
    }(e)) return e;
    if ("function" != typeof e) throw new TypeError("Super expression must either be null or a function");
    if (void 0 !== t) {
      if (t.has(e)) return t.get(e);
      t.set(e, n);
    }
    function n() {
      return function (e, t, n) {
        if (St()) return Reflect.construct.apply(null, arguments);
        var r = [null];
        r.push.apply(r, t);
        var o = new (e.bind.apply(e, r))();
        return n && Pt(o, n.prototype), o;
      }(e, arguments, gt(this).constructor);
    }
    return n.prototype = Object.create(e.prototype, {
      constructor: {
        value: n,
        enumerable: false,
        writable: true,
        configurable: true
      }
    }), Pt(n, e);
  }, Ct(e);
}
function xt() {
  return st.createElement("svg", {
    width: "15",
    height: "15",
    className: "DocSearch-Control-Key-Icon"
  }, st.createElement("path", {
    d: "M4.505 4.496h2M5.505 5.496v5M8.216 4.496l.055 5.993M10 7.5c.333.333.5.667.5 1v2M12.326 4.5v5.996M8.384 4.496c1.674 0 2.116 0 2.116 1.5s-.442 1.5-2.116 1.5M3.205 9.303c-.09.448-.277 1.21-1.241 1.203C1 10.5.5 9.513.5 8V7c0-1.57.5-2.5 1.464-2.494.964.006 1.134.598 1.24 1.342M12.553 10.5h1.953",
    strokeWidth: "1.2",
    stroke: "currentColor",
    fill: "none",
    strokeLinecap: "square"
  }));
}
function At() {
  return st.createElement("svg", {
    width: "20",
    height: "20",
    className: "DocSearch-Search-Icon",
    viewBox: "0 0 20 20",
    "aria-hidden": "true"
  }, st.createElement("path", {
    d: "M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z",
    stroke: "currentColor",
    fill: "none",
    fillRule: "evenodd",
    strokeLinecap: "round",
    strokeLinejoin: "round"
  }));
}
var Nt = ["translations"],
  Tt = "Ctrl",
  Rt = st.forwardRef(function (e, t) {
    var n = e.translations,
      r = void 0 === n ? {} : n,
      o = Et(e, Nt),
      i = r.buttonText,
      a = void 0 === i ? "Search" : i,
      c = r.buttonAriaLabel,
      u = void 0 === c ? "Search" : c,
      l = It(oe(null), 2),
      s = l[0],
      f = l[1];
    ae(function () {
      "undefined" != typeof navigator && (/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform) ? f("⌘") : f(Tt));
    }, []);
    var p = It(s === Tt ? [Tt, "Ctrl", st.createElement(xt, null)] : ["Meta", "Command", s], 3),
      m = p[0],
      v = p[1],
      h = p[2];
    return st.createElement("button", _t({
      type: "button",
      className: "DocSearch DocSearch-Button",
      "aria-label": "".concat(u, " (").concat(v, "+K)")
    }, o, {
      ref: t
    }), st.createElement("span", {
      className: "DocSearch-Button-Container"
    }, st.createElement(At, null), st.createElement("span", {
      className: "DocSearch-Button-Placeholder"
    }, a)), st.createElement("span", {
      className: "DocSearch-Button-Keys"
    }, null !== s && st.createElement(st.Fragment, null, st.createElement(Lt, {
      reactsToKey: m
    }, h), st.createElement(Lt, {
      reactsToKey: "k"
    }, "K"))));
  });
function Lt(e) {
  var t = e.reactsToKey,
    n = e.children,
    r = It(oe(false), 2),
    o = r[0],
    i = r[1];
  return ae(function () {
    if (t) return window.addEventListener("keydown", e), window.addEventListener("keyup", n), function () {
      window.removeEventListener("keydown", e), window.removeEventListener("keyup", n);
    };
    function e(e) {
      e.key === t && i(true);
    }
    function n(e) {
      e.key !== t && "Meta" !== e.key || i(false);
    }
  }, [t]), st.createElement("kbd", {
    className: o ? "DocSearch-Button-Key DocSearch-Button-Key--pressed" : "DocSearch-Button-Key"
  }, n);
}
function qt(e, t) {
  var n = void 0;
  return function () {
    for (var r = arguments.length, o = new Array(r), i = 0; i < r; i++) o[i] = arguments[i];
    n && clearTimeout(n), n = setTimeout(function () {
      return e.apply(void 0, o);
    }, t);
  };
}
function Mt(e) {
  return e.reduce(function (e, t) {
    return e.concat(t);
  }, []);
}
var Ht = 0;
function Ut(e) {
  return 0 === e.collections.length ? 0 : e.collections.reduce(function (e, t) {
    return e + t.items.length;
  }, 0);
}
function Ft(e) {
  return e !== Object(e);
}
function Bt(e, t) {
  if (e === t) return true;
  if (Ft(e) || Ft(t) || "function" == typeof e || "function" == typeof t) return e === t;
  if (Object.keys(e).length !== Object.keys(t).length) return false;
  for (var n = 0, r = Object.keys(e); n < r.length; n++) {
    var o = r[n];
    if (!(o in t)) return false;
    if (!Bt(e[o], t[o])) return false;
  }
  return true;
}
var Vt = function () {},
  Kt = [{
    segment: "autocomplete-core",
    version: "1.17.9"
  }];
function Wt(e) {
  var t = e.item,
    n = e.items,
    r = void 0 === n ? [] : n;
  return {
    index: t.__autocomplete_indexName,
    items: [t],
    positions: [1 + r.findIndex(function (e) {
      return e.objectID === t.objectID;
    })],
    queryID: t.__autocomplete_queryID,
    algoliaSource: ["autocomplete"]
  };
}
function zt(e, t) {
  (null == t || t > e.length) && (t = e.length);
  for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
  return r;
}
var Jt = ["items"],
  Qt = ["items"];
function $t(e) {
  return $t = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, $t(e);
}
function Zt(e) {
  return function (e) {
    if (Array.isArray(e)) return Gt(e);
  }(e) || function (e) {
    if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e);
  }(e) || function (e, t) {
    if (e) {
      if ("string" == typeof e) return Gt(e, t);
      var n = Object.prototype.toString.call(e).slice(8, -1);
      return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? Gt(e, t) : void 0;
    }
  }(e) || function () {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function Gt(e, t) {
  (null == t || t > e.length) && (t = e.length);
  for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
  return r;
}
function Yt(e, t) {
  if (null == e) return {};
  var n,
    r,
    o = function (e, t) {
      if (null == e) return {};
      var n,
        r,
        o = {},
        i = Object.keys(e);
      for (r = 0; r < i.length; r++) n = i[r], t.indexOf(n) >= 0 || (o[n] = e[n]);
      return o;
    }(e, t);
  if (Object.getOwnPropertySymbols) {
    var i = Object.getOwnPropertySymbols(e);
    for (r = 0; r < i.length; r++) n = i[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (o[n] = e[n]);
  }
  return o;
}
function Xt(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function en(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? Xt(Object(n), true).forEach(function (t) {
      tn(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Xt(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function tn(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== $t(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== $t(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === $t(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
function nn(e) {
  return e.map(function (e) {
    var t = e.items,
      n = Yt(e, Jt);
    return en(en({}, n), {}, {
      objectIDs: (null == t ? void 0 : t.map(function (e) {
        return e.objectID;
      })) || n.objectIDs
    });
  });
}
function rn(e) {
  var t = e.items.reduce(function (e, t) {
    var n;
    return e[t.__autocomplete_indexName] = (null !== (n = e[t.__autocomplete_indexName]) && void 0 !== n ? n : []).concat(t), e;
  }, {});
  return Object.keys(t).map(function (e) {
    return {
      index: e,
      items: t[e],
      algoliaSource: ["autocomplete"]
    };
  });
}
function on(e) {
  return e.objectID && e.__autocomplete_indexName && e.__autocomplete_queryID;
}
function an(e) {
  return an = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, an(e);
}
function cn(e) {
  return function (e) {
    if (Array.isArray(e)) return un(e);
  }(e) || function (e) {
    if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e);
  }(e) || function (e, t) {
    if (e) {
      if ("string" == typeof e) return un(e, t);
      var n = Object.prototype.toString.call(e).slice(8, -1);
      return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? un(e, t) : void 0;
    }
  }(e) || function () {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function un(e, t) {
  (null == t || t > e.length) && (t = e.length);
  for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
  return r;
}
function ln(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function sn(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? ln(Object(n), true).forEach(function (t) {
      fn(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : ln(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function fn(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== an(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== an(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === an(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
var pn = "2.15.0",
  mn = "https://cdn.jsdelivr.net/npm/search-insights@".concat(pn, "/dist/search-insights.min.js"),
  vn = qt(function (e) {
    var t = e.onItemsChange,
      n = e.items,
      r = e.insights,
      o = e.state;
    t({
      insights: r,
      insightsEvents: rn({
        items: n
      }).map(function (e) {
        return sn({
          eventName: "Items Viewed"
        }, e);
      }),
      state: o
    });
  }, 400);
function hn(e) {
  var t = function (e) {
      return sn({
        onItemsChange: function (e) {
          var t = e.insights,
            n = e.insightsEvents,
            r = e.state;
          t.viewedObjectIDs.apply(t, cn(n.map(function (e) {
            return sn(sn({}, e), {}, {
              algoliaSource: dn(e.algoliaSource, r.context)
            });
          })));
        },
        onSelect: function (e) {
          var t = e.insights,
            n = e.insightsEvents,
            r = e.state;
          t.clickedObjectIDsAfterSearch.apply(t, cn(n.map(function (e) {
            return sn(sn({}, e), {}, {
              algoliaSource: dn(e.algoliaSource, r.context)
            });
          })));
        },
        onActive: Vt,
        __autocomplete_clickAnalytics: true
      }, e);
    }(e),
    n = t.insightsClient,
    r = t.insightsInitParams,
    o = t.onItemsChange,
    i = t.onSelect,
    a = t.onActive,
    c = t.__autocomplete_clickAnalytics,
    u = n;
  if (n || "undefined" != typeof window && function () {
    var e = {
        window: window
      }.window,
      t = e.AlgoliaAnalyticsObject || "aa";
    "string" == typeof t && (u = e[t]), u || (e.AlgoliaAnalyticsObject = t, e[t] || (e[t] = function () {
      e[t].queue || (e[t].queue = []);
      for (var n = arguments.length, r = new Array(n), o = 0; o < n; o++) r[o] = arguments[o];
      e[t].queue.push(r);
    }), e[t].version = pn, u = e[t], function (e) {
      var t = "[Autocomplete]: Could not load search-insights.js. Please load it manually following https://alg.li/insights-autocomplete";
      try {
        var n = e.document.createElement("script");
        n.async = !0, n.src = mn, n.onerror = function () {
          console.error(t);
        }, document.body.appendChild(n);
      } catch (e) {
        console.error(t);
      }
    }(e));
  }(), !u) return {};
  r && u("init", sn({
    partial: true
  }, r));
  var l = function (e) {
      var t,
        n,
        r,
        o = (t = function (e) {
          return function (e) {
            if (Array.isArray(e)) return e;
          }(e) || function (e) {
            var t = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
            if (null != t) {
              var n,
                r,
                o,
                i,
                a = [],
                c = true,
                u = false;
              try {
                for (o = (t = t.call(e)).next; !(c = (n = o.call(t)).done) && (a.push(n.value), 2 !== a.length); c = !0);
              } catch (e) {
                u = true, r = e;
              } finally {
                try {
                  if (!c && null != t.return && (i = t.return(), Object(i) !== i)) return;
                } finally {
                  if (u) throw r;
                }
              }
              return a;
            }
          }(e) || function (e) {
            if (e) {
              if ("string" == typeof e) return zt(e, 2);
              var t = Object.prototype.toString.call(e).slice(8, -1);
              return "Object" === t && e.constructor && (t = e.constructor.name), "Map" === t || "Set" === t ? Array.from(e) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? zt(e, 2) : void 0;
            }
          }(e) || function () {
            throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
          }();
        }((e.version || "").split(".").map(Number)), n = t[0], r = t[1], n >= 3 || 2 === n && r >= 4 || 1 === n && r >= 10);
      function i(t, n, r) {
        if (o && void 0 !== r) {
          var i = r[0].__autocomplete_algoliaCredentials,
            a = {
              "X-Algolia-Application-Id": i.appId,
              "X-Algolia-API-Key": i.apiKey
            };
          e.apply(void 0, [t].concat(Zt(n), [{
            headers: a
          }]));
        } else e.apply(void 0, [t].concat(Zt(n)));
      }
      return {
        init: function (t, n) {
          e("init", {
            appId: t,
            apiKey: n
          });
        },
        setAuthenticatedUserToken: function (t) {
          e("setAuthenticatedUserToken", t);
        },
        setUserToken: function (t) {
          e("setUserToken", t);
        },
        clickedObjectIDsAfterSearch: function () {
          for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
          t.length > 0 && i("clickedObjectIDsAfterSearch", nn(t), t[0].items);
        },
        clickedObjectIDs: function () {
          for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
          t.length > 0 && i("clickedObjectIDs", nn(t), t[0].items);
        },
        clickedFilters: function () {
          for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
          n.length > 0 && e.apply(void 0, ["clickedFilters"].concat(n));
        },
        convertedObjectIDsAfterSearch: function () {
          for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
          t.length > 0 && i("convertedObjectIDsAfterSearch", nn(t), t[0].items);
        },
        convertedObjectIDs: function () {
          for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
          t.length > 0 && i("convertedObjectIDs", nn(t), t[0].items);
        },
        convertedFilters: function () {
          for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
          n.length > 0 && e.apply(void 0, ["convertedFilters"].concat(n));
        },
        viewedObjectIDs: function () {
          for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++) t[n] = arguments[n];
          t.length > 0 && t.reduce(function (e, t) {
            var n = t.items,
              r = Yt(t, Qt);
            return [].concat(Zt(e), Zt(function (e) {
              for (var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 20, n = [], r = 0; r < e.objectIDs.length; r += t) n.push(en(en({}, e), {}, {
                objectIDs: e.objectIDs.slice(r, r + t)
              }));
              return n;
            }(en(en({}, r), {}, {
              objectIDs: (null == n ? void 0 : n.map(function (e) {
                return e.objectID;
              })) || r.objectIDs
            })).map(function (e) {
              return {
                items: n,
                payload: e
              };
            })));
          }, []).forEach(function (e) {
            var t = e.items;
            return i("viewedObjectIDs", [e.payload], t);
          });
        },
        viewedFilters: function () {
          for (var t = arguments.length, n = new Array(t), r = 0; r < t; r++) n[r] = arguments[r];
          n.length > 0 && e.apply(void 0, ["viewedFilters"].concat(n));
        }
      };
    }(u),
    s = {
      current: []
    },
    f = qt(function (e) {
      var t = e.state;
      if (t.isOpen) {
        var n = t.collections.reduce(function (e, t) {
          return [].concat(cn(e), cn(t.items));
        }, []).filter(on);
        Bt(s.current.map(function (e) {
          return e.objectID;
        }), n.map(function (e) {
          return e.objectID;
        })) || (s.current = n, n.length > 0 && vn({
          onItemsChange: o,
          items: n,
          insights: l,
          state: t
        }));
      }
    }, 0);
  return {
    name: "aa.algoliaInsightsPlugin",
    subscribe: function (e) {
      var t = e.setContext,
        n = e.onSelect,
        r = e.onActive;
      function o(e) {
        t({
          algoliaInsightsPlugin: {
            __algoliaSearchParameters: sn(sn({}, c ? {
              clickAnalytics: true
            } : {}), e ? {
              userToken: yn(e)
            } : {}),
            insights: l
          }
        });
      }
      u("addAlgoliaAgent", "insights-plugin"), o(), u("onUserTokenChange", function (e) {
        o(e);
      }), u("getUserToken", null, function (e, t) {
        o(t);
      }), n(function (e) {
        var t = e.item,
          n = e.state,
          r = e.event,
          o = e.source;
        on(t) && i({
          state: n,
          event: r,
          insights: l,
          item: t,
          insightsEvents: [sn({
            eventName: "Item Selected"
          }, Wt({
            item: t,
            items: o.getItems().filter(on)
          }))]
        });
      }), r(function (e) {
        var t = e.item,
          n = e.source,
          r = e.state,
          o = e.event;
        on(t) && a({
          state: r,
          event: o,
          insights: l,
          item: t,
          insightsEvents: [sn({
            eventName: "Item Active"
          }, Wt({
            item: t,
            items: n.getItems().filter(on)
          }))]
        });
      });
    },
    onStateChange: function (e) {
      var t = e.state;
      f({
        state: t
      });
    },
    __autocomplete_pluginOptions: e
  };
}
function dn() {
  var e,
    t = arguments.length > 1 ? arguments[1] : void 0;
  return [].concat(cn(arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []), ["autocomplete-internal"], cn(null !== (e = t.algoliaInsightsPlugin) && void 0 !== e && e.__automaticInsights ? ["autocomplete-automatic"] : []));
}
function yn(e) {
  return "number" == typeof e ? e.toString() : e;
}
function _n(e, t) {
  var n = t;
  return {
    then: function (t, r) {
      return _n(e.then(bn(t, n, e), bn(r, n, e)), n);
    },
    catch: function (t) {
      return _n(e.catch(bn(t, n, e)), n);
    },
    finally: function (t) {
      return t && n.onCancelList.push(t), _n(e.finally(bn(t && function () {
        return n.onCancelList = [], t();
      }, n, e)), n);
    },
    cancel: function () {
      n.isCanceled = true;
      var e = n.onCancelList;
      n.onCancelList = [], e.forEach(function (e) {
        e();
      });
    },
    isCanceled: function () {
      return true === n.isCanceled;
    }
  };
}
function gn(e) {
  return _n(e, {
    isCanceled: false,
    onCancelList: []
  });
}
function bn(e, t, n) {
  return e ? function (n) {
    return t.isCanceled ? n : e(n);
  } : n;
}
function Sn(e, t, n, r) {
  if (!n) return null;
  if (e < 0 && (null === t || null !== r && 0 === t)) return n + e;
  var o = (null === t ? -1 : t) + e;
  return o <= -1 || o >= n ? null === r ? null : 0 : o;
}
function On(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function wn(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? On(Object(n), true).forEach(function (t) {
      En(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : On(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function En(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== jn(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== jn(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === jn(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
function jn(e) {
  return jn = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, jn(e);
}
function Pn(e) {
  var t = function (e) {
    var t = e.collections.map(function (e) {
      return e.items.length;
    }).reduce(function (e, t, n) {
      var r = (e[n - 1] || 0) + t;
      return e.push(r), e;
    }, []).reduce(function (t, n) {
      return n <= e.activeItemId ? t + 1 : t;
    }, 0);
    return e.collections[t];
  }(e);
  if (!t) return null;
  var n = t.items[function (e) {
      for (var t = e.state, n = e.collection, r = false, o = 0, i = 0; false === r;) {
        var a = t.collections[o];
        if (a === n) {
          r = true;
          break;
        }
        i += a.items.length, o++;
      }
      return t.activeItemId - i;
    }({
      state: e,
      collection: t
    })],
    r = t.source;
  return {
    item: n,
    itemInputValue: r.getItemInputValue({
      item: n,
      state: e
    }),
    itemUrl: r.getItemUrl({
      item: n,
      state: e
    }),
    source: r
  };
}
function In(e, t, n) {
  return [e, null == n ? void 0 : n.sourceId, t].filter(Boolean).join("-").replace(/\s/g, "");
}
var kn = /((gt|sm)-|galaxy nexus)|samsung[- ]|samsungbrowser/i;
function Dn(e) {
  return e.nativeEvent || e;
}
function Cn(e) {
  return Cn = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, Cn(e);
}
function xn(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function An(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== Cn(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== Cn(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === Cn(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
function Nn(e) {
  return Nn = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, Nn(e);
}
function Tn(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function Rn(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? Tn(Object(n), true).forEach(function (t) {
      Ln(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Tn(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function Ln(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== Nn(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== Nn(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === Nn(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
function qn(e) {
  return qn = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, qn(e);
}
function Mn(e, t) {
  (null == t || t > e.length) && (t = e.length);
  for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
  return r;
}
function Hn(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function Un(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? Hn(Object(n), true).forEach(function (t) {
      Fn(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Hn(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function Fn(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== qn(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== qn(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === qn(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
function Bn(e) {
  return Bn = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, Bn(e);
}
function Vn(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function Kn(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? Vn(Object(n), true).forEach(function (t) {
      Wn(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Vn(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function Wn(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== Bn(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== Bn(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === Bn(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
function zn(e) {
  return zn = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, zn(e);
}
function Jn(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function Qn(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? Jn(Object(n), true).forEach(function (t) {
      $n(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Jn(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function $n(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== zn(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== zn(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === zn(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
function Zn(e) {
  return function (e) {
    if (Array.isArray(e)) return Gn(e);
  }(e) || function (e) {
    if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e);
  }(e) || function (e, t) {
    if (e) {
      if ("string" == typeof e) return Gn(e, t);
      var n = Object.prototype.toString.call(e).slice(8, -1);
      return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? Gn(e, t) : void 0;
    }
  }(e) || function () {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function Gn(e, t) {
  (null == t || t > e.length) && (t = e.length);
  for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
  return r;
}
function Yn(e) {
  return Boolean(e.execute);
}
function Xn(e) {
  var t = e.reduce(function (e, t) {
    if (!Yn(t)) return e.push(t), e;
    var n = t.searchClient,
      r = t.execute,
      o = t.requesterId,
      i = t.requests,
      a = e.find(function (e) {
        return Yn(t) && Yn(e) && e.searchClient === n && Boolean(o) && e.requesterId === o;
      });
    if (a) {
      var c;
      (c = a.items).push.apply(c, Zn(i));
    } else {
      var u = {
        execute: r,
        requesterId: o,
        items: i,
        searchClient: n
      };
      e.push(u);
    }
    return e;
  }, []).map(function (e) {
    if (!Yn(e)) return Promise.resolve(e);
    var t = e,
      n = t.execute,
      r = t.items;
    return n({
      searchClient: t.searchClient,
      requests: r
    });
  });
  return Promise.all(t).then(function (e) {
    return Mt(e);
  });
}
function er(e) {
  return er = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, er(e);
}
var tr = ["event", "nextState", "props", "query", "refresh", "store"];
function nr(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function rr(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? nr(Object(n), true).forEach(function (t) {
      or(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : nr(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function or(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== er(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== er(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === er(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
var ir,
  ar,
  cr,
  ur = null,
  lr = (ir = -1, ar = -1, cr = void 0, function (e) {
    var t = ++ir;
    return Promise.resolve(e).then(function (e) {
      return cr && t < ar ? cr : (ar = t, cr = e, e);
    });
  });
function sr(e) {
  var t = e.event,
    n = e.nextState,
    r = void 0 === n ? {} : n,
    o = e.props,
    i = e.query,
    a = e.refresh,
    c = e.store,
    u = function (e, t) {
      if (null == e) return {};
      var n,
        r,
        o = function (e, t) {
          if (null == e) return {};
          var n,
            r,
            o = {},
            i = Object.keys(e);
          for (r = 0; r < i.length; r++) n = i[r], t.indexOf(n) >= 0 || (o[n] = e[n]);
          return o;
        }(e, t);
      if (Object.getOwnPropertySymbols) {
        var i = Object.getOwnPropertySymbols(e);
        for (r = 0; r < i.length; r++) n = i[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (o[n] = e[n]);
      }
      return o;
    }(e, tr);
  ur && o.environment.clearTimeout(ur);
  var l = u.setCollections,
    s = u.setIsOpen,
    f = u.setQuery,
    p = u.setActiveItemId,
    m = u.setStatus,
    v = u.setContext;
  if (f(i), p(o.defaultActiveItemId), !i && false === o.openOnFocus) {
    var h,
      d = c.getState().collections.map(function (e) {
        return rr(rr({}, e), {}, {
          items: []
        });
      });
    m("idle"), l(d), s(null !== (h = r.isOpen) && void 0 !== h ? h : o.shouldPanelOpen({
      state: c.getState()
    }));
    var y = gn(lr(d).then(function () {
      return Promise.resolve();
    }));
    return c.pendingRequests.add(y);
  }
  m("loading"), ur = o.environment.setTimeout(function () {
    m("stalled");
  }, o.stallThreshold);
  var _ = gn(lr(o.getSources(rr({
    query: i,
    refresh: a,
    state: c.getState()
  }, u)).then(function (e) {
    return Promise.all(e.map(function (e) {
      return Promise.resolve(e.getItems(rr({
        query: i,
        refresh: a,
        state: c.getState()
      }, u))).then(function (t) {
        return function (e, t, n) {
          if (o = e, Boolean(null == o ? void 0 : o.execute)) {
            var r = "algolia" === e.requesterId ? Object.assign.apply(Object, [{}].concat(Zn(Object.keys(n.context).map(function (e) {
              var t;
              return null === (t = n.context[e]) || void 0 === t ? void 0 : t.__algoliaSearchParameters;
            })))) : {};
            return Qn(Qn({}, e), {}, {
              requests: e.queries.map(function (n) {
                return {
                  query: "algolia" === e.requesterId ? Qn(Qn({}, n), {}, {
                    params: Qn(Qn({}, r), n.params)
                  }) : n,
                  sourceId: t,
                  transformResponse: e.transformResponse
                };
              })
            });
          }
          var o;
          return {
            items: e,
            sourceId: t
          };
        }(t, e.sourceId, c.getState());
      });
    })).then(Xn).then(function (t) {
      var n,
        r = t.some(function (e) {
          return function (e) {
            return !Array.isArray(e) && Boolean(null == e ? void 0 : e._automaticInsights);
          }(e.items);
        });
      return r && v({
        algoliaInsightsPlugin: rr(rr({}, (null === (n = c.getState().context) || void 0 === n ? void 0 : n.algoliaInsightsPlugin) || {}), {}, {
          __automaticInsights: r
        })
      }), function (e, t, n) {
        return t.map(function (t) {
          var r,
            o = e.filter(function (e) {
              return e.sourceId === t.sourceId;
            }),
            i = o.map(function (e) {
              return e.items;
            }),
            a = o[0].transformResponse,
            c = a ? a({
              results: r = i,
              hits: r.map(function (e) {
                return e.hits;
              }).filter(Boolean),
              facetHits: r.map(function (e) {
                var t;
                return null === (t = e.facetHits) || void 0 === t ? void 0 : t.map(function (e) {
                  return {
                    label: e.value,
                    count: e.count,
                    _highlightResult: {
                      label: {
                        value: e.highlighted
                      }
                    }
                  };
                });
              }).filter(Boolean)
            }) : i;
          return t.onResolve({
            source: t,
            results: i,
            items: c,
            state: n.getState()
          }), c.every(Boolean), 'The `getItems` function from source "'.concat(t.sourceId, '" must return an array of items but returned ').concat(JSON.stringify(void 0), ".\n\nDid you forget to return items?\n\nSee: https://www.algolia.com/doc/ui-libraries/autocomplete/core-concepts/sources/#param-getitems"), {
            source: t,
            items: c
          };
        });
      }(t, e, c);
    }).then(function (e) {
      return function (e) {
        var t = e.props,
          n = e.state,
          r = e.collections.reduce(function (e, t) {
            return Kn(Kn({}, e), {}, Wn({}, t.source.sourceId, Kn(Kn({}, t.source), {}, {
              getItems: function () {
                return Mt(t.items);
              }
            })));
          }, {}),
          o = t.plugins.reduce(function (e, t) {
            return t.reshape ? t.reshape(e) : e;
          }, {
            sourcesBySourceId: r,
            state: n
          }).sourcesBySourceId;
        return Mt(t.reshape({
          sourcesBySourceId: o,
          sources: Object.values(o),
          state: n
        })).filter(Boolean).map(function (e) {
          return {
            source: e,
            items: e.getItems()
          };
        });
      }({
        collections: e,
        props: o,
        state: c.getState()
      });
    });
  }))).then(function (e) {
    var n;
    m("idle"), l(e);
    var f = o.shouldPanelOpen({
      state: c.getState()
    });
    s(null !== (n = r.isOpen) && void 0 !== n ? n : o.openOnFocus && !i && f || f);
    var p = Pn(c.getState());
    if (null !== c.getState().activeItemId && p) {
      var v = p.item,
        h = p.itemInputValue,
        d = p.itemUrl,
        y = p.source;
      y.onActive(rr({
        event: t,
        item: v,
        itemInputValue: h,
        itemUrl: d,
        refresh: a,
        source: y,
        state: c.getState()
      }, u));
    }
  }).finally(function () {
    m("idle"), ur && o.environment.clearTimeout(ur);
  });
  return c.pendingRequests.add(_);
}
function fr(e) {
  return fr = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, fr(e);
}
var pr = ["event", "props", "refresh", "store"];
function mr(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function vr(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? mr(Object(n), true).forEach(function (t) {
      hr(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : mr(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function hr(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== fr(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== fr(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === fr(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
function dr(e) {
  return dr = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, dr(e);
}
var yr = ["props", "refresh", "store"],
  _r = ["inputElement", "formElement", "panelElement"],
  gr = ["inputElement"],
  br = ["inputElement", "maxLength"],
  Sr = ["source"],
  Or = ["item", "source"];
function wr(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function Er(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? wr(Object(n), true).forEach(function (t) {
      jr(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : wr(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function jr(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== dr(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== dr(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === dr(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
function Pr(e, t) {
  if (null == e) return {};
  var n,
    r,
    o = function (e, t) {
      if (null == e) return {};
      var n,
        r,
        o = {},
        i = Object.keys(e);
      for (r = 0; r < i.length; r++) n = i[r], t.indexOf(n) >= 0 || (o[n] = e[n]);
      return o;
    }(e, t);
  if (Object.getOwnPropertySymbols) {
    var i = Object.getOwnPropertySymbols(e);
    for (r = 0; r < i.length; r++) n = i[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (o[n] = e[n]);
  }
  return o;
}
function Ir(e) {
  return Ir = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, Ir(e);
}
function kr(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function Dr(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? kr(Object(n), true).forEach(function (t) {
      Cr(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : kr(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function Cr(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== Ir(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== Ir(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === Ir(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
function xr(e) {
  var t,
    n,
    r,
    o,
    i = e.plugins,
    a = e.options,
    c = null === (t = ((null === (n = a.__autocomplete_metadata) || void 0 === n ? void 0 : n.userAgents) || [])[0]) || void 0 === t ? void 0 : t.segment,
    u = c ? Cr({}, c, Object.keys((null === (r = a.__autocomplete_metadata) || void 0 === r ? void 0 : r.options) || {})) : {};
  return {
    plugins: i.map(function (e) {
      return {
        name: e.name,
        options: Object.keys(e.__autocomplete_pluginOptions || [])
      };
    }),
    options: Dr({
      "autocomplete-core": Object.keys(a)
    }, u),
    ua: Kt.concat((null === (o = a.__autocomplete_metadata) || void 0 === o ? void 0 : o.userAgents) || [])
  };
}
function Ar(e) {
  var t,
    n = e.state;
  return false === n.isOpen || null === n.activeItemId ? null : (null === (t = Pn(n)) || void 0 === t ? void 0 : t.itemInputValue) || null;
}
function Nr(e) {
  return Nr = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, Nr(e);
}
function Tr(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function Rr(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? Tr(Object(n), true).forEach(function (t) {
      Lr(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Tr(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function Lr(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== Nr(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== Nr(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === Nr(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
var qr = function (e, t) {
  switch (t.type) {
    case "setActiveItemId":
    case "mousemove":
      return Rr(Rr({}, e), {}, {
        activeItemId: t.payload
      });
    case "setQuery":
      return Rr(Rr({}, e), {}, {
        query: t.payload,
        completion: null
      });
    case "setCollections":
      return Rr(Rr({}, e), {}, {
        collections: t.payload
      });
    case "setIsOpen":
      return Rr(Rr({}, e), {}, {
        isOpen: t.payload
      });
    case "setStatus":
      return Rr(Rr({}, e), {}, {
        status: t.payload
      });
    case "setContext":
      return Rr(Rr({}, e), {}, {
        context: Rr(Rr({}, e.context), t.payload)
      });
    case "ArrowDown":
      var n = Rr(Rr({}, e), {}, {
        activeItemId: t.payload.hasOwnProperty("nextActiveItemId") ? t.payload.nextActiveItemId : Sn(1, e.activeItemId, Ut(e), t.props.defaultActiveItemId)
      });
      return Rr(Rr({}, n), {}, {
        completion: Ar({
          state: n
        })
      });
    case "ArrowUp":
      var r = Rr(Rr({}, e), {}, {
        activeItemId: Sn(-1, e.activeItemId, Ut(e), t.props.defaultActiveItemId)
      });
      return Rr(Rr({}, r), {}, {
        completion: Ar({
          state: r
        })
      });
    case "Escape":
      return e.isOpen ? Rr(Rr({}, e), {}, {
        activeItemId: null,
        isOpen: false,
        completion: null
      }) : Rr(Rr({}, e), {}, {
        activeItemId: null,
        query: "",
        status: "idle",
        collections: []
      });
    case "submit":
      return Rr(Rr({}, e), {}, {
        activeItemId: null,
        isOpen: false,
        status: "idle"
      });
    case "reset":
      return Rr(Rr({}, e), {}, {
        activeItemId: true === t.props.openOnFocus ? t.props.defaultActiveItemId : null,
        status: "idle",
        completion: null,
        query: ""
      });
    case "focus":
      return Rr(Rr({}, e), {}, {
        activeItemId: t.props.defaultActiveItemId,
        isOpen: (t.props.openOnFocus || Boolean(e.query)) && t.props.shouldPanelOpen({
          state: e
        })
      });
    case "blur":
      return t.props.debug ? e : Rr(Rr({}, e), {}, {
        isOpen: false,
        activeItemId: null
      });
    case "mouseleave":
      return Rr(Rr({}, e), {}, {
        activeItemId: t.props.defaultActiveItemId
      });
    default:
      return "The reducer action ".concat(JSON.stringify(t.type), " is not supported."), e;
  }
};
function Mr(e) {
  return Mr = "function" == typeof Symbol && "symbol" == t(Symbol.iterator) ? function (e) {
    return t(e);
  } : function (e) {
    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : t(e);
  }, Mr(e);
}
function Hr(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function (t) {
      return Object.getOwnPropertyDescriptor(e, t).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function Ur(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = null != arguments[t] ? arguments[t] : {};
    t % 2 ? Hr(Object(n), true).forEach(function (t) {
      Fr(e, t, n[t]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Hr(Object(n)).forEach(function (t) {
      Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
    });
  }
  return e;
}
function Fr(e, t, n) {
  return (t = function (e) {
    var t = function (e) {
      if ("object" !== Mr(e) || null === e) return e;
      var t = e[Symbol.toPrimitive];
      if (void 0 !== t) {
        var n = t.call(e, "string");
        if ("object" !== Mr(n)) return n;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return String(e);
    }(e);
    return "symbol" === Mr(t) ? t : String(t);
  }(t)) in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
function Br(e) {
  var t = [],
    n = function (e, t) {
      var n,
        r = "undefined" != typeof window ? window : {},
        o = e.plugins || [];
      return Un(Un({
        debug: false,
        openOnFocus: false,
        enterKeyHint: void 0,
        ignoreCompositionEvents: false,
        placeholder: "",
        autoFocus: false,
        defaultActiveItemId: null,
        stallThreshold: 300,
        insights: void 0,
        environment: r,
        shouldPanelOpen: function (e) {
          return Ut(e.state) > 0;
        },
        reshape: function (e) {
          return e.sources;
        }
      }, e), {}, {
        id: null !== (n = e.id) && void 0 !== n ? n : "autocomplete-".concat(Ht++),
        plugins: o,
        initialState: Un({
          activeItemId: null,
          query: "",
          completion: null,
          collections: [],
          isOpen: false,
          status: "idle",
          context: {}
        }, e.initialState),
        onStateChange: function (t) {
          var n;
          null === (n = e.onStateChange) || void 0 === n || n.call(e, t), o.forEach(function (e) {
            var n;
            return null === (n = e.onStateChange) || void 0 === n ? void 0 : n.call(e, t);
          });
        },
        onSubmit: function (t) {
          var n;
          null === (n = e.onSubmit) || void 0 === n || n.call(e, t), o.forEach(function (e) {
            var n;
            return null === (n = e.onSubmit) || void 0 === n ? void 0 : n.call(e, t);
          });
        },
        onReset: function (t) {
          var n;
          null === (n = e.onReset) || void 0 === n || n.call(e, t), o.forEach(function (e) {
            var n;
            return null === (n = e.onReset) || void 0 === n ? void 0 : n.call(e, t);
          });
        },
        getSources: function (n) {
          return Promise.all([].concat(function (e) {
            return function (e) {
              if (Array.isArray(e)) return Mn(e);
            }(e) || function (e) {
              if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e);
            }(e) || function (e, t) {
              if (e) {
                if ("string" == typeof e) return Mn(e, t);
                var n = Object.prototype.toString.call(e).slice(8, -1);
                return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? Mn(e, t) : void 0;
              }
            }(e) || function () {
              throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
            }();
          }(o.map(function (e) {
            return e.getSources;
          })), [e.getSources]).filter(Boolean).map(function (e) {
            return function (e, t) {
              var n = [];
              return Promise.resolve(e(t)).then(function (e) {
                return Promise.all(e.filter(function (e) {
                  return Boolean(e);
                }).map(function (e) {
                  if (e.sourceId, n.includes(e.sourceId)) throw new Error("[Autocomplete] The `sourceId` ".concat(JSON.stringify(e.sourceId), " is not unique."));
                  n.push(e.sourceId);
                  var t = {
                    getItemInputValue: function (e) {
                      return e.state.query;
                    },
                    getItemUrl: function () {},
                    onSelect: function (e) {
                      (0, e.setIsOpen)(false);
                    },
                    onActive: Vt,
                    onResolve: Vt
                  };
                  Object.keys(t).forEach(function (e) {
                    t[e].__default = true;
                  });
                  var r = wn(wn({}, t), e);
                  return Promise.resolve(r);
                }));
              });
            }(e, n);
          })).then(function (e) {
            return Mt(e);
          }).then(function (e) {
            return e.map(function (e) {
              return Un(Un({}, e), {}, {
                onSelect: function (n) {
                  e.onSelect(n), t.forEach(function (e) {
                    var t;
                    return null === (t = e.onSelect) || void 0 === t ? void 0 : t.call(e, n);
                  });
                },
                onActive: function (n) {
                  e.onActive(n), t.forEach(function (e) {
                    var t;
                    return null === (t = e.onActive) || void 0 === t ? void 0 : t.call(e, n);
                  });
                },
                onResolve: function (n) {
                  e.onResolve(n), t.forEach(function (e) {
                    var t;
                    return null === (t = e.onResolve) || void 0 === t ? void 0 : t.call(e, n);
                  });
                }
              });
            });
          });
        },
        navigator: Un({
          navigate: function (e) {
            var t = e.itemUrl;
            r.location.assign(t);
          },
          navigateNewTab: function (e) {
            var t = e.itemUrl,
              n = r.open(t, "_blank", "noopener");
            null == n || n.focus();
          },
          navigateNewWindow: function (e) {
            var t = e.itemUrl;
            r.open(t, "_blank", "noopener");
          }
        }, e.navigator)
      });
    }(e, t),
    r = function (e, t, n) {
      var r,
        o = t.initialState;
      return {
        getState: function () {
          return o;
        },
        dispatch: function (r, i) {
          var a = function (e) {
            for (var t = 1; t < arguments.length; t++) {
              var n = null != arguments[t] ? arguments[t] : {};
              t % 2 ? xn(Object(n), true).forEach(function (t) {
                An(e, t, n[t]);
              }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : xn(Object(n)).forEach(function (t) {
                Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
              });
            }
            return e;
          }({}, o);
          o = e(o, {
            type: r,
            props: t,
            payload: i
          }), n({
            state: o,
            prevState: a
          });
        },
        pendingRequests: (r = [], {
          add: function (e) {
            return r.push(e), e.finally(function () {
              r = r.filter(function (t) {
                return t !== e;
              });
            });
          },
          cancelAll: function () {
            r.forEach(function (e) {
              return e.cancel();
            });
          },
          isEmpty: function () {
            return 0 === r.length;
          }
        })
      };
    }(qr, n, function (e) {
      var t,
        r,
        i = e.prevState,
        l = e.state;
      if (n.onStateChange(Ur({
        prevState: i,
        state: l,
        refresh: a,
        navigator: n.navigator
      }, o)), !u() && null !== (t = l.context) && void 0 !== t && null !== (r = t.algoliaInsightsPlugin) && void 0 !== r && r.__automaticInsights && false !== n.insights) {
        var s = hn({
          __autocomplete_clickAnalytics: false
        });
        n.plugins.push(s), c([s]);
      }
    }),
    o = function (e) {
      var t = e.store;
      return {
        setActiveItemId: function (e) {
          t.dispatch("setActiveItemId", e);
        },
        setQuery: function (e) {
          t.dispatch("setQuery", e);
        },
        setCollections: function (e) {
          var n = 0,
            r = e.map(function (e) {
              return Rn(Rn({}, e), {}, {
                items: Mt(e.items).map(function (e) {
                  return Rn(Rn({}, e), {}, {
                    __autocomplete_id: n++
                  });
                })
              });
            });
          t.dispatch("setCollections", r);
        },
        setIsOpen: function (e) {
          t.dispatch("setIsOpen", e);
        },
        setStatus: function (e) {
          t.dispatch("setStatus", e);
        },
        setContext: function (e) {
          t.dispatch("setContext", e);
        }
      };
    }({
      store: r
    }),
    i = function (e) {
      var t = e.props,
        n = e.refresh,
        r = e.store,
        o = Pr(e, yr);
      return {
        getEnvironmentProps: function (e) {
          var n = e.inputElement,
            o = e.formElement,
            i = e.panelElement;
          function a(e) {
            !r.getState().isOpen && r.pendingRequests.isEmpty() || e.target === n || false === [o, i].some(function (t) {
              return (n = t) === (r = e.target) || n.contains(r);
              var n, r;
            }) && (r.dispatch("blur", null), t.debug || r.pendingRequests.cancelAll());
          }
          return Er({
            onTouchStart: a,
            onMouseDown: a,
            onTouchMove: function (e) {
              false !== r.getState().isOpen && n === t.environment.document.activeElement && e.target !== n && n.blur();
            }
          }, Pr(e, _r));
        },
        getRootProps: function (e) {
          return Er({
            role: "combobox",
            "aria-expanded": r.getState().isOpen,
            "aria-haspopup": "listbox",
            "aria-controls": r.getState().isOpen ? r.getState().collections.map(function (e) {
              var n = e.source;
              return In(t.id, "list", n);
            }).join(" ") : void 0,
            "aria-labelledby": In(t.id, "label")
          }, e);
        },
        getFormProps: function (e) {
          return e.inputElement, Er({
            action: "",
            noValidate: true,
            role: "search",
            onSubmit: function (i) {
              var a;
              i.preventDefault(), t.onSubmit(Er({
                event: i,
                refresh: n,
                state: r.getState()
              }, o)), r.dispatch("submit", null), null === (a = e.inputElement) || void 0 === a || a.blur();
            },
            onReset: function (i) {
              var a;
              i.preventDefault(), t.onReset(Er({
                event: i,
                refresh: n,
                state: r.getState()
              }, o)), r.dispatch("reset", null), null === (a = e.inputElement) || void 0 === a || a.focus();
            }
          }, Pr(e, gr));
        },
        getLabelProps: function (e) {
          return Er({
            htmlFor: In(t.id, "input"),
            id: In(t.id, "label")
          }, e);
        },
        getInputProps: function (e) {
          var i;
          function a(e) {
            (t.openOnFocus || Boolean(r.getState().query)) && sr(Er({
              event: e,
              props: t,
              query: r.getState().completion || r.getState().query,
              refresh: n,
              store: r
            }, o)), r.dispatch("focus", null);
          }
          var c = e || {};
          c.inputElement;
          var u = c.maxLength,
            l = void 0 === u ? 512 : u,
            s = Pr(c, br),
            f = Pn(r.getState()),
            p = function (e) {
              return Boolean(e && e.match(kn));
            }((null === (i = t.environment.navigator) || void 0 === i ? void 0 : i.userAgent) || ""),
            m = t.enterKeyHint || (null != f && f.itemUrl && !p ? "go" : "search");
          return Er({
            "aria-autocomplete": "both",
            "aria-activedescendant": r.getState().isOpen && null !== r.getState().activeItemId ? In(t.id, "item-".concat(r.getState().activeItemId), null == f ? void 0 : f.source) : void 0,
            "aria-controls": r.getState().isOpen ? r.getState().collections.map(function (e) {
              var n = e.source;
              return In(t.id, "list", n);
            }).join(" ") : void 0,
            "aria-labelledby": In(t.id, "label"),
            value: r.getState().completion || r.getState().query,
            id: In(t.id, "input"),
            autoComplete: "off",
            autoCorrect: "off",
            autoCapitalize: "off",
            enterKeyHint: m,
            spellCheck: "false",
            autoFocus: t.autoFocus,
            placeholder: t.placeholder,
            maxLength: l,
            type: "search",
            onChange: function (e) {
              var i = e.currentTarget.value;
              t.ignoreCompositionEvents && Dn(e).isComposing ? o.setQuery(i) : sr(Er({
                event: e,
                props: t,
                query: i.slice(0, l),
                refresh: n,
                store: r
              }, o));
            },
            onCompositionEnd: function (e) {
              sr(Er({
                event: e,
                props: t,
                query: e.currentTarget.value.slice(0, l),
                refresh: n,
                store: r
              }, o));
            },
            onKeyDown: function (e) {
              Dn(e).isComposing || function (e) {
                var t = e.event,
                  n = e.props,
                  r = e.refresh,
                  o = e.store,
                  i = function (e, t) {
                    if (null == e) return {};
                    var n,
                      r,
                      o = function (e, t) {
                        if (null == e) return {};
                        var n,
                          r,
                          o = {},
                          i = Object.keys(e);
                        for (r = 0; r < i.length; r++) n = i[r], t.indexOf(n) >= 0 || (o[n] = e[n]);
                        return o;
                      }(e, t);
                    if (Object.getOwnPropertySymbols) {
                      var i = Object.getOwnPropertySymbols(e);
                      for (r = 0; r < i.length; r++) n = i[r], t.indexOf(n) >= 0 || Object.prototype.propertyIsEnumerable.call(e, n) && (o[n] = e[n]);
                    }
                    return o;
                  }(e, pr);
                if ("ArrowUp" === t.key || "ArrowDown" === t.key) {
                  var a = function () {
                      var e = Pn(o.getState()),
                        t = n.environment.document.getElementById(In(n.id, "item-".concat(o.getState().activeItemId), null == e ? void 0 : e.source));
                      t && (t.scrollIntoViewIfNeeded ? t.scrollIntoViewIfNeeded(false) : t.scrollIntoView(false));
                    },
                    c = function () {
                      var e = Pn(o.getState());
                      if (null !== o.getState().activeItemId && e) {
                        var n = e.item,
                          a = e.itemInputValue,
                          c = e.itemUrl,
                          u = e.source;
                        u.onActive(vr({
                          event: t,
                          item: n,
                          itemInputValue: a,
                          itemUrl: c,
                          refresh: r,
                          source: u,
                          state: o.getState()
                        }, i));
                      }
                    };
                  t.preventDefault(), false === o.getState().isOpen && (n.openOnFocus || Boolean(o.getState().query)) ? sr(vr({
                    event: t,
                    props: n,
                    query: o.getState().query,
                    refresh: r,
                    store: o
                  }, i)).then(function () {
                    o.dispatch(t.key, {
                      nextActiveItemId: n.defaultActiveItemId
                    }), c(), setTimeout(a, 0);
                  }) : (o.dispatch(t.key, {}), c(), a());
                } else if ("Escape" === t.key) t.preventDefault(), o.dispatch(t.key, null), o.pendingRequests.cancelAll();else if ("Tab" === t.key) o.dispatch("blur", null), o.pendingRequests.cancelAll();else if ("Enter" === t.key) {
                  if (null === o.getState().activeItemId || o.getState().collections.every(function (e) {
                    return 0 === e.items.length;
                  })) return void (n.debug || o.pendingRequests.cancelAll());
                  t.preventDefault();
                  var u = Pn(o.getState()),
                    l = u.item,
                    s = u.itemInputValue,
                    f = u.itemUrl,
                    p = u.source;
                  if (t.metaKey || t.ctrlKey) void 0 !== f && (p.onSelect(vr({
                    event: t,
                    item: l,
                    itemInputValue: s,
                    itemUrl: f,
                    refresh: r,
                    source: p,
                    state: o.getState()
                  }, i)), n.navigator.navigateNewTab({
                    itemUrl: f,
                    item: l,
                    state: o.getState()
                  }));else if (t.shiftKey) void 0 !== f && (p.onSelect(vr({
                    event: t,
                    item: l,
                    itemInputValue: s,
                    itemUrl: f,
                    refresh: r,
                    source: p,
                    state: o.getState()
                  }, i)), n.navigator.navigateNewWindow({
                    itemUrl: f,
                    item: l,
                    state: o.getState()
                  }));else if (t.altKey) ;else {
                    if (void 0 !== f) return p.onSelect(vr({
                      event: t,
                      item: l,
                      itemInputValue: s,
                      itemUrl: f,
                      refresh: r,
                      source: p,
                      state: o.getState()
                    }, i)), void n.navigator.navigate({
                      itemUrl: f,
                      item: l,
                      state: o.getState()
                    });
                    sr(vr({
                      event: t,
                      nextState: {
                        isOpen: false
                      },
                      props: n,
                      query: s,
                      refresh: r,
                      store: o
                    }, i)).then(function () {
                      p.onSelect(vr({
                        event: t,
                        item: l,
                        itemInputValue: s,
                        itemUrl: f,
                        refresh: r,
                        source: p,
                        state: o.getState()
                      }, i));
                    });
                  }
                }
              }(Er({
                event: e,
                props: t,
                refresh: n,
                store: r
              }, o));
            },
            onFocus: a,
            onBlur: Vt,
            onClick: function (n) {
              e.inputElement !== t.environment.document.activeElement || r.getState().isOpen || a(n);
            }
          }, s);
        },
        getPanelProps: function (e) {
          return Er({
            onMouseDown: function (e) {
              e.preventDefault();
            },
            onMouseLeave: function () {
              r.dispatch("mouseleave", null);
            }
          }, e);
        },
        getListProps: function (e) {
          var n = e || {},
            r = n.source,
            o = Pr(n, Sr);
          return Er({
            role: "listbox",
            "aria-labelledby": In(t.id, "label"),
            id: In(t.id, "list", r)
          }, o);
        },
        getItemProps: function (e) {
          var i = e.item,
            a = e.source,
            c = Pr(e, Or);
          return Er({
            id: In(t.id, "item-".concat(i.__autocomplete_id), a),
            role: "option",
            "aria-selected": r.getState().activeItemId === i.__autocomplete_id,
            onMouseMove: function (e) {
              if (i.__autocomplete_id !== r.getState().activeItemId) {
                r.dispatch("mousemove", i.__autocomplete_id);
                var t = Pn(r.getState());
                if (null !== r.getState().activeItemId && t) {
                  var a = t.item,
                    c = t.itemInputValue,
                    u = t.itemUrl,
                    l = t.source;
                  l.onActive(Er({
                    event: e,
                    item: a,
                    itemInputValue: c,
                    itemUrl: u,
                    refresh: n,
                    source: l,
                    state: r.getState()
                  }, o));
                }
              }
            },
            onMouseDown: function (e) {
              e.preventDefault();
            },
            onClick: function (e) {
              var c = a.getItemInputValue({
                  item: i,
                  state: r.getState()
                }),
                u = a.getItemUrl({
                  item: i,
                  state: r.getState()
                });
              (u ? Promise.resolve() : sr(Er({
                event: e,
                nextState: {
                  isOpen: false
                },
                props: t,
                query: c,
                refresh: n,
                store: r
              }, o))).then(function () {
                a.onSelect(Er({
                  event: e,
                  item: i,
                  itemInputValue: c,
                  itemUrl: u,
                  refresh: n,
                  source: a,
                  state: r.getState()
                }, o));
              });
            }
          }, c);
        }
      };
    }(Ur({
      props: n,
      refresh: a,
      store: r,
      navigator: n.navigator
    }, o));
  function a() {
    return sr(Ur({
      event: new Event("input"),
      nextState: {
        isOpen: r.getState().isOpen
      },
      props: n,
      navigator: n.navigator,
      query: r.getState().query,
      refresh: a,
      store: r
    }, o));
  }
  function c(e) {
    e.forEach(function (e) {
      var r;
      return null === (r = e.subscribe) || void 0 === r ? void 0 : r.call(e, Ur(Ur({}, o), {}, {
        navigator: n.navigator,
        refresh: a,
        onSelect: function (e) {
          t.push({
            onSelect: e
          });
        },
        onActive: function (e) {
          t.push({
            onActive: e
          });
        },
        onResolve: function (e) {
          t.push({
            onResolve: e
          });
        }
      }));
    });
  }
  function u() {
    return n.plugins.some(function (e) {
      return "aa.algoliaInsightsPlugin" === e.name;
    });
  }
  if (n.insights && !u()) {
    var l = "boolean" == typeof n.insights ? {} : n.insights;
    n.plugins.push(hn(l));
  }
  return c(n.plugins), function (e) {
    var t,
      n,
      r = e.metadata,
      o = e.environment;
    if (null === (t = o.navigator) || void 0 === t || null === (n = t.userAgent) || void 0 === n ? void 0 : n.includes("Algolia Crawler")) {
      var i = o.document.createElement("meta"),
        a = o.document.querySelector("head");
      i.name = "algolia:metadata", setTimeout(function () {
        i.content = JSON.stringify(r), a.appendChild(i);
      }, 0);
    }
  }({
    metadata: xr({
      plugins: n.plugins,
      options: e
    }),
    environment: n.environment
  }), Ur(Ur({
    refresh: a,
    navigator: n.navigator
  }, i), o);
}
function Vr(e) {
  var t = e.translations,
    n = (void 0 === t ? {} : t).searchByText,
    r = void 0 === n ? "Search by" : n;
  return st.createElement("a", {
    href: "https://www.algolia.com/ref/docsearch/?utm_source=".concat(window.location.hostname, "&utm_medium=referral&utm_content=powered_by&utm_campaign=docsearch"),
    target: "_blank",
    rel: "noopener noreferrer"
  }, st.createElement("span", {
    className: "DocSearch-Label"
  }, r), st.createElement("svg", {
    width: "77",
    height: "19",
    "aria-label": "Algolia",
    role: "img",
    id: "Layer_1",
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 2196.2 500"
  }, st.createElement("defs", null, st.createElement("style", null, ".cls-1,.cls-2{fill:#003dff;}.cls-2{fill-rule:evenodd;}")), st.createElement("path", {
    className: "cls-2",
    d: "M1070.38,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z"
  }), st.createElement("rect", {
    className: "cls-1",
    x: "1845.88",
    y: "104.73",
    width: "62.58",
    height: "277.9",
    rx: "5.9",
    ry: "5.9"
  }), st.createElement("path", {
    className: "cls-2",
    d: "M1851.78,71.38h50.77c3.26,0,5.9-2.64,5.9-5.9V5.9c0-3.62-3.24-6.39-6.82-5.83l-50.77,7.95c-2.87,.45-4.99,2.92-4.99,5.83v51.62c0,3.26,2.64,5.9,5.9,5.9Z"
  }), st.createElement("path", {
    className: "cls-2",
    d: "M1764.03,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z"
  }), st.createElement("path", {
    className: "cls-2",
    d: "M1631.95,142.72c-11.14-12.25-24.83-21.65-40.78-28.31-15.92-6.53-33.26-9.85-52.07-9.85-18.78,0-36.15,3.17-51.92,9.85-15.59,6.66-29.29,16.05-40.76,28.31-11.47,12.23-20.38,26.87-26.76,44.03-6.38,17.17-9.24,37.37-9.24,58.36,0,20.99,3.19,36.87,9.55,54.21,6.38,17.32,15.14,32.11,26.45,44.36,11.29,12.23,24.83,21.62,40.6,28.46,15.77,6.83,40.12,10.33,52.4,10.48,12.25,0,36.78-3.82,52.7-10.48,15.92-6.68,29.46-16.23,40.78-28.46,11.29-12.25,20.05-27.04,26.25-44.36,6.22-17.34,9.24-33.22,9.24-54.21,0-20.99-3.34-41.19-10.03-58.36-6.38-17.17-15.14-31.8-26.43-44.03Zm-44.43,163.75c-11.47,15.75-27.56,23.7-48.09,23.7-20.55,0-36.63-7.8-48.1-23.7-11.47-15.75-17.21-34.01-17.21-61.2,0-26.89,5.59-49.14,17.06-64.87,11.45-15.75,27.54-23.52,48.07-23.52,20.55,0,36.63,7.78,48.09,23.52,11.47,15.57,17.36,37.98,17.36,64.87,0,27.19-5.72,45.3-17.19,61.2Z"
  }), st.createElement("path", {
    className: "cls-2",
    d: "M894.42,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z"
  }), st.createElement("path", {
    className: "cls-2",
    d: "M2133.97,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z"
  }), st.createElement("path", {
    className: "cls-2",
    d: "M1314.05,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-11.79,18.34-19.6,39.64-22.11,62.59-.58,5.3-.88,10.68-.88,16.14s.31,11.15,.93,16.59c4.28,38.09,23.14,71.61,50.66,94.52,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47h0c17.99,0,34.61-5.93,48.16-15.97,16.29-11.58,28.88-28.54,34.48-47.75v50.26h-.11v11.08c0,21.84-5.71,38.27-17.34,49.36-11.61,11.08-31.04,16.63-58.25,16.63-11.12,0-28.79-.59-46.6-2.41-2.83-.29-5.46,1.5-6.27,4.22l-12.78,43.11c-1.02,3.46,1.27,7.02,4.83,7.53,21.52,3.08,42.52,4.68,54.65,4.68,48.91,0,85.16-10.75,108.89-32.21,21.48-19.41,33.15-48.89,35.2-88.52V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,64.1s.65,139.13,0,143.36c-12.08,9.77-27.11,13.59-43.49,14.7-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-1.32,0-2.63-.03-3.94-.1-40.41-2.11-74.52-37.26-74.52-79.38,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33Z"
  }), st.createElement("path", {
    className: "cls-1",
    d: "M249.83,0C113.3,0,2,110.09,.03,246.16c-2,138.19,110.12,252.7,248.33,253.5,42.68,.25,83.79-10.19,120.3-30.03,3.56-1.93,4.11-6.83,1.08-9.51l-23.38-20.72c-4.75-4.21-11.51-5.4-17.36-2.92-25.48,10.84-53.17,16.38-81.71,16.03-111.68-1.37-201.91-94.29-200.13-205.96,1.76-110.26,92-199.41,202.67-199.41h202.69V407.41l-115-102.18c-3.72-3.31-9.42-2.66-12.42,1.31-18.46,24.44-48.53,39.64-81.93,37.34-46.33-3.2-83.87-40.5-87.34-86.81-4.15-55.24,39.63-101.52,94-101.52,49.18,0,89.68,37.85,93.91,85.95,.38,4.28,2.31,8.27,5.52,11.12l29.95,26.55c3.4,3.01,8.79,1.17,9.63-3.3,2.16-11.55,2.92-23.58,2.07-35.92-4.82-70.34-61.8-126.93-132.17-131.26-80.68-4.97-148.13,58.14-150.27,137.25-2.09,77.1,61.08,143.56,138.19,145.26,32.19,.71,62.03-9.41,86.14-26.95l150.26,133.2c6.44,5.71,16.61,1.14,16.61-7.47V9.48C499.66,4.25,495.42,0,490.18,0H249.83Z"
  })));
}
function Kr(e) {
  return st.createElement("svg", {
    width: "15",
    height: "15",
    "aria-label": e.ariaLabel,
    role: "img"
  }, st.createElement("g", {
    fill: "none",
    stroke: "currentColor",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: "1.2"
  }, e.children));
}
function Wr(e) {
  var t = e.translations,
    n = void 0 === t ? {} : t,
    r = n.selectText,
    o = void 0 === r ? "to select" : r,
    i = n.selectKeyAriaLabel,
    a = void 0 === i ? "Enter key" : i,
    c = n.navigateText,
    u = void 0 === c ? "to navigate" : c,
    l = n.navigateUpKeyAriaLabel,
    s = void 0 === l ? "Arrow up" : l,
    f = n.navigateDownKeyAriaLabel,
    p = void 0 === f ? "Arrow down" : f,
    m = n.closeText,
    v = void 0 === m ? "to close" : m,
    h = n.closeKeyAriaLabel,
    d = void 0 === h ? "Escape key" : h,
    y = n.searchByText,
    _ = void 0 === y ? "Search by" : y;
  return st.createElement(st.Fragment, null, st.createElement("div", {
    className: "DocSearch-Logo"
  }, st.createElement(Vr, {
    translations: {
      searchByText: _
    }
  })), st.createElement("ul", {
    className: "DocSearch-Commands"
  }, st.createElement("li", null, st.createElement("kbd", {
    className: "DocSearch-Commands-Key"
  }, st.createElement(Kr, {
    ariaLabel: a
  }, st.createElement("path", {
    d: "M12 3.53088v3c0 1-1 2-2 2H4M7 11.53088l-3-3 3-3"
  }))), st.createElement("span", {
    className: "DocSearch-Label"
  }, o)), st.createElement("li", null, st.createElement("kbd", {
    className: "DocSearch-Commands-Key"
  }, st.createElement(Kr, {
    ariaLabel: p
  }, st.createElement("path", {
    d: "M7.5 3.5v8M10.5 8.5l-3 3-3-3"
  }))), st.createElement("kbd", {
    className: "DocSearch-Commands-Key"
  }, st.createElement(Kr, {
    ariaLabel: s
  }, st.createElement("path", {
    d: "M7.5 11.5v-8M10.5 6.5l-3-3-3 3"
  }))), st.createElement("span", {
    className: "DocSearch-Label"
  }, u)), st.createElement("li", null, st.createElement("kbd", {
    className: "DocSearch-Commands-Key"
  }, st.createElement(Kr, {
    ariaLabel: d
  }, st.createElement("path", {
    d: "M13.6167 8.936c-.1065.3583-.6883.962-1.4875.962-.7993 0-1.653-.9165-1.653-2.1258v-.5678c0-1.2548.7896-2.1016 1.653-2.1016.8634 0 1.3601.4778 1.4875 1.0724M9 6c-.1352-.4735-.7506-.9219-1.46-.8972-.7092.0246-1.344.57-1.344 1.2166s.4198.8812 1.3445.9805C8.465 7.3992 8.968 7.9337 9 8.5c.032.5663-.454 1.398-1.4595 1.398C6.6593 9.898 6 9 5.963 8.4851m-1.4748.5368c-.2635.5941-.8099.876-1.5443.876s-1.7073-.6248-1.7073-2.204v-.4603c0-1.0416.721-2.131 1.7073-2.131.9864 0 1.6425 1.031 1.5443 2.2492h-2.956"
  }))), st.createElement("span", {
    className: "DocSearch-Label"
  }, v))));
}
function zr(e) {
  var t = e.hit,
    n = e.children;
  return st.createElement("a", {
    href: t.url
  }, n);
}
function Jr() {
  return st.createElement("svg", {
    viewBox: "0 0 38 38",
    stroke: "currentColor",
    strokeOpacity: ".5"
  }, st.createElement("g", {
    fill: "none",
    fillRule: "evenodd"
  }, st.createElement("g", {
    transform: "translate(1 1)",
    strokeWidth: "2"
  }, st.createElement("circle", {
    strokeOpacity: ".3",
    cx: "18",
    cy: "18",
    r: "18"
  }), st.createElement("path", {
    d: "M36 18c0-9.94-8.06-18-18-18"
  }, st.createElement("animateTransform", {
    attributeName: "transform",
    type: "rotate",
    from: "0 18 18",
    to: "360 18 18",
    dur: "1s",
    repeatCount: "indefinite"
  })))));
}
function Qr() {
  return st.createElement("svg", {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20"
  }, st.createElement("g", {
    stroke: "currentColor",
    fill: "none",
    fillRule: "evenodd",
    strokeLinecap: "round",
    strokeLinejoin: "round"
  }, st.createElement("path", {
    d: "M3.18 6.6a8.23 8.23 0 1112.93 9.94h0a8.23 8.23 0 01-11.63 0"
  }), st.createElement("path", {
    d: "M6.44 7.25H2.55V3.36M10.45 6v5.6M10.45 11.6L13 13"
  })));
}
function $r() {
  return st.createElement("svg", {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20"
  }, st.createElement("path", {
    d: "M10 10l5.09-5.09L10 10l5.09 5.09L10 10zm0 0L4.91 4.91 10 10l-5.09 5.09L10 10z",
    stroke: "currentColor",
    fill: "none",
    fillRule: "evenodd",
    strokeLinecap: "round",
    strokeLinejoin: "round"
  }));
}
function Zr() {
  return st.createElement("svg", {
    className: "DocSearch-Hit-Select-Icon",
    width: "20",
    height: "20",
    viewBox: "0 0 20 20"
  }, st.createElement("g", {
    stroke: "currentColor",
    fill: "none",
    fillRule: "evenodd",
    strokeLinecap: "round",
    strokeLinejoin: "round"
  }, st.createElement("path", {
    d: "M18 3v4c0 2-2 4-4 4H2"
  }), st.createElement("path", {
    d: "M8 17l-6-6 6-6"
  })));
}
var Gr = function () {
  return st.createElement("svg", {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20"
  }, st.createElement("path", {
    d: "M17 6v12c0 .52-.2 1-1 1H4c-.7 0-1-.33-1-1V2c0-.55.42-1 1-1h8l5 5zM14 8h-3.13c-.51 0-.87-.34-.87-.87V4",
    stroke: "currentColor",
    fill: "none",
    fillRule: "evenodd",
    strokeLinejoin: "round"
  }));
};
function Yr(e) {
  switch (e.type) {
    case "lvl1":
      return st.createElement(Gr, null);
    case "content":
      return st.createElement(eo, null);
    default:
      return st.createElement(Xr, null);
  }
}
function Xr() {
  return st.createElement("svg", {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20"
  }, st.createElement("path", {
    d: "M13 13h4-4V8H7v5h6v4-4H7V8H3h4V3v5h6V3v5h4-4v5zm-6 0v4-4H3h4z",
    stroke: "currentColor",
    fill: "none",
    fillRule: "evenodd",
    strokeLinecap: "round",
    strokeLinejoin: "round"
  }));
}
function eo() {
  return st.createElement("svg", {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20"
  }, st.createElement("path", {
    d: "M17 5H3h14zm0 5H3h14zm0 5H3h14z",
    stroke: "currentColor",
    fill: "none",
    fillRule: "evenodd",
    strokeLinejoin: "round"
  }));
}
function to() {
  return st.createElement("svg", {
    width: "20",
    height: "20",
    viewBox: "0 0 20 20"
  }, st.createElement("path", {
    d: "M10 14.2L5 17l1-5.6-4-4 5.5-.7 2.5-5 2.5 5 5.6.8-4 4 .9 5.5z",
    stroke: "currentColor",
    fill: "none",
    fillRule: "evenodd",
    strokeLinejoin: "round"
  }));
}
function no() {
  return st.createElement("svg", {
    width: "40",
    height: "40",
    viewBox: "0 0 20 20",
    fill: "none",
    fillRule: "evenodd",
    stroke: "currentColor",
    strokeLinecap: "round",
    strokeLinejoin: "round"
  }, st.createElement("path", {
    d: "M19 4.8a16 16 0 00-2-1.2m-3.3-1.2A16 16 0 001.1 4.7M16.7 8a12 12 0 00-2.8-1.4M10 6a12 12 0 00-6.7 2M12.3 14.7a4 4 0 00-4.5 0M14.5 11.4A8 8 0 0010 10M3 16L18 2M10 18h0"
  }));
}
function ro() {
  return st.createElement("svg", {
    width: "40",
    height: "40",
    viewBox: "0 0 20 20",
    fill: "none",
    fillRule: "evenodd",
    stroke: "currentColor",
    strokeLinecap: "round",
    strokeLinejoin: "round"
  }, st.createElement("path", {
    d: "M15.5 4.8c2 3 1.7 7-1 9.7h0l4.3 4.3-4.3-4.3a7.8 7.8 0 01-9.8 1m-2.2-2.2A7.8 7.8 0 0113.2 2.4M2 18L18 2"
  }));
}
function oo(e) {
  var t = e.translations,
    n = void 0 === t ? {} : t,
    r = n.titleText,
    o = void 0 === r ? "Unable to fetch results" : r,
    i = n.helpText,
    a = void 0 === i ? "You might want to check your network connection." : i;
  return st.createElement("div", {
    className: "DocSearch-ErrorScreen"
  }, st.createElement("div", {
    className: "DocSearch-Screen-Icon"
  }, st.createElement(no, null)), st.createElement("p", {
    className: "DocSearch-Title"
  }, o), st.createElement("p", {
    className: "DocSearch-Help"
  }, a));
}
var io = ["translations"];
function ao(e) {
  var t = e.translations,
    n = void 0 === t ? {} : t,
    r = Et(e, io),
    o = n.noResultsText,
    i = void 0 === o ? "No results for" : o,
    a = n.suggestedQueryText,
    c = void 0 === a ? "Try searching for" : a,
    u = n.reportMissingResultsText,
    l = void 0 === u ? "Believe this query should return results?" : u,
    s = n.reportMissingResultsLinkText,
    f = void 0 === s ? "Let us know." : s,
    p = r.state.context.searchSuggestions;
  return st.createElement("div", {
    className: "DocSearch-NoResults"
  }, st.createElement("div", {
    className: "DocSearch-Screen-Icon"
  }, st.createElement(ro, null)), st.createElement("p", {
    className: "DocSearch-Title"
  }, i, ' "', st.createElement("strong", null, r.state.query), '"'), p && p.length > 0 && st.createElement("div", {
    className: "DocSearch-NoResults-Prefill-List"
  }, st.createElement("p", {
    className: "DocSearch-Help"
  }, c, ":"), st.createElement("ul", null, p.slice(0, 3).reduce(function (e, t) {
    return [].concat(kt(e), [st.createElement("li", {
      key: t
    }, st.createElement("button", {
      className: "DocSearch-Prefill",
      key: t,
      type: "button",
      onClick: function () {
        r.setQuery(t.toLowerCase() + " "), r.refresh(), r.inputRef.current.focus();
      }
    }, t))]);
  }, []))), r.getMissingResultsUrl && st.createElement("p", {
    className: "DocSearch-Help"
  }, "".concat(l, " "), st.createElement("a", {
    href: r.getMissingResultsUrl({
      query: r.state.query
    }),
    target: "_blank",
    rel: "noopener noreferrer"
  }, f)));
}
var co = ["hit", "attribute", "tagName"];
function uo(e, t) {
  return t.split(".").reduce(function (e, t) {
    return null != e && e[t] ? e[t] : null;
  }, e);
}
function lo(e) {
  var t = e.hit,
    n = e.attribute,
    r = e.tagName;
  return b(void 0 === r ? "span" : r, wt(wt({}, Et(e, co)), {}, {
    dangerouslySetInnerHTML: {
      __html: uo(t, "_snippetResult.".concat(n, ".value")) || uo(t, n)
    }
  }));
}
function so(e) {
  return e.collection && 0 !== e.collection.items.length ? st.createElement("section", {
    className: "DocSearch-Hits"
  }, st.createElement("div", {
    className: "DocSearch-Hit-source"
  }, e.title), st.createElement("ul", e.getListProps(), e.collection.items.map(function (t, n) {
    return st.createElement(fo, _t({
      key: [e.title, t.objectID].join(":"),
      item: t,
      index: n
    }, e));
  }))) : null;
}
function fo(e) {
  var t = e.item,
    n = e.index,
    r = e.renderIcon,
    o = e.renderAction,
    i = e.getItemProps,
    a = e.onItemClick,
    c = e.collection,
    u = e.hitComponent,
    l = It(st.useState(false), 2),
    s = l[0],
    f = l[1],
    p = It(st.useState(false), 2),
    m = p[0],
    v = p[1],
    h = st.useRef(null),
    d = u;
  return st.createElement("li", _t({
    className: ["DocSearch-Hit", t.__docsearch_parent && "DocSearch-Hit--Child", s && "DocSearch-Hit--deleting", m && "DocSearch-Hit--favoriting"].filter(Boolean).join(" "),
    onTransitionEnd: function () {
      h.current && h.current();
    }
  }, i({
    item: t,
    source: c.source,
    onClick: function (e) {
      a(t, e);
    }
  })), st.createElement(d, {
    hit: t
  }, st.createElement("div", {
    className: "DocSearch-Hit-Container"
  }, r({
    item: t,
    index: n
  }), t.hierarchy[t.type] && "lvl1" === t.type && st.createElement("div", {
    className: "DocSearch-Hit-content-wrapper"
  }, st.createElement(lo, {
    className: "DocSearch-Hit-title",
    hit: t,
    attribute: "hierarchy.lvl1"
  }), t.content && st.createElement(lo, {
    className: "DocSearch-Hit-path",
    hit: t,
    attribute: "content"
  })), t.hierarchy[t.type] && ("lvl2" === t.type || "lvl3" === t.type || "lvl4" === t.type || "lvl5" === t.type || "lvl6" === t.type) && st.createElement("div", {
    className: "DocSearch-Hit-content-wrapper"
  }, st.createElement(lo, {
    className: "DocSearch-Hit-title",
    hit: t,
    attribute: "hierarchy.".concat(t.type)
  }), st.createElement(lo, {
    className: "DocSearch-Hit-path",
    hit: t,
    attribute: "hierarchy.lvl1"
  })), "content" === t.type && st.createElement("div", {
    className: "DocSearch-Hit-content-wrapper"
  }, st.createElement(lo, {
    className: "DocSearch-Hit-title",
    hit: t,
    attribute: "content"
  }), st.createElement(lo, {
    className: "DocSearch-Hit-path",
    hit: t,
    attribute: "hierarchy.lvl1"
  })), o({
    item: t,
    runDeleteTransition: function (e) {
      f(true), h.current = e;
    },
    runFavoriteTransition: function (e) {
      v(true), h.current = e;
    }
  }))));
}
function po(e, t, n) {
  return e.reduce(function (e, r) {
    var o = t(r);
    return e.hasOwnProperty(o) || (e[o] = []), e[o].length < (n || 5) && e[o].push(r), e;
  }, {});
}
function mo(e) {
  return e;
}
function vo(e) {
  return 1 === e.button || e.altKey || e.ctrlKey || e.metaKey || e.shiftKey;
}
function ho() {}
var yo = /(<mark>|<\/mark>)/g,
  _o = RegExp(yo.source);
function go(e) {
  var t,
    n,
    r = e;
  if (!r.__docsearch_parent && !e._highlightResult) return e.hierarchy.lvl0;
  var o = r.__docsearch_parent ? null === (t = r.__docsearch_parent) || void 0 === t || null === (t = t._highlightResult) || void 0 === t || null === (t = t.hierarchy) || void 0 === t ? void 0 : t.lvl0 : null === (n = e._highlightResult) || void 0 === n || null === (n = n.hierarchy) || void 0 === n ? void 0 : n.lvl0;
  return o ? o.value && _o.test(o.value) ? o.value.replace(yo, "") : o.value : e.hierarchy.lvl0;
}
function bo(e) {
  return st.createElement("div", {
    className: "DocSearch-Dropdown-Container"
  }, e.state.collections.map(function (t) {
    if (0 === t.items.length) return null;
    var n = go(t.items[0]);
    return st.createElement(so, _t({}, e, {
      key: t.source.sourceId,
      title: n,
      collection: t,
      renderIcon: function (e) {
        var n,
          r = e.item,
          o = e.index;
        return st.createElement(st.Fragment, null, r.__docsearch_parent && st.createElement("svg", {
          className: "DocSearch-Hit-Tree",
          viewBox: "0 0 24 54"
        }, st.createElement("g", {
          stroke: "currentColor",
          fill: "none",
          fillRule: "evenodd",
          strokeLinecap: "round",
          strokeLinejoin: "round"
        }, r.__docsearch_parent !== (null === (n = t.items[o + 1]) || void 0 === n ? void 0 : n.__docsearch_parent) ? st.createElement("path", {
          d: "M8 6v21M20 27H8.3"
        }) : st.createElement("path", {
          d: "M8 6v42M20 27H8.3"
        }))), st.createElement("div", {
          className: "DocSearch-Hit-icon"
        }, st.createElement(Yr, {
          type: r.type
        })));
      },
      renderAction: function () {
        return st.createElement("div", {
          className: "DocSearch-Hit-action"
        }, st.createElement(Zr, null));
      }
    }));
  }), e.resultsFooterComponent && st.createElement("section", {
    className: "DocSearch-HitsFooter"
  }, st.createElement(e.resultsFooterComponent, {
    state: e.state
  })));
}
var So = ["translations"];
function Oo(e) {
  var t = e.translations,
    n = void 0 === t ? {} : t,
    r = Et(e, So),
    o = n.recentSearchesTitle,
    i = void 0 === o ? "Recent" : o,
    a = n.noRecentSearchesText,
    c = void 0 === a ? "No recent searches" : a,
    u = n.saveRecentSearchButtonTitle,
    l = void 0 === u ? "Save this search" : u,
    s = n.removeRecentSearchButtonTitle,
    f = void 0 === s ? "Remove this search from history" : s,
    p = n.favoriteSearchesTitle,
    m = void 0 === p ? "Favorite" : p,
    v = n.removeFavoriteSearchButtonTitle,
    h = void 0 === v ? "Remove this search from favorites" : v;
  return "idle" === r.state.status && false === r.hasCollections ? r.disableUserPersonalization ? null : st.createElement("div", {
    className: "DocSearch-StartScreen"
  }, st.createElement("p", {
    className: "DocSearch-Help"
  }, c)) : false === r.hasCollections ? null : st.createElement("div", {
    className: "DocSearch-Dropdown-Container"
  }, st.createElement(so, _t({}, r, {
    title: i,
    collection: r.state.collections[0],
    renderIcon: function () {
      return st.createElement("div", {
        className: "DocSearch-Hit-icon"
      }, st.createElement(Qr, null));
    },
    renderAction: function (e) {
      var t = e.item,
        n = e.runFavoriteTransition,
        o = e.runDeleteTransition;
      return st.createElement(st.Fragment, null, st.createElement("div", {
        className: "DocSearch-Hit-action"
      }, st.createElement("button", {
        className: "DocSearch-Hit-action-button",
        title: l,
        type: "submit",
        onClick: function (e) {
          e.preventDefault(), e.stopPropagation(), n(function () {
            r.favoriteSearches.add(t), r.recentSearches.remove(t), r.refresh();
          });
        }
      }, st.createElement(to, null))), st.createElement("div", {
        className: "DocSearch-Hit-action"
      }, st.createElement("button", {
        className: "DocSearch-Hit-action-button",
        title: f,
        type: "submit",
        onClick: function (e) {
          e.preventDefault(), e.stopPropagation(), o(function () {
            r.recentSearches.remove(t), r.refresh();
          });
        }
      }, st.createElement($r, null))));
    }
  })), st.createElement(so, _t({}, r, {
    title: m,
    collection: r.state.collections[1],
    renderIcon: function () {
      return st.createElement("div", {
        className: "DocSearch-Hit-icon"
      }, st.createElement(to, null));
    },
    renderAction: function (e) {
      var t = e.item,
        n = e.runDeleteTransition;
      return st.createElement("div", {
        className: "DocSearch-Hit-action"
      }, st.createElement("button", {
        className: "DocSearch-Hit-action-button",
        title: h,
        type: "submit",
        onClick: function (e) {
          e.preventDefault(), e.stopPropagation(), n(function () {
            r.favoriteSearches.remove(t), r.refresh();
          });
        }
      }, st.createElement($r, null)));
    }
  })));
}
var wo = ["translations"],
  Eo = st.memo(function (e) {
    var t = e.translations,
      n = void 0 === t ? {} : t,
      r = Et(e, wo);
    if ("error" === r.state.status) return st.createElement(oo, {
      translations: null == n ? void 0 : n.errorScreen
    });
    var o = r.state.collections.some(function (e) {
      return e.items.length > 0;
    });
    return r.state.query ? false === o ? st.createElement(ao, _t({}, r, {
      translations: null == n ? void 0 : n.noResultsScreen
    })) : st.createElement(bo, r) : st.createElement(Oo, _t({}, r, {
      hasCollections: o,
      translations: null == n ? void 0 : n.startScreen
    }));
  }, function (e, t) {
    return "loading" === t.state.status || "stalled" === t.state.status;
  }),
  jo = ["translations"];
function Po(e) {
  var t = e.translations,
    n = void 0 === t ? {} : t,
    r = Et(e, jo),
    o = n.resetButtonTitle,
    i = void 0 === o ? "Clear the query" : o,
    a = n.resetButtonAriaLabel,
    c = void 0 === a ? "Clear the query" : a,
    u = n.cancelButtonText,
    l = void 0 === u ? "Cancel" : u,
    s = n.cancelButtonAriaLabel,
    f = void 0 === s ? "Cancel" : s,
    p = n.searchInputLabel,
    m = void 0 === p ? "Search" : p,
    v = r.getFormProps({
      inputElement: r.inputRef.current
    }).onReset;
  return st.useEffect(function () {
    r.autoFocus && r.inputRef.current && r.inputRef.current.focus();
  }, [r.autoFocus, r.inputRef]), st.useEffect(function () {
    r.isFromSelection && r.inputRef.current && r.inputRef.current.select();
  }, [r.isFromSelection, r.inputRef]), st.createElement(st.Fragment, null, st.createElement("form", {
    className: "DocSearch-Form",
    onSubmit: function (e) {
      e.preventDefault();
    },
    onReset: v
  }, st.createElement("label", _t({
    className: "DocSearch-MagnifierLabel"
  }, r.getLabelProps()), st.createElement(At, null), st.createElement("span", {
    className: "DocSearch-VisuallyHiddenForAccessibility"
  }, m)), st.createElement("div", {
    className: "DocSearch-LoadingIndicator"
  }, st.createElement(Jr, null)), st.createElement("input", _t({
    className: "DocSearch-Input",
    ref: r.inputRef
  }, r.getInputProps({
    inputElement: r.inputRef.current,
    autoFocus: r.autoFocus,
    maxLength: 64
  }))), st.createElement("button", {
    type: "reset",
    title: i,
    className: "DocSearch-Reset",
    "aria-label": c,
    hidden: !r.state.query
  }, st.createElement($r, null))), st.createElement("button", {
    className: "DocSearch-Cancel",
    type: "reset",
    "aria-label": f,
    onClick: r.onClose
  }, l));
}
var Io = ["_highlightResult", "_snippetResult"];
function ko(e) {
  var t = e.key,
    n = e.limit,
    r = void 0 === n ? 5 : n,
    o = function (e) {
      return false === function () {
        var e = "__TEST_KEY__";
        try {
          return localStorage.setItem(e, ""), localStorage.removeItem(e), !0;
        } catch (e) {
          return false;
        }
      }() ? {
        setItem: function () {},
        getItem: function () {
          return [];
        }
      } : {
        setItem: function (t) {
          return window.localStorage.setItem(e, JSON.stringify(t));
        },
        getItem: function () {
          var t = window.localStorage.getItem(e);
          return t ? JSON.parse(t) : [];
        }
      };
    }(t),
    i = o.getItem().slice(0, r);
  return {
    add: function (e) {
      var t = e;
      t._highlightResult, t._snippetResult;
      var n = Et(t, Io),
        a = i.findIndex(function (e) {
          return e.objectID === n.objectID;
        });
      a > -1 && i.splice(a, 1), i.unshift(n), i = i.slice(0, r), o.setItem(i);
    },
    remove: function (e) {
      i = i.filter(function (t) {
        return t.objectID !== e.objectID;
      }), o.setItem(i);
    },
    getAll: function () {
      return i;
    }
  };
}
function Do(e) {
  var t,
    n = "algolia-client-js-".concat(e.key);
  function r() {
    return void 0 === t && (t = e.localStorage || window.localStorage), t;
  }
  function o() {
    return JSON.parse(r().getItem(n) || "{}");
  }
  function i(e) {
    r().setItem(n, JSON.stringify(e));
  }
  return {
    get: function (t, n) {
      var r = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {
        miss: function () {
          return Promise.resolve();
        }
      };
      return Promise.resolve().then(function () {
        var n, r, a;
        return n = e.timeToLive ? 1e3 * e.timeToLive : null, r = o(), i(a = Object.fromEntries(Object.entries(r).filter(function (e) {
          return void 0 !== It(e, 2)[1].timestamp;
        }))), n && i(Object.fromEntries(Object.entries(a).filter(function (e) {
          var t = It(e, 2)[1],
            r = new Date().getTime();
          return !(t.timestamp + n < r);
        }))), o()[JSON.stringify(t)];
      }).then(function (e) {
        return Promise.all([e ? e.value : n(), void 0 !== e]);
      }).then(function (e) {
        var t = It(e, 2),
          n = t[0],
          o = t[1];
        return Promise.all([n, o || r.miss(n)]);
      }).then(function (e) {
        return It(e, 1)[0];
      });
    },
    set: function (e, t) {
      return Promise.resolve().then(function () {
        var i = o();
        return i[JSON.stringify(e)] = {
          timestamp: new Date().getTime(),
          value: t
        }, r().setItem(n, JSON.stringify(i)), t;
      });
    },
    delete: function (e) {
      return Promise.resolve().then(function () {
        var t = o();
        delete t[JSON.stringify(e)], r().setItem(n, JSON.stringify(t));
      });
    },
    clear: function () {
      return Promise.resolve().then(function () {
        r().removeItem(n);
      });
    }
  };
}
function Co(e) {
  var t = kt(e.caches),
    n = t.shift();
  return void 0 === n ? {
    get: function (e, t) {
      var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {
        miss: function () {
          return Promise.resolve();
        }
      };
      return t().then(function (e) {
        return Promise.all([e, n.miss(e)]);
      }).then(function (e) {
        return It(e, 1)[0];
      });
    },
    set: function (e, t) {
      return Promise.resolve(t);
    },
    delete: function (e) {
      return Promise.resolve();
    },
    clear: function () {
      return Promise.resolve();
    }
  } : {
    get: function (e, r) {
      var o = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {
        miss: function () {
          return Promise.resolve();
        }
      };
      return n.get(e, r, o).catch(function () {
        return Co({
          caches: t
        }).get(e, r, o);
      });
    },
    set: function (e, r) {
      return n.set(e, r).catch(function () {
        return Co({
          caches: t
        }).set(e, r);
      });
    },
    delete: function (e) {
      return n.delete(e).catch(function () {
        return Co({
          caches: t
        }).delete(e);
      });
    },
    clear: function () {
      return n.clear().catch(function () {
        return Co({
          caches: t
        }).clear();
      });
    }
  };
}
function xo() {
  var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {
      serializable: true
    },
    t = {};
  return {
    get: function (n, r) {
      var o = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {
          miss: function () {
            return Promise.resolve();
          }
        },
        i = JSON.stringify(n);
      if (i in t) return Promise.resolve(e.serializable ? JSON.parse(t[i]) : t[i]);
      var a = r();
      return a.then(function (e) {
        return o.miss(e);
      }).then(function () {
        return a;
      });
    },
    set: function (n, r) {
      return t[JSON.stringify(n)] = e.serializable ? JSON.stringify(r) : r, Promise.resolve(r);
    },
    delete: function (e) {
      return delete t[JSON.stringify(e)], Promise.resolve();
    },
    clear: function () {
      return t = {}, Promise.resolve();
    }
  };
}
function Ao(e) {
  var t = e.algoliaAgents,
    n = e.client,
    r = e.version,
    o = function (e) {
      var t = {
        value: "Algolia for JavaScript (".concat(e, ")"),
        add: function (e) {
          var n = "; ".concat(e.segment).concat(void 0 !== e.version ? " (".concat(e.version, ")") : "");
          return -1 === t.value.indexOf(n) && (t.value = "".concat(t.value).concat(n)), t;
        }
      };
      return t;
    }(r).add({
      segment: n,
      version: r
    });
  return t.forEach(function (e) {
    return o.add(e);
  }), o;
}
var No = 12e4;
function To(e) {
  var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "up",
    n = Date.now();
  return wt(wt({}, e), {}, {
    status: t,
    lastUpdate: n,
    isUp: function () {
      return "up" === t || Date.now() - n > No;
    },
    isTimedOut: function () {
      return "timed out" === t && Date.now() - n <= No;
    }
  });
}
var Ro = function () {
    function e(t, n) {
      var r;
      return ht(this, e), yt(r = vt(this, e, [t]), "name", "AlgoliaError"), n && (r.name = n), r;
    }
    return bt(e, Ct(Error)), dt(e);
  }(),
  Lo = function () {
    function e(t, n, r) {
      var o;
      return ht(this, e), yt(o = vt(this, e, [t, r]), "stackTrace", void 0), o.stackTrace = n, o;
    }
    return bt(e, Ro), dt(e);
  }(),
  qo = function () {
    function e(t) {
      return ht(this, e), vt(this, e, ["Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.", t, "RetryError"]);
    }
    return bt(e, Lo), dt(e);
  }(),
  Mo = function () {
    function e(t, n, r) {
      var o,
        i = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : "ApiError";
      return ht(this, e), yt(o = vt(this, e, [t, r, i]), "status", void 0), o.status = n, o;
    }
    return bt(e, Lo), dt(e);
  }(),
  Ho = function () {
    function e(t, n) {
      var r;
      return ht(this, e), yt(r = vt(this, e, [t, "DeserializationError"]), "response", void 0), r.response = n, r;
    }
    return bt(e, Ro), dt(e);
  }(),
  Uo = function () {
    function e(t, n, r, o) {
      var i;
      return ht(this, e), yt(i = vt(this, e, [t, n, o, "DetailedApiError"]), "error", void 0), i.error = r, i;
    }
    return bt(e, Mo), dt(e);
  }();
function Fo(e, t, n) {
  var r,
    o = (r = n, Object.keys(r).filter(function (e) {
      return void 0 !== r[e];
    }).sort().map(function (e) {
      return "".concat(e, "=").concat(encodeURIComponent("[object Array]" === Object.prototype.toString.call(r[e]) ? r[e].join(",") : r[e]).replace(/\+/g, "%20"));
    }).join("&")),
    i = "".concat(e.protocol, "://").concat(e.url).concat(e.port ? ":".concat(e.port) : "", "/").concat("/" === t.charAt(0) ? t.substring(1) : t);
  return o.length && (i += "?".concat(o)), i;
}
function Bo(e, t) {
  if ("GET" !== e.method && (void 0 !== e.data || void 0 !== t.data)) {
    var n = Array.isArray(e.data) ? e.data : wt(wt({}, e.data), t.data);
    return JSON.stringify(n);
  }
}
function Vo(e, t, n) {
  var r = wt(wt(wt({
      Accept: "application/json"
    }, e), t), n),
    o = {};
  return Object.keys(r).forEach(function (e) {
    var t = r[e];
    o[e.toLowerCase()] = t;
  }), o;
}
function Ko(e) {
  try {
    return JSON.parse(e.content);
  } catch (t) {
    throw new Ho(t.message, e);
  }
}
function Wo(e, t) {
  var n = e.content,
    r = e.status;
  try {
    var o = JSON.parse(n);
    return "error" in o ? new Uo(o.message, r, o.error, t) : new Mo(o.message, r, t);
  } catch (e) {}
  return new Mo(n, r, t);
}
function zo(e) {
  return e.map(function (e) {
    return Jo(e);
  });
}
function Jo(e) {
  var t = e.request.headers["x-algolia-api-key"] ? {
    "x-algolia-api-key": "*****"
  } : {};
  return wt(wt({}, e), {}, {
    request: wt(wt({}, e.request), {}, {
      headers: wt(wt({}, e.request.headers), t)
    })
  });
}
var Qo = ["appId", "apiKey", "authMode", "algoliaAgents"],
  $o = ["params"],
  Zo = "5.19.0";
function Go(e) {
  return [{
    url: "".concat(e, "-dsn.algolia.net"),
    accept: "read",
    protocol: "https"
  }, {
    url: "".concat(e, ".algolia.net"),
    accept: "write",
    protocol: "https"
  }].concat(function (e) {
    for (var t = e, n = e.length - 1; n > 0; n--) {
      var r = Math.floor(Math.random() * (n + 1)),
        o = e[n];
      t[n] = e[r], t[r] = o;
    }
    return t;
  }([{
    url: "".concat(e, "-1.algolianet.com"),
    accept: "readWrite",
    protocol: "https"
  }, {
    url: "".concat(e, "-2.algolianet.com"),
    accept: "readWrite",
    protocol: "https"
  }, {
    url: "".concat(e, "-3.algolianet.com"),
    accept: "readWrite",
    protocol: "https"
  }]));
}
var Yo = "3.9.0";
var Xo = ["footer", "searchBox"];
function ei(e) {
  var t = e.appId,
    n = e.apiKey,
    r = e.indexName,
    o = e.placeholder,
    i = void 0 === o ? "Search docs" : o,
    a = e.searchParameters,
    c = e.maxResultsPerGroup,
    u = e.onClose,
    l = void 0 === u ? ho : u,
    s = e.transformItems,
    f = void 0 === s ? mo : s,
    p = e.hitComponent,
    m = void 0 === p ? zr : p,
    v = e.resultsFooterComponent,
    h = void 0 === v ? function () {
      return null;
    } : v,
    d = e.navigator,
    y = e.initialScrollY,
    _ = void 0 === y ? 0 : y,
    g = e.transformSearchClient,
    b = void 0 === g ? mo : g,
    S = e.disableUserPersonalization,
    O = void 0 !== S && S,
    w = e.initialQuery,
    E = void 0 === w ? "" : w,
    j = e.translations,
    P = void 0 === j ? {} : j,
    I = e.getMissingResultsUrl,
    k = e.insights,
    D = void 0 !== k && k,
    C = P.footer,
    x = P.searchBox,
    A = Et(P, Xo),
    N = It(st.useState({
      query: "",
      collections: [],
      completion: null,
      context: {},
      isOpen: false,
      activeItemId: null,
      status: "idle"
    }), 2),
    T = N[0],
    R = N[1],
    L = st.useRef(null),
    q = st.useRef(null),
    M = st.useRef(null),
    H = st.useRef(null),
    U = st.useRef(null),
    F = st.useRef(10),
    B = st.useRef("undefined" != typeof window ? window.getSelection().toString().slice(0, 64) : "").current,
    V = st.useRef(E || B).current,
    K = function (e, t, n) {
      return st.useMemo(function () {
        var r = function (e, t) {
          if (!e || "string" != typeof e) throw new Error("`appId` is missing.");
          if (!t || "string" != typeof t) throw new Error("`apiKey` is missing.");
          return function (e) {
            var t = e.appId,
              n = e.apiKey,
              r = e.authMode,
              o = e.algoliaAgents,
              i = Et(e, Qo),
              a = function (e, t) {
                var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "WithinHeaders",
                  r = {
                    "x-algolia-api-key": t,
                    "x-algolia-application-id": e
                  };
                return {
                  headers: function () {
                    return "WithinHeaders" === n ? r : {};
                  },
                  queryParameters: function () {
                    return "WithinQueryParameters" === n ? r : {};
                  }
                };
              }(t, n, r),
              c = function (e) {
                var t = e.hosts,
                  n = e.hostsCache,
                  r = e.baseHeaders,
                  o = e.logger,
                  i = e.baseQueryParameters,
                  a = e.algoliaAgent,
                  c = e.timeouts,
                  u = e.requester,
                  l = e.requestsCache,
                  s = e.responsesCache;
                function f(e) {
                  return p.apply(this, arguments);
                }
                function p() {
                  return (p = mt(jt().mark(function e(t) {
                    var r, o, i, a, c;
                    return jt().wrap(function (e) {
                      for (;;) switch (e.prev = e.next) {
                        case 0:
                          return e.next = 2, Promise.all(t.map(function (e) {
                            return n.get(e, function () {
                              return Promise.resolve(To(e));
                            });
                          }));
                        case 2:
                          return r = e.sent, o = r.filter(function (e) {
                            return e.isUp();
                          }), i = r.filter(function (e) {
                            return e.isTimedOut();
                          }), a = [].concat(kt(o), kt(i)), c = a.length > 0 ? a : t, e.abrupt("return", {
                            hosts: c,
                            getTimeout: function (e, t) {
                              return (0 === i.length && 0 === e ? 1 : i.length + 3 + e) * t;
                            }
                          });
                        case 8:
                        case "end":
                          return e.stop();
                      }
                    }, e);
                  }))).apply(this, arguments);
                }
                function m(e, t) {
                  return v.apply(this, arguments);
                }
                function v() {
                  return v = mt(jt().mark(function e(l, s) {
                    var p,
                      m,
                      v,
                      h,
                      d,
                      y,
                      _,
                      g,
                      b,
                      S,
                      O,
                      w,
                      E,
                      j = arguments;
                    return jt().wrap(function (e) {
                      for (;;) switch (e.prev = e.next) {
                        case 0:
                          if (p = !(j.length > 2 && void 0 !== j[2]) || j[2], m = [], v = Bo(l, s), h = Vo(r, l.headers, s.headers), d = "GET" === l.method ? wt(wt({}, l.data), s.data) : {}, y = wt(wt(wt({}, i), l.queryParameters), d), a.value && (y["x-algolia-agent"] = a.value), s && s.queryParameters) for (_ = 0, g = Object.keys(s.queryParameters); _ < g.length; _++) b = g[_], s.queryParameters[b] && "[object Object]" !== Object.prototype.toString.call(s.queryParameters[b]) ? y[b] = s.queryParameters[b].toString() : y[b] = s.queryParameters[b];
                          return S = 0, O = function () {
                            var e = mt(jt().mark(function e(t, r) {
                              var i, a, f, d, _, g;
                              return jt().wrap(function (e) {
                                for (;;) switch (e.prev = e.next) {
                                  case 0:
                                    if (void 0 !== (i = t.pop())) {
                                      e.next = 3;
                                      break;
                                    }
                                    throw new qo(zo(m));
                                  case 3:
                                    return a = wt(wt({}, c), s.timeouts), f = {
                                      data: v,
                                      headers: h,
                                      method: l.method,
                                      url: Fo(i, l.path, y),
                                      connectTimeout: r(S, a.connect),
                                      responseTimeout: r(S, p ? a.read : a.write)
                                    }, d = function (e) {
                                      var n = {
                                        request: f,
                                        response: e,
                                        host: i,
                                        triesLeft: t.length
                                      };
                                      return m.push(n), n;
                                    }, e.next = 8, u.send(f);
                                  case 8:
                                    if (w = (b = _ = e.sent).isTimedOut, E = b.status, !(w || function (e) {
                                      return !e.isTimedOut && !~~e.status;
                                    }({
                                      isTimedOut: w,
                                      status: E
                                    }) || 2 != ~~(E / 100) && 4 != ~~(E / 100))) {
                                      e.next = 16;
                                      break;
                                    }
                                    return g = d(_), _.isTimedOut && S++, o.info("Retryable failure", Jo(g)), e.next = 15, n.set(i, To(i, _.isTimedOut ? "timed out" : "down"));
                                  case 15:
                                    return e.abrupt("return", O(t, r));
                                  case 16:
                                    if (2 != ~~(_.status / 100)) {
                                      e.next = 18;
                                      break;
                                    }
                                    return e.abrupt("return", Ko(_));
                                  case 18:
                                    throw d(_), Wo(_, m);
                                  case 20:
                                  case "end":
                                    return e.stop();
                                }
                                var b, w, E;
                              }, e);
                            }));
                            return function (t, n) {
                              return e.apply(this, arguments);
                            };
                          }(), w = t.filter(function (e) {
                            return "readWrite" === e.accept || (p ? "read" === e.accept : "write" === e.accept);
                          }), e.next = 13, f(w);
                        case 13:
                          return E = e.sent, e.abrupt("return", O(kt(E.hosts).reverse(), E.getTimeout));
                        case 15:
                        case "end":
                          return e.stop();
                      }
                    }, e);
                  })), v.apply(this, arguments);
                }
                return {
                  hostsCache: n,
                  requester: u,
                  timeouts: c,
                  logger: o,
                  algoliaAgent: a,
                  baseHeaders: r,
                  baseQueryParameters: i,
                  hosts: t,
                  request: function (e) {
                    var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
                      n = e.useReadTransporter || "GET" === e.method;
                    if (!n) return m(e, t, n);
                    var o = function () {
                      return m(e, t);
                    };
                    if (true !== (t.cacheable || e.cacheable)) return o();
                    var a = {
                      request: e,
                      requestOptions: t,
                      transporter: {
                        queryParameters: i,
                        headers: r
                      }
                    };
                    return s.get(a, function () {
                      return l.get(a, function () {
                        return l.set(a, o()).then(function (e) {
                          return Promise.all([l.delete(a), e]);
                        }, function (e) {
                          return Promise.all([l.delete(a), Promise.reject(e)]);
                        }).then(function (e) {
                          var t = It(e, 2);
                          return t[0], t[1];
                        });
                      });
                    }, {
                      miss: function (e) {
                        return s.set(a, e);
                      }
                    });
                  },
                  requestsCache: l,
                  responsesCache: s
                };
              }(wt(wt({
                hosts: Go(t)
              }, i), {}, {
                algoliaAgent: Ao({
                  algoliaAgents: o,
                  client: "Lite",
                  version: Zo
                }),
                baseHeaders: wt(wt({
                  "content-type": "text/plain"
                }, a.headers()), i.baseHeaders),
                baseQueryParameters: wt(wt({}, a.queryParameters()), i.baseQueryParameters)
              }));
            return {
              transporter: c,
              appId: t,
              apiKey: n,
              clearCache: function () {
                return Promise.all([c.requestsCache.clear(), c.responsesCache.clear()]).then(function () {});
              },
              get _ua() {
                return c.algoliaAgent.value;
              },
              addAlgoliaAgent: function (e, t) {
                c.algoliaAgent.add({
                  segment: e,
                  version: t
                });
              },
              setClientApiKey: function (e) {
                var t = e.apiKey;
                r && "WithinHeaders" !== r ? c.baseQueryParameters["x-algolia-api-key"] = t : c.baseHeaders["x-algolia-api-key"] = t;
              },
              searchForHits: function (e, t) {
                return this.search(e, t);
              },
              searchForFacets: function (e, t) {
                return this.search(e, t);
              },
              customPost: function (e, t) {
                var n = e.path,
                  r = e.parameters,
                  o = e.body;
                if (!n) throw new Error("Parameter `path` is required when calling `customPost`.");
                var i = {
                  method: "POST",
                  path: "/{path}".replace("{path}", n),
                  queryParameters: r || {},
                  headers: {},
                  data: o || {}
                };
                return c.request(i, t);
              },
              getRecommendations: function (e, t) {
                if (e && Array.isArray(e) && (e = {
                  requests: e
                }), !e) throw new Error("Parameter `getRecommendationsParams` is required when calling `getRecommendations`.");
                if (!e.requests) throw new Error("Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.");
                var n = {
                  method: "POST",
                  path: "/1/indexes/*/recommendations",
                  queryParameters: {},
                  headers: {},
                  data: e,
                  useReadTransporter: true,
                  cacheable: true
                };
                return c.request(n, t);
              },
              search: function (e, t) {
                if (e && Array.isArray(e)) {
                  var n = {
                    requests: e.map(function (e) {
                      var t = e.params,
                        n = Et(e, $o);
                      return "facet" === n.type ? wt(wt(wt({}, n), t), {}, {
                        type: "facet"
                      }) : wt(wt(wt({}, n), t), {}, {
                        facet: void 0,
                        maxFacetHits: void 0,
                        facetQuery: void 0
                      });
                    })
                  };
                  e = n;
                }
                if (!e) throw new Error("Parameter `searchMethodParams` is required when calling `search`.");
                if (!e.requests) throw new Error("Parameter `searchMethodParams.requests` is required when calling `search`.");
                var r = {
                  method: "POST",
                  path: "/1/indexes/*/queries",
                  queryParameters: {},
                  headers: {},
                  data: e,
                  useReadTransporter: true,
                  cacheable: true
                };
                return c.request(r, t);
              }
            };
          }(wt({
            appId: e,
            apiKey: t,
            timeouts: {
              connect: 1e3,
              read: 2e3,
              write: 3e4
            },
            logger: {
              debug: function (e, t) {
                return Promise.resolve();
              },
              info: function (e, t) {
                return Promise.resolve();
              },
              error: function (e, t) {
                return Promise.resolve();
              }
            },
            requester: {
              send: function (e) {
                return new Promise(function (t) {
                  var n = new XMLHttpRequest();
                  n.open(e.method, e.url, true), Object.keys(e.headers).forEach(function (t) {
                    return n.setRequestHeader(t, e.headers[t]);
                  });
                  var r,
                    o = function (e, r) {
                      return setTimeout(function () {
                        n.abort(), t({
                          status: 0,
                          content: r,
                          isTimedOut: true
                        });
                      }, e);
                    },
                    i = o(e.connectTimeout, "Connection timeout");
                  n.onreadystatechange = function () {
                    n.readyState > n.OPENED && void 0 === r && (clearTimeout(i), r = o(e.responseTimeout, "Socket timeout"));
                  }, n.onerror = function () {
                    0 === n.status && (clearTimeout(i), clearTimeout(r), t({
                      content: n.responseText || "Network request failed",
                      status: n.status,
                      isTimedOut: false
                    }));
                  }, n.onload = function () {
                    clearTimeout(i), clearTimeout(r), t({
                      content: n.responseText,
                      status: n.status,
                      isTimedOut: false
                    });
                  }, n.send(e.data);
                });
              }
            },
            algoliaAgents: [{
              segment: "Browser"
            }],
            authMode: "WithinQueryParameters",
            responsesCache: xo(),
            requestsCache: xo({
              serializable: false
            }),
            hostsCache: Co({
              caches: [Do({
                key: "".concat(Zo, "-").concat(e)
              }), xo()]
            })
          }, void 0));
        }(e, t);
        return r.addAlgoliaAgent("docsearch", Yo), false === /docsearch.js \(.*\)/.test(r.transporter.algoliaAgent.value) && r.addAlgoliaAgent("docsearch-react", Yo), n(r);
      }, [e, t, n]);
    }(t, n, b),
    W = st.useRef(ko({
      key: "__DOCSEARCH_FAVORITE_SEARCHES__".concat(r),
      limit: 10
    })).current,
    z = st.useRef(ko({
      key: "__DOCSEARCH_RECENT_SEARCHES__".concat(r),
      limit: 0 === W.getAll().length ? 7 : 4
    })).current,
    J = st.useCallback(function (e) {
      if (!O) {
        var t = "content" === e.type ? e.__docsearch_parent : e;
        t && -1 === W.getAll().findIndex(function (e) {
          return e.objectID === t.objectID;
        }) && z.add(t);
      }
    }, [W, z, O]),
    Q = st.useCallback(function (e) {
      if (T.context.algoliaInsightsPlugin && e.__autocomplete_id) {
        var t = e,
          n = {
            eventName: "Item Selected",
            index: t.__autocomplete_indexName,
            items: [t],
            positions: [e.__autocomplete_id],
            queryID: t.__autocomplete_queryID
          };
        T.context.algoliaInsightsPlugin.insights.clickedObjectIDsAfterSearch(n);
      }
    }, [T.context.algoliaInsightsPlugin]),
    $ = st.useMemo(function () {
      return Br({
        id: "docsearch",
        defaultActiveItemId: 0,
        placeholder: i,
        openOnFocus: true,
        initialState: {
          query: V,
          context: {
            searchSuggestions: []
          }
        },
        insights: D,
        navigator: d,
        onStateChange: function (e) {
          R(e.state);
        },
        getSources: function (e) {
          var o = e.query,
            i = e.state,
            u = e.setContext,
            s = e.setStatus;
          if (!o) return O ? [] : [{
            sourceId: "recentSearches",
            onSelect: function (e) {
              var t = e.item,
                n = e.event;
              J(t), vo(n) || l();
            },
            getItemUrl: function (e) {
              return e.item.url;
            },
            getItems: function () {
              return z.getAll();
            }
          }, {
            sourceId: "favoriteSearches",
            onSelect: function (e) {
              var t = e.item,
                n = e.event;
              J(t), vo(n) || l();
            },
            getItemUrl: function (e) {
              return e.item.url;
            },
            getItems: function () {
              return W.getAll();
            }
          }];
          var p = Boolean(D);
          return K.search({
            requests: [wt({
              query: o,
              indexName: r,
              attributesToRetrieve: ["hierarchy.lvl0", "hierarchy.lvl1", "hierarchy.lvl2", "hierarchy.lvl3", "hierarchy.lvl4", "hierarchy.lvl5", "hierarchy.lvl6", "content", "type", "url"],
              attributesToSnippet: ["hierarchy.lvl1:".concat(F.current), "hierarchy.lvl2:".concat(F.current), "hierarchy.lvl3:".concat(F.current), "hierarchy.lvl4:".concat(F.current), "hierarchy.lvl5:".concat(F.current), "hierarchy.lvl6:".concat(F.current), "content:".concat(F.current)],
              snippetEllipsisText: "…",
              highlightPreTag: "<mark>",
              highlightPostTag: "</mark>",
              hitsPerPage: 20,
              clickAnalytics: p
            }, a)]
          }).catch(function (e) {
            throw "RetryError" === e.name && s("error"), e;
          }).then(function (e) {
            var o = e.results[0],
              a = o.hits,
              s = o.nbHits,
              m = po(a, function (e) {
                return go(e);
              }, c);
            i.context.searchSuggestions.length < Object.keys(m).length && u({
              searchSuggestions: Object.keys(m)
            }), u({
              nbHits: s
            });
            var v = {};
            return p && (v = {
              __autocomplete_indexName: r,
              __autocomplete_queryID: o.queryID,
              __autocomplete_algoliaCredentials: {
                appId: t,
                apiKey: n
              }
            }), Object.values(m).map(function (e, t) {
              return {
                sourceId: "hits".concat(t),
                onSelect: function (e) {
                  var t = e.item,
                    n = e.event;
                  J(t), vo(n) || l();
                },
                getItemUrl: function (e) {
                  return e.item.url;
                },
                getItems: function () {
                  return Object.values(po(e, function (e) {
                    return e.hierarchy.lvl1;
                  }, c)).map(f).map(function (e) {
                    return e.map(function (t) {
                      var n = null,
                        r = e.find(function (e) {
                          return "lvl1" === e.type && e.hierarchy.lvl1 === t.hierarchy.lvl1;
                        });
                      return "lvl1" !== t.type && r && (n = r), wt(wt({}, t), {}, {
                        __docsearch_parent: n
                      }, v);
                    });
                  }).flat();
                }
              };
            });
          });
        }
      });
    }, [r, a, c, K, l, z, W, J, V, i, d, f, O, D, t, n]),
    Z = $.getEnvironmentProps,
    G = $.getRootProps,
    Y = $.refresh;
  return function (e) {
    var t = e.getEnvironmentProps,
      n = e.panelElement,
      r = e.formElement,
      o = e.inputElement;
    st.useEffect(function () {
      if (n && r && o) {
        var e = t({
            panelElement: n,
            formElement: r,
            inputElement: o
          }),
          i = e.onTouchStart,
          a = e.onTouchMove;
        return window.addEventListener("touchstart", i), window.addEventListener("touchmove", a), function () {
          window.removeEventListener("touchstart", i), window.removeEventListener("touchmove", a);
        };
      }
    }, [t, n, r, o]);
  }({
    getEnvironmentProps: Z,
    panelElement: H.current,
    formElement: M.current,
    inputElement: U.current
  }), function (e) {
    var t = e.container;
    st.useEffect(function () {
      if (t) {
        var e = t.querySelectorAll("a[href]:not([disabled]), button:not([disabled]), input:not([disabled])"),
          n = e[0],
          r = e[e.length - 1];
        return t.addEventListener("keydown", o), function () {
          t.removeEventListener("keydown", o);
        };
      }
      function o(e) {
        "Tab" === e.key && (e.shiftKey ? document.activeElement === n && (e.preventDefault(), r.focus()) : document.activeElement === r && (e.preventDefault(), n.focus()));
      }
    }, [t]);
  }({
    container: L.current
  }), st.useEffect(function () {
    return document.body.classList.add("DocSearch--active"), function () {
      var e, t;
      document.body.classList.remove("DocSearch--active"), null === (e = (t = window).scrollTo) || void 0 === e || e.call(t, 0, _);
    };
  }, []), st.useLayoutEffect(function () {
    var e = window.innerWidth - document.body.clientWidth;
    return document.body.style.marginRight = "".concat(e, "px"), function () {
      document.body.style.marginRight = "0px";
    };
  }, []), st.useEffect(function () {
    window.matchMedia("(max-width: 768px)").matches && (F.current = 5);
  }, []), st.useEffect(function () {
    H.current && (H.current.scrollTop = 0);
  }, [T.query]), st.useEffect(function () {
    V.length > 0 && (Y(), U.current && U.current.focus());
  }, [V, Y]), st.useEffect(function () {
    function e() {
      if (q.current) {
        var e = .01 * window.innerHeight;
        q.current.style.setProperty("--docsearch-vh", "".concat(e, "px"));
      }
    }
    return e(), window.addEventListener("resize", e), function () {
      window.removeEventListener("resize", e);
    };
  }, []), st.createElement("div", _t({
    ref: L
  }, G({
    "aria-expanded": true
  }), {
    className: ["DocSearch", "DocSearch-Container", "stalled" === T.status && "DocSearch-Container--Stalled", "error" === T.status && "DocSearch-Container--Errored"].filter(Boolean).join(" "),
    role: "button",
    tabIndex: 0,
    onMouseDown: function (e) {
      e.target === e.currentTarget && l();
    }
  }), st.createElement("div", {
    className: "DocSearch-Modal",
    ref: q
  }, st.createElement("header", {
    className: "DocSearch-SearchBar",
    ref: M
  }, st.createElement(Po, _t({}, $, {
    state: T,
    autoFocus: 0 === V.length,
    inputRef: U,
    isFromSelection: Boolean(V) && V === B,
    translations: x,
    onClose: l
  }))), st.createElement("div", {
    className: "DocSearch-Dropdown",
    ref: H
  }, st.createElement(Eo, _t({}, $, {
    indexName: r,
    state: T,
    hitComponent: m,
    resultsFooterComponent: h,
    disableUserPersonalization: O,
    recentSearches: z,
    favoriteSearches: W,
    inputRef: U,
    translations: A,
    getMissingResultsUrl: I,
    onItemClick: function (e, t) {
      Q(e), J(e), vo(t) || l();
    }
  }))), st.createElement("footer", {
    className: "DocSearch-Footer"
  }, st.createElement(Wr, {
    translations: C
  }))));
}
function ti(e) {
  var t,
    n,
    r = st.useRef(null),
    o = It(st.useState(false), 2),
    i = o[0],
    a = o[1],
    c = It(st.useState((null == e ? void 0 : e.initialQuery) || void 0), 2),
    u = c[0],
    l = c[1],
    s = st.useCallback(function () {
      a(true);
    }, [a]),
    f = st.useCallback(function () {
      a(false), l(null == e ? void 0 : e.initialQuery);
    }, [a, e.initialQuery]);
  return function (e) {
    var t = e.isOpen,
      n = e.onOpen,
      r = e.onClose,
      o = e.onInput,
      i = e.searchButtonRef;
    st.useEffect(function () {
      function e(e) {
        var a;
        if ("Escape" === e.code && t || "k" === (null === (a = e.key) || void 0 === a ? void 0 : a.toLowerCase()) && (e.metaKey || e.ctrlKey) || !function (e) {
          var t = e.target,
            n = t.tagName;
          return t.isContentEditable || "INPUT" === n || "SELECT" === n || "TEXTAREA" === n;
        }(e) && "/" === e.key && !t) return e.preventDefault(), void (t ? r() : document.body.classList.contains("DocSearch--active") || n());
        i && i.current === document.activeElement && o && /[a-zA-Z0-9]/.test(String.fromCharCode(e.keyCode)) && o(e);
      }
      return window.addEventListener("keydown", e), function () {
        window.removeEventListener("keydown", e);
      };
    }, [t, n, r, o, i]);
  }({
    isOpen: i,
    onOpen: s,
    onClose: f,
    onInput: st.useCallback(function (e) {
      a(true), l(e.key);
    }, [a, l]),
    searchButtonRef: r
  }), st.createElement(st.Fragment, null, st.createElement(Rt, {
    ref: r,
    translations: null == e || null === (t = e.translations) || void 0 === t ? void 0 : t.button,
    onClick: s
  }), i && We(st.createElement(ei, _t({}, e, {
    initialScrollY: window.scrollY,
    initialQuery: u,
    translations: null == e || null === (n = e.translations) || void 0 === n ? void 0 : n.modal,
    onClose: f
  })), document.body));
}
function ni(t) {
  Ye(st.createElement(ti, e({}, t, {
    transformSearchClient: function (e) {
      return e.addAlgoliaAgent("docsearch.js", Yo), t.transformSearchClient ? t.transformSearchClient(e) : e;
    }
  })), function (e) {
    var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : window;
    return "string" == typeof e ? t.document.querySelector(e) : e;
  }(t.container, t.environment));
}

ni({
  container: '#docsearch',
  appId: "NE1EGTYLS9",
  indexName: "tabler",
  apiKey: "********************************"
});
//# sourceMappingURL=docs.js.map
